import { WindsurfApiService } from "./src/claude/claude-windsurf.js";

/**
 * 直接测试Windsurf API调用 - 绕过本地服务器
 */
async function testWindsurfDirectAPI() {
  console.log("🧪 直接测试Windsurf API调用...");

  try {
    // 初始化Windsurf服务
    console.log("\n📋 步骤1: 初始化Windsurf服务");
    const windsurfService = new WindsurfApiService();

    // 初始化认证
    console.log("\n🔐 步骤2: 初始化认证");
    await windsurfService.initializeAuth();

    console.log(
      `✅ API Key已加载: ${windsurfService.apiKey?.substring(0, 20)}...`
    );

    // 测试模型列表
    console.log("\n📋 步骤3: 获取支持的模型");
    const models = await windsurfService.getModels();
    console.log(`✅ 支持的模型数量: ${models.length}`);
    console.log(`模型列表: ${models.map((m) => m.id).join(", ")}`);

    // 测试聊天功能
    console.log("\n💬 步骤4: 测试聊天功能");
    const testMessages = [
      {
        role: "user",
        content:
          'Hello! Please respond with just "API test successful" if you can see this message.',
      },
    ];

    console.log("发送测试消息...");
    const response = await windsurfService.chatCompletion(
      testMessages,
      "claude-4-sonnet",
      {
        max_tokens: 50,
        temperature: 0.1,
      }
    );

    console.log("✅ 聊天响应成功");
    console.log(
      `响应内容: ${
        response.content?.[0]?.text ||
        response.choices?.[0]?.message?.content ||
        "无内容"
      }`
    );
    console.log(`使用的模型: ${response.model || "未知"}`);
    console.log(
      `Token使用: 输入=${response.usage?.input_tokens || 0}, 输出=${
        response.usage?.output_tokens || 0
      }`
    );

    // 检查是否是真实API响应还是模拟响应
    if (response.content?.[0]?.text?.includes("fallback response")) {
      console.log("⚠️ 这是模拟响应，真实API调用可能失败了");
      console.log("需要检查API端点和认证方式");
    } else {
      console.log("🎉 真实API调用成功！");
    }

    return true;
  } catch (error) {
    console.error("❌ 测试失败:", error.message);
    console.error("错误详情:", error.stack);

    // 分析错误类型
    if (
      error.message.includes("401") ||
      error.message.includes("Unauthorized")
    ) {
      console.log("\n🔍 401错误分析:");
      console.log("- API Key可能无效或已过期");
      console.log("- 认证格式可能不正确");
      console.log("- API端点可能不正确");
    } else if (
      error.message.includes("403") ||
      error.message.includes("Forbidden")
    ) {
      console.log("\n🔍 403错误分析:");
      console.log("- 账号可能被限制访问");
      console.log("- 需要等待一段时间再试");
    } else if (
      error.message.includes("ECONNREFUSED") ||
      error.message.includes("ENOTFOUND")
    ) {
      console.log("\n🔍 网络错误分析:");
      console.log("- API端点可能不存在");
      console.log("- 网络连接问题");
    }

    return false;
  }
}

// 运行测试
console.log("🚀 开始Windsurf API直接测试");
testWindsurfDirectAPI()
  .then((success) => {
    if (success) {
      console.log("\n🎉 所有测试通过！");
    } else {
      console.log("\n❌ 测试失败，需要进一步调试");
    }
  })
  .catch((error) => {
    console.error("\n🚨 测试过程中发生未捕获的错误:", error);
  });

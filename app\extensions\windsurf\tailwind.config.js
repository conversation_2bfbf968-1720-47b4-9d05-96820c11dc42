const defaultTheme = require('tailwindcss/defaultTheme');

/** @type {import('tailwindcss').Config} */
module.exports = {
  safelist: [
    // These tailwind classes are not properly generated by Tailwind
    // TODO(andy): figure out why they're not being successfully generated
    // from ToolStatus code in Cascade.
    'group-hover:shadow',
    'group-hover:opacity-20',
    '-left-[0.125rem]',
    '-bottom-[0.125rem]',
  ],
  // The base directory is the project repo and not the extension repo.
  content: [
    './extensions/windsurf/src/panel/chat/**/*.(js|jsx|ts|tsx)',
    '../exa/chat_client/src/**/*.(js|jsx|ts|tsx)',
    '../exa/design_system/src/**/*.(js|jsx|ts|tsx)',
  ],
  darkMode: 'class', // or 'media' or 'class'
  theme: {
    screens: {
      'ws-xs': '16rem',
      'ws-sm': '22rem',
      'ws-md': '30rem',

      sm: '40rem',
      // => @media (min-width: 640px) { ... }

      md: '48rem',
      // => @media (min-width: 768px) { ... }

      lg: '64rem',
      // => @media (min-width: 1024px) { ... }

      xl: '80rem',
      // => @media (min-width: 1280px) { ... }

      '2xl': '96rem',
      // => @media (min-width: 1536px) { ... }
    },
    extend: {
      fontFamily: {
        default: ['var(--default-font)', ...defaultTheme.fontFamily.sans],
        heading: ['var(--header-font)', ...defaultTheme.fontFamily.sans],
        mono: [
          'SF Mono',
          'Monaco',
          'Menlo',
          'Courier',
          'monospace',
          ...defaultTheme.fontFamily.mono,
        ],
      },
      fontSize: {
        // Use rem instead of px for font-size.
        xs: '0.688rem',
        sm: '0.75rem',
        base: '0.813rem',
        lg: '1rem',
        xl: '1.15rem',
        '2xl': '1.363rem',
        '3xl': '1.753rem',
        '4xl': '2.041rem',
        '5xl': '2.552rem',
      },
      colors: {
        // Primary brand colors.
        'brand-dark': {
          DEFAULT: '#09b6a2',
          50: '#effefa',
          100: '#c9fef3',
          200: '#93fce7',
          300: '#55f3d9',
          400: '#22dfc6',
          500: '#09b6a2',
          600: '#049d8e',
          700: '#087d73',
          800: '#0c635c',
          900: '#0f524d',
        },
        'brand-light': {
          DEFAULT: '#71E9D8',
          50: '#f0fdfa',
          100: '#cdfaf1',
          200: '#9bf4e4',
          300: '#71e9d8',
          400: '#31d0be',
          500: '#18b4a5',
          600: '#109187',
          700: '#12736e',
          800: '#135c58',
          900: '#144d4a',
        },

        // TODO (k): Deprecate this!
        secondary: {
          DEFAULT: '#B2FFE6',
          50: '#F7FFFC',
          100: '#F0FFFA',
          200: '#E0FFF5',
          300: '#D1FFF0',
          400: '#C2FFEB',
          500: '#B2FFE6',
          600: '#A3FFE0',
          700: '#94FFDB',
          800: '#85FFD6',
          900: '#75FFD1',
        },

        // TODO (k): Deprecate this!
        gray: {
          50: '#EAEAEC',
          100: '#D5D6D9',
          200: '#C0C1C6',
          300: '#ABADB3',
          400: '#9698A1',
          500: '#81838E',
          600: '#6C6F7B',
          700: '#575A68',
          800: '#424655',
          900: '#2D3142',
        },

        // Color Palette.
        red: {
          DEFAULT: '#cb3d3d',
          50: '#fdf3f3',
          100: '#fbe5e5',
          200: '#f9cfcf',
          300: '#f3aeae',
          400: '#ea7f7f',
          500: '#de5555',
          600: '#cb3d3d',
          700: '#a92c2c',
          800: '#8c2828',
          900: '#752727',
        },
        orange: {
          DEFAULT: '#e8975f',
          50: '#fdf6ef',
          100: '#faebda',
          200: '#f5d3b3',
          300: '#eeb683',
          400: '#e8975f',
          500: '#e0712f',
          600: '#d25924',
          700: '#ae4320',
          800: '#8b3721',
          900: '#70301e',
        },
        gold: {
          DEFAULT: '#cab43e',
          50: '#faf9ec',
          100: '#f2f1cf',
          200: '#e6e1a2',
          300: '#d8cc6c',
          400: '#cab43e',
          500: '#bca136',
          600: '#a2812c',
          700: '#826026',
          800: '#6d4f26',
          900: '#5e4225',
        },
        green: {
          DEFAULT: '#7aae66',
          50: '#f6f9f4',
          100: '#e9f3e5',
          200: '#d4e6cc',
          300: '#b0d2a3',
          400: '#7aae66',
          500: '#63984f',
          600: '#4e7c3d',
          700: '#406233',
          800: '#354f2c',
          900: '#2c4225',
        },
        blue: {
          DEFAULT: '#6a9fcb',
          50: '#f4f7fb',
          100: '#e7eff7',
          200: '#cadced',
          300: '#9cbedd',
          400: '#6a9fcb',
          500: '#4380b4',
          600: '#316698',
          700: '#29527b',
          800: '#254667',
          900: '#243d56',
        },
        violet: {
          DEFAULT: '#bb84cc',
          50: '#fbf8fc',
          100: '#f6eef9',
          200: '#efe0f4',
          300: '#e2c8ea',
          400: '#cea5db',
          500: '#bb84cc',
          600: '#a665b8',
          700: '#8f519f',
          800: '#764683',
          900: '#60396a',
        },
        surface: {
          DEFAULT: '#1d1f21',
          50: '#f6f6f7',
          100: '#e1e4e6',
          200: '#c3c8cc',
          300: '#9da5ab',
          400: '#788189',
          500: '#5d676f',
          600: '#495158',
          700: '#3d4348',
          800: '#33383c',
          900: '#1d1f21',
        },
        'editor-content-area': '#2B2C2E',
        'editor-background': '#3A3A3B',
        'editor-tab-inactive': '#404040',

        // Variables used in the chat client and other IDE surfaces.
        'ide-chat-background': `var(--codeium-chat-background)`,
        'ide-editor-background': 'var(--codeium-editor-background)',
        'ide-editor-color': 'var(--codeium-editor-color)',
        'ide-text-color': 'var(--codeium-text-color)',
        'ide-link-color': 'var(--codeium-link-color)',
        'ide-link-hover-color': 'var(--codeium-link-hover-color)',
        'ide-caption-color': 'var(--codeium-caption-color)',
        'ide-message-block-user-background':
          'var(--codeium-message-block-user-background)',
        'ide-message-block-user-color':
          'var(--codeium-message-block-user-color)',
        'ide-message-block-bot-background':
          'var(--codeium-message-block-bot-background)',
        'ide-message-block-bot-color': 'var(--codeium-message-block-bot-color)',
        'ide-input-color': 'var(--codeium-input-color)',
        'ide-input-placeholder': 'var(--codeium-input-placeholder)',
        'ide-input-background': 'var(--codeium-input-background)',
        'ide-tooltip-background': 'var(--codeium-tooltip-background)',
        'ide-tooltip-color': 'var(--codeium-tooltip-color)',
        'ide-active-selection-background':
          'var(--codeium-active-selection-background)',
        'ide-active-selection-color': 'var(--codeium-active-selection-color)',
        'ide-button-hover-background': 'var(--codeium-button-hover-background)',
        'ide-button-background': 'var(--codeium-button-background)',
        'ide-button-color': 'var(--codeium-button-color)',
        'ide-button-secondary-hover-background':
          'var(--codeium-button-secondary-hover-background)',
        'ide-button-secondary-background':
          'var(--codeium-button-secondary-background)',
        'ide-button-secondary-color': 'var(--codeium-button-secondary-color)',
        'ide-focus-border': 'var(--codeium-focus-border)',
        'ide-sidebar-title-color': 'var(--codeium-sidebar-title-color)',
        'ide-icon-color': 'var(--codeium-icon-color)',
        'ide-disabled-color': 'var(--codeium-disabled-color)',
        'ide-notificationsWarningIcon-foreground':
          'var(--codeium-notificationsWarningIcon-foreground)',
        'ide-toolbar-background': 'var(--codeium-toolbar-background)',
        'ide-toolbar-hover-background':
          'var(--codeium-toolbar-hover-background)',
      },
      screens: {
        xs: '25rem',
      },
      animation: {
        blink: '1s pulse infinite',
      },
    },
  },
  variants: {
    extend: {},
  },
  plugins: [require('@tailwindcss/typography')],
};

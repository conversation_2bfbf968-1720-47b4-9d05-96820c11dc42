/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var $bb=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?$ub.isErrorNoTelemetry(e)?new $ub(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},$cb=new $bb;function $gb(e){$kb(e)||$cb.onUnexpectedError(e)}function $ib(e){if(e instanceof Error){const{name:t,message:n,cause:i}=e,r=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:r,noTelemetry:$ub.isErrorNoTelemetry(e),cause:i?$ib(i):void 0,code:e.code}}return e}var canceledName="Canceled";function $kb(e){return e instanceof $lb?!0:e instanceof Error&&e.name===canceledName&&e.message===canceledName}var $lb=class extends Error{constructor(){super(canceledName),this.name=this.message}};function $ob(e){return e?new Error(`Illegal state: ${e}`):new Error("Illegal state")}var $ub=class ht extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof ht)return t;const n=new ht;return n.message=t.message,n.stack=t.stack,n}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}},_a;function $a(e,t){const n=Object.create(null);for(const i of e){const r=t(i);let s=n[r];s||(s=n[r]=[]),s.push(i)}return n}var $e=class{static{_a=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[_a]="SetWithKey";for(const n of e)this.add(n)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(n=>e.call(t,n,n,this))}[Symbol.iterator](){return this.values()}};function $Pc(e,t){const n=this;let i=!1,r;return function(){if(i)return r;if(i=!0,t)try{r=e.apply(n,arguments)}finally{t()}else r=e.apply(n,arguments);return r}}function $4(e,t,n=0,i=e.length){let r=n,s=i;for(;r<s;){const l=Math.floor((r+s)/2);t(e[l])?r=l+1:s=l}return r-1}var $8=class mt{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if(mt.assertInvariants){if(this.d){for(const i of this.e)if(this.d(i)&&!t(i))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const n=$4(this.e,t,this.c);return this.c=n+1,n===-1?void 0:this.e[n]}},CompareResult;(function(e){function t(s){return s<0}e.isLessThan=t;function n(s){return s<=0}e.isLessThanOrEqual=n;function i(s){return s>0}e.isGreaterThan=i;function r(s){return s===0}e.isNeitherLessOrGreaterThan=r,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(CompareResult||(CompareResult={}));function $9b(e,t){return(n,i)=>t(e(n),e(i))}var $$b=(e,t)=>e-t,$dc=class it{static{this.empty=new it(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(n=>(t(n),!0))}toArray(){const t=[];return this.iterate(n=>(t.push(n),!0)),t}filter(t){return new it(n=>this.iterate(i=>t(i)?n(i):!0))}map(t){return new it(n=>this.iterate(i=>n(t(i))))}some(t){let n=!1;return this.iterate(i=>(n=t(i),!n)),n}findFirst(t){let n;return this.iterate(i=>t(i)?(n=i,!1):!0),n}findLast(t){let n;return this.iterate(i=>(t(i)&&(n=i),!0)),n}findLastMaxBy(t){let n,i=!0;return this.iterate(r=>((i||CompareResult.isGreaterThan(t(r,n)))&&(i=!1,n=r),!0)),n}},_a2,_b,_c,ResourceMapEntry=class{constructor(e,t){this.uri=e,this.value=t}};function isEntries(e){return Array.isArray(e)}var $Fc=class K{static{this.c=t=>t.toString()}constructor(t,n){if(this[_a2]="ResourceMap",t instanceof K)this.d=new Map(t.d),this.e=n??K.c;else if(isEntries(t)){this.d=new Map,this.e=n??K.c;for(const[i,r]of t)this.set(i,r)}else this.d=new Map,this.e=t??K.c}set(t,n){return this.d.set(this.e(t),new ResourceMapEntry(t,n)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,n){typeof n<"u"&&(t=t.bind(n));for(const[i,r]of this.d)t(r.value,r.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(_a2=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},$Gc=class{constructor(e,t){this[_b]="ResourceSet",!e||typeof e=="function"?this.c=new $Fc(e):(this.c=new $Fc(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((n,i)=>e.call(t,i,i,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(_b=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},Touch;(function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"})(Touch||(Touch={}));var $Hc=class{constructor(){this[_c]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const n=this.c.get(e);if(n)return t!==0&&this.n(n,t),n.value}set(e,t,n=0){let i=this.c.get(e);if(i)i.value=t,n!==0&&this.n(i,n);else{switch(i={key:e,value:t,next:void 0,previous:void 0},n){case 0:this.l(i);break;case 1:this.k(i);break;case 2:this.l(i);break;default:this.l(i);break}this.c.set(e,i),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const n=this.g;let i=this.d;for(;i;){if(t?e.bind(t)(i.value,i.key,this):e(i.value,i.key,this),this.g!==n)throw new Error("LinkedMap got modified during iteration.");i=i.next}}keys(){const e=this,t=this.g;let n=this.d;const i={[Symbol.iterator](){return i},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const r={value:n.key,done:!1};return n=n.next,r}else return{value:void 0,done:!0}}};return i}values(){const e=this,t=this.g;let n=this.d;const i={[Symbol.iterator](){return i},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const r={value:n.value,done:!1};return n=n.next,r}else return{value:void 0,done:!0}}};return i}entries(){const e=this,t=this.g;let n=this.d;const i={[Symbol.iterator](){return i},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const r={value:[n.key,n.value],done:!1};return n=n.next,r}else return{value:void 0,done:!0}}};return i}[(_c=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,n=this.size;for(;t&&n>e;)this.c.delete(t.key),t=t.next,n--;this.d=t,this.f=n,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,n=this.size;for(;t&&n>e;)this.c.delete(t.key),t=t.previous,n--;this.e=t,this.f=n,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,n=e.previous;if(!t||!n)throw new Error("Invalid list");t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const n=e.next,i=e.previous;e===this.e?(i.next=void 0,this.e=i):(n.previous=i,i.next=n),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const n=e.next,i=e.previous;e===this.d?(n.previous=void 0,this.d=n):(n.previous=i,i.next=n),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,n)=>{e.push([n,t])}),e}fromJSON(e){this.clear();for(const[t,n]of e)this.set(t,n)}},Cache=class extends $Hc{constructor(e,t=1){super(),this.o=e,this.p=Math.min(Math.max(0,t),1)}get limit(){return this.o}set limit(e){this.o=e,this.q()}get ratio(){return this.p}set ratio(e){this.p=Math.min(Math.max(0,e),1),this.q()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},$Ic=class extends Cache{constructor(e,t=1){super(e,t)}r(e){this.h(e)}set(e,t){return super.set(e,t),this.q(),this}},$Mc=class{constructor(){this.c=new Map}add(e,t){let n=this.c.get(e);n||(n=new Set,this.c.set(e,n)),n.add(t)}delete(e,t){const n=this.c.get(e);n&&(n.delete(t),n.size===0&&this.c.delete(e))}forEach(e,t){const n=this.c.get(e);n&&n.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}};function $2c(e){return!!e&&typeof e[Symbol.iterator]=="function"}var Iterable;(function(e){function t(v){return v&&typeof v=="object"&&typeof v[Symbol.iterator]=="function"}e.is=t;const n=Object.freeze([]);function i(){return n}e.empty=i;function*r(v){yield v}e.single=r;function s(v){return t(v)?v:r(v)}e.wrap=s;function l(v){return v||n}e.from=l;function*o(v){for(let x=v.length-1;x>=0;x--)yield v[x]}e.reverse=o;function a(v){return!v||v[Symbol.iterator]().next().done===!0}e.isEmpty=a;function h(v){return v[Symbol.iterator]().next().value}e.first=h;function c(v,x){let E=0;for(const A of v)if(x(A,E++))return!0;return!1}e.some=c;function u(v,x){for(const E of v)if(x(E))return E}e.find=u;function*f(v,x){for(const E of v)x(E)&&(yield E)}e.filter=f;function*b(v,x){let E=0;for(const A of v)yield x(A,E++)}e.map=b;function*p(v,x){let E=0;for(const A of v)yield*x(A,E++)}e.flatMap=p;function*m(...v){for(const x of v)$2c(x)?yield*x:yield x}e.concat=m;function d(v,x,E){let A=E;for(const D of v)A=x(A,D);return A}e.reduce=d;function C(v){let x=0;for(const E of v)x++;return x}e.length=C;function*g(v,x,E=v.length){for(x<-v.length&&(x=0),x<0&&(x+=v.length),E<0?E+=v.length:E>v.length&&(E=v.length);x<E;x++)yield v[x]}e.slice=g;function w(v,x=Number.POSITIVE_INFINITY){const E=[];if(x===0)return[E,v];const A=v[Symbol.iterator]();for(let D=0;D<x;D++){const P=A.next();if(P.done)return[E,e.empty()];E.push(P.value)}return[E,{[Symbol.iterator](){return A}}]}e.consume=w;async function M(v){const x=[];for await(const E of v)x.push(E);return Promise.resolve(x)}e.asyncToArray=M})(Iterable||(Iterable={}));var TRACK_DISPOSABLES=!1,disposableTracker=null,$hd=class pt{constructor(){this.b=new Map}static{this.a=0}c(t){let n=this.b.get(t);return n||(n={parent:null,source:null,isSingleton:!1,value:t,idx:pt.a++},this.b.set(t,n)),n}trackDisposable(t){const n=this.c(t);n.source||(n.source=new Error().stack)}setParent(t,n){const i=this.c(t);i.parent=n}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,n){const i=n.get(t);if(i)return i;const r=t.parent?this.f(this.c(t.parent),n):t;return n.set(t,r),r}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,i])=>i.source!==null&&!this.f(i,t).isSingleton).flatMap(([i])=>i)}computeLeakingDisposables(t=10,n){let i;if(n)i=n;else{const a=new Map,h=[...this.b.values()].filter(u=>u.source!==null&&!this.f(u,a).isSingleton);if(h.length===0)return;const c=new Set(h.map(u=>u.value));if(i=h.filter(u=>!(u.parent&&c.has(u.parent))),i.length===0)throw new Error("There are cyclic diposable chains!")}if(!i)return;function r(a){function h(u,f){for(;u.length>0&&f.some(b=>typeof b=="string"?b===u[0]:u[0].match(b));)u.shift()}const c=a.source.split(`
`).map(u=>u.trim().replace("at ","")).filter(u=>u!=="");return h(c,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),c.reverse()}const s=new $Mc;for(const a of i){const h=r(a);for(let c=0;c<=h.length;c++)s.add(h.slice(0,c).join(`
`),a)}i.sort($9b(a=>a.idx,$$b));let l="",o=0;for(const a of i.slice(0,t)){o++;const h=r(a),c=[];for(let u=0;u<h.length;u++){let f=h[u];f=`(shared with ${s.get(h.slice(0,u+1).join(`
`)).size}/${i.length} leaks) at ${f}`;const p=s.get(h.slice(0,u).join(`
`)),m=$a([...p].map(d=>r(d)[u]),d=>d);delete m[h[u]];for(const[d,C]of Object.entries(m))c.unshift(`    - stacktraces of ${C.length} other leaks continue with ${d}`);c.unshift(f)}l+=`


==================== Leaking disposable ${o}/${i.length}: ${a.value.constructor.name} ====================
${c.join(`
`)}
============================================================

`}return i.length>t&&(l+=`


... and ${i.length-t} more leaking disposables

`),{leaks:i,details:l}}};function $id(e){disposableTracker=e}if(TRACK_DISPOSABLES){const e="__is_disposable_tracked__";$id(new class{trackDisposable(t){const n=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(n)},3e3)}setParent(t,n){if(t&&t!==$sd.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==$sd.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}function $jd(e){return disposableTracker?.trackDisposable(e),e}function $kd(e){disposableTracker?.markAsDisposed(e)}function setParentOfDisposable(e,t){disposableTracker?.setParent(e,t)}function setParentOfDisposables(e,t){if(disposableTracker)for(const n of e)disposableTracker.setParent(n,t)}function $nd(e){if(Iterable.is(e)){const t=[];for(const n of e)if(n)try{n.dispose()}catch(i){t.push(i)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function $pd(...e){const t=$qd(()=>$nd(e));return setParentOfDisposables(e,t),t}function $qd(e){const t=$jd({dispose:$Pc(()=>{$kd(t),e()})});return t}var $rd=class bt{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,$jd(this)}dispose(){this.g||($kd(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{$nd(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return setParentOfDisposable(t,this),this.g?bt.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),setParentOfDisposable(t,null))}},$sd=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new $rd,$jd(this),setParentOfDisposable(this.q,this)}dispose(){$kd(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}},Node=class rt{static{this.Undefined=new rt(void 0)}constructor(t){this.element=t,this.next=rt.Undefined,this.prev=rt.Undefined}},$Fd=class{constructor(){this.a=Node.Undefined,this.b=Node.Undefined,this.c=0}get size(){return this.c}isEmpty(){return this.a===Node.Undefined}clear(){let e=this.a;for(;e!==Node.Undefined;){const t=e.next;e.prev=Node.Undefined,e.next=Node.Undefined,e=t}this.a=Node.Undefined,this.b=Node.Undefined,this.c=0}unshift(e){return this.d(e,!1)}push(e){return this.d(e,!0)}d(e,t){const n=new Node(e);if(this.a===Node.Undefined)this.a=n,this.b=n;else if(t){const r=this.b;this.b=n,n.prev=r,r.next=n}else{const r=this.a;this.a=n,n.next=r,r.prev=n}this.c+=1;let i=!1;return()=>{i||(i=!0,this.e(n))}}shift(){if(this.a!==Node.Undefined){const e=this.a.element;return this.e(this.a),e}}pop(){if(this.b!==Node.Undefined){const e=this.b.element;return this.e(this.b),e}}e(e){if(e.prev!==Node.Undefined&&e.next!==Node.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===Node.Undefined&&e.next===Node.Undefined?(this.a=Node.Undefined,this.b=Node.Undefined):e.next===Node.Undefined?(this.b=this.b.prev,this.b.next=Node.Undefined):e.prev===Node.Undefined&&(this.a=this.a.next,this.a.prev=Node.Undefined);this.c-=1}*[Symbol.iterator](){let e=this.a;for(;e!==Node.Undefined;)yield e.element,e=e.next}},hasPerformanceNow=globalThis.performance&&typeof globalThis.performance.now=="function",$4e=class vt{static create(t){return new vt(t)}constructor(t){this.c=hasPerformanceNow&&t===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},_enableDisposeWithListenerWarning=!1,_enableSnapshotPotentialLeakWarning=!1,Event;(function(e){e.None=()=>$sd.None;function t(I){if(_enableSnapshotPotentialLeakWarning){const{onDidAddListener:L}=I,N=Stacktrace.create();let y=0;I.onDidAddListener=()=>{++y===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),N.print()),L?.()}}}function n(I,L){return b(I,()=>{},0,void 0,!0,void 0,L)}e.defer=n;function i(I){return(L,N=null,y)=>{let $=!1,O;return O=I(R=>{if(!$)return O?O.dispose():$=!0,L.call(N,R)},null,y),$&&O.dispose(),O}}e.once=i;function r(I,L){return e.once(e.filter(I,L))}e.onceIf=r;function s(I,L,N){return u((y,$=null,O)=>I(R=>y.call($,L(R)),null,O),N)}e.map=s;function l(I,L,N){return u((y,$=null,O)=>I(R=>{L(R),y.call($,R)},null,O),N)}e.forEach=l;function o(I,L,N){return u((y,$=null,O)=>I(R=>L(R)&&y.call($,R),null,O),N)}e.filter=o;function a(I){return I}e.signal=a;function h(...I){return(L,N=null,y)=>{const $=$pd(...I.map(O=>O(R=>L.call(N,R))));return f($,y)}}e.any=h;function c(I,L,N,y){let $=N;return s(I,O=>($=L($,O),$),y)}e.reduce=c;function u(I,L){let N;const y={onWillAddFirstListener(){N=I($.fire,$)},onDidRemoveLastListener(){N?.dispose()}};L||t(y);const $=new $0e(y);return L?.add($),$.event}function f(I,L){return L instanceof Array?L.push(I):L&&L.add(I),I}function b(I,L,N=100,y=!1,$=!1,O,R){let F,q,T,et=0,J;const gt={leakWarningThreshold:O,onWillAddFirstListener(){F=I(Ft=>{et++,q=L(q,Ft),y&&!T&&(nt.fire(q),q=void 0),J=()=>{const qt=q;q=void 0,T=void 0,(!y||et>1)&&nt.fire(qt),et=0},typeof N=="number"?(clearTimeout(T),T=setTimeout(J,N)):T===void 0&&(T=0,queueMicrotask(J))})},onWillRemoveListener(){$&&et>0&&J?.()},onDidRemoveLastListener(){J=void 0,F.dispose()}};R||t(gt);const nt=new $0e(gt);return R?.add(nt),nt.event}e.debounce=b;function p(I,L=0,N){return e.debounce(I,(y,$)=>y?(y.push($),y):[$],L,void 0,!0,void 0,N)}e.accumulate=p;function m(I,L=(y,$)=>y===$,N){let y=!0,$;return o(I,O=>{const R=y||!L(O,$);return y=!1,$=O,R},N)}e.latch=m;function d(I,L,N){return[e.filter(I,L,N),e.filter(I,y=>!L(y),N)]}e.split=d;function C(I,L=!1,N=[],y){let $=N.slice(),O=I(q=>{$?$.push(q):F.fire(q)});y&&y.add(O);const R=()=>{$?.forEach(q=>F.fire(q)),$=null},F=new $0e({onWillAddFirstListener(){O||(O=I(q=>F.fire(q)),y&&y.add(O))},onDidAddFirstListener(){$&&(L?setTimeout(R):R())},onDidRemoveLastListener(){O&&O.dispose(),O=null}});return y&&y.add(F),F.event}e.buffer=C;function g(I,L){return(y,$,O)=>{const R=L(new M);return I(function(F){const q=R.evaluate(F);q!==w&&y.call($,q)},void 0,O)}}e.chain=g;const w=Symbol("HaltChainable");class M{constructor(){this.f=[]}map(L){return this.f.push(L),this}forEach(L){return this.f.push(N=>(L(N),N)),this}filter(L){return this.f.push(N=>L(N)?N:w),this}reduce(L,N){let y=N;return this.f.push($=>(y=L(y,$),y)),this}latch(L=(N,y)=>N===y){let N=!0,y;return this.f.push($=>{const O=N||!L($,y);return N=!1,y=$,O?$:w}),this}evaluate(L){for(const N of this.f)if(L=N(L),L===w)break;return L}}function v(I,L,N=y=>y){const y=(...F)=>R.fire(N(...F)),$=()=>I.on(L,y),O=()=>I.removeListener(L,y),R=new $0e({onWillAddFirstListener:$,onDidRemoveLastListener:O});return R.event}e.fromNodeEventEmitter=v;function x(I,L,N=y=>y){const y=(...F)=>R.fire(N(...F)),$=()=>I.addEventListener(L,y),O=()=>I.removeEventListener(L,y),R=new $0e({onWillAddFirstListener:$,onDidRemoveLastListener:O});return R.event}e.fromDOMEventEmitter=x;function E(I,L){return new Promise(N=>i(I)(N,null,L))}e.toPromise=E;function A(I){const L=new $0e;return I.then(N=>{L.fire(N)},()=>{L.fire(void 0)}).finally(()=>{L.dispose()}),L.event}e.fromPromise=A;function D(I,L){return I(N=>L.fire(N))}e.forward=D;function P(I,L,N){return L(N),I(y=>L(y))}e.runAndSubscribe=P;class k{constructor(L,N){this._observable=L,this.f=0,this.g=!1;const y={onWillAddFirstListener:()=>{L.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{L.removeObserver(this)}};N||t(y),this.emitter=new $0e(y),N&&N.add(this.emitter)}beginUpdate(L){this.f++}handlePossibleChange(L){}handleChange(L,N){this.g=!0}endUpdate(L){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function S(I,L){return new k(I,L).emitter.event}e.fromObservable=S;function V(I){return(L,N,y)=>{let $=0,O=!1;const R={beginUpdate(){$++},endUpdate(){$--,$===0&&(I.reportChanges(),O&&(O=!1,L.call(N)))},handlePossibleChange(){},handleChange(){O=!0}};I.addObserver(R),I.reportChanges();const F={dispose(){I.removeObserver(R)}};return y instanceof $rd?y.add(F):Array.isArray(y)&&y.push(F),F}}e.fromObservableLight=V})(Event||(Event={}));var $6e=class ut{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${ut.f++}`,ut.all.add(this)}start(t){this.g=new $4e,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},_globalLeakWarningThreshold=-1,LeakageMonitor=class Ct{static{this.f=1}constructor(t,n,i=(Ct.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=n,this.name=i,this.h=0}dispose(){this.g?.clear()}check(t,n){const i=this.threshold;if(i<=0||n<i)return;this.g||(this.g=new Map);const r=this.g.get(t.value)||0;if(this.g.set(t.value,r+1),this.h-=1,this.h<=0){this.h=i*.5;const[s,l]=this.getMostFrequentStack(),o=`[${this.name}] potential listener LEAK detected, having ${n} listeners already. MOST frequent listener (${l}):`;console.warn(o),console.warn(s);const a=new $8e(o,s);this.j(a)}return()=>{const s=this.g.get(t.value)||0;this.g.set(t.value,s-1)}}getMostFrequentStack(){if(!this.g)return;let t,n=0;for(const[i,r]of this.g)(!t||n<r)&&(t=[i,r],n=r);return t}},Stacktrace=class wt{static create(){const t=new Error;return new wt(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},$8e=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},$9e=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},id=0,UniqueContainer=class{constructor(e){this.value=e,this.id=id++}},compactionThreshold=2,forEachListener=(e,t)=>{if(e instanceof UniqueContainer)t(e);else for(let n=0;n<e.length;n++){const i=e[n];i&&t(i)}},$0e=class{constructor(e){this.z=0,this.f=e,this.g=_globalLeakWarningThreshold>0||this.f?.leakWarningThreshold?new LeakageMonitor(e?.onListenerError??$gb,this.f?.leakWarningThreshold??_globalLeakWarningThreshold):void 0,this.j=this.f?._profName?new $6e(this.f._profName):void 0,this.w=this.f?.deliveryQueue}dispose(){if(!this.m){if(this.m=!0,this.w?.current===this&&this.w.reset(),this.u){if(_enableDisposeWithListenerWarning){const e=this.u;queueMicrotask(()=>{forEachListener(e,t=>t.stack?.print())})}this.u=void 0,this.z=0}this.f?.onDidRemoveLastListener?.(),this.g?.dispose()}}get event(){return this.q??=(e,t,n)=>{if(this.g&&this.z>this.g.threshold**2){const o=`[${this.g.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.z} vs ${this.g.threshold})`;console.warn(o);const a=this.g.getMostFrequentStack()??["UNKNOWN stack",-1],h=new $9e(`${o}. HINT: Stack shows most frequent listener (${a[1]}-times)`,a[0]);return(this.f?.onListenerError||$gb)(h),$sd.None}if(this.m)return $sd.None;t&&(e=e.bind(t));const i=new UniqueContainer(e);let r,s;this.g&&this.z>=Math.ceil(this.g.threshold*.2)&&(i.stack=Stacktrace.create(),r=this.g.check(i.stack,this.z+1)),_enableDisposeWithListenerWarning&&(i.stack=s??Stacktrace.create()),this.u?this.u instanceof UniqueContainer?(this.w??=new EventDeliveryQueuePrivate,this.u=[this.u,i]):this.u.push(i):(this.f?.onWillAddFirstListener?.(this),this.u=i,this.f?.onDidAddFirstListener?.(this)),this.f?.onDidAddListener?.(this),this.z++;const l=$qd(()=>{r?.(),this.A(i)});return n instanceof $rd?n.add(l):Array.isArray(n)&&n.push(l),l},this.q}A(e){if(this.f?.onWillRemoveListener?.(this),!this.u)return;if(this.z===1){this.u=void 0,this.f?.onDidRemoveLastListener?.(this),this.z=0;return}const t=this.u,n=t.indexOf(e);if(n===-1)throw console.log("disposed?",this.m),console.log("size?",this.z),console.log("arr?",JSON.stringify(this.u)),new Error("Attempted to dispose unknown listener");this.z--,t[n]=void 0;const i=this.w.current===this;if(this.z*compactionThreshold<=t.length){let r=0;for(let s=0;s<t.length;s++)t[s]?t[r++]=t[s]:i&&r<this.w.end&&(this.w.end--,r<this.w.i&&this.w.i--);t.length=r}}B(e,t){if(!e)return;const n=this.f?.onListenerError||$gb;if(!n){e.value(t);return}try{e.value(t)}catch(i){n(i)}}C(e){const t=e.current.u;for(;e.i<e.end;)this.B(t[e.i++],e.value);e.reset()}fire(e){if(this.w?.current&&(this.C(this.w),this.j?.stop()),this.j?.start(this.z),this.u)if(this.u instanceof UniqueContainer)this.B(this.u,e);else{const t=this.w;t.enqueue(this,e,this.u.length),this.C(t)}this.j?.stop()}hasListeners(){return this.z>0}},EventDeliveryQueuePrivate=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};function $f(){return globalThis._VSCODE_NLS_MESSAGES}function $g(){return globalThis._VSCODE_NLS_LANGUAGE}var isPseudo=$g()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;function _format(e,t){let n;return t.length===0?n=e:n=e.replace(/\{(\d+)\}/g,(i,r)=>{const s=t[parseInt(r)];let l=i;return typeof s=="string"?l=s:(typeof s=="number"||typeof s=="boolean"||s===void 0||s===null)&&(l=String(s)),l}),isPseudo&&(n="\uFF3B"+n.replace(/[aouei]/g,"$&$&")+"\uFF3D"),n}function localize(e,t,...n){return _format(typeof e=="number"?lookupMessage(e,t):t,n)}function lookupMessage(e,t){const n=$f()?.[e];if(typeof n!="string"){if(typeof t=="string")return t;throw new Error(`!!! NLS MISSING: ${e} !!!`)}return n}var $j="en",_isWindows=!1,_isMacintosh=!1,_isLinux=!1,_isLinuxSnap=!1,_isNative=!1,_isWeb=!1,_isElectron=!1,_isIOS=!1,_isCI=!1,_isMobile=!1,_locale=void 0,_language=$j,_platformLocale=$j,_translationsConfigFile=void 0,_userAgent=void 0,$globalThis=globalThis,nodeProcess=void 0;typeof $globalThis.vscode<"u"&&typeof $globalThis.vscode.process<"u"?nodeProcess=$globalThis.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(nodeProcess=process);var isElectronProcess=typeof nodeProcess?.versions?.electron=="string",isElectronRenderer=isElectronProcess&&nodeProcess?.type==="renderer";if(typeof nodeProcess=="object"){_isWindows=nodeProcess.platform==="win32",_isMacintosh=nodeProcess.platform==="darwin",_isLinux=nodeProcess.platform==="linux",_isLinuxSnap=_isLinux&&!!nodeProcess.env.SNAP&&!!nodeProcess.env.SNAP_REVISION,_isElectron=isElectronProcess,_isCI=!!nodeProcess.env.CI||!!nodeProcess.env.BUILD_ARTIFACTSTAGINGDIRECTORY,_locale=$j,_language=$j;const e=nodeProcess.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);_locale=t.userLocale,_platformLocale=t.osLocale,_language=t.resolvedLanguage||$j,_translationsConfigFile=t.languagePack?.translationsConfigFile}catch{}_isNative=!0}else typeof navigator=="object"&&!isElectronRenderer?(_userAgent=navigator.userAgent,_isWindows=_userAgent.indexOf("Windows")>=0,_isMacintosh=_userAgent.indexOf("Macintosh")>=0,_isIOS=(_userAgent.indexOf("Macintosh")>=0||_userAgent.indexOf("iPad")>=0||_userAgent.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,_isLinux=_userAgent.indexOf("Linux")>=0,_isMobile=_userAgent?.indexOf("Mobi")>=0,_isWeb=!0,_language=$g()||$j,_locale=navigator.language.toLowerCase(),_platformLocale=_locale):console.error("Unable to resolve platform.");var Platform;(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(Platform||(Platform={}));var _platform=0;_isMacintosh?_platform=1:_isWindows?_platform=3:_isLinux&&(_platform=2);var $l=_isWindows,$m=_isMacintosh,$n=_isLinux,$p=_isNative,$r=_isWeb,$s=_isWeb&&typeof $globalThis.importScripts=="function",$t=$s?$globalThis.origin:void 0,$y=_userAgent,$z=_language,Language;(function(e){function t(){return $z}e.value=t;function n(){return $z.length===2?$z==="en":$z.length>=3?$z[0]==="e"&&$z[1]==="n"&&$z[2]==="-":!1}e.isDefaultVariant=n;function i(){return $z==="en"}e.isDefault=i})(Language||(Language={}));var $D=typeof $globalThis.postMessage=="function"&&!$globalThis.importScripts,$E=(()=>{if($D){const e=[];$globalThis.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let i=0,r=e.length;i<r;i++){const s=e[i];if(s.id===n.data.vscodeScheduleAsyncWork){e.splice(i,1),s.callback();return}}});let t=0;return n=>{const i=++t;e.push({id:i,callback:n}),$globalThis.postMessage({vscodeScheduleAsyncWork:i},"*")}}return e=>setTimeout(e)})(),OperatingSystem;(function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"})(OperatingSystem||(OperatingSystem={}));var $H=!!($y&&$y.indexOf("Chrome")>=0),$I=!!($y&&$y.indexOf("Firefox")>=0),$J=!!(!$H&&$y&&$y.indexOf("Safari")>=0),$K=!!($y&&$y.indexOf("Edg/")>=0),$L=!!($y&&$y.indexOf("Android")>=0),shortcutEvent=Object.freeze(function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}),CancellationToken;(function(e){function t(n){return n===e.None||n===e.Cancelled||n instanceof MutableToken?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Event.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:shortcutEvent})})(CancellationToken||(CancellationToken={}));var MutableToken=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?shortcutEvent:(this.b||(this.b=new $0e),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}};function $Zf(e){return e}var $1f=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=$Zf):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}},$3f=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}};function $6f(e){return!e||typeof e!="string"?!0:e.trim().length===0}function $$f(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function $hg(e,t,n={}){if(!e)throw new Error("Cannot create regex from empty string");t||(e=$$f(e)),n.wholeWord&&(/\B/.test(e.charAt(0))||(e="\\b"+e),/\B/.test(e.charAt(e.length-1))||(e=e+"\\b"));let i="";return n.global&&(i+="g"),n.matchCase||(i+="i"),n.multiline&&(i+="m"),n.unicode&&(i+="u"),new RegExp(e,i)}function $kg(e){return e.split(/\r\n|\r|\n/)}function $mg(e){for(let t=0,n=e.length;t<n;t++){const i=e.charCodeAt(t);if(i!==32&&i!==9)return t}return-1}function $og(e,t=e.length-1){for(let n=t;n>=0;n--){const i=e.charCodeAt(n);if(i!==32&&i!==9)return n}return-1}function $rg(e,t){return e<t?-1:e>t?1:0}function $sg(e,t,n=0,i=e.length,r=0,s=t.length){for(;n<i&&r<s;n++,r++){const a=e.charCodeAt(n),h=t.charCodeAt(r);if(a<h)return-1;if(a>h)return 1}const l=i-n,o=s-r;return l<o?-1:l>o?1:0}function $ug(e,t,n=0,i=e.length,r=0,s=t.length){for(;n<i&&r<s;n++,r++){let a=e.charCodeAt(n),h=t.charCodeAt(r);if(a===h)continue;if(a>=128||h>=128)return $sg(e.toLowerCase(),t.toLowerCase(),n,i,r,s);$wg(a)&&(a-=32),$wg(h)&&(h-=32);const c=a-h;if(c!==0)return c}const l=i-n,o=s-r;return l<o?-1:l>o?1:0}function $wg(e){return e>=97&&e<=122}function $xg(e){return e>=65&&e<=90}function $yg(e,t){return e.length===t.length&&$ug(e,t)===0}function $zg(e,t){const n=t.length;return t.length>e.length?!1:$ug(e,t,0,n)===0}function $Cg(e){return 55296<=e&&e<=56319}function $Dg(e){return 56320<=e&&e<=57343}function $Eg(e,t){return(e-55296<<10)+(t-56320)+65536}function $Fg(e,t,n){const i=e.charCodeAt(n);if($Cg(i)&&n+1<t){const r=e.charCodeAt(n+1);if($Dg(r))return $Eg(i,r)}return i}var CONTAINS_RTL=void 0;function makeContainsRtl(){return/(?:[\u05BE\u05C0\u05C3\u05C6\u05D0-\u05F4\u0608\u060B\u060D\u061B-\u064A\u066D-\u066F\u0671-\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u0710\u0712-\u072F\u074D-\u07A5\u07B1-\u07EA\u07F4\u07F5\u07FA\u07FE-\u0815\u081A\u0824\u0828\u0830-\u0858\u085E-\u088E\u08A0-\u08C9\u200F\uFB1D\uFB1F-\uFB28\uFB2A-\uFD3D\uFD50-\uFDC7\uFDF0-\uFDFC\uFE70-\uFEFC]|\uD802[\uDC00-\uDD1B\uDD20-\uDE00\uDE10-\uDE35\uDE40-\uDEE4\uDEEB-\uDF35\uDF40-\uDFFF]|\uD803[\uDC00-\uDD23\uDE80-\uDEA9\uDEAD-\uDF45\uDF51-\uDF81\uDF86-\uDFF6]|\uD83A[\uDC00-\uDCCF\uDD00-\uDD43\uDD4B-\uDFFF]|\uD83B[\uDC00-\uDEBB])/}function $Mg(e){return CONTAINS_RTL||(CONTAINS_RTL=makeContainsRtl()),CONTAINS_RTL.test(e)}var IS_BASIC_ASCII=/^[\t\n\r\x20-\x7E]*$/;function $Ng(e){return IS_BASIC_ASCII.test(e)}var $Og=/[\u2028\u2029]/;function $Pg(e){return $Og.test(e)}var CSI_SEQUENCE=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,OSC_SEQUENCE=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,ESC_SEQUENCE=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,CONTROL_SEQUENCES=new RegExp("(?:"+[CSI_SEQUENCE.source,OSC_SEQUENCE.source,ESC_SEQUENCE.source].join("|")+")","g"),$Xg="\uFEFF";function $Yg(e){return!!(e&&e.length>0&&e.charCodeAt(0)===65279)}var GraphemeBreakType;(function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"})(GraphemeBreakType||(GraphemeBreakType={}));var GraphemeBreakTree=class X{static{this.c=null}static getInstance(){return X.c||(X.c=new X),X.c}constructor(){this.d=getGraphemeBreakRawData()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const n=this.d,i=n.length/3;let r=1;for(;r<=i;)if(t<n[3*r])r=2*r;else if(t>n[3*r+1])r=2*r+1;else return n[3*r+2];return 0}};function getGraphemeBreakRawData(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var CodePoint;(function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"})(CodePoint||(CodePoint={}));var $9g=class Y{static{this.c=new $3f(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new $1f({getCacheKey:JSON.stringify},t=>{function n(c){const u=new Map;for(let f=0;f<c.length;f+=2)u.set(c[f],c[f+1]);return u}function i(c,u){const f=new Map(c);for(const[b,p]of u)f.set(b,p);return f}function r(c,u){if(!c)return u;const f=new Map;for(const[b,p]of c)u.has(b)&&f.set(b,p);return f}const s=this.c.value;let l=t.filter(c=>!c.startsWith("_")&&c in s);l.length===0&&(l=["_default"]);let o;for(const c of l){const u=n(s[c]);o=r(o,u)}const a=n(s._common),h=i(a,o);return new Y(h)})}static getInstance(t){return Y.d.get(Array.from(t))}static{this.e=new $3f(()=>Object.keys(Y.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return Y.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let n=0;n<t.length;n++){const i=t.codePointAt(n);if(typeof i=="number"&&this.isAmbiguous(i))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},$0g=class Q{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(Q.c())].flat())),this.d}static isInvisibleCharacter(t){return Q.e().has(t)}static containsInvisibleCharacter(t){for(let n=0;n<t.length;n++){const i=t.codePointAt(n);if(typeof i=="number"&&(Q.isInvisibleCharacter(i)||i===32))return!0}return!1}static get codePoints(){return Q.e()}},DEFAULT_CHANNEL="default",INITIALIZE="$initialize",MessageType;(function(e){e[e.Request=0]="Request",e[e.Reply=1]="Reply",e[e.SubscribeEvent=2]="SubscribeEvent",e[e.Event=3]="Event",e[e.UnsubscribeEvent=4]="UnsubscribeEvent"})(MessageType||(MessageType={}));var RequestMessage=class{constructor(e,t,n,i,r){this.vsWorker=e,this.req=t,this.channel=n,this.method=i,this.args=r,this.type=0}},ReplyMessage=class{constructor(e,t,n,i){this.vsWorker=e,this.seq=t,this.res=n,this.err=i,this.type=1}},SubscribeEventMessage=class{constructor(e,t,n,i,r){this.vsWorker=e,this.req=t,this.channel=n,this.eventName=i,this.arg=r,this.type=2}},EventMessage=class{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}},UnsubscribeEventMessage=class{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}},WebWorkerProtocol=class{constructor(e){this.a=-1,this.g=e,this.b=0,this.c=Object.create(null),this.d=new Map,this.f=new Map}setWorkerId(e){this.a=e}sendMessage(e,t,n){const i=String(++this.b);return new Promise((r,s)=>{this.c[i]={resolve:r,reject:s},this.o(new RequestMessage(this.a,i,e,t,n))})}listen(e,t,n){let i=null;const r=new $0e({onWillAddFirstListener:()=>{i=String(++this.b),this.d.set(i,r),this.o(new SubscribeEventMessage(this.a,i,e,t,n))},onDidRemoveLastListener:()=>{this.d.delete(i),this.o(new UnsubscribeEventMessage(this.a,i)),i=null}});return r.event}handleMessage(e){!e||!e.vsWorker||this.a!==-1&&e.vsWorker!==this.a||this.h(e)}createProxyToRemoteChannel(e,t){const n={get:(i,r)=>(typeof r=="string"&&!i[r]&&(propertyIsDynamicEvent(r)?i[r]=s=>this.listen(e,r,s):propertyIsEvent(r)?i[r]=this.listen(e,r,void 0):r.charCodeAt(0)===36&&(i[r]=async(...s)=>(await t?.(),this.sendMessage(e,r,s)))),i[r])};return new Proxy(Object.create(null),n)}h(e){switch(e.type){case 1:return this.j(e);case 0:return this.k(e);case 2:return this.l(e);case 3:return this.m(e);case 4:return this.n(e)}}j(e){if(!this.c[e.seq]){console.warn("Got reply to unknown seq");return}const t=this.c[e.seq];if(delete this.c[e.seq],e.err){let n=e.err;e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),t.reject(n);return}t.resolve(e.res)}k(e){const t=e.req;this.g.handleMessage(e.channel,e.method,e.args).then(i=>{this.o(new ReplyMessage(this.a,t,i,void 0))},i=>{i.detail instanceof Error&&(i.detail=$ib(i.detail)),this.o(new ReplyMessage(this.a,t,void 0,$ib(i)))})}l(e){const t=e.req,n=this.g.handleEvent(e.channel,e.eventName,e.arg)(i=>{this.o(new EventMessage(this.a,t,i))});this.f.set(t,n)}m(e){if(!this.d.has(e.req)){console.warn("Got event for unknown req");return}this.d.get(e.req).fire(e.event)}n(e){if(!this.f.has(e.req)){console.warn("Got unsubscribe for unknown req");return}this.f.get(e.req).dispose(),this.f.delete(e.req)}o(e){const t=[];if(e.type===0)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else e.type===1&&e.res instanceof ArrayBuffer&&t.push(e.res);this.g.sendMessage(e,t)}};function propertyIsEvent(e){return e[0]==="o"&&e[1]==="n"&&$xg(e.charCodeAt(2))}function propertyIsDynamicEvent(e){return/^onDynamic/.test(e)&&$xg(e.charCodeAt(9))}var $pjb=class{constructor(e,t){this.b=new Map,this.c=new Map,this.a=new WebWorkerProtocol({sendMessage:(n,i)=>{e(n,i)},handleMessage:(n,i,r)=>this.d(n,i,r),handleEvent:(n,i,r)=>this.f(n,i,r)}),this.requestHandler=t(this)}onmessage(e){this.a.handleMessage(e)}d(e,t,n){if(e===DEFAULT_CHANNEL&&t===INITIALIZE)return this.g(n[0]);const i=e===DEFAULT_CHANNEL?this.requestHandler:this.b.get(e);if(!i)return Promise.reject(new Error(`Missing channel ${e} on worker thread`));if(typeof i[t]!="function")return Promise.reject(new Error(`Missing method ${t} on worker thread channel ${e}`));try{return Promise.resolve(i[t].apply(i,n))}catch(r){return Promise.reject(r)}}f(e,t,n){const i=e===DEFAULT_CHANNEL?this.requestHandler:this.b.get(e);if(!i)throw new Error(`Missing channel ${e} on worker thread`);if(propertyIsDynamicEvent(t)){const r=i[t].call(i,n);if(typeof r!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return r}if(propertyIsEvent(t)){const r=i[t];if(typeof r!="function")throw new Error(`Missing event ${t} on request handler.`);return r}throw new Error(`Malformed event name ${t}`)}setChannel(e,t){this.b.set(e,t)}getChannel(e){if(!this.c.has(e)){const t=this.a.createProxyToRemoteChannel(e);this.c.set(e,t)}return this.c.get(e)}async g(e){this.a.setWorkerId(e)}},initialized=!1;function $Etb(e){if(initialized)throw new Error("WebWorker already initialized!");initialized=!0;const t=new $pjb(n=>globalThis.postMessage(n),n=>e(n));return globalThis.onmessage=n=>{t.onmessage(n.data)},t}function $Ftb(e){globalThis.onmessage=t=>{initialized||$Etb(e)}}var $gV=class{constructor(e,t,n,i){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=i}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}},hasBuffer=typeof Buffer<"u",indexOfTable=new $3f(()=>new Uint8Array(256)),textEncoder,textDecoder,$Wi=class z{static alloc(t){return hasBuffer?new z(Buffer.allocUnsafe(t)):new z(new Uint8Array(t))}static wrap(t){return hasBuffer&&!Buffer.isBuffer(t)&&(t=Buffer.from(t.buffer,t.byteOffset,t.byteLength)),new z(t)}static fromString(t,n){return!(n?.dontUseNodeBuffer||!1)&&hasBuffer?new z(Buffer.from(t)):(textEncoder||(textEncoder=new TextEncoder),new z(textEncoder.encode(t)))}static fromByteArray(t){const n=z.alloc(t.length);for(let i=0,r=t.length;i<r;i++)n.buffer[i]=t[i];return n}static concat(t,n){if(typeof n>"u"){n=0;for(let s=0,l=t.length;s<l;s++)n+=t[s].byteLength}const i=z.alloc(n);let r=0;for(let s=0,l=t.length;s<l;s++){const o=t[s];i.set(o,r),r+=o.byteLength}return i}constructor(t){this.buffer=t,this.byteLength=this.buffer.byteLength}clone(){const t=z.alloc(this.byteLength);return t.set(this),t}toString(){return hasBuffer?this.buffer.toString():(textDecoder||(textDecoder=new TextDecoder),textDecoder.decode(this.buffer))}slice(t,n){return new z(this.buffer.subarray(t,n))}set(t,n){if(t instanceof z)this.buffer.set(t.buffer,n);else if(t instanceof Uint8Array)this.buffer.set(t,n);else if(t instanceof ArrayBuffer)this.buffer.set(new Uint8Array(t),n);else if(ArrayBuffer.isView(t))this.buffer.set(new Uint8Array(t.buffer,t.byteOffset,t.byteLength),n);else throw new Error("Unknown argument 'array'")}readUInt32BE(t){return $1i(this.buffer,t)}writeUInt32BE(t,n){$2i(this.buffer,t,n)}readUInt32LE(t){return $3i(this.buffer,t)}writeUInt32LE(t,n){$4i(this.buffer,t,n)}readUInt8(t){return $5i(this.buffer,t)}writeUInt8(t,n){$6i(this.buffer,t,n)}indexOf(t,n=0){return $Xi(this.buffer,t instanceof z?t.buffer:t,n)}equals(t){return this===t?!0:this.byteLength!==t.byteLength?!1:this.buffer.every((n,i)=>n===t.buffer[i])}};function $Xi(e,t,n=0){const i=t.byteLength,r=e.byteLength;if(i===0)return 0;if(i===1)return e.indexOf(t[0]);if(i>r-n)return-1;const s=indexOfTable.value;s.fill(t.length);for(let h=0;h<t.length;h++)s[t[h]]=t.length-h-1;let l=n+t.length-1,o=l,a=-1;for(;l<r;)if(e[l]===t[o]){if(o===0){a=l;break}l--,o--}else l+=Math.max(t.length-o,s[e[l]]),o=t.length-1;return a}function $Yi(e,t){return e[t+0]<<0>>>0|e[t+1]<<8>>>0}function $Zi(e,t,n){e[n+0]=t&255,t=t>>>8,e[n+1]=t&255}function $1i(e,t){return e[t]*2**24+e[t+1]*2**16+e[t+2]*2**8+e[t+3]}function $2i(e,t,n){e[n+3]=t,t=t>>>8,e[n+2]=t,t=t>>>8,e[n+1]=t,t=t>>>8,e[n]=t}function $3i(e,t){return e[t+0]<<0>>>0|e[t+1]<<8>>>0|e[t+2]<<16>>>0|e[t+3]<<24>>>0}function $4i(e,t,n){e[n+0]=t&255,t=t>>>8,e[n+1]=t&255,t=t>>>8,e[n+2]=t&255,t=t>>>8,e[n+3]=t&255}function $5i(e,t){return e[t]}function $6i(e,t,n){e[n]=t}function $dj(e){let t=0,n=0,i=0;const r=new Uint8Array(Math.floor(e.length/4*3)),s=o=>{switch(n){case 3:r[i++]=t|o,n=0;break;case 2:r[i++]=t|o>>>2,t=o<<6,n=3;break;case 1:r[i++]=t|o>>>4,t=o<<4,n=2;break;default:t=o<<2,n=1}};for(let o=0;o<e.length;o++){const a=e.charCodeAt(o);if(a>=65&&a<=90)s(a-65);else if(a>=97&&a<=122)s(a-97+26);else if(a>=48&&a<=57)s(a-48+52);else if(a===43||a===45)s(62);else if(a===47||a===95)s(63);else{if(a===61)break;throw new SyntaxError(`Unexpected base64 character ${e[o]}`)}}const l=i;for(;n>0;)s(0);return $Wi.wrap(r).slice(0,l)}var base64Alphabet="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",base64UrlSafeAlphabet="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_";function $ej({buffer:e},t=!0,n=!1){const i=n?base64UrlSafeAlphabet:base64Alphabet;let r="";const s=e.byteLength%3;let l=0;for(;l<e.byteLength-s;l+=3){const o=e[l+0],a=e[l+1],h=e[l+2];r+=i[o>>>2],r+=i[(o<<4|a>>>4)&63],r+=i[(a<<2|h>>>6)&63],r+=i[h&63]}if(s===1){const o=e[l+0];r+=i[o>>>2],r+=i[o<<4&63],t&&(r+="==")}else if(s===2){const o=e[l+0],a=e[l+1];r+=i[o>>>2],r+=i[(o<<4|a>>>4)&63],r+=i[a<<2&63],t&&(r+="=")}return r}function $fj(e){return $gj(e,0)}function $gj(e,t){switch(typeof e){case"object":return e===null?$hj(349,t):Array.isArray(e)?arrayHash(e,t):objectHash(e,t);case"string":return $ij(e,t);case"boolean":return booleanHash(e,t);case"number":return $hj(e,t);case"undefined":return $hj(937,t);default:return $hj(617,t)}}function $hj(e,t){return(t<<5)-t+e|0}function booleanHash(e,t){return $hj(e?433:863,t)}function $ij(e,t){t=$hj(149417,t);for(let n=0,i=e.length;n<i;n++)t=$hj(e.charCodeAt(n),t);return t}function arrayHash(e,t){return t=$hj(104579,t),e.reduce((n,i)=>$gj(i,n),t)}function objectHash(e,t){return t=$hj(181387,t),Object.keys(e).sort().reduce((n,i)=>(n=$ij(i,n),$gj(e[i],n)),t)}var SHA1Constant;(function(e){e[e.BLOCK_SIZE=64]="BLOCK_SIZE",e[e.UNICODE_REPLACEMENT=65533]="UNICODE_REPLACEMENT"})(SHA1Constant||(SHA1Constant={}));function leftRotate(e,t,n=32){const i=n-t,r=~((1<<i)-1);return(e<<t|(r&e)>>>i)>>>0}function toHexString(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(n=>n.toString(16).padStart(2,"0")).join(""):(e>>>0).toString(16).padStart(t/4,"0")}var $kj=class Lt{static{this.g=new DataView(new ArrayBuffer(320))}constructor(){this.h=1732584193,this.l=4023233417,this.m=2562383102,this.n=271733878,this.o=3285377520,this.p=new Uint8Array(67),this.q=new DataView(this.p.buffer),this.r=0,this.t=0,this.u=0,this.v=!1}update(t){const n=t.length;if(n===0)return;const i=this.p;let r=this.r,s=this.u,l,o;for(s!==0?(l=s,o=-1,s=0):(l=t.charCodeAt(0),o=0);;){let a=l;if($Cg(l))if(o+1<n){const h=t.charCodeAt(o+1);$Dg(h)?(o++,a=$Eg(l,h)):a=65533}else{s=l;break}else $Dg(l)&&(a=65533);if(r=this.w(i,r,a),o++,o<n)l=t.charCodeAt(o);else break}this.r=r,this.u=s}w(t,n,i){return i<128?t[n++]=i:i<2048?(t[n++]=192|(i&1984)>>>6,t[n++]=128|(i&63)>>>0):i<65536?(t[n++]=224|(i&61440)>>>12,t[n++]=128|(i&4032)>>>6,t[n++]=128|(i&63)>>>0):(t[n++]=240|(i&1835008)>>>18,t[n++]=128|(i&258048)>>>12,t[n++]=128|(i&4032)>>>6,t[n++]=128|(i&63)>>>0),n>=64&&(this.y(),n-=64,this.t+=64,t[0]=t[64],t[1]=t[65],t[2]=t[66]),n}digest(){return this.v||(this.v=!0,this.u&&(this.u=0,this.r=this.w(this.p,this.r,65533)),this.t+=this.r,this.x()),toHexString(this.h)+toHexString(this.l)+toHexString(this.m)+toHexString(this.n)+toHexString(this.o)}x(){this.p[this.r++]=128,this.p.subarray(this.r).fill(0),this.r>56&&(this.y(),this.p.fill(0));const t=8*this.t;this.q.setUint32(56,Math.floor(t/4294967296),!1),this.q.setUint32(60,t%4294967296,!1),this.y()}y(){const t=Lt.g,n=this.q;for(let u=0;u<64;u+=4)t.setUint32(u,n.getUint32(u,!1),!1);for(let u=64;u<320;u+=4)t.setUint32(u,leftRotate(t.getUint32(u-12,!1)^t.getUint32(u-32,!1)^t.getUint32(u-56,!1)^t.getUint32(u-64,!1),1),!1);let i=this.h,r=this.l,s=this.m,l=this.n,o=this.o,a,h,c;for(let u=0;u<80;u++)u<20?(a=r&s|~r&l,h=1518500249):u<40?(a=r^s^l,h=1859775393):u<60?(a=r&s|r&l|s&l,h=2400959708):(a=r^s^l,h=3395469782),c=leftRotate(i,5)+a+o+h+t.getUint32(u*4,!1)&4294967295,o=l,l=s,s=leftRotate(r,30),r=i,i=c;this.h=this.h+i&4294967295,this.l=this.l+r&4294967295,this.m=this.m+s&4294967295,this.n=this.n+l&4294967295,this.o=this.o+o&4294967295}},Debug=class{static Assert(e,t){if(!e)throw new Error(t)}},MyArray=class{static Copy(e,t,n,i,r){for(let s=0;s<r;s++)n[i+s]=e[t+s]}static Copy2(e,t,n,i,r){for(let s=0;s<r;s++)n[i+s]=e[t+s]}},LocalConstants;(function(e){e[e.MaxDifferencesHistory=1447]="MaxDifferencesHistory"})(LocalConstants||(LocalConstants={}));var DiffChangeHelper=class{constructor(){this.a=[],this.b=1073741824,this.c=1073741824,this.d=0,this.e=0}MarkNextChange(){(this.d>0||this.e>0)&&this.a.push(new $gV(this.b,this.d,this.c,this.e)),this.d=0,this.e=0,this.b=1073741824,this.c=1073741824}AddOriginalElement(e,t){this.b=Math.min(this.b,e),this.c=Math.min(this.c,t),this.d++}AddModifiedElement(e,t){this.b=Math.min(this.b,e),this.c=Math.min(this.c,t),this.e++}getChanges(){return(this.d>0||this.e>0)&&this.MarkNextChange(),this.a}getReverseChanges(){return(this.d>0||this.e>0)&&this.MarkNextChange(),this.a.reverse(),this.a}},$jV=class H{constructor(t,n,i=null){this.a=i,this.b=t,this.c=n;const[r,s,l]=H.p(t),[o,a,h]=H.p(n);this.d=l&&h,this.e=r,this.f=s,this.g=o,this.h=a,this.m=[],this.n=[]}static o(t){return t.length>0&&typeof t[0]=="string"}static p(t){const n=t.getElements();if(H.o(n)){const i=new Int32Array(n.length);for(let r=0,s=n.length;r<s;r++)i[r]=$ij(n[r],0);return[n,i,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}q(t,n){return this.f[t]!==this.h[n]?!1:this.d?this.e[t]===this.g[n]:!0}r(t,n){if(!this.q(t,n))return!1;const i=H.s(this.b,t),r=H.s(this.c,n);return i===r}static s(t,n){return typeof t.getStrictElement=="function"?t.getStrictElement(n):null}u(t,n){return this.f[t]!==this.f[n]?!1:this.d?this.e[t]===this.e[n]:!0}v(t,n){return this.h[t]!==this.h[n]?!1:this.d?this.g[t]===this.g[n]:!0}ComputeDiff(t){return this.w(0,this.f.length-1,0,this.h.length-1,t)}w(t,n,i,r,s){const l=[!1];let o=this.x(t,n,i,r,l);return s&&(o=this.A(o)),{quitEarly:l[0],changes:o}}x(t,n,i,r,s){for(s[0]=!1;t<=n&&i<=r&&this.q(t,i);)t++,i++;for(;n>=t&&r>=i&&this.q(n,r);)n--,r--;if(t>n||i>r){let u;return i<=r?(Debug.Assert(t===n+1,"originalStart should only be one more than originalEnd"),u=[new $gV(t,0,i,r-i+1)]):t<=n?(Debug.Assert(i===r+1,"modifiedStart should only be one more than modifiedEnd"),u=[new $gV(t,n-t+1,i,0)]):(Debug.Assert(t===n+1,"originalStart should only be one more than originalEnd"),Debug.Assert(i===r+1,"modifiedStart should only be one more than modifiedEnd"),u=[]),u}const l=[0],o=[0],a=this.z(t,n,i,r,l,o,s),h=l[0],c=o[0];if(a!==null)return a;if(!s[0]){const u=this.x(t,h,i,c,s);let f=[];return s[0]?f=[new $gV(h+1,n-(h+1)+1,c+1,r-(c+1)+1)]:f=this.x(h+1,n,c+1,r,s),this.I(u,f)}return[new $gV(t,n-t+1,i,r-i+1)]}y(t,n,i,r,s,l,o,a,h,c,u,f,b,p,m,d,C,g){let w=null,M=null,v=new DiffChangeHelper,x=n,E=i,A=b[0]-d[0]-r,D=-1073741824,P=this.m.length-1;do{const k=A+t;k===x||k<E&&h[k-1]<h[k+1]?(u=h[k+1],p=u-A-r,u<D&&v.MarkNextChange(),D=u,v.AddModifiedElement(u+1,p),A=k+1-t):(u=h[k-1]+1,p=u-A-r,u<D&&v.MarkNextChange(),D=u-1,v.AddOriginalElement(u,p+1),A=k-1-t),P>=0&&(h=this.m[P],t=h[0],x=1,E=h.length-1)}while(--P>=-1);if(w=v.getReverseChanges(),g[0]){let k=b[0]+1,S=d[0]+1;if(w!==null&&w.length>0){const V=w[w.length-1];k=Math.max(k,V.getOriginalEnd()),S=Math.max(S,V.getModifiedEnd())}M=[new $gV(k,f-k+1,S,m-S+1)]}else{v=new DiffChangeHelper,x=l,E=o,A=b[0]-d[0]-a,D=1073741824,P=C?this.n.length-1:this.n.length-2;do{const k=A+s;k===x||k<E&&c[k-1]>=c[k+1]?(u=c[k+1]-1,p=u-A-a,u>D&&v.MarkNextChange(),D=u+1,v.AddOriginalElement(u+1,p+1),A=k+1-s):(u=c[k-1],p=u-A-a,u>D&&v.MarkNextChange(),D=u,v.AddModifiedElement(u+1,p+1),A=k-1-s),P>=0&&(c=this.n[P],s=c[0],x=1,E=c.length-1)}while(--P>=-1);M=v.getChanges()}return this.I(w,M)}z(t,n,i,r,s,l,o){let a=0,h=0,c=0,u=0,f=0,b=0;t--,i--,s[0]=0,l[0]=0,this.m=[],this.n=[];const p=n-t+(r-i),m=p+1,d=new Int32Array(m),C=new Int32Array(m),g=r-i,w=n-t,M=t-i,v=n-r,E=(w-g)%2===0;d[g]=t,C[w]=n,o[0]=!1;for(let A=1;A<=p/2+1;A++){let D=0,P=0;c=this.K(g-A,A,g,m),u=this.K(g+A,A,g,m);for(let S=c;S<=u;S+=2){S===c||S<u&&d[S-1]<d[S+1]?a=d[S+1]:a=d[S-1]+1,h=a-(S-g)-M;const V=a;for(;a<n&&h<r&&this.q(a+1,h+1);)a++,h++;if(d[S]=a,a+h>D+P&&(D=a,P=h),!E&&Math.abs(S-w)<=A-1&&a>=C[S])return s[0]=a,l[0]=h,V<=C[S]&&A<=1448?this.y(g,c,u,M,w,f,b,v,d,C,a,n,s,h,r,l,E,o):null}const k=(D-t+(P-i)-A)/2;if(this.a!==null&&!this.a(D,k))return o[0]=!0,s[0]=D,l[0]=P,k>0&&A<=1448?this.y(g,c,u,M,w,f,b,v,d,C,a,n,s,h,r,l,E,o):(t++,i++,[new $gV(t,n-t+1,i,r-i+1)]);f=this.K(w-A,A,w,m),b=this.K(w+A,A,w,m);for(let S=f;S<=b;S+=2){S===f||S<b&&C[S-1]>=C[S+1]?a=C[S+1]-1:a=C[S-1],h=a-(S-w)-v;const V=a;for(;a>t&&h>i&&this.q(a,h);)a--,h--;if(C[S]=a,E&&Math.abs(S-g)<=A&&a<=d[S])return s[0]=a,l[0]=h,V>=d[S]&&A<=1448?this.y(g,c,u,M,w,f,b,v,d,C,a,n,s,h,r,l,E,o):null}if(A<=1447){let S=new Int32Array(u-c+2);S[0]=g-c+1,MyArray.Copy2(d,c,S,1,u-c+1),this.m.push(S),S=new Int32Array(b-f+2),S[0]=w-f+1,MyArray.Copy2(C,f,S,1,b-f+1),this.n.push(S)}}return this.y(g,c,u,M,w,f,b,v,d,C,a,n,s,h,r,l,E,o)}A(t){for(let n=0;n<t.length;n++){const i=t[n],r=n<t.length-1?t[n+1].originalStart:this.f.length,s=n<t.length-1?t[n+1].modifiedStart:this.h.length,l=i.originalLength>0,o=i.modifiedLength>0;for(;i.originalStart+i.originalLength<r&&i.modifiedStart+i.modifiedLength<s&&(!l||this.u(i.originalStart,i.originalStart+i.originalLength))&&(!o||this.v(i.modifiedStart,i.modifiedStart+i.modifiedLength));){const h=this.r(i.originalStart,i.modifiedStart);if(this.r(i.originalStart+i.originalLength,i.modifiedStart+i.modifiedLength)&&!h)break;i.originalStart++,i.modifiedStart++}const a=[null];if(n<t.length-1&&this.J(t[n],t[n+1],a)){t[n]=a[0],t.splice(n+1,1),n--;continue}}for(let n=t.length-1;n>=0;n--){const i=t[n];let r=0,s=0;if(n>0){const u=t[n-1];r=u.originalStart+u.originalLength,s=u.modifiedStart+u.modifiedLength}const l=i.originalLength>0,o=i.modifiedLength>0;let a=0,h=this.H(i.originalStart,i.originalLength,i.modifiedStart,i.modifiedLength);for(let u=1;;u++){const f=i.originalStart-u,b=i.modifiedStart-u;if(f<r||b<s||l&&!this.u(f,f+i.originalLength)||o&&!this.v(b,b+i.modifiedLength))break;const m=(f===r&&b===s?5:0)+this.H(f,i.originalLength,b,i.modifiedLength);m>h&&(h=m,a=u)}i.originalStart-=a,i.modifiedStart-=a;const c=[null];if(n>0&&this.J(t[n-1],t[n],c)){t[n-1]=c[0],t.splice(n,1),n++;continue}}if(this.d)for(let n=1,i=t.length;n<i;n++){const r=t[n-1],s=t[n],l=s.originalStart-r.originalStart-r.originalLength,o=r.originalStart,a=s.originalStart+s.originalLength,h=a-o,c=r.modifiedStart,u=s.modifiedStart+s.modifiedLength,f=u-c;if(l<5&&h<20&&f<20){const b=this.B(o,h,c,f,l);if(b){const[p,m]=b;(p!==r.originalStart+r.originalLength||m!==r.modifiedStart+r.modifiedLength)&&(r.originalLength=p-r.originalStart,r.modifiedLength=m-r.modifiedStart,s.originalStart=p+l,s.modifiedStart=m+l,s.originalLength=a-s.originalStart,s.modifiedLength=u-s.modifiedStart)}}}return t}B(t,n,i,r,s){if(n<s||r<s)return null;const l=t+n-s+1,o=i+r-s+1;let a=0,h=0,c=0;for(let u=t;u<l;u++)for(let f=i;f<o;f++){const b=this.C(u,f,s);b>0&&b>a&&(a=b,h=u,c=f)}return a>0?[h,c]:null}C(t,n,i){let r=0;for(let s=0;s<i;s++){if(!this.q(t+s,n+s))return 0;r+=this.e[t+s].length}return r}D(t){return t<=0||t>=this.f.length-1?!0:this.d&&/^\s*$/.test(this.e[t])}E(t,n){if(this.D(t)||this.D(t-1))return!0;if(n>0){const i=t+n;if(this.D(i-1)||this.D(i))return!0}return!1}F(t){return t<=0||t>=this.h.length-1?!0:this.d&&/^\s*$/.test(this.g[t])}G(t,n){if(this.F(t)||this.F(t-1))return!0;if(n>0){const i=t+n;if(this.F(i-1)||this.F(i))return!0}return!1}H(t,n,i,r){const s=this.E(t,n)?1:0,l=this.G(i,r)?1:0;return s+l}I(t,n){const i=[];if(t.length===0||n.length===0)return n.length>0?n:t;if(this.J(t[t.length-1],n[0],i)){const r=new Array(t.length+n.length-1);return MyArray.Copy(t,0,r,0,t.length-1),r[t.length-1]=i[0],MyArray.Copy(n,1,r,t.length,n.length-1),r}else{const r=new Array(t.length+n.length);return MyArray.Copy(t,0,r,0,t.length),MyArray.Copy(n,0,r,t.length,n.length),r}}J(t,n,i){if(Debug.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),Debug.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){const r=t.originalStart;let s=t.originalLength;const l=t.modifiedStart;let o=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(s=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(o=n.modifiedStart+n.modifiedLength-t.modifiedStart),i[0]=new $gV(r,s,l,o),!0}else return i[0]=null,!1}K(t,n,i,r){if(t>=0&&t<r)return t;const s=i,l=r-i-1,o=n%2===0;if(t<0){const a=s%2===0;return o===a?0:1}else{const a=l%2===0;return o===a?r-1:r-2}}},precomputedEqualityArray=new Uint32Array(65536),computeLevenshteinDistanceForShortStrings=(e,t)=>{const n=e.length,i=t.length,r=1<<n-1;let s=-1,l=0,o=n,a=n;for(;a--;)precomputedEqualityArray[e.charCodeAt(a)]|=1<<a;for(a=0;a<i;a++){let h=precomputedEqualityArray[t.charCodeAt(a)];const c=h|l;h|=(h&s)+s^s,l|=~(h|s),s&=h,l&r&&o++,s&r&&o--,l=l<<1|1,s=s<<1|~(c|l),l&=c}for(a=n;a--;)precomputedEqualityArray[e.charCodeAt(a)]=0;return o};function computeLevenshteinDistanceForLongStrings(e,t){const n=e.length,i=t.length,r=[],s=[],l=Math.ceil(n/32),o=Math.ceil(i/32);for(let p=0;p<l;p++)r[p]=-1,s[p]=0;let a=0;for(;a<o-1;a++){let p=0,m=-1;const d=a*32,C=Math.min(32,i)+d;for(let g=d;g<C;g++)precomputedEqualityArray[t.charCodeAt(g)]|=1<<g;for(let g=0;g<n;g++){const w=precomputedEqualityArray[e.charCodeAt(g)],M=r[g/32|0]>>>g&1,v=s[g/32|0]>>>g&1,x=w|p,E=((w|v)&m)+m^m|w|v;let A=p|~(E|m),D=m&E;A>>>31^M&&(r[g/32|0]^=1<<g),D>>>31^v&&(s[g/32|0]^=1<<g),A=A<<1|M,D=D<<1|v,m=D|~(x|A),p=A&x}for(let g=d;g<C;g++)precomputedEqualityArray[t.charCodeAt(g)]=0}let h=0,c=-1;const u=a*32,f=Math.min(32,i-u)+u;for(let p=u;p<f;p++)precomputedEqualityArray[t.charCodeAt(p)]|=1<<p;let b=i;for(let p=0;p<n;p++){const m=precomputedEqualityArray[e.charCodeAt(p)],d=r[p/32|0]>>>p&1,C=s[p/32|0]>>>p&1,g=m|h,w=((m|C)&c)+c^c|m|C;let M=h|~(w|c),v=c&w;b+=M>>>i-1&1,b-=v>>>i-1&1,M>>>31^d&&(r[p/32|0]^=1<<p),v>>>31^C&&(s[p/32|0]^=1<<p),M=M<<1|d,v=v<<1|C,c=v|~(g|M),h=M&g}for(let p=u;p<f;p++)precomputedEqualityArray[t.charCodeAt(p)]=0;return b}function $kV(e,t){if(e.length<t.length){const n=t;t=e,e=n}return t.length===0?e.length:e.length<=32?computeLevenshteinDistanceForShortStrings(e,t):computeLevenshteinDistanceForLongStrings(e,t)}var safeProcess,vscodeGlobal=globalThis.vscode;if(typeof vscodeGlobal<"u"&&typeof vscodeGlobal.process<"u"){const e=vscodeGlobal.process;safeProcess={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?safeProcess={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:safeProcess={get platform(){return $l?"win32":$m?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var cwd=safeProcess.cwd,env=safeProcess.env,$ic=safeProcess.platform,$jc=safeProcess.arch,CHAR_UPPERCASE_A=65,CHAR_LOWERCASE_A=97,CHAR_UPPERCASE_Z=90,CHAR_LOWERCASE_Z=122,CHAR_DOT=46,CHAR_FORWARD_SLASH=47,CHAR_BACKWARD_SLASH=92,CHAR_COLON=58,CHAR_QUESTION_MARK=63,ErrorInvalidArgType=class extends Error{constructor(e,t,n){let i;typeof t=="string"&&t.indexOf("not ")===0?(i="must not be",t=t.replace(/^not /,"")):i="must be";const r=e.indexOf(".")!==-1?"property":"argument";let s=`The "${e}" ${r} ${i} of type ${t}`;s+=`. Received type ${typeof n}`,super(s),this.code="ERR_INVALID_ARG_TYPE"}};function validateObject(e,t){if(e===null||typeof e!="object")throw new ErrorInvalidArgType(t,"Object",e)}function validateString(e,t){if(typeof e!="string")throw new ErrorInvalidArgType(t,"string",e)}var platformIsWin32=$ic==="win32";function isPathSeparator(e){return e===CHAR_FORWARD_SLASH||e===CHAR_BACKWARD_SLASH}function isPosixPathSeparator(e){return e===CHAR_FORWARD_SLASH}function isWindowsDeviceRoot(e){return e>=CHAR_UPPERCASE_A&&e<=CHAR_UPPERCASE_Z||e>=CHAR_LOWERCASE_A&&e<=CHAR_LOWERCASE_Z}function normalizeString(e,t,n,i){let r="",s=0,l=-1,o=0,a=0;for(let h=0;h<=e.length;++h){if(h<e.length)a=e.charCodeAt(h);else{if(i(a))break;a=CHAR_FORWARD_SLASH}if(i(a)){if(!(l===h-1||o===1))if(o===2){if(r.length<2||s!==2||r.charCodeAt(r.length-1)!==CHAR_DOT||r.charCodeAt(r.length-2)!==CHAR_DOT){if(r.length>2){const c=r.lastIndexOf(n);c===-1?(r="",s=0):(r=r.slice(0,c),s=r.length-1-r.lastIndexOf(n)),l=h,o=0;continue}else if(r.length!==0){r="",s=0,l=h,o=0;continue}}t&&(r+=r.length>0?`${n}..`:"..",s=2)}else r.length>0?r+=`${n}${e.slice(l+1,h)}`:r=e.slice(l+1,h),s=h-l-1;l=h,o=0}else a===CHAR_DOT&&o!==-1?++o:o=-1}return r}function formatExt(e){return e?`${e[0]==="."?"":"."}${e}`:""}function _format2(e,t){validateObject(t,"pathObject");const n=t.dir||t.root,i=t.base||`${t.name||""}${formatExt(t.ext)}`;return n?n===t.root?`${n}${i}`:`${n}${e}${i}`:i}var $kc={resolve(...e){let t="",n="",i=!1;for(let r=e.length-1;r>=-1;r--){let s;if(r>=0){if(s=e[r],validateString(s,`paths[${r}]`),s.length===0)continue}else t.length===0?s=cwd():(s=env[`=${t}`]||cwd(),(s===void 0||s.slice(0,2).toLowerCase()!==t.toLowerCase()&&s.charCodeAt(2)===CHAR_BACKWARD_SLASH)&&(s=`${t}\\`));const l=s.length;let o=0,a="",h=!1;const c=s.charCodeAt(0);if(l===1)isPathSeparator(c)&&(o=1,h=!0);else if(isPathSeparator(c))if(h=!0,isPathSeparator(s.charCodeAt(1))){let u=2,f=u;for(;u<l&&!isPathSeparator(s.charCodeAt(u));)u++;if(u<l&&u!==f){const b=s.slice(f,u);for(f=u;u<l&&isPathSeparator(s.charCodeAt(u));)u++;if(u<l&&u!==f){for(f=u;u<l&&!isPathSeparator(s.charCodeAt(u));)u++;(u===l||u!==f)&&(a=`\\\\${b}\\${s.slice(f,u)}`,o=u)}}}else o=1;else isWindowsDeviceRoot(c)&&s.charCodeAt(1)===CHAR_COLON&&(a=s.slice(0,2),o=2,l>2&&isPathSeparator(s.charCodeAt(2))&&(h=!0,o=3));if(a.length>0)if(t.length>0){if(a.toLowerCase()!==t.toLowerCase())continue}else t=a;if(i){if(t.length>0)break}else if(n=`${s.slice(o)}\\${n}`,i=h,h&&t.length>0)break}return n=normalizeString(n,!i,"\\",isPathSeparator),i?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){validateString(e,"path");const t=e.length;if(t===0)return".";let n=0,i,r=!1;const s=e.charCodeAt(0);if(t===1)return isPosixPathSeparator(s)?"\\":e;if(isPathSeparator(s))if(r=!0,isPathSeparator(e.charCodeAt(1))){let o=2,a=o;for(;o<t&&!isPathSeparator(e.charCodeAt(o));)o++;if(o<t&&o!==a){const h=e.slice(a,o);for(a=o;o<t&&isPathSeparator(e.charCodeAt(o));)o++;if(o<t&&o!==a){for(a=o;o<t&&!isPathSeparator(e.charCodeAt(o));)o++;if(o===t)return`\\\\${h}\\${e.slice(a)}\\`;o!==a&&(i=`\\\\${h}\\${e.slice(a,o)}`,n=o)}}}else n=1;else isWindowsDeviceRoot(s)&&e.charCodeAt(1)===CHAR_COLON&&(i=e.slice(0,2),n=2,t>2&&isPathSeparator(e.charCodeAt(2))&&(r=!0,n=3));let l=n<t?normalizeString(e.slice(n),!r,"\\",isPathSeparator):"";if(l.length===0&&!r&&(l="."),l.length>0&&isPathSeparator(e.charCodeAt(t-1))&&(l+="\\"),!r&&i===void 0&&e.includes(":")){if(l.length>=2&&isWindowsDeviceRoot(l.charCodeAt(0))&&l.charCodeAt(1)===CHAR_COLON)return`.\\${l}`;let o=e.indexOf(":");do if(o===t-1||isPathSeparator(e.charCodeAt(o+1)))return`.\\${l}`;while((o=e.indexOf(":",o+1))!==-1)}return i===void 0?r?`\\${l}`:l:r?`${i}\\${l}`:`${i}${l}`},isAbsolute(e){validateString(e,"path");const t=e.length;if(t===0)return!1;const n=e.charCodeAt(0);return isPathSeparator(n)||t>2&&isWindowsDeviceRoot(n)&&e.charCodeAt(1)===CHAR_COLON&&isPathSeparator(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let s=0;s<e.length;++s){const l=e[s];validateString(l,"path"),l.length>0&&(t===void 0?t=n=l:t+=`\\${l}`)}if(t===void 0)return".";let i=!0,r=0;if(typeof n=="string"&&isPathSeparator(n.charCodeAt(0))){++r;const s=n.length;s>1&&isPathSeparator(n.charCodeAt(1))&&(++r,s>2&&(isPathSeparator(n.charCodeAt(2))?++r:i=!1))}if(i){for(;r<t.length&&isPathSeparator(t.charCodeAt(r));)r++;r>=2&&(t=`\\${t.slice(r)}`)}return $kc.normalize(t)},relative(e,t){if(validateString(e,"from"),validateString(t,"to"),e===t)return"";const n=$kc.resolve(e),i=$kc.resolve(t);if(n===i||(e=n.toLowerCase(),t=i.toLowerCase(),e===t))return"";if(n.length!==e.length||i.length!==t.length){const p=n.split("\\"),m=i.split("\\");p[p.length-1]===""&&p.pop(),m[m.length-1]===""&&m.pop();const d=p.length,C=m.length,g=d<C?d:C;let w;for(w=0;w<g&&p[w].toLowerCase()===m[w].toLowerCase();w++);return w===0?i:w===g?C>g?m.slice(w).join("\\"):d>g?"..\\".repeat(d-1-w)+"..":"":"..\\".repeat(d-w)+m.slice(w).join("\\")}let r=0;for(;r<e.length&&e.charCodeAt(r)===CHAR_BACKWARD_SLASH;)r++;let s=e.length;for(;s-1>r&&e.charCodeAt(s-1)===CHAR_BACKWARD_SLASH;)s--;const l=s-r;let o=0;for(;o<t.length&&t.charCodeAt(o)===CHAR_BACKWARD_SLASH;)o++;let a=t.length;for(;a-1>o&&t.charCodeAt(a-1)===CHAR_BACKWARD_SLASH;)a--;const h=a-o,c=l<h?l:h;let u=-1,f=0;for(;f<c;f++){const p=e.charCodeAt(r+f);if(p!==t.charCodeAt(o+f))break;p===CHAR_BACKWARD_SLASH&&(u=f)}if(f!==c){if(u===-1)return i}else{if(h>c){if(t.charCodeAt(o+f)===CHAR_BACKWARD_SLASH)return i.slice(o+f+1);if(f===2)return i.slice(o+f)}l>c&&(e.charCodeAt(r+f)===CHAR_BACKWARD_SLASH?u=f:f===2&&(u=3)),u===-1&&(u=0)}let b="";for(f=r+u+1;f<=s;++f)(f===s||e.charCodeAt(f)===CHAR_BACKWARD_SLASH)&&(b+=b.length===0?"..":"\\..");return o+=u,b.length>0?`${b}${i.slice(o,a)}`:(i.charCodeAt(o)===CHAR_BACKWARD_SLASH&&++o,i.slice(o,a))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=$kc.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===CHAR_BACKWARD_SLASH){if(t.charCodeAt(1)===CHAR_BACKWARD_SLASH){const n=t.charCodeAt(2);if(n!==CHAR_QUESTION_MARK&&n!==CHAR_DOT)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(isWindowsDeviceRoot(t.charCodeAt(0))&&t.charCodeAt(1)===CHAR_COLON&&t.charCodeAt(2)===CHAR_BACKWARD_SLASH)return`\\\\?\\${t}`;return t},dirname(e){validateString(e,"path");const t=e.length;if(t===0)return".";let n=-1,i=0;const r=e.charCodeAt(0);if(t===1)return isPathSeparator(r)?e:".";if(isPathSeparator(r)){if(n=i=1,isPathSeparator(e.charCodeAt(1))){let o=2,a=o;for(;o<t&&!isPathSeparator(e.charCodeAt(o));)o++;if(o<t&&o!==a){for(a=o;o<t&&isPathSeparator(e.charCodeAt(o));)o++;if(o<t&&o!==a){for(a=o;o<t&&!isPathSeparator(e.charCodeAt(o));)o++;if(o===t)return e;o!==a&&(n=i=o+1)}}}}else isWindowsDeviceRoot(r)&&e.charCodeAt(1)===CHAR_COLON&&(n=t>2&&isPathSeparator(e.charCodeAt(2))?3:2,i=n);let s=-1,l=!0;for(let o=t-1;o>=i;--o)if(isPathSeparator(e.charCodeAt(o))){if(!l){s=o;break}}else l=!1;if(s===-1){if(n===-1)return".";s=n}return e.slice(0,s)},basename(e,t){t!==void 0&&validateString(t,"suffix"),validateString(e,"path");let n=0,i=-1,r=!0,s;if(e.length>=2&&isWindowsDeviceRoot(e.charCodeAt(0))&&e.charCodeAt(1)===CHAR_COLON&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let l=t.length-1,o=-1;for(s=e.length-1;s>=n;--s){const a=e.charCodeAt(s);if(isPathSeparator(a)){if(!r){n=s+1;break}}else o===-1&&(r=!1,o=s+1),l>=0&&(a===t.charCodeAt(l)?--l===-1&&(i=s):(l=-1,i=o))}return n===i?i=o:i===-1&&(i=e.length),e.slice(n,i)}for(s=e.length-1;s>=n;--s)if(isPathSeparator(e.charCodeAt(s))){if(!r){n=s+1;break}}else i===-1&&(r=!1,i=s+1);return i===-1?"":e.slice(n,i)},extname(e){validateString(e,"path");let t=0,n=-1,i=0,r=-1,s=!0,l=0;e.length>=2&&e.charCodeAt(1)===CHAR_COLON&&isWindowsDeviceRoot(e.charCodeAt(0))&&(t=i=2);for(let o=e.length-1;o>=t;--o){const a=e.charCodeAt(o);if(isPathSeparator(a)){if(!s){i=o+1;break}continue}r===-1&&(s=!1,r=o+1),a===CHAR_DOT?n===-1?n=o:l!==1&&(l=1):n!==-1&&(l=-1)}return n===-1||r===-1||l===0||l===1&&n===r-1&&n===i+1?"":e.slice(n,r)},format:_format2.bind(null,"\\"),parse(e){validateString(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.length;let i=0,r=e.charCodeAt(0);if(n===1)return isPathSeparator(r)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(isPathSeparator(r)){if(i=1,isPathSeparator(e.charCodeAt(1))){let u=2,f=u;for(;u<n&&!isPathSeparator(e.charCodeAt(u));)u++;if(u<n&&u!==f){for(f=u;u<n&&isPathSeparator(e.charCodeAt(u));)u++;if(u<n&&u!==f){for(f=u;u<n&&!isPathSeparator(e.charCodeAt(u));)u++;u===n?i=u:u!==f&&(i=u+1)}}}}else if(isWindowsDeviceRoot(r)&&e.charCodeAt(1)===CHAR_COLON){if(n<=2)return t.root=t.dir=e,t;if(i=2,isPathSeparator(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;i=3}}i>0&&(t.root=e.slice(0,i));let s=-1,l=i,o=-1,a=!0,h=e.length-1,c=0;for(;h>=i;--h){if(r=e.charCodeAt(h),isPathSeparator(r)){if(!a){l=h+1;break}continue}o===-1&&(a=!1,o=h+1),r===CHAR_DOT?s===-1?s=h:c!==1&&(c=1):s!==-1&&(c=-1)}return o!==-1&&(s===-1||c===0||c===1&&s===o-1&&s===l+1?t.base=t.name=e.slice(l,o):(t.name=e.slice(l,s),t.base=e.slice(l,o),t.ext=e.slice(s,o))),l>0&&l!==i?t.dir=e.slice(0,l-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},posixCwd=(()=>{if(platformIsWin32){const e=/\\/g;return()=>{const t=cwd().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>cwd()})(),$lc={resolve(...e){let t="",n=!1;for(let i=e.length-1;i>=0&&!n;i--){const r=e[i];validateString(r,`paths[${i}]`),r.length!==0&&(t=`${r}/${t}`,n=r.charCodeAt(0)===CHAR_FORWARD_SLASH)}if(!n){const i=posixCwd();t=`${i}/${t}`,n=i.charCodeAt(0)===CHAR_FORWARD_SLASH}return t=normalizeString(t,!n,"/",isPosixPathSeparator),n?`/${t}`:t.length>0?t:"."},normalize(e){if(validateString(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===CHAR_FORWARD_SLASH,n=e.charCodeAt(e.length-1)===CHAR_FORWARD_SLASH;return e=normalizeString(e,!t,"/",isPosixPathSeparator),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return validateString(e,"path"),e.length>0&&e.charCodeAt(0)===CHAR_FORWARD_SLASH},join(...e){if(e.length===0)return".";const t=[];for(let n=0;n<e.length;++n){const i=e[n];validateString(i,"path"),i.length>0&&t.push(i)}return t.length===0?".":$lc.normalize(t.join("/"))},relative(e,t){if(validateString(e,"from"),validateString(t,"to"),e===t||(e=$lc.resolve(e),t=$lc.resolve(t),e===t))return"";const n=1,i=e.length,r=i-n,s=1,l=t.length-s,o=r<l?r:l;let a=-1,h=0;for(;h<o;h++){const u=e.charCodeAt(n+h);if(u!==t.charCodeAt(s+h))break;u===CHAR_FORWARD_SLASH&&(a=h)}if(h===o)if(l>o){if(t.charCodeAt(s+h)===CHAR_FORWARD_SLASH)return t.slice(s+h+1);if(h===0)return t.slice(s+h)}else r>o&&(e.charCodeAt(n+h)===CHAR_FORWARD_SLASH?a=h:h===0&&(a=0));let c="";for(h=n+a+1;h<=i;++h)(h===i||e.charCodeAt(h)===CHAR_FORWARD_SLASH)&&(c+=c.length===0?"..":"/..");return`${c}${t.slice(s+a)}`},toNamespacedPath(e){return e},dirname(e){if(validateString(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===CHAR_FORWARD_SLASH;let n=-1,i=!0;for(let r=e.length-1;r>=1;--r)if(e.charCodeAt(r)===CHAR_FORWARD_SLASH){if(!i){n=r;break}}else i=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&validateString(t,"suffix"),validateString(e,"path");let n=0,i=-1,r=!0,s;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let l=t.length-1,o=-1;for(s=e.length-1;s>=0;--s){const a=e.charCodeAt(s);if(a===CHAR_FORWARD_SLASH){if(!r){n=s+1;break}}else o===-1&&(r=!1,o=s+1),l>=0&&(a===t.charCodeAt(l)?--l===-1&&(i=s):(l=-1,i=o))}return n===i?i=o:i===-1&&(i=e.length),e.slice(n,i)}for(s=e.length-1;s>=0;--s)if(e.charCodeAt(s)===CHAR_FORWARD_SLASH){if(!r){n=s+1;break}}else i===-1&&(r=!1,i=s+1);return i===-1?"":e.slice(n,i)},extname(e){validateString(e,"path");let t=-1,n=0,i=-1,r=!0,s=0;for(let l=e.length-1;l>=0;--l){const o=e[l];if(o==="/"){if(!r){n=l+1;break}continue}i===-1&&(r=!1,i=l+1),o==="."?t===-1?t=l:s!==1&&(s=1):t!==-1&&(s=-1)}return t===-1||i===-1||s===0||s===1&&t===i-1&&t===n+1?"":e.slice(t,i)},format:_format2.bind(null,"/"),parse(e){validateString(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.charCodeAt(0)===CHAR_FORWARD_SLASH;let i;n?(t.root="/",i=1):i=0;let r=-1,s=0,l=-1,o=!0,a=e.length-1,h=0;for(;a>=i;--a){const c=e.charCodeAt(a);if(c===CHAR_FORWARD_SLASH){if(!o){s=a+1;break}continue}l===-1&&(o=!1,l=a+1),c===CHAR_DOT?r===-1?r=a:h!==1&&(h=1):r!==-1&&(h=-1)}if(l!==-1){const c=s===0&&n?1:s;r===-1||h===0||h===1&&r===l-1&&r===s+1?t.base=t.name=e.slice(c,l):(t.name=e.slice(c,r),t.base=e.slice(c,l),t.ext=e.slice(r,l))}return s>0?t.dir=e.slice(0,s-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};$lc.win32=$kc.win32=$kc,$lc.posix=$kc.posix=$lc;var $mc=platformIsWin32?$kc.normalize:$lc.normalize,$nc=platformIsWin32?$kc.isAbsolute:$lc.isAbsolute,$oc=platformIsWin32?$kc.join:$lc.join,$pc=platformIsWin32?$kc.resolve:$lc.resolve,$qc=platformIsWin32?$kc.relative:$lc.relative,$rc=platformIsWin32?$kc.dirname:$lc.dirname,$sc=platformIsWin32?$kc.basename:$lc.basename,$tc=platformIsWin32?$kc.extname:$lc.extname,$uc=platformIsWin32?$kc.format:$lc.format,$vc=platformIsWin32?$kc.parse:$lc.parse,$wc=platformIsWin32?$kc.toNamespacedPath:$lc.toNamespacedPath,sep=platformIsWin32?$kc.sep:$lc.sep,$yc=platformIsWin32?$kc.delimiter:$lc.delimiter,_schemePattern=/^\w[\w\d+.-]*$/,_singleSlashStart=/^\//,_doubleSlashStart=/^\/\//;function _validateUri(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!_schemePattern.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!_singleSlashStart.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(_doubleSlashStart.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function _schemeFix(e,t){return!e&&!t?"file":e}function _referenceResolution(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==_slash&&(t=_slash+t):t=_slash;break}return t}var _empty="",_slash="/",_regexp=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,URI=class st{static isUri(t){return t instanceof st?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}constructor(t,n,i,r,s,l=!1){typeof t=="object"?(this.scheme=t.scheme||_empty,this.authority=t.authority||_empty,this.path=t.path||_empty,this.query=t.query||_empty,this.fragment=t.fragment||_empty):(this.scheme=_schemeFix(t,l),this.authority=n||_empty,this.path=_referenceResolution(this.scheme,i||_empty),this.query=r||_empty,this.fragment=s||_empty,_validateUri(this,l))}get fsPath(){return $Bc(this,!1)}with(t){if(!t)return this;let{scheme:n,authority:i,path:r,query:s,fragment:l}=t;return n===void 0?n=this.scheme:n===null&&(n=_empty),i===void 0?i=this.authority:i===null&&(i=_empty),r===void 0?r=this.path:r===null&&(r=_empty),s===void 0?s=this.query:s===null&&(s=_empty),l===void 0?l=this.fragment:l===null&&(l=_empty),n===this.scheme&&i===this.authority&&r===this.path&&s===this.query&&l===this.fragment?this:new Uri(n,i,r,s,l)}static parse(t,n=!1){const i=_regexp.exec(t);return i?new Uri(i[2]||_empty,percentDecode(i[4]||_empty),percentDecode(i[5]||_empty),percentDecode(i[7]||_empty),percentDecode(i[9]||_empty),n):new Uri(_empty,_empty,_empty,_empty,_empty)}static file(t){let n=_empty;if($l&&(t=t.replace(/\\/g,_slash)),t[0]===_slash&&t[1]===_slash){const i=t.indexOf(_slash,2);i===-1?(n=t.substring(2),t=_slash):(n=t.substring(2,i),t=t.substring(i)||_slash)}return new Uri("file",n,t,_empty,_empty)}static from(t,n){return new Uri(t.scheme,t.authority,t.path,t.query,t.fragment,n)}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let i;return $l&&t.scheme==="file"?i=st.file($kc.join($Bc(t,!0),...n)).path:i=$lc.join(t.path,...n),t.with({path:i})}toString(t=!1){return _asFormatted(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof st)return t;{const n=new Uri(t);return n._formatted=t.external??null,n._fsPath=t._sep===_pathSepMarker?t.fsPath??null:null,n}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},_pathSepMarker=$l?1:void 0,Uri=class extends URI{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=$Bc(this,!1)),this._fsPath}toString(e=!1){return e?_asFormatted(this,!0):(this._formatted||(this._formatted=_asFormatted(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=_pathSepMarker),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},encodeTable={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function encodeURIComponentFast(e,t,n){let i,r=-1;for(let s=0;s<e.length;s++){const l=e.charCodeAt(s);if(l>=97&&l<=122||l>=65&&l<=90||l>=48&&l<=57||l===45||l===46||l===95||l===126||t&&l===47||n&&l===91||n&&l===93||n&&l===58)r!==-1&&(i+=encodeURIComponent(e.substring(r,s)),r=-1),i!==void 0&&(i+=e.charAt(s));else{i===void 0&&(i=e.substr(0,s));const o=encodeTable[l];o!==void 0?(r!==-1&&(i+=encodeURIComponent(e.substring(r,s)),r=-1),i+=o):r===-1&&(r=s)}}return r!==-1&&(i+=encodeURIComponent(e.substring(r))),i!==void 0?i:e}function encodeURIComponentMinimal(e){let t;for(let n=0;n<e.length;n++){const i=e.charCodeAt(n);i===35||i===63?(t===void 0&&(t=e.substr(0,n)),t+=encodeTable[i]):t!==void 0&&(t+=e[n])}return t!==void 0?t:e}function $Bc(e,t){let n;return e.authority&&e.path.length>1&&e.scheme==="file"?n=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?n=e.path.substr(1):n=e.path[1].toLowerCase()+e.path.substr(2):n=e.path,$l&&(n=n.replace(/\//g,"\\")),n}function _asFormatted(e,t){const n=t?encodeURIComponentMinimal:encodeURIComponentFast;let i="",{scheme:r,authority:s,path:l,query:o,fragment:a}=e;if(r&&(i+=r,i+=":"),(s||r==="file")&&(i+=_slash,i+=_slash),s){let h=s.indexOf("@");if(h!==-1){const c=s.substr(0,h);s=s.substr(h+1),h=c.lastIndexOf(":"),h===-1?i+=n(c,!1,!1):(i+=n(c.substr(0,h),!1,!1),i+=":",i+=n(c.substr(h+1),!1,!0)),i+="@"}s=s.toLowerCase(),h=s.lastIndexOf(":"),h===-1?i+=n(s,!1,!0):(i+=n(s.substr(0,h),!1,!0),i+=s.substr(h))}if(l){if(l.length>=3&&l.charCodeAt(0)===47&&l.charCodeAt(2)===58){const h=l.charCodeAt(1);h>=65&&h<=90&&(l=`/${String.fromCharCode(h+32)}:${l.substr(3)}`)}else if(l.length>=2&&l.charCodeAt(1)===58){const h=l.charCodeAt(0);h>=65&&h<=90&&(l=`${String.fromCharCode(h+32)}:${l.substr(2)}`)}i+=n(l,!0,!1)}return o&&(i+="?",i+=n(o,!1,!1)),a&&(i+="#",i+=t?a:encodeURIComponentFast(a,!1,!1)),i}function decodeURIComponentGraceful(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+decodeURIComponentGraceful(e.substr(3)):e}}var _rEncodedAsHex=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function percentDecode(e){return e.match(_rEncodedAsHex)?e.replace(_rEncodedAsHex,t=>decodeURIComponentGraceful(t)):e}var $lV=class B{constructor(t,n){this.lineNumber=t,this.column=n}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new B(t,n)}delta(t=0,n=0){return this.with(Math.max(1,this.lineNumber+t),Math.max(1,this.column+n))}equals(t){return B.equals(this,t)}static equals(t,n){return!t&&!n?!0:!!t&&!!n&&t.lineNumber===n.lineNumber&&t.column===n.column}isBefore(t){return B.isBefore(this,t)}static isBefore(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<n.column}isBeforeOrEqual(t){return B.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<=n.column}static compare(t,n){const i=t.lineNumber|0,r=n.lineNumber|0;if(i===r){const s=t.column|0,l=n.column|0;return s-l}return i-r}clone(){return new B(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new B(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}},$mV=class _{constructor(t,n,i,r){t>i||t===i&&n>r?(this.startLineNumber=i,this.startColumn=r,this.endLineNumber=t,this.endColumn=n):(this.startLineNumber=t,this.startColumn=n,this.endLineNumber=i,this.endColumn=r)}isEmpty(){return _.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return _.containsPosition(this,t)}static containsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<t.startColumn||n.lineNumber===t.endLineNumber&&n.column>t.endColumn)}static strictContainsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<=t.startColumn||n.lineNumber===t.endLineNumber&&n.column>=t.endColumn)}containsRange(t){return _.containsRange(this,t)}static containsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>t.endColumn)}strictContainsRange(t){return _.strictContainsRange(this,t)}static strictContainsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<=t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>=t.endColumn)}plusRange(t){return _.plusRange(this,t)}static plusRange(t,n){let i,r,s,l;return n.startLineNumber<t.startLineNumber?(i=n.startLineNumber,r=n.startColumn):n.startLineNumber===t.startLineNumber?(i=n.startLineNumber,r=Math.min(n.startColumn,t.startColumn)):(i=t.startLineNumber,r=t.startColumn),n.endLineNumber>t.endLineNumber?(s=n.endLineNumber,l=n.endColumn):n.endLineNumber===t.endLineNumber?(s=n.endLineNumber,l=Math.max(n.endColumn,t.endColumn)):(s=t.endLineNumber,l=t.endColumn),new _(i,r,s,l)}intersectRanges(t){return _.intersectRanges(this,t)}static intersectRanges(t,n){let i=t.startLineNumber,r=t.startColumn,s=t.endLineNumber,l=t.endColumn;const o=n.startLineNumber,a=n.startColumn,h=n.endLineNumber,c=n.endColumn;return i<o?(i=o,r=a):i===o&&(r=Math.max(r,a)),s>h?(s=h,l=c):s===h&&(l=Math.min(l,c)),i>s||i===s&&r>l?null:new _(i,r,s,l)}equalsRange(t){return _.equalsRange(this,t)}static equalsRange(t,n){return!t&&!n?!0:!!t&&!!n&&t.startLineNumber===n.startLineNumber&&t.startColumn===n.startColumn&&t.endLineNumber===n.endLineNumber&&t.endColumn===n.endColumn}getEndPosition(){return _.getEndPosition(this)}static getEndPosition(t){return new $lV(t.endLineNumber,t.endColumn)}getStartPosition(){return _.getStartPosition(this)}static getStartPosition(t){return new $lV(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new _(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new _(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return _.collapseToStart(this)}static collapseToStart(t){return new _(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return _.collapseToEnd(this)}static collapseToEnd(t){return new _(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new _(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}isSingleLine(){return this.startLineNumber===this.endLineNumber}static fromPositions(t,n=t){return new _(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new _(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn)}static areIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<=n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<=t.startColumn)}static areOnlyIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber-1||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn-1||n.endLineNumber<t.startLineNumber-1||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn-1)}static compareRangesUsingStarts(t,n){if(t&&n){const s=t.startLineNumber|0,l=n.startLineNumber|0;if(s===l){const o=t.startColumn|0,a=n.startColumn|0;if(o===a){const h=t.endLineNumber|0,c=n.endLineNumber|0;if(h===c){const u=t.endColumn|0,f=n.endColumn|0;return u-f}return h-c}return o-a}return s-l}return(t?1:0)-(n?1:0)}static compareRangesUsingEnds(t,n){return t.endLineNumber===n.endLineNumber?t.endColumn===n.endColumn?t.startLineNumber===n.startLineNumber?t.startColumn-n.startColumn:t.startLineNumber-n.startLineNumber:t.endColumn-n.endColumn:t.endLineNumber-n.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}};function $Ew(e,t){const n=Object.create(null);for(const[i,r]of Object.entries(e))t(i,r)&&(n[i]=r);return n}var OverviewRulerLane;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(OverviewRulerLane||(OverviewRulerLane={}));var GlyphMarginLane;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=3]="Right"})(GlyphMarginLane||(GlyphMarginLane={}));var MinimapPosition;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(MinimapPosition||(MinimapPosition={}));var MinimapSectionHeaderStyle;(function(e){e[e.Normal=1]="Normal",e[e.Underlined=2]="Underlined"})(MinimapSectionHeaderStyle||(MinimapSectionHeaderStyle={}));var InjectedTextCursorStops;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(InjectedTextCursorStops||(InjectedTextCursorStops={}));var EndOfLinePreference;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(EndOfLinePreference||(EndOfLinePreference={}));var DefaultEndOfLine;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(DefaultEndOfLine||(DefaultEndOfLine={}));var EndOfLineSequence;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(EndOfLineSequence||(EndOfLineSequence={}));var $jY=class{constructor(e,t){this._findMatchBrand=void 0,this.range=e,this.matches=t}},TrackedRangeStickiness;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(TrackedRangeStickiness||(TrackedRangeStickiness={}));var PositionAffinity;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"})(PositionAffinity||(PositionAffinity={}));var ModelConstants;(function(e){e[e.FIRST_LINE_DETECTION_LENGTH_LIMIT=1e3]="FIRST_LINE_DETECTION_LENGTH_LIMIT"})(ModelConstants||(ModelConstants={}));var $nY=class{constructor(e,t,n){this.regex=e,this.wordSeparators=t,this.simpleSearch=n}},$oY=class{constructor(e,t,n){this.reverseEdits=e,this.changes=t,this.trimAutoWhitespaceLineNumbers=n}},$a1=class{constructor(e,t){this.piece=e,this.color=t,this.size_left=0,this.lf_left=0,this.parent=this,this.left=this,this.right=this}next(){if(this.right!==$b1)return $c1(this.right);let e=this;for(;e.parent!==$b1&&e.parent.left!==e;)e=e.parent;return e.parent===$b1?$b1:e.parent}prev(){if(this.left!==$b1)return $d1(this.left);let e=this;for(;e.parent!==$b1&&e.parent.right!==e;)e=e.parent;return e.parent===$b1?$b1:e.parent}detach(){this.parent=null,this.left=null,this.right=null}},NodeColor;(function(e){e[e.Black=0]="Black",e[e.Red=1]="Red"})(NodeColor||(NodeColor={}));var $b1=new $a1(null,0);$b1.parent=$b1,$b1.left=$b1,$b1.right=$b1,$b1.color=0;function $c1(e){for(;e.left!==$b1;)e=e.left;return e}function $d1(e){for(;e.right!==$b1;)e=e.right;return e}function calculateSize(e){return e===$b1?0:e.size_left+e.piece.length+calculateSize(e.right)}function calculateLF(e){return e===$b1?0:e.lf_left+e.piece.lineFeedCnt+calculateLF(e.right)}function resetSentinel(){$b1.parent=$b1}function $e1(e,t){const n=t.right;n.size_left+=t.size_left+(t.piece?t.piece.length:0),n.lf_left+=t.lf_left+(t.piece?t.piece.lineFeedCnt:0),t.right=n.left,n.left!==$b1&&(n.left.parent=t),n.parent=t.parent,t.parent===$b1?e.root=n:t.parent.left===t?t.parent.left=n:t.parent.right=n,n.left=t,t.parent=n}function $f1(e,t){const n=t.left;t.left=n.right,n.right!==$b1&&(n.right.parent=t),n.parent=t.parent,t.size_left-=n.size_left+(n.piece?n.piece.length:0),t.lf_left-=n.lf_left+(n.piece?n.piece.lineFeedCnt:0),t.parent===$b1?e.root=n:t===t.parent.right?t.parent.right=n:t.parent.left=n,n.right=t,t.parent=n}function $g1(e,t){let n,i;if(t.left===$b1?(i=t,n=i.right):t.right===$b1?(i=t,n=i.left):(i=$c1(t.right),n=i.right),i===e.root){e.root=n,n.color=0,t.detach(),resetSentinel(),e.root.parent=$b1;return}const r=i.color===1;if(i===i.parent.left?i.parent.left=n:i.parent.right=n,i===t?(n.parent=i.parent,$j1(e,n)):(i.parent===t?n.parent=i:n.parent=i.parent,$j1(e,n),i.left=t.left,i.right=t.right,i.parent=t.parent,i.color=t.color,t===e.root?e.root=i:t===t.parent.left?t.parent.left=i:t.parent.right=i,i.left!==$b1&&(i.left.parent=i),i.right!==$b1&&(i.right.parent=i),i.size_left=t.size_left,i.lf_left=t.lf_left,$j1(e,i)),t.detach(),n.parent.left===n){const l=calculateSize(n),o=calculateLF(n);if(l!==n.parent.size_left||o!==n.parent.lf_left){const a=l-n.parent.size_left,h=o-n.parent.lf_left;n.parent.size_left=l,n.parent.lf_left=o,$i1(e,n.parent,a,h)}}if($j1(e,n.parent),r){resetSentinel();return}let s;for(;n!==e.root&&n.color===0;)n===n.parent.left?(s=n.parent.right,s.color===1&&(s.color=0,n.parent.color=1,$e1(e,n.parent),s=n.parent.right),s.left.color===0&&s.right.color===0?(s.color=1,n=n.parent):(s.right.color===0&&(s.left.color=0,s.color=1,$f1(e,s),s=n.parent.right),s.color=n.parent.color,n.parent.color=0,s.right.color=0,$e1(e,n.parent),n=e.root)):(s=n.parent.left,s.color===1&&(s.color=0,n.parent.color=1,$f1(e,n.parent),s=n.parent.left),s.left.color===0&&s.right.color===0?(s.color=1,n=n.parent):(s.left.color===0&&(s.right.color=0,s.color=1,$e1(e,s),s=n.parent.left),s.color=n.parent.color,n.parent.color=0,s.left.color=0,$f1(e,n.parent),n=e.root));n.color=0,resetSentinel()}function $h1(e,t){for($j1(e,t);t!==e.root&&t.parent.color===1;)if(t.parent===t.parent.parent.left){const n=t.parent.parent.right;n.color===1?(t.parent.color=0,n.color=0,t.parent.parent.color=1,t=t.parent.parent):(t===t.parent.right&&(t=t.parent,$e1(e,t)),t.parent.color=0,t.parent.parent.color=1,$f1(e,t.parent.parent))}else{const n=t.parent.parent.left;n.color===1?(t.parent.color=0,n.color=0,t.parent.parent.color=1,t=t.parent.parent):(t===t.parent.left&&(t=t.parent,$f1(e,t)),t.parent.color=0,t.parent.parent.color=1,$e1(e,t.parent.parent))}e.root.color=0}function $i1(e,t,n,i){for(;t!==e.root&&t!==$b1;)t.parent.left===t&&(t.parent.size_left+=n,t.parent.lf_left+=i),t=t.parent}function $j1(e,t){let n=0,i=0;if(t!==e.root){for(;t!==e.root&&t===t.parent.right;)t=t.parent;if(t!==e.root)for(t=t.parent,n=calculateSize(t.left)-t.size_left,i=calculateLF(t.left)-t.lf_left,t.size_left+=n,t.lf_left+=i;t!==e.root&&(n!==0||i!==0);)t.parent.left===t&&(t.parent.size_left+=n,t.parent.lf_left+=i),t=t.parent}}var minute=60,hour=minute*60,day=hour*24,week=day*7,month=day*30,year=day*365,$Ev={DateTimeFormat(e,t){try{return new Intl.DateTimeFormat(e,t)}catch{return new Intl.DateTimeFormat(void 0,t)}},Collator(e,t){try{return new Intl.Collator(e,t)}catch{return new Intl.Collator(void 0,t)}},Segmenter(e,t){try{return new Intl.Segmenter(e,t)}catch{return new Intl.Segmenter(void 0,t)}},Locale(e,t){try{return new Intl.Locale(e,t)}catch{return new Intl.Locale($j,t)}}},Constants;(function(e){e[e.MAX_SAFE_SMALL_INTEGER=1073741824]="MAX_SAFE_SMALL_INTEGER",e[e.MIN_SAFE_SMALL_INTEGER=-1073741824]="MIN_SAFE_SMALL_INTEGER",e[e.MAX_UINT_8=255]="MAX_UINT_8",e[e.MAX_UINT_16=65535]="MAX_UINT_16",e[e.MAX_UINT_32=4294967295]="MAX_UINT_32",e[e.UNICODE_SUPPLEMENTARY_PLANE_BEGIN=65536]="UNICODE_SUPPLEMENTARY_PLANE_BEGIN"})(Constants||(Constants={}));function $4f(e){return e<0?0:e>255?255:e|0}function $5f(e){return e<0?0:e>4294967295?4294967295:e|0}var $9V=class yt{constructor(t){const n=$4f(t);this.c=n,this.a=yt.d(n),this.b=new Map}static d(t){const n=new Uint8Array(256);return n.fill(t),n}set(t,n){const i=$4f(n);t>=0&&t<256?this.a[t]=i:this.b.set(t,i)}get(t){return t>=0&&t<256?this.a[t]:this.b.get(t)||this.c}clear(){this.a.fill(this.c),this.b.clear()}},Boolean2;(function(e){e[e.False=0]="False",e[e.True=1]="True"})(Boolean2||(Boolean2={}));var WordCharacterClass;(function(e){e[e.Regular=0]="Regular",e[e.Whitespace=1]="Whitespace",e[e.WordSeparator=2]="WordSeparator"})(WordCharacterClass||(WordCharacterClass={}));var $$V=class extends $9V{constructor(e,t){super(0),this.e=null,this.f=null,this.g=[],this.intlSegmenterLocales=t,this.intlSegmenterLocales.length>0?this.e=$Ev.Segmenter(this.intlSegmenterLocales,{granularity:"word"}):this.e=null;for(let n=0,i=e.length;n<i;n++)this.set(e.charCodeAt(n),2);this.set(32,1),this.set(9,1)}findPrevIntlWordBeforeOrAtOffset(e,t){let n=null;for(const i of this.h(e)){if(i.index>t)break;n=i}return n}findNextIntlWordAtOrAfterOffset(e,t){for(const n of this.h(e))if(!(n.index<t))return n;return null}h(e){return this.e?this.f===e?this.g:(this.f=e,this.g=this.j(this.e.segment(e)),this.g):[]}j(e){const t=[];for(const n of e)this.k(n)&&t.push(n);return t}k(e){return!!e.isWordLike}},wordClassifierCache=new $Ic(10);function $_V(e,t){const n=`${e}/${t.join(",")}`;let i=wordClassifierCache.get(n);return i||(i=new $$V(e,t),wordClassifierCache.set(n,i)),i}var $k1=class{constructor(e,t,n,i){this.searchString=e,this.isRegex=t,this.matchCase=n,this.wordSeparators=i}parseSearchRequest(){if(this.searchString==="")return null;let e;this.isRegex?e=$l1(this.searchString):e=this.searchString.indexOf(`
`)>=0;let t=null;try{t=$hg(this.searchString,this.isRegex,{matchCase:this.matchCase,wholeWord:!1,multiline:e,global:!0,unicode:!0})}catch{return null}if(!t)return null;let n=!this.isRegex&&!e;return n&&this.searchString.toLowerCase()!==this.searchString.toUpperCase()&&(n=this.matchCase),new $nY(t,this.wordSeparators?$_V(this.wordSeparators,[]):null,n?this.searchString:null)}};function $l1(e){if(!e||e.length===0)return!1;for(let t=0,n=e.length;t<n;t++){const i=e.charCodeAt(t);if(i===10)return!0;if(i===92){if(t++,t>=n)break;const r=e.charCodeAt(t);if(r===110||r===114||r===87)return!0}}return!1}function $m1(e,t,n){if(!n)return new $jY(e,null);const i=[];for(let r=0,s=t.length;r<s;r++)i[r]=t[r];return new $jY(e,i)}function leftIsWordBounday(e,t,n,i,r){if(i===0)return!0;const s=t.charCodeAt(i-1);if(e.get(s)!==0||s===13||s===10)return!0;if(r>0){const l=t.charCodeAt(i);if(e.get(l)!==0)return!0}return!1}function rightIsWordBounday(e,t,n,i,r){if(i+r===n)return!0;const s=t.charCodeAt(i+r);if(e.get(s)!==0||s===13||s===10)return!0;if(r>0){const l=t.charCodeAt(i+r-1);if(e.get(l)!==0)return!0}return!1}function $o1(e,t,n,i,r){return leftIsWordBounday(e,t,n,i,r)&&rightIsWordBounday(e,t,n,i,r)}var $p1=class{constructor(e,t){this._wordSeparators=e,this.a=t,this.b=-1,this.c=0}reset(e){this.a.lastIndex=e,this.b=-1,this.c=0}next(e){const t=e.length;let n;do{if(this.b+this.c===t||(n=this.a.exec(e),!n))return null;const i=n.index,r=n[0].length;if(i===this.b&&r===this.c){if(r===0){$Fg(e,t,this.a.lastIndex)>65535?this.a.lastIndex+=2:this.a.lastIndex+=1;continue}return null}if(this.b=i,this.c=r,!this._wordSeparators||$o1(this._wordSeparators,e,t,i,r))return n}while(n);return null}},AverageBufferSize=65535;function createUintArray(e){let t;return e[e.length-1]<65536?t=new Uint16Array(e.length):t=new Uint32Array(e.length),t.set(e,0),t}var LineStarts=class{constructor(e,t,n,i,r){this.lineStarts=e,this.cr=t,this.lf=n,this.crlf=i,this.isBasicASCII=r}};function $q1(e,t=!0){const n=[0];let i=1;for(let r=0,s=e.length;r<s;r++){const l=e.charCodeAt(r);l===13?r+1<s&&e.charCodeAt(r+1)===10?(n[i++]=r+2,r++):n[i++]=r+1:l===10&&(n[i++]=r+1)}return t?createUintArray(n):n}function $r1(e,t){e.length=0,e[0]=0;let n=1,i=0,r=0,s=0,l=!0;for(let a=0,h=t.length;a<h;a++){const c=t.charCodeAt(a);c===13?a+1<h&&t.charCodeAt(a+1)===10?(s++,e[n++]=a+2,a++):(i++,e[n++]=a+1):c===10?(r++,e[n++]=a+1):l&&c!==9&&(c<32||c>126)&&(l=!1)}const o=new LineStarts(createUintArray(e),i,r,s,l);return e.length=0,o}var $s1=class{constructor(e,t,n,i,r){this.bufferIndex=e,this.start=t,this.end=n,this.lineFeedCnt=i,this.length=r}},$t1=class{constructor(e,t){this.buffer=e,this.lineStarts=t}},PieceTreeSnapshot=class{constructor(e,t){this.a=[],this.c=e,this.d=t,this.b=0,e.root!==$b1&&e.iterate(e.root,n=>(n!==$b1&&this.a.push(n.piece),!0))}read(){return this.a.length===0?this.b===0?(this.b++,this.d):null:this.b>this.a.length-1?null:this.b===0?this.d+this.c.getPieceContent(this.a[this.b++]):this.c.getPieceContent(this.a[this.b++])}},PieceTreeSearchCache=class{constructor(e){this.a=e,this.b=[]}get(e){for(let t=this.b.length-1;t>=0;t--){const n=this.b[t];if(n.nodeStartOffset<=e&&n.nodeStartOffset+n.node.piece.length>=e)return n}return null}get2(e){for(let t=this.b.length-1;t>=0;t--){const n=this.b[t];if(n.nodeStartLineNumber&&n.nodeStartLineNumber<e&&n.nodeStartLineNumber+n.node.piece.lineFeedCnt>=e)return n}return null}set(e){this.b.length>=this.a&&this.b.shift(),this.b.push(e)}validate(e){let t=!1;const n=this.b;for(let i=0;i<n.length;i++){const r=n[i];if(r.node.parent===null||r.nodeStartOffset>=e){n[i]=null,t=!0;continue}}if(t){const i=[];for(const r of n)r!==null&&i.push(r);this.b=i}}},$u1=class{constructor(e,t,n){this.create(e,t,n)}create(e,t,n){this.a=[new $t1("",[0])],this.g={line:0,column:0},this.root=$b1,this.b=1,this.c=0,this.d=t,this.e=t.length,this.f=n;let i=null;for(let r=0,s=e.length;r<s;r++)if(e[r].buffer.length>0){e[r].lineStarts||(e[r].lineStarts=$q1(e[r].buffer));const l=new $s1(r+1,{line:0,column:0},{line:e[r].lineStarts.length-1,column:e[r].buffer.length-e[r].lineStarts[e[r].lineStarts.length-1]},e[r].lineStarts.length-1,e[r].buffer.length);this.a.push(e[r]),i=this.S(i,l)}this.h=new PieceTreeSearchCache(1),this.j={lineNumber:0,value:""},this.y()}normalizeEOL(e){const t=AverageBufferSize,n=t-Math.floor(t/3),i=n*2;let r="",s=0;const l=[];if(this.iterate(this.root,o=>{const a=this.R(o),h=a.length;if(s<=n||s+h<i)return r+=a,s+=h,!0;const c=r.replace(/\r\n|\r|\n/g,e);return l.push(new $t1(c,$q1(c))),r=a,s=h,!0}),s>0){const o=r.replace(/\r\n|\r|\n/g,e);l.push(new $t1(o,$q1(o)))}this.create(l,e,!0)}getEOL(){return this.d}setEOL(e){this.d=e,this.e=this.d.length,this.normalizeEOL(e)}createSnapshot(e){return new PieceTreeSnapshot(this,e)}equal(e){if(this.getLength()!==e.getLength()||this.getLineCount()!==e.getLineCount())return!1;let t=0;return this.iterate(this.root,i=>{if(i===$b1)return!0;const r=this.R(i),s=r.length,l=e.G(t),o=e.G(t+s),a=e.getValueInRange2(l,o);return t+=s,r===a})}getOffsetAt(e,t){let n=0,i=this.root;for(;i!==$b1;)if(i.left!==$b1&&i.lf_left+1>=e)i=i.left;else if(i.lf_left+i.piece.lineFeedCnt+1>=e){n+=i.size_left;const r=this.B(i,e-i.lf_left-2);return n+=r+t-1}else e-=i.lf_left+i.piece.lineFeedCnt,n+=i.size_left+i.piece.length,i=i.right;return n}getPositionAt(e){e=Math.floor(e),e=Math.max(0,e);let t=this.root,n=0;const i=e;for(;t!==$b1;)if(t.size_left!==0&&t.size_left>=e)t=t.left;else if(t.size_left+t.piece.length>=e){const r=this.A(t,e-t.size_left);if(n+=t.lf_left+r.index,r.index===0){const s=this.getOffsetAt(n+1,1),l=i-s;return new $lV(n+1,l+1)}return new $lV(n+1,r.remainder+1)}else if(e-=t.size_left+t.piece.length,n+=t.lf_left+t.piece.lineFeedCnt,t.right===$b1){const r=this.getOffsetAt(n+1,1),s=i-e-r;return new $lV(n+1,s+1)}else t=t.right;return new $lV(1,1)}getValueInRange(e,t){if(e.startLineNumber===e.endLineNumber&&e.startColumn===e.endColumn)return"";const n=this.H(e.startLineNumber,e.startColumn),i=this.H(e.endLineNumber,e.endColumn),r=this.getValueInRange2(n,i);return t?t!==this.d||!this.f?r.replace(/\r\n|\r|\n/g,t):t===this.getEOL()&&this.f?r:r.replace(/\r\n|\r|\n/g,t):r}getValueInRange2(e,t){if(e.node===t.node){const l=e.node,o=this.a[l.piece.bufferIndex].buffer,a=this.u(l.piece.bufferIndex,l.piece.start);return o.substring(a+e.remainder,a+t.remainder)}let n=e.node;const i=this.a[n.piece.bufferIndex].buffer,r=this.u(n.piece.bufferIndex,n.piece.start);let s=i.substring(r+e.remainder,r+n.piece.length);for(n=n.next();n!==$b1;){const l=this.a[n.piece.bufferIndex].buffer,o=this.u(n.piece.bufferIndex,n.piece.start);if(n===t.node){s+=l.substring(o,o+t.remainder);break}else s+=l.substr(o,n.piece.length);n=n.next()}return s}getLinesContent(){const e=[];let t=0,n="",i=!1;return this.iterate(this.root,r=>{if(r===$b1)return!0;const s=r.piece;let l=s.length;if(l===0)return!0;const o=this.a[s.bufferIndex].buffer,a=this.a[s.bufferIndex].lineStarts,h=s.start.line,c=s.end.line;let u=a[h]+s.start.column;if(i&&(o.charCodeAt(u)===10&&(u++,l--),e[t++]=n,n="",i=!1,l===0))return!0;if(h===c)return!this.f&&o.charCodeAt(u+l-1)===13?(i=!0,n+=o.substr(u,l-1)):n+=o.substr(u,l),!0;n+=this.f?o.substring(u,Math.max(u,a[h+1]-this.e)):o.substring(u,a[h+1]).replace(/(\r\n|\r|\n)$/,""),e[t++]=n;for(let f=h+1;f<c;f++)n=this.f?o.substring(a[f],a[f+1]-this.e):o.substring(a[f],a[f+1]).replace(/(\r\n|\r|\n)$/,""),e[t++]=n;return!this.f&&o.charCodeAt(a[c]+s.end.column-1)===13?(i=!0,s.end.column===0?t--:n=o.substr(a[c],s.end.column-1)):n=o.substr(a[c],s.end.column),!0}),i&&(e[t++]=n,n=""),e[t++]=n,e}getLength(){return this.c}getLineCount(){return this.b}getLineContent(e){return this.j.lineNumber===e?this.j.value:(this.j.lineNumber=e,e===this.b?this.j.value=this.getLineRawContent(e):this.f?this.j.value=this.getLineRawContent(e,this.e):this.j.value=this.getLineRawContent(e).replace(/(\r\n|\r|\n)$/,""),this.j.value)}l(e){if(e.remainder===e.node.piece.length){const t=e.node.next();if(!t)return 0;const n=this.a[t.piece.bufferIndex],i=this.u(t.piece.bufferIndex,t.piece.start);return n.buffer.charCodeAt(i)}else{const t=this.a[e.node.piece.bufferIndex],i=this.u(e.node.piece.bufferIndex,e.node.piece.start)+e.remainder;return t.buffer.charCodeAt(i)}}getLineCharCode(e,t){const n=this.H(e,t+1);return this.l(n)}getLineLength(e){if(e===this.getLineCount()){const t=this.getOffsetAt(e,1);return this.getLength()-t}return this.getOffsetAt(e+1,1)-this.getOffsetAt(e,1)-this.e}getCharCode(e){const t=this.G(e);return this.l(t)}getNearestChunk(e){const t=this.G(e);if(t.remainder===t.node.piece.length){const n=t.node.next();if(!n||n===$b1)return"";const i=this.a[n.piece.bufferIndex],r=this.u(n.piece.bufferIndex,n.piece.start);return i.buffer.substring(r,r+n.piece.length)}else{const n=this.a[t.node.piece.bufferIndex],i=this.u(t.node.piece.bufferIndex,t.node.piece.start),r=i+t.remainder,s=i+t.node.piece.length;return n.buffer.substring(r,s)}}findMatchesInNode(e,t,n,i,r,s,l,o,a,h,c){const u=this.a[e.piece.bufferIndex],f=this.u(e.piece.bufferIndex,e.piece.start),b=this.u(e.piece.bufferIndex,r),p=this.u(e.piece.bufferIndex,s);let m;const d={line:0,column:0};let C,g;t._wordSeparators?(C=u.buffer.substring(b,p),g=w=>w+b,t.reset(0)):(C=u.buffer,g=w=>w,t.reset(b));do if(m=t.next(C),m){if(g(m.index)>=p)return h;this.s(e,g(m.index)-f,d);const w=this.t(e.piece.bufferIndex,r,d),M=d.line===r.line?d.column-r.column+i:d.column+1,v=M+m[0].length;if(c[h++]=$m1(new $mV(n+w,M,n+w,v),m,o),g(m.index)+m[0].length>=p||h>=a)return h}while(m);return h}findMatchesLineByLine(e,t,n,i){const r=[];let s=0;const l=new $p1(t.wordSeparators,t.regex);let o=this.H(e.startLineNumber,e.startColumn);if(o===null)return[];const a=this.H(e.endLineNumber,e.endColumn);if(a===null)return[];let h=this.s(o.node,o.remainder);const c=this.s(a.node,a.remainder);if(o.node===a.node)return this.findMatchesInNode(o.node,l,e.startLineNumber,e.startColumn,h,c,t,n,i,s,r),r;let u=e.startLineNumber,f=o.node;for(;f!==a.node;){const p=this.t(f.piece.bufferIndex,h,f.piece.end);if(p>=1){const d=this.a[f.piece.bufferIndex].lineStarts,C=this.u(f.piece.bufferIndex,f.piece.start),g=d[h.line+p],w=u===e.startLineNumber?e.startColumn:1;if(s=this.findMatchesInNode(f,l,u,w,h,this.s(f,g-C),t,n,i,s,r),s>=i)return r;u+=p}const m=u===e.startLineNumber?e.startColumn-1:0;if(u===e.endLineNumber){const d=this.getLineContent(u).substring(m,e.endColumn-1);return s=this.n(t,l,d,e.endLineNumber,m,s,r,n,i),r}if(s=this.n(t,l,this.getLineContent(u).substr(m),u,m,s,r,n,i),s>=i)return r;u++,o=this.H(u,1),f=o.node,h=this.s(o.node,o.remainder)}if(u===e.endLineNumber){const p=u===e.startLineNumber?e.startColumn-1:0,m=this.getLineContent(u).substring(p,e.endColumn-1);return s=this.n(t,l,m,e.endLineNumber,p,s,r,n,i),r}const b=u===e.startLineNumber?e.startColumn:1;return s=this.findMatchesInNode(a.node,l,u,b,h,c,t,n,i,s,r),r}n(e,t,n,i,r,s,l,o,a){const h=e.wordSeparators;if(!o&&e.simpleSearch){const u=e.simpleSearch,f=u.length,b=n.length;let p=-f;for(;(p=n.indexOf(u,p+f))!==-1;)if((!h||$o1(h,n,b,p,f))&&(l[s++]=new $jY(new $mV(i,p+1+r,i,p+1+f+r),null),s>=a))return s;return s}let c;t.reset(0);do if(c=t.next(n),c&&(l[s++]=$m1(new $mV(i,c.index+1+r,i,c.index+1+c[0].length+r),c,o),s>=a))return s;while(c);return s}insert(e,t,n=!1){if(this.f=this.f&&n,this.j.lineNumber=0,this.j.value="",this.root!==$b1){const{node:i,remainder:r,nodeStartOffset:s}=this.G(e),l=i.piece,o=l.bufferIndex,a=this.s(i,r);if(i.piece.bufferIndex===0&&l.end.line===this.g.line&&l.end.column===this.g.column&&s+l.length===e&&t.length<AverageBufferSize){this.F(i,t),this.y();return}if(s===e)this.o(t,i),this.h.validate(e);else if(s+i.piece.length>e){const h=[];let c=new $s1(l.bufferIndex,a,l.end,this.t(l.bufferIndex,a,l.end),this.u(o,l.end)-this.u(o,a));if(this.K()&&this.M(t)&&this.I(i,r)===10){const p={line:c.start.line+1,column:0};c=new $s1(c.bufferIndex,p,c.end,this.t(c.bufferIndex,p,c.end),c.length-1),t+=`
`}if(this.K()&&this.L(t))if(this.I(i,r-1)===13){const p=this.s(i,r-1);this.C(i,p),t="\r"+t,i.piece.length===0&&h.push(i)}else this.C(i,a);else this.C(i,a);const u=this.w(t);c.length>0&&this.S(i,c);let f=i;for(let b=0;b<u.length;b++)f=this.S(f,u[b]);this.v(h)}else this.q(t,i)}else{const i=this.w(t);let r=this.T(null,i[0]);for(let s=1;s<i.length;s++)r=this.S(r,i[s])}this.y()}delete(e,t){if(this.j.lineNumber=0,this.j.value="",t<=0||this.root===$b1)return;const n=this.G(e),i=this.G(e+t),r=n.node,s=i.node;if(r===s){const u=this.s(r,n.remainder),f=this.s(r,i.remainder);if(n.nodeStartOffset===e){if(t===r.piece.length){const b=r.next();$g1(this,r),this.N(b),this.y();return}this.D(r,f),this.h.validate(e),this.N(r),this.y();return}if(n.nodeStartOffset+r.piece.length===e+t){this.C(r,u),this.O(r),this.y();return}this.E(r,u,f),this.y();return}const l=[],o=this.s(r,n.remainder);this.C(r,o),this.h.validate(e),r.piece.length===0&&l.push(r);const a=this.s(s,i.remainder);this.D(s,a),s.piece.length===0&&l.push(s);const h=r.next();for(let u=h;u!==$b1&&u!==s;u=u.next())l.push(u);const c=r.piece.length===0?r.prev():r;this.v(l),this.O(c),this.y()}o(e,t){const n=[];if(this.K()&&this.M(e)&&this.L(t)){const s=t.piece,l={line:s.start.line+1,column:0},o=new $s1(s.bufferIndex,l,s.end,this.t(s.bufferIndex,l,s.end),s.length-1);t.piece=o,e+=`
`,$i1(this,t,-1,-1),t.piece.length===0&&n.push(t)}const i=this.w(e);let r=this.T(t,i[i.length-1]);for(let s=i.length-2;s>=0;s--)r=this.T(r,i[s]);this.N(r),this.v(n)}q(e,t){this.Q(e,t)&&(e+=`
`);const n=this.w(e),i=this.S(t,n[0]);let r=i;for(let s=1;s<n.length;s++)r=this.S(r,n[s]);this.N(i)}s(e,t,n){const i=e.piece,r=e.piece.bufferIndex,s=this.a[r].lineStarts,o=s[i.start.line]+i.start.column+t;let a=i.start.line,h=i.end.line,c=0,u=0,f=0;for(;a<=h&&(c=a+(h-a)/2|0,f=s[c],c!==h);)if(u=s[c+1],o<f)h=c-1;else if(o>=u)a=c+1;else break;return n?(n.line=c,n.column=o-f,null):{line:c,column:o-f}}t(e,t,n){if(n.column===0)return n.line-t.line;const i=this.a[e].lineStarts;if(n.line===i.length-1)return n.line-t.line;const r=i[n.line+1],s=i[n.line]+n.column;if(r>s+1)return n.line-t.line;const l=s-1;return this.a[e].buffer.charCodeAt(l)===13?n.line-t.line+1:n.line-t.line}u(e,t){return this.a[e].lineStarts[t.line]+t.column}v(e){for(let t=0;t<e.length;t++)$g1(this,e[t])}w(e){if(e.length>AverageBufferSize){const h=[];for(;e.length>AverageBufferSize;){const u=e.charCodeAt(AverageBufferSize-1);let f;u===13||u>=55296&&u<=56319?(f=e.substring(0,AverageBufferSize-1),e=e.substring(AverageBufferSize-1)):(f=e.substring(0,AverageBufferSize),e=e.substring(AverageBufferSize));const b=$q1(f);h.push(new $s1(this.a.length,{line:0,column:0},{line:b.length-1,column:f.length-b[b.length-1]},b.length-1,f.length)),this.a.push(new $t1(f,b))}const c=$q1(e);return h.push(new $s1(this.a.length,{line:0,column:0},{line:c.length-1,column:e.length-c[c.length-1]},c.length-1,e.length)),this.a.push(new $t1(e,c)),h}let t=this.a[0].buffer.length;const n=$q1(e,!1);let i=this.g;if(this.a[0].lineStarts[this.a[0].lineStarts.length-1]===t&&t!==0&&this.L(e)&&this.M(this.a[0].buffer)){this.g={line:this.g.line,column:this.g.column+1},i=this.g;for(let h=0;h<n.length;h++)n[h]+=t+1;this.a[0].lineStarts=this.a[0].lineStarts.concat(n.slice(1)),this.a[0].buffer+="_"+e,t+=1}else{if(t!==0)for(let h=0;h<n.length;h++)n[h]+=t;this.a[0].lineStarts=this.a[0].lineStarts.concat(n.slice(1)),this.a[0].buffer+=e}const r=this.a[0].buffer.length,s=this.a[0].lineStarts.length-1,l=r-this.a[0].lineStarts[s],o={line:s,column:l},a=new $s1(0,i,o,this.t(0,i,o),r-t);return this.g=o,[a]}getLinesRawContent(){return this.U(this.root)}getLineRawContent(e,t=0){let n=this.root,i="";const r=this.h.get2(e);if(r){n=r.node;const s=this.B(n,e-r.nodeStartLineNumber-1),l=this.a[n.piece.bufferIndex].buffer,o=this.u(n.piece.bufferIndex,n.piece.start);if(r.nodeStartLineNumber+n.piece.lineFeedCnt===e)i=l.substring(o+s,o+n.piece.length);else{const a=this.B(n,e-r.nodeStartLineNumber);return l.substring(o+s,o+a-t)}}else{let s=0;const l=e;for(;n!==$b1;)if(n.left!==$b1&&n.lf_left>=e-1)n=n.left;else if(n.lf_left+n.piece.lineFeedCnt>e-1){const o=this.B(n,e-n.lf_left-2),a=this.B(n,e-n.lf_left-1),h=this.a[n.piece.bufferIndex].buffer,c=this.u(n.piece.bufferIndex,n.piece.start);return s+=n.size_left,this.h.set({node:n,nodeStartOffset:s,nodeStartLineNumber:l-(e-1-n.lf_left)}),h.substring(c+o,c+a-t)}else if(n.lf_left+n.piece.lineFeedCnt===e-1){const o=this.B(n,e-n.lf_left-2),a=this.a[n.piece.bufferIndex].buffer,h=this.u(n.piece.bufferIndex,n.piece.start);i=a.substring(h+o,h+n.piece.length);break}else e-=n.lf_left+n.piece.lineFeedCnt,s+=n.size_left+n.piece.length,n=n.right}for(n=n.next();n!==$b1;){const s=this.a[n.piece.bufferIndex].buffer;if(n.piece.lineFeedCnt>0){const l=this.B(n,0),o=this.u(n.piece.bufferIndex,n.piece.start);return i+=s.substring(o,o+l-t),i}else{const l=this.u(n.piece.bufferIndex,n.piece.start);i+=s.substr(l,n.piece.length)}n=n.next()}return i}y(){let e=this.root,t=1,n=0;for(;e!==$b1;)t+=e.lf_left+e.piece.lineFeedCnt,n+=e.size_left+e.piece.length,e=e.right;this.b=t,this.c=n,this.h.validate(this.c)}A(e,t){const n=e.piece,i=this.s(e,t),r=i.line-n.start.line;if(this.u(n.bufferIndex,n.end)-this.u(n.bufferIndex,n.start)===t){const s=this.t(e.piece.bufferIndex,n.start,i);if(s!==r)return{index:s,remainder:0}}return{index:r,remainder:i.column}}B(e,t){if(t<0)return 0;const n=e.piece,i=this.a[n.bufferIndex].lineStarts,r=n.start.line+t+1;return r>n.end.line?i[n.end.line]+n.end.column-i[n.start.line]-n.start.column:i[r]-i[n.start.line]-n.start.column}C(e,t){const n=e.piece,i=n.lineFeedCnt,r=this.u(n.bufferIndex,n.end),s=t,l=this.u(n.bufferIndex,s),o=this.t(n.bufferIndex,n.start,s),a=o-i,h=l-r,c=n.length+h;e.piece=new $s1(n.bufferIndex,n.start,s,o,c),$i1(this,e,h,a)}D(e,t){const n=e.piece,i=n.lineFeedCnt,r=this.u(n.bufferIndex,n.start),s=t,l=this.t(n.bufferIndex,s,n.end),o=this.u(n.bufferIndex,s),a=l-i,h=r-o,c=n.length+h;e.piece=new $s1(n.bufferIndex,s,n.end,l,c),$i1(this,e,h,a)}E(e,t,n){const i=e.piece,r=i.start,s=i.end,l=i.length,o=i.lineFeedCnt,a=t,h=this.t(i.bufferIndex,i.start,a),c=this.u(i.bufferIndex,t)-this.u(i.bufferIndex,r);e.piece=new $s1(i.bufferIndex,i.start,a,h,c),$i1(this,e,c-l,h-o);const u=new $s1(i.bufferIndex,n,s,this.t(i.bufferIndex,n,s),this.u(i.bufferIndex,s)-this.u(i.bufferIndex,n)),f=this.S(e,u);this.N(f)}F(e,t){this.Q(t,e)&&(t+=`
`);const n=this.K()&&this.L(t)&&this.M(e),i=this.a[0].buffer.length;this.a[0].buffer+=t;const r=$q1(t,!1);for(let f=0;f<r.length;f++)r[f]+=i;if(n){const f=this.a[0].lineStarts[this.a[0].lineStarts.length-2];this.a[0].lineStarts.pop(),this.g={line:this.g.line-1,column:i-f}}this.a[0].lineStarts=this.a[0].lineStarts.concat(r.slice(1));const s=this.a[0].lineStarts.length-1,l=this.a[0].buffer.length-this.a[0].lineStarts[s],o={line:s,column:l},a=e.piece.length+t.length,h=e.piece.lineFeedCnt,c=this.t(0,e.piece.start,o),u=c-h;e.piece=new $s1(e.piece.bufferIndex,e.piece.start,o,c,a),this.g=o,$i1(this,e,t.length,u)}G(e){let t=this.root;const n=this.h.get(e);if(n)return{node:n.node,nodeStartOffset:n.nodeStartOffset,remainder:e-n.nodeStartOffset};let i=0;for(;t!==$b1;)if(t.size_left>e)t=t.left;else if(t.size_left+t.piece.length>=e){i+=t.size_left;const r={node:t,remainder:e-t.size_left,nodeStartOffset:i};return this.h.set(r),r}else e-=t.size_left+t.piece.length,i+=t.size_left+t.piece.length,t=t.right;return null}H(e,t){let n=this.root,i=0;for(;n!==$b1;)if(n.left!==$b1&&n.lf_left>=e-1)n=n.left;else if(n.lf_left+n.piece.lineFeedCnt>e-1){const r=this.B(n,e-n.lf_left-2),s=this.B(n,e-n.lf_left-1);return i+=n.size_left,{node:n,remainder:Math.min(r+t-1,s),nodeStartOffset:i}}else if(n.lf_left+n.piece.lineFeedCnt===e-1){const r=this.B(n,e-n.lf_left-2);if(r+t-1<=n.piece.length)return{node:n,remainder:r+t-1,nodeStartOffset:i};t-=n.piece.length-r;break}else e-=n.lf_left+n.piece.lineFeedCnt,i+=n.size_left+n.piece.length,n=n.right;for(n=n.next();n!==$b1;){if(n.piece.lineFeedCnt>0){const r=this.B(n,0),s=this.J(n);return{node:n,remainder:Math.min(t-1,r),nodeStartOffset:s}}else if(n.piece.length>=t-1){const r=this.J(n);return{node:n,remainder:t-1,nodeStartOffset:r}}else t-=n.piece.length;n=n.next()}return null}I(e,t){if(e.piece.lineFeedCnt<1)return-1;const n=this.a[e.piece.bufferIndex],i=this.u(e.piece.bufferIndex,e.piece.start)+t;return n.buffer.charCodeAt(i)}J(e){if(!e)return 0;let t=e.size_left;for(;e!==this.root;)e.parent.right===e&&(t+=e.parent.size_left+e.parent.piece.length),e=e.parent;return t}K(){return!(this.f&&this.d===`
`)}L(e){if(typeof e=="string")return e.charCodeAt(0)===10;if(e===$b1||e.piece.lineFeedCnt===0)return!1;const t=e.piece,n=this.a[t.bufferIndex].lineStarts,i=t.start.line,r=n[i]+t.start.column;return i===n.length-1||n[i+1]>r+1?!1:this.a[t.bufferIndex].buffer.charCodeAt(r)===10}M(e){return typeof e=="string"?e.charCodeAt(e.length-1)===13:e===$b1||e.piece.lineFeedCnt===0?!1:this.I(e,e.piece.length-1)===13}N(e){if(this.K()&&this.L(e)){const t=e.prev();this.M(t)&&this.P(t,e)}}O(e){if(this.K()&&this.M(e)){const t=e.next();this.L(t)&&this.P(e,t)}}P(e,t){const n=[],i=this.a[e.piece.bufferIndex].lineStarts;let r;e.piece.end.column===0?r={line:e.piece.end.line-1,column:i[e.piece.end.line]-i[e.piece.end.line-1]-1}:r={line:e.piece.end.line,column:e.piece.end.column-1};const s=e.piece.length-1,l=e.piece.lineFeedCnt-1;e.piece=new $s1(e.piece.bufferIndex,e.piece.start,r,l,s),$i1(this,e,-1,-1),e.piece.length===0&&n.push(e);const o={line:t.piece.start.line+1,column:0},a=t.piece.length-1,h=this.t(t.piece.bufferIndex,o,t.piece.end);t.piece=new $s1(t.piece.bufferIndex,o,t.piece.end,h,a),$i1(this,t,-1,-1),t.piece.length===0&&n.push(t);const c=this.w(`\r
`);this.S(e,c[0]);for(let u=0;u<n.length;u++)$g1(this,n[u])}Q(e,t){if(this.K()&&this.M(e)){const n=t.next();if(this.L(n)){if(e+=`
`,n.piece.length===1)$g1(this,n);else{const i=n.piece,r={line:i.start.line+1,column:0},s=i.length-1,l=this.t(i.bufferIndex,r,i.end);n.piece=new $s1(i.bufferIndex,r,i.end,l,s),$i1(this,n,-1,-1)}return!0}}return!1}iterate(e,t){if(e===$b1)return t($b1);const n=this.iterate(e.left,t);return n&&t(e)&&this.iterate(e.right,t)}R(e){if(e===$b1)return"";const t=this.a[e.piece.bufferIndex],n=e.piece,i=this.u(n.bufferIndex,n.start),r=this.u(n.bufferIndex,n.end);return t.buffer.substring(i,r)}getPieceContent(e){const t=this.a[e.bufferIndex],n=this.u(e.bufferIndex,e.start),i=this.u(e.bufferIndex,e.end);return t.buffer.substring(n,i)}S(e,t){const n=new $a1(t,1);if(n.left=$b1,n.right=$b1,n.parent=$b1,n.size_left=0,n.lf_left=0,this.root===$b1)this.root=n,n.color=0;else if(e.right===$b1)e.right=n,n.parent=e;else{const r=$c1(e.right);r.left=n,n.parent=r}return $h1(this,n),n}T(e,t){const n=new $a1(t,1);if(n.left=$b1,n.right=$b1,n.parent=$b1,n.size_left=0,n.lf_left=0,this.root===$b1)this.root=n,n.color=0;else if(e.left===$b1)e.left=n,n.parent=e;else{const i=$d1(e.left);i.right=n,n.parent=i}return $h1(this,n),n}U(e){let t="";return this.iterate(e,n=>(t+=this.R(n),!0)),t}},StringEOL;(function(e){e[e.Unknown=0]="Unknown",e[e.Invalid=3]="Invalid",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(StringEOL||(StringEOL={}));function $kW(e){let t=0,n=0,i=0,r=0;for(let s=0,l=e.length;s<l;s++){const o=e.charCodeAt(s);o===13?(t===0&&(n=s),t++,s+1<l&&e.charCodeAt(s+1)===10?(r|=2,s++):r|=3,i=s+1):o===10&&(r|=1,t===0&&(n=s),t++,i=s+1)}return t===0&&(n=e.length),[t,n,e.length-i,r]}var _utf16LE_TextDecoder;function getUTF16LE_TextDecoder(){return _utf16LE_TextDecoder||(_utf16LE_TextDecoder=new TextDecoder("UTF-16LE")),_utf16LE_TextDecoder}function $5V(e,t,n){const i=new Uint16Array(e.buffer,t,n);return n>0&&(i[0]===65279||i[0]===65534)?compatDecodeUTF16LE(e,t,n):getUTF16LE_TextDecoder().decode(i)}function compatDecodeUTF16LE(e,t,n){const i=[];let r=0;for(let s=0;s<n;s++){const l=$Yi(e,t);t+=2,i[r++]=String.fromCharCode(l)}return i.join("")}function escapeNewLine(e){return e.replace(/\n/g,"\\n").replace(/\r/g,"\\r")}var $7V=class j{get oldLength(){return this.oldText.length}get oldEnd(){return this.oldPosition+this.oldText.length}get newLength(){return this.newText.length}get newEnd(){return this.newPosition+this.newText.length}constructor(t,n,i,r){this.oldPosition=t,this.oldText=n,this.newPosition=i,this.newText=r}toString(){return this.oldText.length===0?`(insert@${this.oldPosition} "${escapeNewLine(this.newText)}")`:this.newText.length===0?`(delete@${this.oldPosition} "${escapeNewLine(this.oldText)}")`:`(replace@${this.oldPosition} "${escapeNewLine(this.oldText)}" with "${escapeNewLine(this.newText)}")`}static a(t){return 4+2*t.length}static c(t,n,i){const r=n.length;$2i(t,r,i),i+=4;for(let s=0;s<r;s++)$Zi(t,n.charCodeAt(s),i),i+=2;return i}static d(t,n){const i=$1i(t,n);return n+=4,$5V(t,n,i)}writeSize(){return 8+j.a(this.oldText)+j.a(this.newText)}write(t,n){return $2i(t,this.oldPosition,n),n+=4,$2i(t,this.newPosition,n),n+=4,n=j.c(t,this.oldText,n),n=j.c(t,this.newText,n),n}static read(t,n,i){const r=$1i(t,n);n+=4;const s=$1i(t,n);n+=4;const l=j.d(t,n);n+=j.a(l);const o=j.d(t,n);return n+=j.a(o),i.push(new j(r,l,s,o)),n}},$v1=class Z extends $sd{constructor(t,n,i,r,s,l,o){super(),this.m=this.B(new $0e),this.onDidChangeContent=this.m.event,this.f=n,this.j=!l,this.g=r,this.h=s,this.c=new $u1(t,i,o)}equals(t){return!(t instanceof Z)||this.f!==t.f||this.getEOL()!==t.getEOL()?!1:this.c.equal(t.c)}mightContainRTL(){return this.g}mightContainUnusualLineTerminators(){return this.h}resetMightContainUnusualLineTerminators(){this.h=!1}mightContainNonBasicASCII(){return this.j}getBOM(){return this.f}getEOL(){return this.c.getEOL()}createSnapshot(t){return this.c.createSnapshot(t?this.f:"")}getOffsetAt(t,n){return this.c.getOffsetAt(t,n)}getPositionAt(t){return this.c.getPositionAt(t)}getRangeAt(t,n){const i=t+n,r=this.getPositionAt(t),s=this.getPositionAt(i);return new $mV(r.lineNumber,r.column,s.lineNumber,s.column)}getValueInRange(t,n=0){if(t.isEmpty())return"";const i=this.n(n);return this.c.getValueInRange(t,i)}getValueLengthInRange(t,n=0){if(t.isEmpty())return 0;if(t.startLineNumber===t.endLineNumber)return t.endColumn-t.startColumn;const i=this.getOffsetAt(t.startLineNumber,t.startColumn),r=this.getOffsetAt(t.endLineNumber,t.endColumn);let s=0;const l=this.n(n),o=this.getEOL();if(l.length!==o.length){const a=l.length-o.length,h=t.endLineNumber-t.startLineNumber;s=a*h}return r-i+s}getCharacterCountInRange(t,n=0){if(this.j){let i=0;const r=t.startLineNumber,s=t.endLineNumber;for(let l=r;l<=s;l++){const o=this.getLineContent(l),a=l===r?t.startColumn-1:0,h=l===s?t.endColumn-1:o.length;for(let c=a;c<h;c++)$Cg(o.charCodeAt(c))?(i=i+1,c=c+1):i=i+1}return i+=this.n(n).length*(s-r),i}return this.getValueLengthInRange(t,n)}getNearestChunk(t){return this.c.getNearestChunk(t)}getLength(){return this.c.getLength()}getLineCount(){return this.c.getLineCount()}getLinesContent(){return this.c.getLinesContent()}getLineContent(t){return this.c.getLineContent(t)}getLineCharCode(t,n){return this.c.getLineCharCode(t,n)}getCharCode(t){return this.c.getCharCode(t)}getLineLength(t){return this.c.getLineLength(t)}getLineMinColumn(t){return 1}getLineMaxColumn(t){return this.getLineLength(t)+1}getLineFirstNonWhitespaceColumn(t){const n=$mg(this.getLineContent(t));return n===-1?0:n+1}getLineLastNonWhitespaceColumn(t){const n=$og(this.getLineContent(t));return n===-1?0:n+2}n(t){switch(t){case 1:return`
`;case 2:return`\r
`;case 0:return this.getEOL();default:throw new Error("Unknown EOL preference")}}setEOL(t){this.c.setEOL(t)}applyEdits(t,n,i){let r=this.g,s=this.h,l=this.j,o=!0,a=[];for(let m=0;m<t.length;m++){const d=t[m];o&&d._isTracked&&(o=!1);const C=d.range;if(d.text){let x=!0;l||(x=!$Ng(d.text),l=x),!r&&x&&(r=$Mg(d.text)),!s&&x&&(s=$Pg(d.text))}let g="",w=0,M=0,v=0;if(d.text){let x;[w,M,v,x]=$kW(d.text);const E=this.getEOL();x===0||x===(E===`\r
`?2:1)?g=d.text:g=d.text.replace(/\r\n|\r|\n/g,E)}a[m]={sortIndex:m,identifier:d.identifier||null,range:C,rangeOffset:this.getOffsetAt(C.startLineNumber,C.startColumn),rangeLength:this.getValueLengthInRange(C),text:g,eolCount:w,firstLineLength:M,lastLineLength:v,forceMoveMarkers:!!d.forceMoveMarkers,isAutoWhitespaceEdit:d.isAutoWhitespaceEdit||!1}}a.sort(Z.u);let h=!1;for(let m=0,d=a.length-1;m<d;m++){const C=a[m].range.getEndPosition(),g=a[m+1].range.getStartPosition();if(g.isBeforeOrEqual(C)){if(g.isBefore(C))throw new Error("Overlapping ranges are not allowed!");h=!0}}o&&(a=this.s(a));const c=i||n?Z._getInverseEditRanges(a):[],u=[];if(n)for(let m=0;m<a.length;m++){const d=a[m],C=c[m];if(d.isAutoWhitespaceEdit&&d.range.isEmpty())for(let g=C.startLineNumber;g<=C.endLineNumber;g++){let w="";g===C.startLineNumber&&(w=this.getLineContent(d.range.startLineNumber),$mg(w)!==-1)||u.push({lineNumber:g,oldContent:w})}}let f=null;if(i){let m=0;f=[];for(let d=0;d<a.length;d++){const C=a[d],g=c[d],w=this.getValueInRange(C.range),M=C.rangeOffset+m;m+=C.text.length-w.length,f[d]={sortIndex:C.sortIndex,identifier:C.identifier,range:g,text:w,textChange:new $7V(C.rangeOffset,w,M,C.text)}}h||f.sort((d,C)=>d.sortIndex-C.sortIndex)}this.g=r,this.h=s,this.j=l;const b=this.t(a);let p=null;if(n&&u.length>0){u.sort((m,d)=>d.lineNumber-m.lineNumber),p=[];for(let m=0,d=u.length;m<d;m++){const C=u[m].lineNumber;if(m>0&&u[m-1].lineNumber===C)continue;const g=u[m].oldContent,w=this.getLineContent(C);w.length===0||w===g||$mg(w)!==-1||p.push(C)}}return this.m.fire(),new $oY(f,b,p)}s(t){return t.length<1e3?t:[this._toSingleEditOperation(t)]}_toSingleEditOperation(t){let n=!1;const i=t[0].range,r=t[t.length-1].range,s=new $mV(i.startLineNumber,i.startColumn,r.endLineNumber,r.endColumn);let l=i.startLineNumber,o=i.startColumn;const a=[];for(let b=0,p=t.length;b<p;b++){const m=t[b],d=m.range;n=n||m.forceMoveMarkers,a.push(this.getValueInRange(new $mV(l,o,d.startLineNumber,d.startColumn))),m.text.length>0&&a.push(m.text),l=d.endLineNumber,o=d.endColumn}const h=a.join(""),[c,u,f]=$kW(h);return{sortIndex:0,identifier:t[0].identifier,range:s,rangeOffset:this.getOffsetAt(s.startLineNumber,s.startColumn),rangeLength:this.getValueLengthInRange(s,0),text:h,eolCount:c,firstLineLength:u,lastLineLength:f,forceMoveMarkers:n,isAutoWhitespaceEdit:!1}}t(t){t.sort(Z.w);const n=[];for(let i=0;i<t.length;i++){const r=t[i],s=r.range.startLineNumber,l=r.range.startColumn,o=r.range.endLineNumber,a=r.range.endColumn;if(s===o&&l===a&&r.text.length===0)continue;r.text?(this.c.delete(r.rangeOffset,r.rangeLength),this.c.insert(r.rangeOffset,r.text,!0)):this.c.delete(r.rangeOffset,r.rangeLength);const h=new $mV(s,l,o,a);n.push({range:h,rangeLength:r.rangeLength,text:r.text,rangeOffset:r.rangeOffset,forceMoveMarkers:r.forceMoveMarkers})}return n}findMatchesLineByLine(t,n,i,r){return this.c.findMatchesLineByLine(t,n,i,r)}getPieceTree(){return this.c}static _getInverseEditRange(t,n){const i=t.startLineNumber,r=t.startColumn,[s,l,o]=$kW(n);let a;if(n.length>0){const h=s+1;h===1?a=new $mV(i,r,i,r+l):a=new $mV(i,r,i+h-1,o+1)}else a=new $mV(i,r,i,r);return a}static _getInverseEditRanges(t){const n=[];let i=0,r=0,s=null;for(let l=0,o=t.length;l<o;l++){const a=t[l];let h,c;s?s.range.endLineNumber===a.range.startLineNumber?(h=i,c=r+(a.range.startColumn-s.range.endColumn)):(h=i+(a.range.startLineNumber-s.range.endLineNumber),c=a.range.startColumn):(h=a.range.startLineNumber,c=a.range.startColumn);let u;if(a.text.length>0){const f=a.eolCount+1;f===1?u=new $mV(h,c,h,c+a.firstLineLength):u=new $mV(h,c,h+f-1,a.lastLineLength+1)}else u=new $mV(h,c,h,c);i=u.endLineNumber,r=u.endColumn,n.push(u),s=a}return n}static u(t,n){const i=$mV.compareRangesUsingEnds(t.range,n.range);return i===0?t.sortIndex-n.sortIndex:i}static w(t,n){const i=$mV.compareRangesUsingEnds(t.range,n.range);return i===0?n.sortIndex-t.sortIndex:-i}},PieceTreeTextBufferFactory=class{constructor(e,t,n,i,r,s,l,o,a){this.a=e,this.b=t,this.c=n,this.d=i,this.e=r,this.f=s,this.g=l,this.h=o,this.j=a}k(e){const t=this.c+this.d+this.e,n=this.c+this.e;return t===0?e===1?`
`:`\r
`:n>t/2?`\r
`:`
`}create(e){const t=this.k(e),n=this.a;if(this.j&&(t===`\r
`&&(this.c>0||this.d>0)||t===`
`&&(this.c>0||this.e>0)))for(let r=0,s=n.length;r<s;r++){const l=n[r].buffer.replace(/\r\n|\r|\n/g,t),o=$q1(l);n[r]=new $t1(l,o)}const i=new $v1(n,this.b,t,this.f,this.g,this.h,this.j);return{textBuffer:i,disposable:i}}getFirstLineText(e){return this.a[0].buffer.substr(0,e).split(/\r\n|\r|\n/)[0]}},$w1=class{constructor(){this.a=[],this.b="",this.c=!1,this.d=0,this.e=[],this.f=0,this.g=0,this.h=0,this.j=!1,this.k=!1,this.l=!0}acceptChunk(e){if(e.length===0)return;this.a.length===0&&$Yg(e)&&(this.b=$Xg,e=e.substr(1));const t=e.charCodeAt(e.length-1);t===13||t>=55296&&t<=56319?(this.m(e.substr(0,e.length-1),!1),this.c=!0,this.d=t):(this.m(e,!1),this.c=!1,this.d=t)}m(e,t){!t&&e.length===0||(this.c?this.n(String.fromCharCode(this.d)+e):this.n(e))}n(e){const t=$r1(this.e,e);this.a.push(new $t1(e,t.lineStarts)),this.f+=t.cr,this.g+=t.lf,this.h+=t.crlf,t.isBasicASCII||(this.l=!1,this.j||(this.j=$Mg(e)),this.k||(this.k=$Pg(e)))}finish(e=!0){return this.o(),new PieceTreeTextBufferFactory(this.a,this.b,this.f,this.g,this.h,this.j,this.k,this.l,e)}o(){if(this.a.length===0&&this.m("",!0),this.c){this.c=!1;const e=this.a[this.a.length-1];e.buffer+=String.fromCharCode(this.d);const t=$q1(e.buffer);e.lineStarts=t,this.d===13&&this.f++}}};function $$g(e){return e===47||e===92}function $_g(e){return e.replace(/[\\/]/g,$lc.sep)}function $ah(e){return e.indexOf("/")===-1&&(e=$_g(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function $bh(e,t=$lc.sep){if(!e)return"";const n=e.length,i=e.charCodeAt(0);if($$g(i)){if($$g(e.charCodeAt(1))&&!$$g(e.charCodeAt(2))){let s=3;const l=s;for(;s<n&&!$$g(e.charCodeAt(s));s++);if(l!==s&&!$$g(e.charCodeAt(s+1))){for(s+=1;s<n;s++)if($$g(e.charCodeAt(s)))return e.slice(0,s+1).replace(/[\\/]/g,t)}}return t}else if($gh(i)&&e.charCodeAt(1)===58)return $$g(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let r=e.indexOf("://");if(r!==-1){for(r+=3;r<n;r++)if($$g(e.charCodeAt(r)))return e.slice(0,r+1)}return""}function $fh(e,t,n,i=sep){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(n){if(!$zg(e,t))return!1;if(t.length===e.length)return!0;let s=t.length;return t.charAt(t.length-1)===i&&s--,e.charAt(s)===i}return t.charAt(t.length-1)!==i&&(t+=i),e.indexOf(t)===0}function $gh(e){return e>=65&&e<=90||e>=97&&e<=122}var Schemas;(function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatSesssion="vscode-chat-editor",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"})(Schemas||(Schemas={}));var $sh="tkn",RemoteAuthoritiesImpl=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=$lc.join(t??"/",$uh(e))}getServerRootPath(){return this.f}get g(){return $lc.join(this.f,Schemas.vscodeRemoteResource)}set(e,t,n){this.a[e]=t,this.b[e]=n}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(l){return $gb(l),e}const t=e.authority;let n=this.a[t];n&&n.indexOf(":")!==-1&&n.indexOf("[")===-1&&(n=`[${n}]`);const i=this.b[t],r=this.c[t];let s=`path=${encodeURIComponent(e.path)}`;return typeof r=="string"&&(s+=`&${$sh}=${encodeURIComponent(r)}`),URI.from({scheme:$r?this.d:Schemas.vscodeRemoteResource,authority:`${n}:${i}`,path:this.g,query:s})}},$th=new RemoteAuthoritiesImpl;function $uh(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var $zh="vscode-app",FileAccessImpl=class lt{static{this.a=$zh}asBrowserUri(t){const n=this.b(t);return this.uriToBrowserUri(n)}uriToBrowserUri(t){return t.scheme===Schemas.vscodeRemote?$th.rewrite(t):t.scheme===Schemas.file&&($p||$t===`${Schemas.vscodeFileResource}://${lt.a}`)?t.with({scheme:Schemas.vscodeFileResource,authority:t.authority||lt.a,query:null,fragment:null}):t}asFileUri(t){const n=this.b(t);return this.uriToFileUri(n)}uriToFileUri(t){return t.scheme===Schemas.vscodeFileResource?t.with({scheme:Schemas.file,authority:t.authority!==lt.a?t.authority:null,query:null,fragment:null}):t}b(t){if(URI.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const n=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(n))return URI.joinPath(URI.parse(n,!0),t);const i=$oc(n,t);return URI.file(i)}throw new Error("Cannot determine URI for module id!")}},$Ah=new FileAccessImpl,$Bh=Object.freeze({"Cache-Control":"no-cache, no-store"}),$Ch=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),COI;(function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const n="vscode-coi";function i(s){let l;typeof s=="string"?l=new URL(s).searchParams:s instanceof URL?l=s.searchParams:URI.isUri(s)&&(l=new URL(s.toString(!0)).searchParams);const o=l?.get(n);if(o)return t.get(o)}e.getHeadersFromQuery=i;function r(s,l,o){if(!globalThis.crossOriginIsolated)return;const a=l&&o?"3":o?"2":"1";s instanceof URLSearchParams?s.set(n,a):s[n]=a}e.addSearchParam=r})(COI||(COI={}));function $Dh(e){return $Bc(e,!0)}var $Eh=class{constructor(e){this.a=e}compare(e,t,n=!1){return e===t?0:$rg(this.getComparisonKey(e,n),this.getComparisonKey(t,n))}isEqual(e,t,n=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,n)===this.getComparisonKey(t,n)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,n=!1){if(e.scheme===t.scheme){if(e.scheme===Schemas.file)return $fh($Dh(e),$Dh(t),this.a(e))&&e.query===t.query&&(n||e.fragment===t.fragment);if($Uh(e.authority,t.authority))return $fh(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(n||e.fragment===t.fragment)}return!1}joinPath(e,...t){return URI.joinPath(e,...t)}basenameOrAuthority(e){return $Mh(e)||e.authority}basename(e){return $lc.basename(e.path)}extname(e){return $lc.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===Schemas.file?t=URI.file($rc($Dh(e))).path:(t=$lc.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===Schemas.file?t=URI.file($mc($Dh(e))).path:t=$lc.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!$Uh(e.authority,t.authority))return;if(e.scheme===Schemas.file){const r=$qc($Dh(e),$Dh(t));return $l?$_g(r):r}let n=e.path||"/";const i=t.path||"/";if(this.a(e)){let r=0;for(const s=Math.min(n.length,i.length);r<s&&!(n.charCodeAt(r)!==i.charCodeAt(r)&&n.charAt(r).toLowerCase()!==i.charAt(r).toLowerCase());r++);n=i.substr(0,r)+n.substr(r)}return $lc.relative(n,i)}resolvePath(e,t){if(e.scheme===Schemas.file){const n=URI.file($pc($Dh(e),t));return e.with({authority:n.authority,path:n.path})}return t=$ah(t),e.with({path:$lc.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&$yg(e,t)}hasTrailingPathSeparator(e,t=sep){if(e.scheme===Schemas.file){const n=$Dh(e);return n.length>$bh(n).length&&n[n.length-1]===t}else{const n=e.path;return n.length>1&&n.charCodeAt(n.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=sep){return $Vh(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=sep){let n=!1;if(e.scheme===Schemas.file){const i=$Dh(e);n=i!==void 0&&i.length===$bh(i).length&&i[i.length-1]===t}else{t="/";const i=e.path;n=i.length===1&&i.charCodeAt(i.length-1)===47}return!n&&!$Vh(e,t)?e.with({path:e.path+"/"}):e}},$Fh=new $Eh(()=>!1),$Gh=new $Eh(e=>e.scheme===Schemas.file?!$n:!0),$Hh=new $Eh(e=>!0),$Ih=$Fh.isEqual.bind($Fh),$Jh=$Fh.isEqualOrParent.bind($Fh),$Kh=$Fh.getComparisonKey.bind($Fh),$Lh=$Fh.basenameOrAuthority.bind($Fh),$Mh=$Fh.basename.bind($Fh),$Nh=$Fh.extname.bind($Fh),$Oh=$Fh.dirname.bind($Fh),$Ph=$Fh.joinPath.bind($Fh),$Qh=$Fh.normalizePath.bind($Fh),$Rh=$Fh.relativePath.bind($Fh),$Sh=$Fh.resolvePath.bind($Fh),$Th=$Fh.isAbsolutePath.bind($Fh),$Uh=$Fh.isEqualAuthority.bind($Fh),$Vh=$Fh.hasTrailingPathSeparator.bind($Fh),$Wh=$Fh.removeTrailingPathSeparator.bind($Fh),$Xh=$Fh.addTrailingPathSeparator.bind($Fh),DataUri;(function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(n){const i=new Map;n.path.substring(n.path.indexOf(";")+1,n.path.lastIndexOf(";")).split(";").forEach(l=>{const[o,a]=l.split(":");o&&a&&i.set(o,a)});const s=n.path.substring(0,n.path.indexOf(";"));return s&&i.set(e.META_DATA_MIME,s),i}e.parseMetaData=t})(DataUri||(DataUri={}));var $5e=Symbol("MicrotaskDelay"),$ti,$ui;(function(){typeof globalThis.requestIdleCallback!="function"||typeof globalThis.cancelIdleCallback!="function"?$ui=(e,t,n)=>{$E(()=>{if(i)return;const r=Date.now()+15;t(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,r-Date.now())}}))});let i=!1;return{dispose(){i||(i=!0)}}}:$ui=(e,t,n)=>{const i=e.requestIdleCallback(t,typeof n=="number"?{timeout:n}:void 0);let r=!1;return{dispose(){r||(r=!0,e.cancelIdleCallback(i))}}},$ti=(e,t)=>$ui(globalThis,e,t)})();var DeferredOutcome;(function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"})(DeferredOutcome||(DeferredOutcome={}));var Promises;(function(e){async function t(i){let r;const s=await Promise.all(i.map(l=>l.then(o=>o,o=>{r||(r=o)})));if(typeof r<"u")throw r;return s}e.settled=t;function n(i){return new Promise(async(r,s)=>{try{await i(r,s)}catch(l){s(l)}})}e.withAsyncBody=n})(Promises||(Promises={}));var AsyncIterableSourceState;(function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"})(AsyncIterableSourceState||(AsyncIterableSourceState={}));var $Di=class U{static fromArray(t){return new U(n=>{n.emitMany(t)})}static fromPromise(t){return new U(async n=>{n.emitMany(await t)})}static fromPromisesResolveOrder(t){return new U(async n=>{await Promise.all(t.map(async i=>n.emitOne(await i)))})}static merge(t){return new U(async n=>{await Promise.all(t.map(async i=>{for await(const r of i)n.emitOne(r)}))})}static{this.EMPTY=U.fromArray([])}constructor(t,n){this.a=0,this.b=[],this.d=null,this.f=n,this.g=new $0e,queueMicrotask(async()=>{const i={emitOne:r=>this.h(r),emitMany:r=>this.j(r),reject:r=>this.l(r)};try{await Promise.resolve(t(i)),this.k()}catch(r){this.l(r)}finally{i.emitOne=void 0,i.emitMany=void 0,i.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await Event.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,n){return new U(async i=>{for await(const r of t)i.emitOne(n(r))})}map(t){return U.map(this,t)}static filter(t,n){return new U(async i=>{for await(const r of t)n(r)&&i.emitOne(r)})}filter(t){return U.filter(this,t)}static coalesce(t){return U.filter(t,n=>!!n)}coalesce(){return U.coalesce(this)}static async toPromise(t){const n=[];for await(const i of t)n.push(i);return n}toPromise(){return U.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}},CACHE=new $Ic(1e4),$bV=Object.freeze({text:"text/plain",binary:"application/octet-stream",unknown:"application/unknown",markdown:"text/markdown",latex:"text/latex",uriList:"text/uri-list",html:"text/html"}),TokenType;(function(e){e[e.LParen=0]="LParen",e[e.RParen=1]="RParen",e[e.Neg=2]="Neg",e[e.Eq=3]="Eq",e[e.NotEq=4]="NotEq",e[e.Lt=5]="Lt",e[e.LtEq=6]="LtEq",e[e.Gt=7]="Gt",e[e.GtEq=8]="GtEq",e[e.RegexOp=9]="RegexOp",e[e.RegexStr=10]="RegexStr",e[e.True=11]="True",e[e.False=12]="False",e[e.In=13]="In",e[e.Not=14]="Not",e[e.And=15]="And",e[e.Or=16]="Or",e[e.Str=17]="Str",e[e.QuotedStr=18]="QuotedStr",e[e.Error=19]="Error",e[e.EOF=20]="EOF"})(TokenType||(TokenType={}));function hintDidYouMean(...e){switch(e.length){case 1:return localize(1838,null,e[0]);case 2:return localize(1839,null,e[0],e[1]);case 3:return localize(1840,null,e[0],e[1],e[2]);default:return}}var hintDidYouForgetToOpenOrCloseQuote=localize(1841,null),hintDidYouForgetToEscapeSlash=localize(1842,null),$zn=class ct{constructor(){this.c="",this.d=0,this.e=0,this.f=[],this.g=[],this.m=/[a-zA-Z0-9_<>\-\./\\:\*\?\+\[\]\^,#@;"%\$\p{L}-]+/uy}static getLexeme(t){switch(t.type){case 0:return"(";case 1:return")";case 2:return"!";case 3:return t.isTripleEq?"===":"==";case 4:return t.isTripleEq?"!==":"!=";case 5:return"<";case 6:return"<=";case 7:return">=";case 8:return">=";case 9:return"=~";case 10:return t.lexeme;case 11:return"true";case 12:return"false";case 13:return"in";case 14:return"not";case 15:return"&&";case 16:return"||";case 17:return t.lexeme;case 18:return t.lexeme;case 19:return t.lexeme;case 20:return"EOF";default:throw $ob(`unhandled token type: ${JSON.stringify(t)}; have you forgotten to add a case?`)}}static{this.a=new Set(["i","g","s","m","y","u"].map(t=>t.charCodeAt(0)))}static{this.b=new Map([["not",14],["in",13],["false",12],["true",11]])}get errors(){return this.g}reset(t){return this.c=t,this.d=0,this.e=0,this.f=[],this.g=[],this}scan(){for(;!this.r();)switch(this.d=this.e,this.i()){case 40:this.k(0);break;case 41:this.k(1);break;case 33:if(this.h(61)){const n=this.h(61);this.f.push({type:4,offset:this.d,isTripleEq:n})}else this.k(2);break;case 39:this.o();break;case 47:this.q();break;case 61:if(this.h(61)){const n=this.h(61);this.f.push({type:3,offset:this.d,isTripleEq:n})}else this.h(126)?this.k(9):this.l(hintDidYouMean("==","=~"));break;case 60:this.k(this.h(61)?6:5);break;case 62:this.k(this.h(61)?8:7);break;case 38:this.h(38)?this.k(15):this.l(hintDidYouMean("&&"));break;case 124:this.h(124)?this.k(16):this.l(hintDidYouMean("||"));break;case 32:case 13:case 9:case 10:case 160:break;default:this.n()}return this.d=this.e,this.k(20),Array.from(this.f)}h(t){return this.r()||this.c.charCodeAt(this.e)!==t?!1:(this.e++,!0)}i(){return this.c.charCodeAt(this.e++)}j(){return this.r()?0:this.c.charCodeAt(this.e)}k(t){this.f.push({type:t,offset:this.d})}l(t){const n=this.d,i=this.c.substring(this.d,this.e),r={type:19,offset:this.d,lexeme:i};this.g.push({offset:n,lexeme:i,additionalInfo:t}),this.f.push(r)}n(){this.m.lastIndex=this.d;const t=this.m.exec(this.c);if(t){this.e=this.d+t[0].length;const n=this.c.substring(this.d,this.e),i=ct.b.get(n);i?this.k(i):this.f.push({type:17,lexeme:n,offset:this.d})}}o(){for(;this.j()!==39&&!this.r();)this.i();if(this.r()){this.l(hintDidYouForgetToOpenOrCloseQuote);return}this.i(),this.f.push({type:18,lexeme:this.c.substring(this.d+1,this.e-1),offset:this.d+1})}q(){let t=this.e,n=!1,i=!1;for(;;){if(t>=this.c.length){this.e=t,this.l(hintDidYouForgetToEscapeSlash);return}const s=this.c.charCodeAt(t);if(n)n=!1;else if(s===47&&!i){t++;break}else s===91?i=!0:s===92?n=!0:s===93&&(i=!1);t++}for(;t<this.c.length&&ct.a.has(this.c.charCodeAt(t));)t++;this.e=t;const r=this.c.substring(this.d,this.e);this.f.push({type:10,lexeme:r,offset:this.d})}r(){return this.e>=this.c.length}},_util;(function(e){e.serviceIds=new Map,e.DI_TARGET="$di$target",e.DI_DEPENDENCIES="$di$dependencies";function t(n){return n[e.DI_DEPENDENCIES]||[]}e.getServiceDependencies=t})(_util||(_util={}));var $jl=$kl("instantiationService");function storeServiceDependency(e,t,n){t[_util.DI_TARGET]===t?t[_util.DI_DEPENDENCIES].push({id:e,index:n}):(t[_util.DI_DEPENDENCIES]=[{id:e,index:n}],t[_util.DI_TARGET]=t)}function $kl(e){if(_util.serviceIds.has(e))return _util.serviceIds.get(e);const t=function(n,i,r){if(arguments.length!==3)throw new Error("@IServiceName-decorator can only be used to decorate a parameter");storeServiceDependency(t,n,r)};return t.toString=()=>e,_util.serviceIds.set(e,t),t}var CONSTANT_VALUES=new Map;CONSTANT_VALUES.set("false",!1),CONSTANT_VALUES.set("true",!0),CONSTANT_VALUES.set("isMac",$m),CONSTANT_VALUES.set("isLinux",$n),CONSTANT_VALUES.set("isWindows",$l),CONSTANT_VALUES.set("isWeb",$r),CONSTANT_VALUES.set("isMacNative",$m&&!$r),CONSTANT_VALUES.set("isEdge",$K),CONSTANT_VALUES.set("isFirefox",$I),CONSTANT_VALUES.set("isChrome",$H),CONSTANT_VALUES.set("isSafari",$J);var hasOwnProperty=Object.prototype.hasOwnProperty,ContextKeyExprType;(function(e){e[e.False=0]="False",e[e.True=1]="True",e[e.Defined=2]="Defined",e[e.Not=3]="Not",e[e.Equals=4]="Equals",e[e.NotEquals=5]="NotEquals",e[e.And=6]="And",e[e.Regex=7]="Regex",e[e.NotRegex=8]="NotRegex",e[e.Or=9]="Or",e[e.In=10]="In",e[e.NotIn=11]="NotIn",e[e.Greater=12]="Greater",e[e.GreaterEquals=13]="GreaterEquals",e[e.Smaller=14]="Smaller",e[e.SmallerEquals=15]="SmallerEquals"})(ContextKeyExprType||(ContextKeyExprType={}));var defaultConfig={regexParsingWithErrorRecovery:!0},errorEmptyString=localize(1818,null),hintEmptyString=localize(1819,null),errorNoInAfterNot=localize(1820,null),errorClosingParenthesis=localize(1821,null),errorUnexpectedToken=localize(1822,null),hintUnexpectedToken=localize(1823,null),errorUnexpectedEOF=localize(1824,null),hintUnexpectedEOF=localize(1825,null),$Bn=class tt{static{this.c=new Error}get lexingErrors(){return this.d.errors}get parsingErrors(){return this.h}constructor(t=defaultConfig){this.k=t,this.d=new $zn,this.f=[],this.g=0,this.h=[],this.v=/g|y/g}parse(t){if(t===""){this.h.push({message:errorEmptyString,offset:0,lexeme:"",additionalInfo:hintEmptyString});return}this.f=this.d.reset(t).scan(),this.g=0,this.h=[];try{const n=this.l();if(!this.E()){const i=this.D(),r=i.type===17?hintUnexpectedToken:void 0;throw this.h.push({message:errorUnexpectedToken,offset:i.offset,lexeme:$zn.getLexeme(i),additionalInfo:r}),tt.c}return n}catch(n){if(n!==tt.c)throw n;return}}l(){return this.m()}m(){const t=[this.o()];for(;this.y(16);){const n=this.o();t.push(n)}return t.length===1?t[0]:$Cn.or(...t)}o(){const t=[this.s()];for(;this.y(15);){const n=this.s();t.push(n)}return t.length===1?t[0]:$Cn.and(...t)}s(){if(this.y(2)){const t=this.D();switch(t.type){case 11:return this.z(),$Fn.INSTANCE;case 12:return this.z(),$Gn.INSTANCE;case 0:{this.z();const n=this.l();return this.A(1,errorClosingParenthesis),n?.negate()}case 17:return this.z(),$Mn.create(t.lexeme);default:throw this.B("KEY | true | false | '(' expression ')'",t)}}return this.t()}t(){const t=this.D();switch(t.type){case 11:return this.z(),$Cn.true();case 12:return this.z(),$Cn.false();case 0:{this.z();const n=this.l();return this.A(1,errorClosingParenthesis),n}case 17:{const n=t.lexeme;if(this.z(),this.y(9)){const r=this.D();if(!this.k.regexParsingWithErrorRecovery){if(this.z(),r.type!==10)throw this.B("REGEX",r);const s=r.lexeme,l=s.lastIndexOf("/"),o=l===s.length-1?void 0:this.w(s.substring(l+1));let a;try{a=new RegExp(s.substring(1,l),o)}catch{throw this.B("REGEX",r)}return $Rn.create(n,a)}switch(r.type){case 10:case 19:{const s=[r.lexeme];this.z();let l=this.D(),o=0;for(let f=0;f<r.lexeme.length;f++)r.lexeme.charCodeAt(f)===40?o++:r.lexeme.charCodeAt(f)===41&&o--;for(;!this.E()&&l.type!==15&&l.type!==16;){switch(l.type){case 0:o++;break;case 1:o--;break;case 10:case 18:for(let f=0;f<l.lexeme.length;f++)l.lexeme.charCodeAt(f)===40?o++:r.lexeme.charCodeAt(f)===41&&o--}if(o<0)break;s.push($zn.getLexeme(l)),this.z(),l=this.D()}const a=s.join(""),h=a.lastIndexOf("/"),c=h===a.length-1?void 0:this.w(a.substring(h+1));let u;try{u=new RegExp(a.substring(1,h),c)}catch{throw this.B("REGEX",r)}return $Cn.regex(n,u)}case 18:{const s=r.lexeme;this.z();let l=null;if(!$6f(s)){const o=s.indexOf("/"),a=s.lastIndexOf("/");if(o!==a&&o>=0){const h=s.slice(o+1,a),c=s[a+1]==="i"?"i":"";try{l=new RegExp(h,c)}catch{throw this.B("REGEX",r)}}}if(l===null)throw this.B("REGEX",r);return $Rn.create(n,l)}default:throw this.B("REGEX",this.D())}}if(this.y(14)){this.A(13,errorNoInAfterNot);const r=this.u();return $Cn.notIn(n,r)}switch(this.D().type){case 3:{this.z();const r=this.u();if(this.x().type===18)return $Cn.equals(n,r);switch(r){case"true":return $Cn.has(n);case"false":return $Cn.not(n);default:return $Cn.equals(n,r)}}case 4:{this.z();const r=this.u();if(this.x().type===18)return $Cn.notEquals(n,r);switch(r){case"true":return $Cn.not(n);case"false":return $Cn.has(n);default:return $Cn.notEquals(n,r)}}case 5:return this.z(),$Pn.create(n,this.u());case 6:return this.z(),$Qn.create(n,this.u());case 7:return this.z(),$Nn.create(n,this.u());case 8:return this.z(),$On.create(n,this.u());case 13:return this.z(),$Cn.in(n,this.u());default:return $Cn.has(n)}}case 20:throw this.h.push({message:errorUnexpectedEOF,offset:t.offset,lexeme:"",additionalInfo:hintUnexpectedEOF}),tt.c;default:throw this.B(`true | false | KEY 
	| KEY '=~' REGEX 
	| KEY ('==' | '!=' | '<' | '<=' | '>' | '>=' | 'in' | 'not' 'in') value`,this.D())}}u(){const t=this.D();switch(t.type){case 17:case 18:return this.z(),t.lexeme;case 11:return this.z(),"true";case 12:return this.z(),"false";case 13:return this.z(),"in";default:return""}}w(t){return t.replaceAll(this.v,"")}x(){return this.f[this.g-1]}y(t){return this.C(t)?(this.z(),!0):!1}z(){return this.E()||this.g++,this.x()}A(t,n){if(this.C(t))return this.z();throw this.B(n,this.D())}B(t,n,i){const r=localize(1826,null,t,$zn.getLexeme(n)),s=n.offset,l=$zn.getLexeme(n);return this.h.push({message:r,offset:s,lexeme:l,additionalInfo:i}),tt.c}C(t){return this.D().type===t}D(){return this.f[this.g]}E(){return this.D().type===20}},$Cn=class{static false(){return $Fn.INSTANCE}static true(){return $Gn.INSTANCE}static has(e){return $Hn.create(e)}static equals(e,t){return $In.create(e,t)}static notEquals(e,t){return $Ln.create(e,t)}static regex(e,t){return $Rn.create(e,t)}static in(e,t){return $Jn.create(e,t)}static notIn(e,t){return $Kn.create(e,t)}static not(e){return $Mn.create(e)}static and(...e){return $Tn.create(e,null,!0)}static or(...e){return $Un.create(e,null,!0)}static greater(e,t){return $Nn.create(e,t)}static greaterEquals(e,t){return $On.create(e,t)}static smaller(e,t){return $Pn.create(e,t)}static smallerEquals(e,t){return $Qn.create(e,t)}static{this.c=new $Bn({regexParsingWithErrorRecovery:!1})}static deserialize(e){return e==null?void 0:this.c.parse(e)}};function cmp(e,t){return e.cmp(t)}var $Fn=class At{static{this.INSTANCE=new At}constructor(){this.type=0}cmp(t){return this.type-t.type}equals(t){return t.type===this.type}substituteConstants(){return this}evaluate(t){return!1}serialize(){return"false"}keys(){return[]}map(t){return this}negate(){return $Gn.INSTANCE}},$Gn=class Nt{static{this.INSTANCE=new Nt}constructor(){this.type=1}cmp(t){return this.type-t.type}equals(t){return t.type===this.type}substituteConstants(){return this}evaluate(t){return!0}serialize(){return"true"}keys(){return[]}map(t){return this}negate(){return $Fn.INSTANCE}},$Hn=class It{static create(t,n=null){const i=CONSTANT_VALUES.get(t);return typeof i=="boolean"?i?$Gn.INSTANCE:$Fn.INSTANCE:new It(t,n)}constructor(t,n){this.key=t,this.c=n,this.type=2}cmp(t){return t.type!==this.type?this.type-t.type:cmp1(this.key,t.key)}equals(t){return t.type===this.type?this.key===t.key:!1}substituteConstants(){const t=CONSTANT_VALUES.get(this.key);return typeof t=="boolean"?t?$Gn.INSTANCE:$Fn.INSTANCE:this}evaluate(t){return!!t.getValue(this.key)}serialize(){return this.key}keys(){return[this.key]}map(t){return t.mapDefined(this.key)}negate(){return this.c||(this.c=$Mn.create(this.key,this)),this.c}},$In=class Et{static create(t,n,i=null){if(typeof n=="boolean")return n?$Hn.create(t,i):$Mn.create(t,i);const r=CONSTANT_VALUES.get(t);return typeof r=="boolean"?n===(r?"true":"false")?$Gn.INSTANCE:$Fn.INSTANCE:new Et(t,n,i)}constructor(t,n,i){this.c=t,this.d=n,this.f=i,this.type=4}cmp(t){return t.type!==this.type?this.type-t.type:cmp2(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){const t=CONSTANT_VALUES.get(this.c);if(typeof t=="boolean"){const n=t?"true":"false";return this.d===n?$Gn.INSTANCE:$Fn.INSTANCE}return this}evaluate(t){return t.getValue(this.c)==this.d}serialize(){return`${this.c} == '${this.d}'`}keys(){return[this.c]}map(t){return t.mapEquals(this.c,this.d)}negate(){return this.f||(this.f=$Ln.create(this.c,this.d,this)),this.f}},$Jn=class xt{static create(t,n){return new xt(t,n)}constructor(t,n){this.d=t,this.f=n,this.type=10,this.c=null}cmp(t){return t.type!==this.type?this.type-t.type:cmp2(this.d,this.f,t.d,t.f)}equals(t){return t.type===this.type?this.d===t.d&&this.f===t.f:!1}substituteConstants(){return this}evaluate(t){const n=t.getValue(this.f),i=t.getValue(this.d);return Array.isArray(n)?n.includes(i):typeof i=="string"&&typeof n=="object"&&n!==null?hasOwnProperty.call(n,i):!1}serialize(){return`${this.d} in '${this.f}'`}keys(){return[this.d,this.f]}map(t){return t.mapIn(this.d,this.f)}negate(){return this.c||(this.c=$Kn.create(this.d,this.f)),this.c}},$Kn=class St{static create(t,n){return new St(t,n)}constructor(t,n){this.d=t,this.f=n,this.type=11,this.c=$Jn.create(t,n)}cmp(t){return t.type!==this.type?this.type-t.type:this.c.cmp(t.c)}equals(t){return t.type===this.type?this.c.equals(t.c):!1}substituteConstants(){return this}evaluate(t){return!this.c.evaluate(t)}serialize(){return`${this.d} not in '${this.f}'`}keys(){return this.c.keys()}map(t){return t.mapNotIn(this.d,this.f)}negate(){return this.c}},$Ln=class Ot{static create(t,n,i=null){if(typeof n=="boolean")return n?$Mn.create(t,i):$Hn.create(t,i);const r=CONSTANT_VALUES.get(t);return typeof r=="boolean"?n===(r?"true":"false")?$Fn.INSTANCE:$Gn.INSTANCE:new Ot(t,n,i)}constructor(t,n,i){this.c=t,this.d=n,this.f=i,this.type=5}cmp(t){return t.type!==this.type?this.type-t.type:cmp2(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){const t=CONSTANT_VALUES.get(this.c);if(typeof t=="boolean"){const n=t?"true":"false";return this.d===n?$Fn.INSTANCE:$Gn.INSTANCE}return this}evaluate(t){return t.getValue(this.c)!=this.d}serialize(){return`${this.c} != '${this.d}'`}keys(){return[this.c]}map(t){return t.mapNotEquals(this.c,this.d)}negate(){return this.f||(this.f=$In.create(this.c,this.d,this)),this.f}},$Mn=class Mt{static create(t,n=null){const i=CONSTANT_VALUES.get(t);return typeof i=="boolean"?i?$Fn.INSTANCE:$Gn.INSTANCE:new Mt(t,n)}constructor(t,n){this.c=t,this.d=n,this.type=3}cmp(t){return t.type!==this.type?this.type-t.type:cmp1(this.c,t.c)}equals(t){return t.type===this.type?this.c===t.c:!1}substituteConstants(){const t=CONSTANT_VALUES.get(this.c);return typeof t=="boolean"?t?$Fn.INSTANCE:$Gn.INSTANCE:this}evaluate(t){return!t.getValue(this.c)}serialize(){return`!${this.c}`}keys(){return[this.c]}map(t){return t.mapNot(this.c)}negate(){return this.d||(this.d=$Hn.create(this.c,this)),this.d}};function withFloatOrStr(e,t){if(typeof e=="string"){const n=parseFloat(e);isNaN(n)||(e=n)}return typeof e=="string"||typeof e=="number"?t(e):$Fn.INSTANCE}var $Nn=class Dt{static create(t,n,i=null){return withFloatOrStr(n,r=>new Dt(t,r,i))}constructor(t,n,i){this.c=t,this.d=n,this.f=i,this.type=12}cmp(t){return t.type!==this.type?this.type-t.type:cmp2(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))>this.d}serialize(){return`${this.c} > ${this.d}`}keys(){return[this.c]}map(t){return t.mapGreater(this.c,this.d)}negate(){return this.f||(this.f=$Qn.create(this.c,this.d,this)),this.f}},$On=class kt{static create(t,n,i=null){return withFloatOrStr(n,r=>new kt(t,r,i))}constructor(t,n,i){this.c=t,this.d=n,this.f=i,this.type=13}cmp(t){return t.type!==this.type?this.type-t.type:cmp2(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))>=this.d}serialize(){return`${this.c} >= ${this.d}`}keys(){return[this.c]}map(t){return t.mapGreaterEquals(this.c,this.d)}negate(){return this.f||(this.f=$Pn.create(this.c,this.d,this)),this.f}},$Pn=class Rt{static create(t,n,i=null){return withFloatOrStr(n,r=>new Rt(t,r,i))}constructor(t,n,i){this.c=t,this.d=n,this.f=i,this.type=14}cmp(t){return t.type!==this.type?this.type-t.type:cmp2(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))<this.d}serialize(){return`${this.c} < ${this.d}`}keys(){return[this.c]}map(t){return t.mapSmaller(this.c,this.d)}negate(){return this.f||(this.f=$On.create(this.c,this.d,this)),this.f}},$Qn=class _t{static create(t,n,i=null){return withFloatOrStr(n,r=>new _t(t,r,i))}constructor(t,n,i){this.c=t,this.d=n,this.f=i,this.type=15}cmp(t){return t.type!==this.type?this.type-t.type:cmp2(this.c,this.d,t.c,t.d)}equals(t){return t.type===this.type?this.c===t.c&&this.d===t.d:!1}substituteConstants(){return this}evaluate(t){return typeof this.d=="string"?!1:parseFloat(t.getValue(this.c))<=this.d}serialize(){return`${this.c} <= ${this.d}`}keys(){return[this.c]}map(t){return t.mapSmallerEquals(this.c,this.d)}negate(){return this.f||(this.f=$Nn.create(this.c,this.d,this)),this.f}},$Rn=class Pt{static create(t,n){return new Pt(t,n)}constructor(t,n){this.d=t,this.f=n,this.type=7,this.c=null}cmp(t){if(t.type!==this.type)return this.type-t.type;if(this.d<t.d)return-1;if(this.d>t.d)return 1;const n=this.f?this.f.source:"",i=t.f?t.f.source:"";return n<i?-1:n>i?1:0}equals(t){if(t.type===this.type){const n=this.f?this.f.source:"",i=t.f?t.f.source:"";return this.d===t.d&&n===i}return!1}substituteConstants(){return this}evaluate(t){const n=t.getValue(this.d);return this.f?this.f.test(n):!1}serialize(){const t=this.f?`/${this.f.source}/${this.f.flags}`:"/invalid/";return`${this.d} =~ ${t}`}keys(){return[this.d]}map(t){return t.mapRegex(this.d,this.f)}negate(){return this.c||(this.c=$Sn.create(this)),this.c}},$Sn=class ft{static create(t){return new ft(t)}constructor(t){this.c=t,this.type=8}cmp(t){return t.type!==this.type?this.type-t.type:this.c.cmp(t.c)}equals(t){return t.type===this.type?this.c.equals(t.c):!1}substituteConstants(){return this}evaluate(t){return!this.c.evaluate(t)}serialize(){return`!(${this.c.serialize()})`}keys(){return this.c.keys()}map(t){return new ft(this.c.map(t))}negate(){return this.c}};function eliminateConstantsInArray(e){let t=null;for(let n=0,i=e.length;n<i;n++){const r=e[n].substituteConstants();if(e[n]!==r&&t===null){t=[];for(let s=0;s<n;s++)t[s]=e[s]}t!==null&&(t[n]=r)}return t===null?e:t}var $Tn=class G{static create(t,n,i){return G.d(t,n,i)}constructor(t,n){this.expr=t,this.c=n,this.type=6}cmp(t){if(t.type!==this.type)return this.type-t.type;if(this.expr.length<t.expr.length)return-1;if(this.expr.length>t.expr.length)return 1;for(let n=0,i=this.expr.length;n<i;n++){const r=cmp(this.expr[n],t.expr[n]);if(r!==0)return r}return 0}equals(t){if(t.type===this.type){if(this.expr.length!==t.expr.length)return!1;for(let n=0,i=this.expr.length;n<i;n++)if(!this.expr[n].equals(t.expr[n]))return!1;return!0}return!1}substituteConstants(){const t=eliminateConstantsInArray(this.expr);return t===this.expr?this:G.create(t,this.c,!1)}evaluate(t){for(let n=0,i=this.expr.length;n<i;n++)if(!this.expr[n].evaluate(t))return!1;return!0}static d(t,n,i){const r=[];let s=!1;for(const l of t)if(l){if(l.type===1){s=!0;continue}if(l.type===0)return $Fn.INSTANCE;if(l.type===6){r.push(...l.expr);continue}r.push(l)}if(r.length===0&&s)return $Gn.INSTANCE;if(r.length!==0){if(r.length===1)return r[0];r.sort(cmp);for(let l=1;l<r.length;l++)r[l-1].equals(r[l])&&(r.splice(l,1),l--);if(r.length===1)return r[0];for(;r.length>1;){const l=r[r.length-1];if(l.type!==9)break;r.pop();const o=r.pop(),a=r.length===0,h=$Un.create(l.expr.map(c=>G.create([c,o],null,i)),null,a);h&&(r.push(h),r.sort(cmp))}if(r.length===1)return r[0];if(i){for(let l=0;l<r.length;l++)for(let o=l+1;o<r.length;o++)if(r[l].negate().equals(r[o]))return $Fn.INSTANCE;if(r.length===1)return r[0]}return new G(r,n)}}serialize(){return this.expr.map(t=>t.serialize()).join(" && ")}keys(){const t=[];for(const n of this.expr)t.push(...n.keys());return t}map(t){return new G(this.expr.map(n=>n.map(t)),null)}negate(){if(!this.c){const t=[];for(const n of this.expr)t.push(n.negate());this.c=$Un.create(t,this,!0)}return this.c}},$Un=class W{static create(t,n,i){return W.d(t,n,i)}constructor(t,n){this.expr=t,this.c=n,this.type=9}cmp(t){if(t.type!==this.type)return this.type-t.type;if(this.expr.length<t.expr.length)return-1;if(this.expr.length>t.expr.length)return 1;for(let n=0,i=this.expr.length;n<i;n++){const r=cmp(this.expr[n],t.expr[n]);if(r!==0)return r}return 0}equals(t){if(t.type===this.type){if(this.expr.length!==t.expr.length)return!1;for(let n=0,i=this.expr.length;n<i;n++)if(!this.expr[n].equals(t.expr[n]))return!1;return!0}return!1}substituteConstants(){const t=eliminateConstantsInArray(this.expr);return t===this.expr?this:W.create(t,this.c,!1)}evaluate(t){for(let n=0,i=this.expr.length;n<i;n++)if(this.expr[n].evaluate(t))return!0;return!1}static d(t,n,i){let r=[],s=!1;if(t){for(let l=0,o=t.length;l<o;l++){const a=t[l];if(a){if(a.type===0){s=!0;continue}if(a.type===1)return $Gn.INSTANCE;if(a.type===9){r=r.concat(a.expr);continue}r.push(a)}}if(r.length===0&&s)return $Fn.INSTANCE;r.sort(cmp)}if(r.length!==0){if(r.length===1)return r[0];for(let l=1;l<r.length;l++)r[l-1].equals(r[l])&&(r.splice(l,1),l--);if(r.length===1)return r[0];if(i){for(let l=0;l<r.length;l++)for(let o=l+1;o<r.length;o++)if(r[l].negate().equals(r[o]))return $Gn.INSTANCE;if(r.length===1)return r[0]}return new W(r,n)}}serialize(){return this.expr.map(t=>t.serialize()).join(" || ")}keys(){const t=[];for(const n of this.expr)t.push(...n.keys());return t}map(t){return new W(this.expr.map(n=>n.map(t)),null)}negate(){if(!this.c){const t=[];for(const n of this.expr)t.push(n.negate());for(;t.length>1;){const n=t.shift(),i=t.shift(),r=[];for(const s of getTerminals(n))for(const l of getTerminals(i))r.push($Tn.create([s,l],null,!1));t.unshift(W.create(r,null,!1))}this.c=W.create(t,this,!0)}return this.c}},$Vn=class ot extends $Hn{static{this.d=[]}static all(){return ot.d.values()}constructor(t,n,i){super(t,null),this.f=n,typeof i=="object"?ot.d.push({...i,key:t}):i!==!0&&ot.d.push({key:t,description:i,type:n!=null?typeof n:void 0})}bindTo(t){return t.createKey(this.key,this.f)}getValue(t){return t.getContextKeyValue(this.key)}toNegated(){return this.negate()}isEqualTo(t){return $In.create(this.key,t)}notEqualsTo(t){return $Ln.create(this.key,t)}greater(t){return $Nn.create(this.key,t)}},$Wn=$kl("contextKeyService");function cmp1(e,t){return e<t?-1:e>t?1:0}function cmp2(e,t,n,i){return e<n?-1:e>n?1:t<i?-1:t>i?1:0}function getTerminals(e){return e.type===9?e.expr:[e]}var $hl=class{constructor(e,t=[],n=!1){this.ctor=e,this.staticArguments=t,this.supportsDelayedInstantiation=n}},_registry=[],InstantiationType;(function(e){e[e.Eager=0]="Eager",e[e.Delayed=1]="Delayed"})(InstantiationType||(InstantiationType={}));function $3U(e,t,n){t instanceof $hl||(t=new $hl(t,[],!!n)),_registry.push([e,t])}var $b5=$kl("notebookDocumentService"),_lengths=["W","X","Y","Z","a","b","c","d","e","f"],_padRegexp=new RegExp(`^[${_lengths.join("")}]+`),_radix=7;function $c5(e){if(e.scheme!==Schemas.vscodeNotebookCell)return;const t=e.fragment.indexOf("s");if(t<0)return;const n=parseInt(e.fragment.substring(0,t).replace(_padRegexp,""),_radix),i=$dj(e.fragment.substring(t+1)).toString();if(!isNaN(n))return{handle:n,notebook:e.with({scheme:i,fragment:null})}}function $d5(e,t){const n=t.toString(_radix),r=`${n.length<_lengths.length?_lengths[n.length-1]:"z"}${n}s${$ej($Wi.fromString(e.scheme),!0,!0)}`;return e.with({scheme:Schemas.vscodeNotebookCell,fragment:r})}function $e5(e){if(e.scheme!==Schemas.vscodeNotebookMetadata)return;const t=$dj(e.fragment).toString();return e.with({scheme:t,fragment:null})}function $f5(e){const t=`${$ej($Wi.fromString(e.scheme),!0,!0)}`;return e.with({scheme:Schemas.vscodeNotebookMetadata,fragment:t})}function $g5(e){if(e.scheme!==Schemas.vscodeNotebookCellOutput)return;const t=new URLSearchParams(e.query),n=t.get("openIn");if(!n)return;const i=t.get("outputId")??void 0,r=$c5(e.with({scheme:Schemas.vscodeNotebookCell,query:null})),s=t.get("outputIndex")?parseInt(t.get("outputIndex")||"",10):void 0;return{notebook:r?r.notebook:e.with({scheme:t.get("notebookScheme")||Schemas.file,fragment:null,query:null}),openIn:n,outputId:i,outputIndex:s,cellHandle:r?.handle,cellFragment:e.fragment}}var $h5=class{constructor(){this.a=new $Fc}getNotebook(e){if(e.scheme===Schemas.vscodeNotebookCell){const t=$c5(e);if(t){const n=this.a.get(t.notebook);if(n)return n}}if(e.scheme===Schemas.vscodeNotebookCellOutput){const t=$g5(e);if(t){const n=this.a.get(t.notebook);if(n)return n}}return this.a.get(e)}addNotebookDocument(e){this.a.set(e.uri,e)}removeNotebookDocument(e){this.a.delete(e.uri)}};$3U($b5,$h5,1);var CellKind;(function(e){e[e.Markup=1]="Markup",e[e.Code=2]="Code"})(CellKind||(CellKind={}));var $o5=["application/json","application/javascript","text/html","image/svg+xml",$bV.latex,$bV.markdown,"image/png","image/jpeg",$bV.text],$p5=[$bV.latex,$bV.markdown,"application/json","text/html","image/svg+xml","image/png","image/jpeg",$bV.text],NotebookRunState;(function(e){e[e.Running=1]="Running",e[e.Idle=2]="Idle"})(NotebookRunState||(NotebookRunState={}));var NotebookCellExecutionState;(function(e){e[e.Unconfirmed=1]="Unconfirmed",e[e.Pending=2]="Pending",e[e.Executing=3]="Executing"})(NotebookCellExecutionState||(NotebookCellExecutionState={}));var NotebookExecutionState;(function(e){e[e.Unconfirmed=1]="Unconfirmed",e[e.Pending=2]="Pending",e[e.Executing=3]="Executing"})(NotebookExecutionState||(NotebookExecutionState={}));var NotebookRendererMatch;(function(e){e[e.WithHardKernelDependency=0]="WithHardKernelDependency",e[e.WithOptionalKernelDependency=1]="WithOptionalKernelDependency",e[e.Pure=2]="Pure",e[e.Never=3]="Never"})(NotebookRendererMatch||(NotebookRendererMatch={}));var RendererMessagingSpec;(function(e){e.Always="always",e.Never="never",e.Optional="optional"})(RendererMessagingSpec||(RendererMessagingSpec={}));var NotebookCellsChangeType;(function(e){e[e.ModelChange=1]="ModelChange",e[e.Move=2]="Move",e[e.ChangeCellLanguage=5]="ChangeCellLanguage",e[e.Initialize=6]="Initialize",e[e.ChangeCellMetadata=7]="ChangeCellMetadata",e[e.Output=8]="Output",e[e.OutputItem=9]="OutputItem",e[e.ChangeCellContent=10]="ChangeCellContent",e[e.ChangeDocumentMetadata=11]="ChangeDocumentMetadata",e[e.ChangeCellInternalMetadata=12]="ChangeCellInternalMetadata",e[e.ChangeCellMime=13]="ChangeCellMime",e[e.Unknown=100]="Unknown"})(NotebookCellsChangeType||(NotebookCellsChangeType={}));var SelectionStateType;(function(e){e[e.Handle=0]="Handle",e[e.Index=1]="Index"})(SelectionStateType||(SelectionStateType={}));var CellEditType;(function(e){e[e.Replace=1]="Replace",e[e.Output=2]="Output",e[e.Metadata=3]="Metadata",e[e.CellLanguage=4]="CellLanguage",e[e.DocumentMetadata=5]="DocumentMetadata",e[e.Move=6]="Move",e[e.OutputItems=7]="OutputItems",e[e.PartialMetadata=8]="PartialMetadata",e[e.PartialInternalMetadata=9]="PartialInternalMetadata"})(CellEditType||(CellEditType={}));var NotebookMetadataUri;(function(e){e.scheme=Schemas.vscodeNotebookMetadata;function t(i){return $f5(i)}e.generate=t;function n(i){return $e5(i)}e.parse=n})(NotebookMetadataUri||(NotebookMetadataUri={}));var CellUri;(function(e){e.scheme=Schemas.vscodeNotebookCell;function t(a,h){return $d5(a,h)}e.generate=t;function n(a){return $c5(a)}e.parse=n;function i(a,h){return a.with({scheme:Schemas.vscodeNotebookCellOutput,query:new URLSearchParams({openIn:"editor",outputId:h??"",notebookScheme:a.scheme!==Schemas.file?a.scheme:""}).toString()})}e.generateCellOutputUriWithId=i;function r(a,h,c){return a.with({scheme:Schemas.vscodeNotebookCellOutput,fragment:h.fragment,query:new URLSearchParams({openIn:"notebook",outputIndex:String(c)}).toString()})}e.generateCellOutputUriWithIndex=r;function s(a){return $g5(a)}e.parseCellOutputUri=s;function l(a,h,c){return e.generate(a,h).with({scheme:c})}e.generateCellPropertyUri=l;function o(a,h){if(a.scheme===h)return e.parse(a.with({scheme:e.scheme}))}e.parseCellPropertyUri=o})(CellUri||(CellUri={}));var $u5=new $Vn("notebookEditorCursorAtBoundary","none"),$v5=new $Vn("notebookEditorCursorAtLineBoundary","none"),NotebookEditorPriority;(function(e){e.default="default",e.option="option"})(NotebookEditorPriority||(NotebookEditorPriority={}));var NotebookFindScopeType;(function(e){e.Cells="cells",e.Text="text",e.None="none"})(NotebookFindScopeType||(NotebookFindScopeType={}));var CellStatusbarAlignment;(function(e){e[e.Left=1]="Left",e[e.Right=2]="Right"})(CellStatusbarAlignment||(CellStatusbarAlignment={}));var $z5=class at{static{this.d="notebook/"}static create(t,n){return`${at.d}${t}/${n??t}`}static parse(t){if(t.startsWith(at.d)){const n=t.substring(at.d.length).split("/");if(n.length===2)return{notebookType:n[0],viewType:n[1]}}}},textDecoder2=new TextDecoder,$C5="\x1B[A",MOVE_CURSOR_1_LINE_COMMAND_BYTES=$C5.split("").map(e=>e.charCodeAt(0)),BACKSPACE_CHARACTER=8,CARRIAGE_RETURN_CHARACTER=13,$yV="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function createWordRegExp(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of $yV)e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}var $zV=createWordRegExp();function $AV(e){let t=$zV;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}var _defaultConfig=new $Fd;_defaultConfig.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function $CV(e,t,n,i,r){if(t=$AV(t),r||(r=Iterable.first(_defaultConfig)),n.length>r.maxLen){let h=e-r.maxLen/2;return h<0?h=0:i+=h,n=n.substring(h,e+r.maxLen/2),$CV(e,t,n,i,r)}const s=Date.now(),l=e-1-i;let o=-1,a=null;for(let h=1;!(Date.now()-s>=r.timeBudget);h++){const c=l-r.windowSize*h;t.lastIndex=Math.max(0,c);const u=_findRegexMatchEnclosingPosition(t,n,l,o);if(!u&&a||(a=u,c<=0))break;o=c}if(a){const h={word:a[0],startColumn:i+1+a.index,endColumn:i+1+a.index+a[0].length};return t.lastIndex=0,h}return null}function _findRegexMatchEnclosingPosition(e,t,n,i){let r;for(;r=e.exec(t);){const s=r.index||0;if(s<=n&&e.lastIndex>=n)return r;if(i>0&&s>i)return null}return null}var $X5=class{constructor(e){this.a=e,this.b=new Uint32Array(e.length),this.c=new Int32Array(1),this.c[0]=-1}getCount(){return this.a.length}insertValues(e,t){e=$5f(e);const n=this.a,i=this.b,r=t.length;return r===0?!1:(this.a=new Uint32Array(n.length+r),this.a.set(n.subarray(0,e),0),this.a.set(n.subarray(e),e+r),this.a.set(t,e),e-1<this.c[0]&&(this.c[0]=e-1),this.b=new Uint32Array(this.a.length),this.c[0]>=0&&this.b.set(i.subarray(0,this.c[0]+1)),!0)}setValue(e,t){return e=$5f(e),t=$5f(t),this.a[e]===t?!1:(this.a[e]=t,e-1<this.c[0]&&(this.c[0]=e-1),!0)}removeValues(e,t){e=$5f(e),t=$5f(t);const n=this.a,i=this.b;if(e>=n.length)return!1;const r=n.length-e;return t>=r&&(t=r),t===0?!1:(this.a=new Uint32Array(n.length-t),this.a.set(n.subarray(0,e),0),this.a.set(n.subarray(e+t),e),this.b=new Uint32Array(this.a.length),e-1<this.c[0]&&(this.c[0]=e-1),this.c[0]>=0&&this.b.set(i.subarray(0,this.c[0]+1)),!0)}getTotalSum(){return this.a.length===0?0:this.d(this.a.length-1)}getPrefixSum(e){return e<0?0:(e=$5f(e),this.d(e))}d(e){if(e<=this.c[0])return this.b[e];let t=this.c[0]+1;t===0&&(this.b[0]=this.a[0],t++),e>=this.a.length&&(e=this.a.length-1);for(let n=t;n<=e;n++)this.b[n]=this.b[n-1]+this.a[n];return this.c[0]=Math.max(this.c[0],e),this.b[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.a.length-1,i=0,r=0,s=0;for(;t<=n;)if(i=t+(n-t)/2|0,r=this.b[i],s=r-this.a[i],e<s)n=i-1;else if(e>=r)t=i+1;else break;return new $Z5(i,e-s)}},$Z5=class{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}},$15=class{constructor(e,t,n,i){this.a=e,this.b=t,this.c=n,this.d=i,this.f=null,this.g=null}dispose(){this.b.length=0}get version(){return this.d}getText(){return this.g===null&&(this.g=this.b.join(this.c)),this.g}onEvents(e){e.eol&&e.eol!==this.c&&(this.c=e.eol,this.f=null);const t=e.changes;for(const n of t)this.k(n.range),this.l(new $lV(n.range.startLineNumber,n.range.startColumn),n.text);this.d=e.versionId,this.g=null}h(){if(!this.f){const e=this.c.length,t=this.b.length,n=new Uint32Array(t);for(let i=0;i<t;i++)n[i]=this.b[i].length+e;this.f=new $X5(n)}}j(e,t){this.b[e]=t,this.f&&this.f.setValue(e,this.b[e].length+this.c.length)}k(e){if(e.startLineNumber===e.endLineNumber){if(e.startColumn===e.endColumn)return;this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.startLineNumber-1].substring(e.endColumn-1));return}this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.endLineNumber-1].substring(e.endColumn-1)),this.b.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this.f&&this.f.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber)}l(e,t){if(t.length===0)return;const n=$kg(t);if(n.length===1){this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+n[0]+this.b[e.lineNumber-1].substring(e.column-1));return}n[n.length-1]+=this.b[e.lineNumber-1].substring(e.column-1),this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+n[0]);const i=new Uint32Array(n.length-1);for(let r=1;r<n.length;r++)this.b.splice(e.lineNumber+r-1,0,n[r]),i[r-1]=n[r].length+this.c.length;this.f&&this.f.insertValues(e.lineNumber,i)}},$jAb=60*1e3,$nAb=class extends $15{get uri(){return this.a}get eol(){return this.c}getValue(){return this.getText()}findMatches(e){const t=[];for(let n=0;n<this.b.length;n++){const i=this.b[n],r=this.offsetAt(new $lV(n+1,1)),s=i.matchAll(e);for(const l of s)(l.index||l.index===0)&&(l.index=l.index+r),t.push(l)}return t}getLinesContent(){return this.b.slice(0)}getLineCount(){return this.b.length}getLineContent(e){return this.b[e-1]}getWordAtPosition(e,t){const n=$CV(e.column,$AV(t),this.b[e.lineNumber-1],0);return n?new $mV(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}getWordUntilPosition(e,t){const n=this.getWordAtPosition(e,t);return n?{word:this.b[e.lineNumber-1].substring(n.startColumn-1,e.column-1),startColumn:n.startColumn,endColumn:e.column}:{word:"",startColumn:e.column,endColumn:e.column}}words(e){const t=this.b,n=this.m.bind(this);let i=0,r="",s=0,l=[];return{*[Symbol.iterator](){for(;;)if(s<l.length){const o=r.substring(l[s].start,l[s].end);s+=1,yield o}else if(i<t.length)r=t[i],l=n(r,e),s=0,i+=1;else break}}}getLineWords(e,t){const n=this.b[e-1],i=this.m(n,t),r=[];for(const s of i)r.push({word:n.substring(s.start,s.end),startColumn:s.start+1,endColumn:s.end+1});return r}m(e,t){const n=[];let i;for(t.lastIndex=0;(i=t.exec(e))&&i[0].length!==0;)n.push({start:i.index,end:i.index+i[0].length});return n}getValueInRange(e){if(e=this.n(e),e.startLineNumber===e.endLineNumber)return this.b[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);const t=this.c,n=e.startLineNumber-1,i=e.endLineNumber-1,r=[];r.push(this.b[n].substring(e.startColumn-1));for(let s=n+1;s<i;s++)r.push(this.b[s]);return r.push(this.b[i].substring(0,e.endColumn-1)),r.join(t)}offsetAt(e){return e=this.o(e),this.h(),this.f.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this.h();const t=this.f.getIndexOf(e),n=this.b[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}n(e){const t=this.o({lineNumber:e.startLineNumber,column:e.startColumn}),n=this.o({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}o(e){if(!$lV.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,i=!1;if(t<1)t=1,n=1,i=!0;else if(t>this.b.length)t=this.b.length,n=this.b[t-1].length+1,i=!0;else{const r=this.b[t-1].length+1;n<1?(n=1,i=!0):n>r&&(n=r,i=!0)}return i?{lineNumber:t,column:n}:e}};function $yAc(e,t){const n={modifiedToOriginal:new Map,originalToModified:new Map},i=[],r=new Map,s=new Set,l=new Map,o=(h,c)=>{if(r.has(h))return!1;const u=l.get(h)?.dist??Number.MAX_SAFE_INTEGER;return c.editCount<u},a=(h,c)=>{r.set(c,h),s.add(h)};for(let h=0;h<e.length;h++){const c=e[h],{index:u,editCount:f,percentage:b}=computeClosestCell({cell:c,index:h},t,!0,n,o);u>=0&&f===0?(a(h,u),i.push({modified:h,original:u,dist:f,percentage:b,possibleOriginal:u})):(l.set(u,{dist:f,modifiedIndex:h}),i.push({modified:h,original:-1,dist:f,percentage:b,possibleOriginal:u}))}return i.forEach((h,c)=>{if(h.original>=0)return;const u=c>0?i.slice(0,c).reverse().find(E=>E.original>=0):void 0,f=u?.original??-1,b=u?.modified??-1,p=i.slice(c+1).find(E=>E.original>=0),m=new Set,d=i.findIndex((E,A)=>A>c&&E.original>=0),C=d>=0?i[d].original:-1;t.forEach((E,A)=>{if(r.has(A)){m.add(A);return}p&&A>=p.original&&m.add(A),C>=0&&A>C&&m.add(A)});const g=e[c];if(h.original===-1&&h.possibleOriginal>=0&&!m.has(h.possibleOriginal)&&o(h.possibleOriginal,{editCount:h.dist})){a(c,h.possibleOriginal),h.original=h.possibleOriginal;return}if(f>0&&b>0&&f===b&&(d>=0?d:e.length-1)===(C>=0?C:t.length-1)&&!m.has(c)&&c<t.length){const E=(d>=0?d:e.length)-b,A=(C>=0?C:t.length)-f;if(E===A&&g.cellKind===t[c].cellKind){a(c,c),h.original=c;return}}const{index:w,percentage:M}=computeClosestCell({cell:g,index:c},t,!1,n,(E,A)=>{if(m.has(E))return!1;if(d>0||f>0){const D=n.originalToModified.get(E);if(D&&f<E&&Array.from(D).find(([k,S])=>k===c||k>=d||s.has(c)?!1:S.editCount<A.editCount))return!1}return!m.has(E)});if(w>=0&&c>0&&i[c-1].original===w-1){a(c,w),i[c].original=w;return}const v=c>0&&t.length>i[c-1].original?i[c-1].original+1:-1,x=c>0&&v>=0&&v<t.length?t[v].getValue():void 0;if(w>=0&&c>0&&typeof x=="string"&&!r.has(v)&&(g.getValue().includes(x)||x.includes(g.getValue()))){a(c,v),i[c].original=v;return}if(M<90||c===0&&i.length===1){a(c,w),i[c].original=w;return}}),i}function computeClosestCell({cell:e,index:t},n,i,r,s){let l=1/0,o=-1;const a=e.internalMetadata?.internalId;if(a){const c=n.findIndex(u=>u.internalMetadata?.internalId===a);if(c>=0)return{index:c,editCount:0,percentage:Number.MAX_SAFE_INTEGER}}for(let c=0;c<n.length;c++){if(n[c].cellKind!==e.cellKind)continue;const u=n[c].getValue(),f=r.modifiedToOriginal.get(t)??new Map,b=f.get(c)??{editCount:computeNumberOfEdits(e,n[c])};f.set(c,b),r.modifiedToOriginal.set(t,f);const p=r.originalToModified.get(c)??new Map;if(p.set(t,b),r.originalToModified.set(c,p),!!s(c,b)&&!(u.length===0&&i)){if(u===e.getValue()&&e.getValue().length>0)return{index:c,editCount:0,percentage:0};b.editCount<l&&(l=b.editCount,o=c)}}if(o===-1)return{index:-1,editCount:Number.MAX_SAFE_INTEGER,percentage:Number.MAX_SAFE_INTEGER};const h=!e.getValue().length&&!n[o].getValue().length?0:e.getValue().length?l*100/e.getValue().length:Number.MAX_SAFE_INTEGER;return{index:o,editCount:l,percentage:h}}function computeNumberOfEdits(e,t){return e.getValue()===t.getValue()?0:$kV(e.getValue(),t.getValue())}var $8s=function(){if(typeof crypto.randomUUID=="function")return crypto.randomUUID.bind(crypto);const e=new Uint8Array(16),t=[];for(let n=0;n<256;n++)t.push(n.toString(16).padStart(2,"0"));return function(){crypto.getRandomValues(e),e[6]=e[6]&15|64,e[8]=e[8]&63|128;let i=0,r="";return r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+="-",r+=t[e[i++]],r+=t[e[i++]],r+="-",r+=t[e[i++]],r+=t[e[i++]],r+="-",r+=t[e[i++]],r+=t[e[i++]],r+="-",r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r+=t[e[i++]],r}}();function $ogc(e,t,n){const i=n.cellsDiff.changes,r=[];let s=0,l=0,o=-1;for(let a=0;a<i.length;a++){const h=i[a];for(let u=0;u<h.originalStart-s;u++){const f=e.cells[s+u],b=t.cells[l+u];f.getHashValue()===b.getHashValue()?r.push({originalCellIndex:s+u,modifiedCellIndex:l+u,type:"unchanged"}):(o===-1&&(o=r.length),r.push({originalCellIndex:s+u,modifiedCellIndex:l+u,type:"modified"}))}const c=computeModifiedLCS(h,e,t);c.length&&o===-1&&(o=r.length),r.push(...c),s=h.originalStart+h.originalLength,l=h.modifiedStart+h.modifiedLength}for(let a=s;a<e.cells.length;a++)r.push({originalCellIndex:a,modifiedCellIndex:a-s+l,type:"unchanged"});return{cellDiffInfo:r,firstChangeIndex:o}}function computeModifiedLCS(e,t,n){const i=[],r=Math.min(e.originalLength,e.modifiedLength);for(let s=0;s<r;s++){const l=t.cells[e.originalStart+s],o=n.cells[e.modifiedStart+s];if(l.cellKind!==o.cellKind)i.push({originalCellIndex:e.originalStart+s,type:"delete"}),i.push({modifiedCellIndex:e.modifiedStart+s,type:"insert"});else{const a=l.equal(o);i.push({originalCellIndex:e.originalStart+s,modifiedCellIndex:e.modifiedStart+s,type:a?"unchanged":"modified"})}}for(let s=r;s<e.originalLength;s++)i.push({originalCellIndex:e.originalStart+s,type:"delete"});for(let s=r;s<e.modifiedLength;s++)i.push({modifiedCellIndex:e.modifiedStart+s,type:"insert"});return i}var PREFIX_FOR_UNMATCHED_ORIGINAL_CELLS="unmatchedOriginalCell",MirrorCell=class{get eol(){return this.d===`\r
`?2:1}constructor(e,t,n,i,r,s,l,o,a,h){this.handle=e,this.d=i,this.language=s,this.cellKind=l,this.outputs=o,this.metadata=a,this.internalMetadata=h,this.a=new $nAb(t,n,i,r)}onEvents(e){this.a.onEvents(e),this.b=void 0}getValue(){return this.a.getValue()}getLinesContent(){return this.a.getLinesContent()}getComparisonValue(){return this.b??=this.f()}f(){let e=$hj(104579,0);e=$gj(this.language,e),e=$gj(this.getValue(),e),e=$gj(this.metadata,e),e=$gj(this.internalMetadata?.internalId||"",e);for(const n of this.outputs){e=$gj(n.metadata,e);for(const i of n.outputs)e=$gj(i.mime,e)}const t=this.outputs.flatMap(n=>n.outputs.map(i=>$fj(Array.from(i.data.buffer))));for(const n of t)e=$hj(n,e);return e}},MirrorNotebookDocument=class{constructor(e,t,n,i){this.uri=e,this.cells=t,this.metadata=n,this.transientDocumentMetadata=i}acceptModelChanged(e){e.rawEvents.forEach(t=>{if(t.kind===NotebookCellsChangeType.ModelChange)this._spliceNotebookCells(t.changes);else if(t.kind===NotebookCellsChangeType.Move){const n=this.cells.splice(t.index,1);this.cells.splice(t.newIdx,0,...n)}else if(t.kind===NotebookCellsChangeType.Output){const n=this.cells[t.index];n.outputs=t.outputs}else if(t.kind===NotebookCellsChangeType.ChangeCellLanguage){this.a(t.index);const n=this.cells[t.index];n.language=t.language}else if(t.kind===NotebookCellsChangeType.ChangeCellMetadata){this.a(t.index);const n=this.cells[t.index];n.metadata=t.metadata}else if(t.kind===NotebookCellsChangeType.ChangeCellInternalMetadata){this.a(t.index);const n=this.cells[t.index];n.internalMetadata=t.internalMetadata}else t.kind===NotebookCellsChangeType.ChangeDocumentMetadata&&(this.metadata=t.metadata)})}a(e){if(e<0||e>=this.cells.length)throw new Error(`Illegal index ${e}. Cells length: ${this.cells.length}`)}_spliceNotebookCells(e){e.reverse().forEach(t=>{const i=t[2].map(r=>new MirrorCell(r.handle,URI.parse(r.url),r.source,r.eol,r.versionId,r.language,r.cellKind,r.outputs,r.metadata));this.cells.splice(t[0],t[1],...i)})}},CellSequence=class dt{static create(t){const n=t.cells.map(i=>i.getComparisonValue());return new dt(n)}static createWithCellId(t,n){const i=t.map(r=>n?`${$gj(r.internalMetadata?.internalId,$hj(104579,0))}#${r.getComparisonValue()}`:`${$gj(r.internalMetadata?.internalId,$hj(104579,0))}}`);return new dt(i)}constructor(t){this.hashValue=t}getElements(){return this.hashValue}},$zAc=class{constructor(){this.a=Object.create(null)}dispose(){}$acceptNewModel(e,t,n,i){this.a[e]=new MirrorNotebookDocument(URI.parse(e),i.map(r=>new MirrorCell(r.handle,URI.parse(r.url),r.source,r.eol,r.versionId,r.language,r.cellKind,r.outputs,r.metadata,r.internalMetadata)),t,n)}$acceptModelChanged(e,t){this.a[e]?.acceptModelChanged(t)}$acceptCellModelChanged(e,t,n){this.a[e].cells.find(r=>r.handle===t)?.onEvents(n)}$acceptRemovedModel(e){this.a[e]&&delete this.a[e]}async $computeDiff(e,t){const n=this.b(e),i=this.b(t),r=new NotebookTextModelFacade(n),s=new NotebookTextModelFacade(i),l=$Ew(n.metadata,d=>!n.transientDocumentMetadata[d]),o=$Ew(i.metadata,d=>!i.transientDocumentMetadata[d]),a=JSON.stringify(l)!==JSON.stringify(o),h=new $jV(CellSequence.create(n),CellSequence.create(i)).ComputeDiff(!1);if(h.changes.length===0)return{metadataChanged:a,cellsDiff:h};if($ogc(r,s,{cellsDiff:{changes:h.changes,quitEarly:!1},metadataChanged:!1}).cellDiffInfo.every(d=>d.type==="modified"))return{metadataChanged:a,cellsDiff:h};let u=this.canComputeDiffWithCellIds(n,i);if(!u){const d=$yAc(i.cells,n.cells);d.some(C=>C.original!==-1)&&(this.updateCellIdsBasedOnMappings(d,n.cells,i.cells),u=!0)}if(!u)return{metadataChanged:a,cellsDiff:h};const f=new $jV(CellSequence.createWithCellId(n.cells),CellSequence.createWithCellId(i.cells)).ComputeDiff(!1),b=$ogc(r,s,{cellsDiff:{changes:f.changes,quitEarly:!1},metadataChanged:!1}).cellDiffInfo;let p=0;const m=[];return f.changes.forEach(d=>{if(!d.originalLength&&d.modifiedLength){const C=b.findIndex(g=>g.type==="insert"&&g.modifiedCellIndex===d.modifiedStart);b.slice(p,C).forEach(g=>{if(g.type==="unchanged"||g.type==="modified"){const w=n.cells[g.originalCellIndex],M=i.cells[g.modifiedCellIndex];(g.type==="modified"||w.getComparisonValue()!==M.getComparisonValue())&&m.push(new $gV(g.originalCellIndex,1,g.modifiedCellIndex,1))}}),m.push(d),p=C+1}else if(d.originalLength&&!d.modifiedLength){const C=b.findIndex(g=>g.type==="delete"&&g.originalCellIndex===d.originalStart);b.slice(p,C).forEach(g=>{if(g.type==="unchanged"||g.type==="modified"){const w=n.cells[g.originalCellIndex],M=i.cells[g.modifiedCellIndex];(g.type==="modified"||w.getComparisonValue()!==M.getComparisonValue())&&m.push(new $gV(g.originalCellIndex,1,g.modifiedCellIndex,1))}}),m.push(d),p=C+1}else{const C=b.findIndex(g=>g.type==="delete"&&g.originalCellIndex===d.originalStart||g.type==="insert"&&g.modifiedCellIndex===d.modifiedStart);b.slice(p,C).forEach(g=>{if(g.type==="unchanged"||g.type==="modified"){const w=n.cells[g.originalCellIndex],M=i.cells[g.modifiedCellIndex];(g.type==="modified"||w.getComparisonValue()!==M.getComparisonValue())&&m.push(new $gV(g.originalCellIndex,1,g.modifiedCellIndex,1))}}),m.push(d),p=C+1}}),b.slice(p).forEach(d=>{if(d.type==="unchanged"||d.type==="modified"){const C=n.cells[d.originalCellIndex],g=i.cells[d.modifiedCellIndex];(d.type==="modified"||C.getComparisonValue()!==g.getComparisonValue())&&m.push(new $gV(d.originalCellIndex,1,d.modifiedCellIndex,1))}}),{metadataChanged:a,cellsDiff:{changes:m,quitEarly:!1}}}canComputeDiffWithCellIds(e,t){return this.canComputeDiffWithCellInternalIds(e,t)||this.canComputeDiffWithCellMetadataIds(e,t)}canComputeDiffWithCellInternalIds(e,t){const n=e.cells.map((r,s)=>({index:s,id:r.internalMetadata?.internalId||""})),i=t.cells.map((r,s)=>({index:s,id:r.internalMetadata?.internalId||""}));return n.some(r=>!r.id)||i.some(r=>!r.id)?!1:n.some(r=>i.find(s=>s.id===r.id))}canComputeDiffWithCellMetadataIds(e,t){const n=e.cells.map((r,s)=>({index:s,id:r.metadata?.id||""})),i=t.cells.map((r,s)=>({index:s,id:r.metadata?.id||""}));return n.some(r=>!r.id)||i.some(r=>!r.id)||n.every(r=>!i.find(s=>s.id===r.id))?!1:(e.cells.map((r,s)=>{r.internalMetadata=r.internalMetadata||{},r.internalMetadata.internalId=r.metadata?.id||""}),t.cells.map((r,s)=>{r.internalMetadata=r.internalMetadata||{},r.internalMetadata.internalId=r.metadata?.id||""}),!0)}isOriginalCellMatchedWithModifiedCell(e){return(e.internalMetadata?.internalId||"").startsWith(PREFIX_FOR_UNMATCHED_ORIGINAL_CELLS)}updateCellIdsBasedOnMappings(e,t,n){const i=new Map;return t.map((r,s)=>{r.internalMetadata=r.internalMetadata||{internalId:""},r.internalMetadata.internalId=`${PREFIX_FOR_UNMATCHED_ORIGINAL_CELLS}${$8s()}`;const l=e.find(o=>o.original===s);l&&(r.internalMetadata.internalId=$8s(),i.set(l.modified,r.internalMetadata.internalId))}),n.map((r,s)=>{r.internalMetadata=r.internalMetadata||{internalId:""},r.internalMetadata.internalId=i.get(s)??$8s()}),!0}$canPromptRecommendation(e){const n=this.b(e).cells;for(let i=0;i<n.length;i++){const r=n[i];if(r.cellKind===CellKind.Markup||r.language!=="python")continue;const l=new $k1("import\\s*pandas|from\\s*pandas",!0,!1,null).parseSearchRequest();if(!l)continue;const o=new $w1;o.acceptChunk(r.getValue());const h=o.finish(!0).create(r.eol).textBuffer,c=h.getLineCount(),u=Math.min(c,20),f=new $mV(1,1,u,h.getLineLength(u)+1);if(h.findMatchesLineByLine(f,l,!0,1).length>0)return!0}return!1}b(e){return this.a[e]}};function $AAc(){return new $zAc}var NotebookTextModelFacade=class{constructor(e){this.notebook=e,this.cells=e.cells.map(t=>new NotebookCellTextModelFacade(t))}},NotebookCellTextModelFacade=class{get cellKind(){return this.a.cellKind}constructor(e){this.a=e}getHashValue(){return this.a.getComparisonValue()}equal(e){return e.cellKind!==this.cellKind?!1:this.getHashValue()===e.getHashValue()}};$Ftb($AAc);

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/b8f002c02d165600299a109bf21d02d139c52644/core/vs/workbench/contrib/notebook/common/services/notebookWebWorkerMain.js.map

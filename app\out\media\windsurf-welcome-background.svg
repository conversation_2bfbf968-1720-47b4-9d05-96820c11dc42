<svg width="2861" height="1137" fill="none" xmlns="http://www.w3.org/2000/svg"><g clip-path="url(#a)"><mask id="b" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="0" width="4682" height="2581"><path d="M4682 0H0v2581h4682V0Z" fill="#fff"/></mask><g mask="url(#b)"><path d="M.341 790.412C78.5 882.704 206.748 1002.21 397.962 1059.23 893.763 1207.04 1443.29 765.636 1955.45 554.066c533.26-220.297 1312.99-384.409 2532.36-149.928h.03M0 712.006c74.476 107.328 198.286 247.031 389.329 311.164C899.494 1194.42 1452.02 680.137 1955.94 398.336 2478 106.402 3314.95-141.25 4682.1 93.093M4487.81 404.15C3268.44 169.668 2488.7 333.78 1955.45 554.077 1443.28 765.647 893.758 1207.05 397.958 1059.24 206.744 1002.22 78.495 882.715.337 790.424m.341 78.406c81.817 77.255 214.381 176.73 405.913 226.45C887.686 1220.17 1435.06 851.399 1954.96 709.807c545.04-148.453 1267-229.231 2338.58 5.388M1.023 947.252c85.475 62.198 222.258 141.848 414.18 184.078 466.098 102.58 1012.057-193.75 1539.247-265.8 557.25-76.15 1220.98-74.054 2144.81 160.7l.03-.02m-4097.925-.55c89.157 47.16 230.061 107.21 422.495 141.73 450.881 80.89 995.89-150.53 1530.13-146.13 569.71 4.7 1174.98 81.1 1951.03 316m-194.28 311.06c-628.27-235.03-1175.08-401.06-1757.26-471.31-541.31-65.33-1085.514 86.33-1521.007 26.46-192.995-26.53-337.948-67.27-430.787-99.39m.342 78.41c96.497 17.08 245.474 38.8 439.077 57.01 420.057 39.54 963.245-40 1511.885 93.24 594.35 144.31 1083 391.45 1563.49 626.62M2.003 1182.5c96.522 17.07 245.474 38.8 439.078 57.02 420.056 39.54 963.239-39.98 1511.889 93.23 594.34 144.31 1083 391.46 1563.48 626.63v-.02M2.345 1260.91c100.179 2.05 253.18 5.11 447.369 14.67 404.644 19.89 946.416 12.11 1502.766 212.9 606.12 218.76 1037.01 546.64 1369.71 781.92-332.73-235.31-763.61-563.18-1369.72-781.92-556.35-200.79-1098.116-193.01-1502.76-212.9-194.165-9.56-347.19-12.62-447.37-14.67m.342 78.41c311.561-39.01 1102.628-97.38 1949.278 304.89 617.44 293.37 991.02 701.79 1175.97 937.24" stroke="url(#c)" stroke-miterlimit="10" stroke-dasharray="11.3 11.3"/></g></g><defs><radialGradient id="c" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="matrix(2719.75365 263.72518 -155.65278 1605.2211 -71 1027)"><stop offset=".194" stop-color="#fff" stop-opacity="0"/><stop offset=".406" stop-color="#fff"/><stop offset="1" stop-color="#fff" stop-opacity="0"/></radialGradient><clipPath id="a"><path fill="#fff" d="M0 0h2861v1137H0z"/></clipPath></defs></svg>
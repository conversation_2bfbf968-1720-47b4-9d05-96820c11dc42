const path = require('path');
const TerserPlugin = require('terser-webpack-plugin');

/**@type {import('webpack').Configuration} */
const webviewConfig = {
  mode: 'none', // this leaves the source code as close as possible to the original (when packaging we set this to 'production')
  context: __dirname,
  target: ['web', 'es2020'],
  entry: './src/panel/chat/chat.tsx',
  experiments: { outputModule: true },
  output: {
    path: path.resolve(__dirname, 'out'),
    filename: 'media/chat.js',
    libraryTarget: 'module',
    chunkFormat: 'module',
  },
  resolve: {
    mainFields: ['module', 'main'],
    extensions: ['.ts', '.js', '.tsx', '.jsx'],
    alias: {
      react: path.resolve(__dirname, './node_modules/react'),
      'react-dom': path.resolve(__dirname, './node_modules/react-dom'),
      '@exa/design-system/src': path.resolve(
        __dirname,
        './node_modules/@exa/design-system/dist',
      ),
      '@exa/api-server-connect-ts/src': path.resolve(
        __dirname,
        './node_modules/@exa/api-server-connect-ts/dist',
      ),
    },
    fallback: {
      tty: require.resolve('tty-browserify'),
    },
  },
  module: {
    rules: [
      {
        test: /\.tsx?$/,
        exclude: /node_modules/,
        loader: 'babel-loader',
        options: {
          presets: [
            '@babel/preset-env',
            ['@babel/preset-react', { runtime: 'automatic' }],
            '@babel/preset-typescript',
          ],
        },
      },
      {
        test: /\.css$/i,
        exclude: [/node_modules/],
        include: path.resolve(__dirname, 'src/panel/chat'),
        use: ['style-loader', 'css-loader', 'postcss-loader'],
      },
    ],
  },
  optimization: {
    sideEffects: false,

    // This will be enabled when building in `production`, making the minimizer effective.
    minimize: false,
    minimizer: [
      new TerserPlugin({
        parallel: true,
      }),
    ],
  },
  performance: {
    maxEntrypointSize: 10485760, // 10MB in bytes
    maxAssetSize: 10485760, // 10MB in bytes
  },
  // generate source maps for Sentry
  devtool: 'source-map',
};

module.exports = webviewConfig;

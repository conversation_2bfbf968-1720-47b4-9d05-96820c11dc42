{"name": "windsurf-remote-wsl", "displayName": "Windsurf Remote - WSL", "description": "Open any folder in the Windows Subsystem for Linux (WSL).", "version": "0.0.1", "publisher": "codeium", "engines": {"vscode": "^1.70.2"}, "extensionKind": ["ui"], "enabledApiProposals": ["resolvers", "contribViewsRemote"], "keywords": ["remote development", "remote", "wsl"], "api": "none", "activationEvents": ["onCommand:windsurfremotewsl.connect", "onCommand:windsurfremotewsl.connectInNewWindow", "onCommand:windsurfremotewsl.connectUsingDistro", "onCommand:windsurfremotewsl.connectUsingDistroInNewWindow", "onCommand:windsurfremotewsl.showLog", "onResolveRemoteAuthority:wsl", "onView:windsurfWslTargets"], "main": "./dist/extension.js", "contributes": {"configuration": {"title": "WSL", "properties": {"remote.WSL.serverDownloadUrlTemplate": {"type": "string", "description": "The URL from where the Windsurf server will be downloaded. The following variables can be substituted: ${os}, ${arch}, ${windsurfVersion}, ${vscodeVersion} ${commit}, ${quality}.", "scope": "application", "default": "https://windsurf-stable.codeiumdata.com/${os}-reh-${arch}/${quality}/${commit}/windsurf-reh-${os}-${arch}-${windsurfVersion}.tar.gz"}, "remote.WSL.experimental.disableServerChecksum": {"type": "boolean", "description": "Experimental: Disable the server checksum verification. This is only recommended for development and testing.", "scope": "application", "default": false}}}, "views": {"remote": [{"id": "windsurfWslTargets", "name": "WSL Targets (Windsurf)", "group": "targets@1", "when": "(isWindows && !isWeb)", "remoteName": "wsl"}]}, "commands": [{"command": "windsurfremotewsl.connect", "title": "Connect to WSL", "category": "Remote-WSL"}, {"command": "windsurfremotewsl.connectInNewWindow", "title": "Connect to WSL in New Window", "category": "Remote-WSL"}, {"command": "windsurfremotewsl.connectUsingDistro", "title": "Connect to WSL using Distro...", "category": "Remote-WSL"}, {"command": "windsurfremotewsl.connectUsingDistroInNewWindow", "title": "Connect to WSL using Distro in New Window...", "category": "Remote-WSL"}, {"command": "windsurfremotewsl.showLog", "title": "Show Log", "category": "Remote-WSL"}, {"command": "windsurfremotewsl.explorer.emptyWindowInNewWindow", "title": "Connect in New Window", "icon": "$(empty-window)"}, {"command": "windsurfremotewsl.explorer.emptyWindowInCurrentWindow", "title": "Connect in Current Window"}, {"command": "windsurfremotewsl.explorer.reopenFolderInCurrentWindow", "title": "Open in Current Window"}, {"command": "windsurfremotewsl.explorer.reopenFolderInNewWindow", "title": "Open in New Window", "icon": "$(folder-opened)"}, {"command": "windsurfremotewsl.explorer.deleteFolderHistoryItem", "title": "Remove From Recent List", "icon": "$(x)"}, {"command": "windsurfremotewsl.explorer.refresh", "title": "Refresh", "icon": "$(refresh)"}, {"command": "windsurfremotewsl.explorer.addDistro", "title": "Add a Distro", "icon": "$(plus)"}, {"command": "windsurfremotewsl.explorer.setDefaultDistro", "title": "Set as <PERSON><PERSON><PERSON>"}, {"command": "windsurfremotewsl.explorer.deleteDistro", "title": "Delete Distro"}], "resourceLabelFormatters": [{"scheme": "vscode-remote", "authority": "wsl+*", "formatting": {"label": "${path}", "separator": "/", "tildify": true, "workspaceSuffix": "WSL"}}], "menus": {"statusBar/remoteIndicator": [{"command": "windsurfremotewsl.connect", "when": "(isWindows && !isWeb)", "group": "remote_20_wsl_1general@1"}, {"command": "windsurfremotewsl.connectUsingDistro", "when": "(isWindows && !isWeb)", "group": "remote_20_wsl_1general@2"}, {"command": "windsurfremotewsl.showLog", "when": "remoteName =~ /^wsl$/", "group": "remote_20_wsl_1general@4"}], "commandPalette": [{"command": "windsurfremotewsl.connect", "when": "(isWindows && !isWeb)"}, {"command": "windsurfremotewsl.connectInNewWindow", "when": "(isWindows && !isWeb)"}, {"command": "windsurfremotewsl.connectUsingDistro", "when": "(isWindows && !isWeb)"}, {"command": "windsurfremotewsl.connectUsingDistroInNewWindow", "when": "(isWindows && !isWeb)"}, {"command": "windsurfremotewsl.explorer.refresh", "when": "false"}, {"command": "windsurfremotewsl.explorer.addDistro", "when": "false"}, {"command": "windsurfremotewsl.explorer.emptyWindowInNewWindow", "when": "false"}, {"command": "windsurfremotewsl.explorer.emptyWindowInCurrentWindow", "when": "false"}, {"command": "windsurfremotewsl.explorer.reopenFolderInCurrentWindow", "when": "false"}, {"command": "windsurfremotewsl.explorer.reopenFolderInNewWindow", "when": "false"}, {"command": "windsurfremotewsl.explorer.deleteFolderHistoryItem", "when": "false"}, {"command": "windsurfremotewsl.explorer.setDefaultDistro", "when": "false"}, {"command": "windsurfremotewsl.explorer.deleteDistro", "when": "false"}], "view/title": [{"command": "windsurfremotewsl.explorer.addDistro", "when": "view == windsurfWslTargets", "group": "navigation"}, {"command": "windsurfremotewsl.explorer.refresh", "when": "view == windsurfWslTargets", "group": "navigation"}], "view/item/context": [{"command": "windsurfremotewsl.explorer.emptyWindowInNewWindow", "when": "viewItem =~ /^windsurfremotewsl.explorer.distro$/", "group": "inline@1"}, {"command": "windsurfremotewsl.explorer.emptyWindowInNewWindow", "when": "viewItem =~ /^windsurfremotewsl.explorer.distro$/", "group": "navigation@2"}, {"command": "windsurfremotewsl.explorer.emptyWindowInCurrentWindow", "when": "viewItem =~ /^windsurfremotewsl.explorer.distro$/", "group": "navigation@1"}, {"command": "windsurfremotewsl.explorer.setDefaultDistro", "when": "viewItem =~ /^windsurfremotewsl.explorer.distro$/", "group": "management@1"}, {"command": "windsurfremotewsl.explorer.deleteDistro", "when": "viewItem =~ /^windsurfremotewsl.explorer.distro$/", "group": "management@2"}, {"command": "windsurfremotewsl.explorer.reopenFolderInNewWindow", "when": "viewItem == windsurfremotewsl.explorer.folder", "group": "inline@1"}, {"command": "windsurfremotewsl.explorer.reopenFolderInNewWindow", "when": "viewItem == windsurfremotewsl.explorer.folder", "group": "navigation@2"}, {"command": "windsurfremotewsl.explorer.reopenFolderInCurrentWindow", "when": "viewItem == windsurfremotewsl.explorer.folder", "group": "navigation@1"}, {"command": "windsurfremotewsl.explorer.deleteFolderHistoryItem", "when": "viewItem =~ /^windsurfremotewsl.explorer.folder/", "group": "navigation@3"}, {"command": "windsurfremotewsl.explorer.deleteFolderHistoryItem", "when": "viewItem =~ /^windsurfremotewsl.explorer.folder/", "group": "inline@2"}]}}, "prettier": {"singleQuote": true, "useTabs": true, "tabWidth": 4, "trailingComma": "all", "printWidth": 80}}
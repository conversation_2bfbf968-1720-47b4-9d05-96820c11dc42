(()=>{"use strict";var e={927:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.deactivate=t.activate=void 0;const u=i(r(398)),s=r(360),c=i(r(154)),a={},p={fs:r(896),path:r(928),util:r(23),child_process:r(317),os:r(857),http:r(611),https:r(692),dns:r(250),querystring:r(480),events:r(434),vscode:u};t.activate=async function(e){const t=u.commands.registerCommand("windsurf-code-executor.executeCode",(async e=>{const t=s.CodeExecutorOutputChannel.getInstance(),r=[],n={...globalThis,toolkit:a,...p,require:e=>{if(p.hasOwnProperty(e))return p[e];throw new Error(`Module ${e} is not allowed in sandbox`)},output:e=>{r.push(e),t.appendLine(e)}},o=c.createContext(n),i=`(async () => { ${e} })();`;try{const e=new c.Script(i);await e.runInContext(o)}catch(e){const t=e?.message??String(e);r.push(`CODE ERROR: ${t}`)}return r.join("\n")}));e.subscriptions.push(t)},t.deactivate=function(){}},360:function(e,t,r){var n=this&&this.__createBinding||(Object.create?function(e,t,r,n){void 0===n&&(n=r);var o=Object.getOwnPropertyDescriptor(t,r);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[r]}}),Object.defineProperty(e,n,o)}:function(e,t,r,n){void 0===n&&(n=r),e[n]=t[r]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var r in e)"default"!==r&&Object.prototype.hasOwnProperty.call(e,r)&&n(t,e,r);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.CodeExecutorOutputChannel=void 0;const u=i(r(398));class s{static getInstance(){return s.instance||(this.instance=u.window.createOutputChannel("Cascade JS Code Execution",{log:!0})),this.instance}}t.CodeExecutorOutputChannel=s},398:e=>{e.exports=require("vscode")},317:e=>{e.exports=require("child_process")},250:e=>{e.exports=require("dns")},434:e=>{e.exports=require("events")},896:e=>{e.exports=require("fs")},611:e=>{e.exports=require("http")},692:e=>{e.exports=require("https")},857:e=>{e.exports=require("os")},928:e=>{e.exports=require("path")},480:e=>{e.exports=require("querystring")},23:e=>{e.exports=require("util")},154:e=>{e.exports=require("vm")}},t={},r=function r(n){var o=t[n];if(void 0!==o)return o.exports;var i=t[n]={exports:{}};return e[n].call(i.exports,i,i.exports,r),i.exports}(927),n=exports;for(var o in r)n[o]=r[o];r.__esModule&&Object.defineProperty(n,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/b8f002c02d165600299a109bf21d02d139c52644/extensions/windsurf-code-executor/dist/extension.js.map
{"name": "windsurf", "displayName": "Windsurf", "description": "The modern coding superpower: free AI code acceleration plugin for your favorite languages. Type less. Code more. Ship faster.", "version": "0.2.0", "publisher": "codeium", "engines": {"vscode": "^1.68.0"}, "main": "./dist/extension.js", "activationEvents": ["*"], "enabledApiProposals": ["contribSourceControlInputBoxMenu", "windsurfEditorNudge", "windsurfAuth", "inlineCompletionsAdditions", "windsurfTerminalSuggestions"], "extensionDependencies": ["vscode.git"], "contributes": {"customEditors": [{"viewType": "windsurf.workflowEditor", "displayName": "Workflow Editor", "selector": [{"filenamePattern": "**/.windsurf/workflows/**/*.md"}, {"filenamePattern": "**/.codeium/windsurf/global_workflows/*.md"}], "priority": "default"}, {"viewType": "windsurf.ruleEditor", "displayName": "Rule Editor", "selector": [{"filenamePattern": "**/.windsurf/rules/**/*.md"}], "priority": "default"}], "authentication": [{"id": "windsurf_auth", "label": "Windsurf"}], "commands": [{"command": "windsurf.login", "title": "Log in to Windsurf", "category": "Windsurf"}, {"command": "windsurf.loginWithAuthToken", "title": "Provide <PERSON> (Backup Login)", "category": "Windsurf"}, {"command": "windsurf.importVSCodeSettings", "title": "Import VS Code settings", "category": "Windsurf"}, {"command": "windsurf.importVSCodeExtensions", "title": "Import VS Code extensions", "category": "Windsurf"}, {"command": "windsurf.importVSCodeRecentWorkspaces", "title": "Import VS Code recent workspaces", "category": "Windsurf"}, {"command": "windsurf.importCursorSettings", "title": "Import Cursor settings", "category": "Windsurf"}, {"command": "windsurf.importCursorExtensions", "title": "Import Cursor extensions", "category": "Windsurf"}, {"command": "windsurf.importWindsurfSettings", "title": "Import Windsurf settings", "category": "Windsurf"}, {"command": "windsurf.importWindsurfExtensions", "title": "Import Windsurf extensions", "category": "Windsurf"}, {"command": "windsurf.generateCommitMessage", "title": "Generate Commit Message", "category": "Windsurf", "icon": "$(sparkle)"}, {"command": "windsurf.prioritized.chat.open", "title": "Open Chat with <PERSON>", "category": "Windsurf"}, {"command": "windsurf.sendTerminalToChat", "title": "Send Terminal to Chat", "category": "Windsurf"}, {"command": "windsurf.restartLanguageServer", "title": "Restart Language Server", "category": "Windsurf"}, {"command": "windsurf.resetProductEducation", "title": "Reset Product Education", "category": "Windsurf"}, {"command": "windsurf.openProfile", "title": "View Account", "category": "Windsurf"}, {"command": "windsurf.openBillingPage", "title": "Purchase Add-on Credits", "category": "Windsurf"}, {"command": "windsurf.openAutoRefillPage", "title": "Enable Auto Refill", "category": "Windsurf"}, {"command": "windsurf.downloadDiagnostics", "title": "Download Windsurf Diagnostics", "category": "Windsurf"}, {"command": "windsurf.copyApi<PERSON>ey", "title": "Copy API Key to Clipboard", "category": "Windsurf"}, {"command": "windsurf.openChangeLog", "title": "Open Changelog", "category": "Windsurf"}, {"command": "windsurf.triggerAutoCascade", "title": "Ask Auto Cascade", "category": "Windsurf"}, {"command": "windsurf.importRulesFromCursor", "title": "Import rules from Cursor", "category": "Windsurf"}, {"command": "windsurf.triggerCascade", "title": "Start Cascade Conversation", "category": "Windsurf"}, {"command": "windsurf.createWorkflow", "title": "Create New Workflow", "category": "Windsurf"}, {"command": "windsurf.createGlobalWorkflow", "title": "Create New Global Workflow", "category": "Windsurf"}, {"command": "windsurf.createRule", "title": "Create New Rule", "category": "Windsurf"}, {"command": "windsurf.openBrowser", "title": "Open Windsurf Browser", "category": "Windsurf"}, {"command": "windsurf.setServiceUrl", "title": "Set Service URL", "category": "Windsurf", "when": "windsurf:isSecureFlavor"}], "configuration": {"title": "Windsurf Editor", "properties": {"windsurf.marketplaceExtensionGalleryServiceURL": {"type": "string", "restricted": true, "default": "https://marketplace.windsurf.com/vscode/gallery", "markdownDescription": "Changes the base URL for marketplace search results. [Available Options](https://github.com/VSCodium/vscodium/blob/master/docs/index.md#extensions-marketplace). You must restart Windsurf to use the new marketplace after changing this value."}, "windsurf.marketplaceGalleryItemURL": {"type": "string", "restricted": true, "default": "https://marketplace.windsurf.com/vscode/item", "markdownDescription": "Changes the base URL on each extension page. [Available Options](https://github.com/VSCodium/vscodium/blob/master/docs/index.md#extensions-marketplace). You must restart Windsurf to use the new marketplace after changing this value."}, "windsurf.searchMaxWorkspaceFileCount": {"type": "integer", "default": 5000, "description": "Windsurf will attempt to compute embeddings for workspaces up to this many files. This file count ignores .gitignore and binary files. Raising this limit from the default value may lead to performance issues. Values 0 or below will be treated as unlimited."}, "windsurf.serviceUrl": {"type": "string", "default": "", "markdownDescription": "**For Windsurf Secure only**: Base URL of your Windsurf service (e.g., `https://your-company.windsurf.com`). Reload Windsurf to apply this setting.", "when": "windsurf:isSecureFlavor"}, "windsurf.enableCursorImportCursor": {"type": "boolean", "default": false, "description": "Enable the Cursor-import commands in the palette"}}}, "menus": {"commandPalette": [{"command": "windsurf.triggerAutoCascade", "when": "workbench.view.extension.codeiumDev.defaultViewContainerLocation"}, {"command": "windsurf.importRulesFromCursor"}, {"command": "windsurf.createWorkflow"}, {"command": "windsurf.createRule"}, {"command": "windsurf.openBrowser", "when": "windsurf.browserFeatureEnabled"}, {"command": "windsurf.importCursorSettings", "when": "windsurf:enableCursorImportCursor", "group": "2_import"}, {"command": "windsurf.importCursorExtensions", "when": "windsurf:enableCursorImportCursor", "group": "2_import"}, {"command": "windsurf.importVSCodeSettings", "when": "windsurf:enableVSCodeImport", "group": "2_import"}, {"command": "windsurf.importVSCodeExtensions", "when": "windsurf:enableVSCodeImport", "group": "2_import"}, {"command": "windsurf.copyApi<PERSON>ey", "when": "windsurf:isInternalUser"}], "editor/context": [{"command": "windsurf.prioritized.chat.open", "group": "CodeiumGroup@1", "when": "editorTextFocus && editorHasSelection"}]}, "keybindings": [{"command": "windsurf.prioritized.chat.open", "key": "ctrl+l", "mac": "cmd+l", "when": "!terminalFocus && !windsurf.deepwikiPanel.focused"}, {"command": "windsurf.sendTerminalToChat", "key": "ctrl+l", "mac": "cmd+l", "when": "terminalFocus && !windsurf.deepwikiPanel.focused"}, {"command": "windsurf.prioritized.terminalCommand.open", "key": "ctrl+i", "mac": "cmd+i", "when": "terminalFocus && !cascadeUiTerminalFocus"}, {"command": "windsurf.terminalCommand.run", "key": "ctrl+enter", "mac": "cmd+enter", "when": "terminalFocus && windsurf.canTriggerTerminalCommandAction"}, {"command": "windsurf.terminalCommand.accept", "key": "alt+enter", "when": "terminalFocus && windsurf.canTriggerTerminalCommandAction"}, {"command": "windsurf.terminalCommand.reject", "key": "ctrl+backspace", "mac": "cmd+backspace", "when": "terminalFocus && windsurf.canTriggerTerminalCommandAction"}, {"command": "windsurf.prioritized.command.open", "key": "ctrl+i", "mac": "cmd+i", "when": "editorTextFocus && !windsurf.interactiveCascade.enabled && !windsurf.isAgentModeInputBoxFocused"}, {"command": "windsurf.command.accept", "key": "ctrl+enter", "mac": "cmd+enter", "when": "editorTextFocus && !editorHasSelection && windsurf.canAcceptOrRejectCommand"}, {"command": "windsurf.command.reject", "key": "ctrl+backspace", "mac": "cmd+backspace", "when": "editorTextFocus && !editorHasSelection && windsurf.canAcceptOrRejectCommand"}, {"command": "windsurf.triggerCascade", "key": "ctrl+shift+i", "mac": "cmd+shift+i", "when": "!windsurf.interactiveCascade.enabled"}, {"command": "windsurf.prioritized.cascadeAcceptAllInFile", "key": "ctrl+enter", "mac": "cmd+enter", "when": "editorTextFocus && windsurf.canAcceptOrRejectAllCascadeEditsInFile"}, {"command": "windsurf.prioritized.cascadeRejectAllInFile", "key": "ctrl+backspace", "mac": "cmd+backspace", "when": "editorTextFocus && windsurf.canAcceptOrRejectAllCascadeEditsInFile"}, {"command": "windsurf.prioritized.cascadeFocusNextHunk", "key": "alt+j", "mac": "alt+j", "when": "editorTextFocus && windsurf.canAcceptOrRejectAllCascadeEditsInFile"}, {"command": "windsurf.prioritized.cascadeFocusPreviousHunk", "key": "alt+k", "mac": "alt+k", "when": "editorTextFocus && windsurf.canAcceptOrRejectAllCascadeEditsInFile"}, {"command": "windsurf.prioritized.cascadeAcceptFocusedHunk", "key": "alt+enter", "mac": "alt+enter", "when": "windsurf.canAcceptOrRejectFocusedHunk"}, {"command": "windsurf.prioritized.cascadeRejectFocusedHunk", "key": "alt+shift+backspace", "mac": "alt+shift+backspace", "when": "windsurf.canAcceptOrRejectFocusedHunk"}, {"command": "editor.action.inlineSuggest.trigger", "key": "alt+\\", "when": "editorTextFocus && !editorHasSelection && !inlineSuggestionsVisible"}, {"command": "windsurf.prioritized.supercompleteAccept", "key": "tab", "mac": "tab", "when": "editorTextFocus && !windsurf.interactiveCascade.isCursorPosInSuggestedAction && ((inlineSuggestionVisible && (inlineSuggestionHasIndentationLessThanTabSize || (vim.active && vim.mode == 'Normal') || (neovim.init && neovim.mode == 'normal'))) || ((windsurf.richGhostTextShown || windsurf.sideHintShown || windsurf.tabActionShown)))"}, {"command": "windsurf.prioritized.supercompleteEscape", "key": "escape", "mac": "escape", "when": "!(editorTextFocus && ((vim.active && vim.mode != 'Normal') || (neovim.init && neovim.mode != 'normal'))) && ((windsurf.richGhostTextShown || windsurf.sideHintShown || windsurf.tabActionShown) || windsurf.postApplyDecorationShown || inlineSuggestionVisible) && !suggestWidgetVisible && !windsurf.interactiveCascade.isCursorPosInSuggestedAction"}, {"key": "escape", "command": "extension.vim_escape", "when": "editorTextFocus && vim.active && !inDebugRepl"}, {"command": "windsurf.prioritized.chat.openNewConversation", "key": "ctrl+shift+l", "mac": "cmd+shift+l", "when": "!terminalFocus"}, {"command": "windsurf.prioritized.chat.openNewConversation", "key": "ctrl+n", "mac": "cmd+n", "when": "windsurf.cascadePanel.focused"}, {"command": "windsurf.prioritized.interactiveCascade.editCurrentLocation", "key": "alt+\\", "mac": "alt+\\", "when": "windsurf.interactiveCascade.enabled && editorTextFocus"}, {"command": "windsurf.prioritized.interactiveCascade.editCurrentLocationWithInstruction", "key": "cmd+alt+\\", "mac": "cmd+alt+\\", "when": "windsurf.interactiveCascade.enabled && editorTextFocus"}, {"command": "windsurf.prioritized.interactiveCascade.refresh", "key": "ctrl+alt+r", "mac": "cmd+alt+r", "when": "windsurf.interactiveCascade.enabled && editorTextFocus"}, {"command": "windsurf.prioritized.interactiveCascade.continue", "key": "ctrl+alt+c", "mac": "cmd+alt+c", "when": "windsurf.interactiveCascade.enabled && editorTextFocus"}, {"command": "windsurf.prioritized.interactiveCascade.acceptAllDiffZones", "key": "alt+o", "mac": "alt+o", "when": "windsurf.interactiveCascade.enabled && editorTextFocus"}, {"command": "windsurf.prioritized.interactiveCascade.toggleMostRecentInactive", "key": "alt+p", "mac": "alt+p", "when": "windsurf.interactiveCascade.enabled"}, {"command": "windsurf.prioritized.interactiveCascade.toggleMostRecentActive", "key": "alt+l", "mac": "alt+l", "when": "windsurf.interactiveCascade.enabled"}, {"command": "windsurf.prioritized.interactiveCascade.debug", "key": "alt+d", "when": "windsurf.interactiveCascade.enabled", "mac": "alt+d"}, {"command": "windsurf.prioritized.interactiveCascade.reset", "key": "ctrl+alt+d", "mac": "cmd+alt+d", "when": "windsurf.interactiveCascade.enabled"}, {"command": "windsurf.cascade.acceptCascadeStep", "key": "alt+enter", "when": "!editorTextFocus"}, {"command": "windsurf.cascade.rejectCascadeStep", "key": "alt+shift+backspace", "when": "!editorTextFocus"}, {"command": "windsurf.cascade.pressMicrophone", "key": "ctrl+shift+m", "mac": "cmd+shift+m", "when": "!terminalFocus && windsurf.cascadePanel.visible"}, {"command": "windsurf.prioritized.chat.toggleWriteChatMode", "key": "ctrl+.", "mac": "cmd+.", "when": "!terminalFocus && windsurf.cascadePanel.focused"}], "languages": [{"id": "jsonc", "filenames": ["mcp_config.json"]}], "jsonValidation": [{"fileMatch": "**/mcp_config.json", "url": "./schemas/mcp_config.schema.json"}]}, "resolutions": {"@exa/api-server-connect-ts": "file:../../../exa/api_server_connect_ts", "@exa/design-system": "file:../../../exa/design_system"}, "prettier": {"singleQuote": true, "plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["vscode", "<THIRD_PARTY_MODULES>", "^@exa/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}}
/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/html{font-size:13px}.mac{font-family:-apple-system,BlinkMacSystemFont,sans-serif}.mac:lang(zh-Hans){font-family:-apple-system,BlinkMacSystemFont,PingFang SC,Hiragino Sans GB,sans-serif}.mac:lang(zh-Hant){font-family:-apple-system,BlinkMacSystemFont,PingFang TC,sans-serif}.mac:lang(ja){font-family:-apple-system,BlinkMacSystemFont,Hiragino Kaku Gothic Pro,sans-serif}.mac:lang(ko){font-family:-apple-system,<PERSON><PERSON>MacS<PERSON>temFont,Apple SD Gothic Neo,Nanum Gothic,AppleGothic,sans-serif}.windows{font-family:Segoe WPC,Segoe UI,sans-serif}.windows:lang(zh-Hans){font-family:Segoe WPC,Segoe UI,Microsoft YaHei,sans-serif}.windows:lang(zh-Hant){font-family:Segoe WPC,Segoe UI,Microsoft Jhenghei,sans-serif}.windows:lang(ja){font-family:Segoe WPC,Segoe UI,Yu Gothic UI,Meiryo UI,sans-serif}.windows:lang(ko){font-family:Segoe WPC,Segoe UI,Malgun Gothic,Dotom,sans-serif}.linux{font-family:system-ui,Ubuntu,Droid Sans,sans-serif}.linux:lang(zh-Hans){font-family:system-ui,Ubuntu,Droid Sans,Source Han Sans SC,Source Han Sans CN,Source Han Sans,sans-serif}.linux:lang(zh-Hant){font-family:system-ui,Ubuntu,Droid Sans,Source Han Sans TC,Source Han Sans TW,Source Han Sans,sans-serif}.linux:lang(ja){font-family:system-ui,Ubuntu,Droid Sans,Source Han Sans J,Source Han Sans JP,Source Han Sans,sans-serif}.linux:lang(ko){font-family:system-ui,Ubuntu,Droid Sans,Source Han Sans K,Source Han Sans JR,Source Han Sans,UnDotum,FBaekmuk Gulim,sans-serif}body{color:#ccc;height:100%;margin:0;padding:0;user-select:none;width:100%}.cpu{width:60px}.pid{width:50px}.memory{width:90px}.monaco-list:focus{outline:0}.monaco-list-row:first-of-type{border-bottom:1px solid}.row{display:flex}.centered{text-align:center}.nameLabel{overflow:hidden;text-align:left;text-overflow:ellipsis;width:calc(100% - 185px)}.data{font-weight:400;height:22px;padding-left:.5rem;text-align:left;white-space:pre}.error{padding-left:20px;white-space:nowrap}@font-face{font-display:block;font-family:codicon;src:url(../../../../media/codicon.ttf?5d4d76ab2ce5108968ad644d591a16a6) format("truetype")}.codicon[class*=codicon-]{display:inline-block;font:normal normal normal 16px/1 codicon;text-align:center;text-decoration:none;text-rendering:auto;text-transform:none;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;user-select:none;-webkit-user-select:none}.codicon-wrench-subaction{opacity:.5}@keyframes codicon-spin{to{transform:rotate(1turn)}}.codicon-gear.codicon-modifier-spin,.codicon-loading.codicon-modifier-spin,.codicon-notebook-state-executing.codicon-modifier-spin,.codicon-sync.codicon-modifier-spin{animation:codicon-spin 1.5s steps(30) infinite}.codicon-modifier-disabled{opacity:.4}.codicon-loading,.codicon-tree-item-loading:before{animation-duration:1s!important;animation-timing-function:cubic-bezier(.53,.21,.29,.67)!important}.monaco-aria-container{left:-999em;position:absolute}.monaco-list{height:100%;position:relative;white-space:nowrap;width:100%}.monaco-list.mouse-support{user-select:none;-webkit-user-select:none}.monaco-list>.monaco-scrollable-element{height:100%}.monaco-list-rows{height:100%;position:relative;width:100%}.monaco-list.horizontal-scrolling .monaco-list-rows{min-width:100%;width:auto}.monaco-list-row{box-sizing:border-box;overflow:hidden;position:absolute;width:100%}.monaco-list.mouse-support .monaco-list-row{cursor:pointer;touch-action:none}.monaco-list .monaco-scrollable-element>.scrollbar.vertical,.monaco-pane-view>.monaco-split-view2.vertical>.monaco-scrollable-element>.scrollbar.vertical{z-index:14}.monaco-list-row.scrolling{display:none!important}.monaco-list.element-focused,.monaco-list.selection-multiple,.monaco-list.selection-single{outline:0!important}.monaco-list-type-filter-message{box-sizing:border-box;height:100%;left:0;opacity:.7;padding:40px 1em 1em;pointer-events:none;position:absolute;text-align:center;top:0;white-space:normal;width:100%}.monaco-list-type-filter-message:empty{display:none}.monaco-scrollable-element>.scrollbar>.scra{cursor:pointer;font-size:11px!important}.monaco-scrollable-element>.visible{background:transparent;opacity:1;transition:opacity .1s linear;z-index:11}.monaco-scrollable-element>.invisible{opacity:0;pointer-events:none}.monaco-scrollable-element>.invisible.fade{transition:opacity .8s linear}.monaco-scrollable-element>.shadow{display:none;position:absolute}.monaco-scrollable-element>.shadow.top{box-shadow:var(--vscode-scrollbar-shadow) 0 6px 6px -6px inset;display:block;height:3px;left:3px;top:0;width:100%}.monaco-scrollable-element>.shadow.left{box-shadow:var(--vscode-scrollbar-shadow) 6px 0 6px -6px inset;display:block;height:100%;left:0;top:3px;width:3px}.monaco-scrollable-element>.shadow.top-left-corner{display:block;height:3px;left:0;top:0;width:3px}.monaco-scrollable-element>.shadow.top.left{box-shadow:var(--vscode-scrollbar-shadow) 6px 0 6px -6px inset}.monaco-scrollable-element>.scrollbar>.slider{background:var(--vscode-scrollbarSlider-background)}.monaco-scrollable-element>.scrollbar>.slider:hover{background:var(--vscode-scrollbarSlider-hoverBackground)}.monaco-scrollable-element>.scrollbar>.slider.active{background:var(--vscode-scrollbarSlider-activeBackground)}.monaco-drag-image{background-color:var(--vscode-list-activeSelectionBackground);border-radius:10px;color:var(--vscode-list-activeSelectionForeground);display:inline-block;font-size:12px;max-width:120px;outline:1px solid var(--vscode-list-focusOutline);outline-offset:-1px;overflow:hidden;padding:1px 7px;position:absolute;text-overflow:ellipsis;white-space:nowrap;z-index:1000}.monaco-select-box-dropdown-padding{--dropdown-padding-top:1px;--dropdown-padding-bottom:1px}.hc-black .monaco-select-box-dropdown-padding,.hc-light .monaco-select-box-dropdown-padding{--dropdown-padding-top:3px;--dropdown-padding-bottom:4px}.monaco-select-box-dropdown-container{box-sizing:border-box;display:none}.monaco-select-box-dropdown-container>.select-box-details-pane>.select-box-description-markdown *{margin:0}.monaco-select-box-dropdown-container>.select-box-details-pane>.select-box-description-markdown a:focus{outline:1px solid -webkit-focus-ring-color;outline-offset:-1px}.monaco-select-box-dropdown-container>.select-box-details-pane>.select-box-description-markdown code{font-family:var(--monaco-monospace-font);line-height:15px}.monaco-select-box-dropdown-container.visible{border-bottom-left-radius:3px;border-bottom-right-radius:3px;display:flex;flex-direction:column;overflow:hidden;text-align:left;width:1px}.monaco-select-box-dropdown-container>.select-box-dropdown-list-container{align-self:flex-start;box-sizing:border-box;flex:0 0 auto;overflow:hidden;padding-bottom:var(--dropdown-padding-bottom);padding-left:1px;padding-right:1px;padding-top:var(--dropdown-padding-top);width:100%}.monaco-select-box-dropdown-container>.select-box-details-pane{padding:5px}.hc-black .monaco-select-box-dropdown-container>.select-box-dropdown-list-container{padding-bottom:var(--dropdown-padding-bottom);padding-top:var(--dropdown-padding-top)}.monaco-select-box-dropdown-container>.select-box-dropdown-list-container .monaco-list .monaco-list-row{cursor:pointer}.monaco-select-box-dropdown-container>.select-box-dropdown-list-container .monaco-list .monaco-list-row>.option-text{float:left;overflow:hidden;padding-left:3.5px;text-overflow:ellipsis;white-space:nowrap}.monaco-select-box-dropdown-container>.select-box-dropdown-list-container .monaco-list .monaco-list-row>.option-detail{float:left;opacity:.7;overflow:hidden;padding-left:3.5px;text-overflow:ellipsis;white-space:nowrap}.monaco-select-box-dropdown-container>.select-box-dropdown-list-container .monaco-list .monaco-list-row>.option-decorator-right{float:right;overflow:hidden;padding-right:10px;text-overflow:ellipsis;white-space:nowrap}.monaco-select-box-dropdown-container>.select-box-dropdown-list-container .monaco-list .monaco-list-row>.visually-hidden{height:1px;left:-10000px;overflow:hidden;position:absolute;top:auto;width:1px}.monaco-select-box-dropdown-container>.select-box-dropdown-container-width-control{align-self:flex-start;flex:1 1 auto;opacity:0}.monaco-select-box-dropdown-container>.select-box-dropdown-container-width-control>.width-control-div{max-height:0;overflow:hidden}.monaco-select-box-dropdown-container>.select-box-dropdown-container-width-control>.width-control-div>.option-text-width-control{padding-left:4px;padding-right:8px;white-space:nowrap}.monaco-select-box{border-radius:2px;cursor:pointer;width:100%}.monaco-select-box-dropdown-container{font-size:13px;font-weight:400;text-transform:none}.monaco-action-bar .action-item.select-container{cursor:default}.monaco-action-bar .action-item .monaco-select-box{cursor:pointer;min-height:18px;min-width:100px;padding:2px 23px 2px 8px}.mac .monaco-action-bar .action-item .monaco-select-box{border-radius:5px;font-size:11px}.monaco-action-bar{height:100%;white-space:nowrap}.monaco-action-bar .actions-container{align-items:center;display:flex;height:100%;margin:0 auto;padding:0;width:100%}.monaco-action-bar.vertical .actions-container{display:flex;flex-direction:column}.monaco-action-bar .action-item{align-items:center;cursor:pointer;display:block;justify-content:center;position:relative}.monaco-action-bar .action-item.disabled{cursor:default}.monaco-action-bar .action-item .codicon,.monaco-action-bar .action-item .icon{display:block}.monaco-action-bar .action-item .codicon{align-items:center;display:flex;height:16px;width:16px}.monaco-action-bar .action-label{border-radius:5px;display:flex;font-size:12px;padding:3px}.monaco-action-bar .action-item.disabled .action-label:not(.icon),.monaco-action-bar .action-item.disabled .action-label:not(.icon):before,.monaco-action-bar .action-item.disabled .action-label:not(.icon):hover{color:var(--vscode-disabledForeground)}.monaco-action-bar .action-item.disabled .action-label.icon,.monaco-action-bar .action-item.disabled .action-label.icon:before,.monaco-action-bar .action-item.disabled .action-label.icon:hover{opacity:.6}.monaco-action-bar.vertical{text-align:left}.monaco-action-bar.vertical .action-item{display:block}.monaco-action-bar.vertical .action-label.separator{border-bottom:1px solid #bbb;display:block;margin-left:.8em;margin-right:.8em;padding-top:1px}.monaco-action-bar .action-item .action-label.separator{background-color:#bbb;cursor:default;height:16px;margin:5px 4px!important;min-width:1px;padding:0;width:1px}.secondary-actions .monaco-action-bar .action-label{margin-left:6px}.monaco-action-bar .action-item.select-container{align-items:center;display:flex;flex:1;justify-content:center;margin-right:10px;max-width:170px;min-width:60px;overflow:hidden}.monaco-action-bar .action-item.action-dropdown-item{display:flex}.monaco-action-bar .action-item.action-dropdown-item>.action-dropdown-item-separator{align-items:center;cursor:default;display:flex}.monaco-action-bar .action-item.action-dropdown-item>.action-dropdown-item-separator>div{width:1px}.monaco-custom-toggle{border:1px solid transparent;border-radius:3px;box-sizing:border-box;cursor:pointer;float:left;height:20px;margin-left:2px;overflow:hidden;padding:1px;user-select:none;-webkit-user-select:none;width:20px}.monaco-custom-toggle:hover{background-color:var(--vscode-inputOption-hoverBackground)}.hc-black .monaco-custom-toggle:hover,.hc-light .monaco-custom-toggle:hover{border:1px dashed var(--vscode-focusBorder)}.hc-black .monaco-custom-toggle,.hc-black .monaco-custom-toggle:hover,.hc-light .monaco-custom-toggle,.hc-light .monaco-custom-toggle:hover{background:none}.monaco-custom-toggle.monaco-checkbox{background-size:16px!important;border:1px solid transparent;border-radius:3px;height:18px;margin-left:0;margin-right:9px;opacity:1;padding:0;width:18px}.monaco-action-bar .checkbox-action-item{align-items:center;border-radius:2px;display:flex;padding-right:2px}.monaco-action-bar .checkbox-action-item:hover{background-color:var(--vscode-toolbar-hoverBackground)}.monaco-action-bar .checkbox-action-item>.monaco-custom-toggle.monaco-checkbox{margin-right:4px}.monaco-action-bar .checkbox-action-item>.checkbox-label{font-size:12px}.monaco-custom-toggle.monaco-checkbox:not(.checked):before{visibility:hidden}.monaco-inputbox{border-radius:4px;box-sizing:border-box;display:block;font-size:inherit;padding:0;position:relative}.monaco-inputbox>.ibwrapper>.input,.monaco-inputbox>.ibwrapper>.mirror{padding:4px 6px}.monaco-inputbox>.ibwrapper{position:relative;width:100%}.monaco-inputbox>.ibwrapper>.input{border:none;box-sizing:border-box;color:inherit;display:inline-block;font-family:inherit;font-size:inherit;height:100%;line-height:inherit;resize:none;width:100%}.monaco-inputbox>.ibwrapper>input{text-overflow:ellipsis}.monaco-inputbox>.ibwrapper>textarea.input{display:block;outline:none;scrollbar-width:none}.monaco-inputbox>.ibwrapper>textarea.input::-webkit-scrollbar{display:none}.monaco-inputbox>.ibwrapper>textarea.input.empty{white-space:nowrap}.monaco-inputbox>.ibwrapper>.mirror{box-sizing:border-box;display:inline-block;left:0;position:absolute;top:0;visibility:hidden;white-space:pre-wrap;width:100%;word-wrap:break-word}.monaco-inputbox,.monaco-inputbox:focus,.monaco-inputbox:focus-within,.monaco-inputbox>.ibwrapper,.monaco-inputbox>.ibwrapper:focus,.monaco-inputbox>.ibwrapper:focus-within,.monaco-inputbox>.ibwrapper>.input,.monaco-inputbox>.ibwrapper>.input:focus{border-color:transparent!important;box-shadow:none!important;outline:none!important}.monaco-inputbox-container{display:none!important;text-align:right}.monaco-inputbox-container .monaco-inputbox-message{box-sizing:border-box;display:inline-block;font-size:12px;line-height:17px;margin-top:-1px;overflow:hidden;padding:.4em;text-align:left;width:100%;word-wrap:break-word}.monaco-inputbox .monaco-action-bar{position:absolute;right:2px;top:4px}.monaco-inputbox .monaco-action-bar .action-item{margin-left:2px}.monaco-inputbox .monaco-action-bar .action-item .codicon{background-repeat:no-repeat;height:16px;width:16px}.monaco-findInput{position:relative}.monaco-findInput .monaco-inputbox{font-size:13px;width:100%}.monaco-findInput>.controls{position:absolute;right:2px;top:3px}.vs .monaco-findInput.disabled{background-color:#e1e1e1}.vs-dark .monaco-findInput.disabled{background-color:#333}.hc-light .monaco-findInput.highlight-0 .controls,.monaco-findInput.highlight-0 .controls{animation:monaco-findInput-highlight-0 .1s linear 0s}.hc-light .monaco-findInput.highlight-1 .controls,.monaco-findInput.highlight-1 .controls{animation:monaco-findInput-highlight-1 .1s linear 0s}.hc-black .monaco-findInput.highlight-0 .controls,.vs-dark .monaco-findInput.highlight-0 .controls{animation:monaco-findInput-highlight-dark-0 .1s linear 0s}.hc-black .monaco-findInput.highlight-1 .controls,.vs-dark .monaco-findInput.highlight-1 .controls{animation:monaco-findInput-highlight-dark-1 .1s linear 0s}@keyframes monaco-findInput-highlight-0{0%{background:rgba(253,255,0,.8)}to{background:transparent}}@keyframes monaco-findInput-highlight-1{0%{background:rgba(253,255,0,.8)}99%{background:transparent}}@keyframes monaco-findInput-highlight-dark-0{0%{background:hsla(0,0%,100%,.44)}to{background:transparent}}@keyframes monaco-findInput-highlight-dark-1{0%{background:hsla(0,0%,100%,.44)}99%{background:transparent}}.monaco-findInput .ai-replace-model-select{display:inline-block;margin-left:2px;position:relative;vertical-align:top}.monaco-findInput .ai-replace-model-toggle{border:1px solid transparent;border-radius:3px;cursor:pointer;display:inline-block;height:20px;line-height:20px;text-align:center;vertical-align:top;width:16px}.monaco-findInput .ai-replace-model-toggle:hover{background-color:hsla(0,0%,50%,.1)}.monaco-findInput .ai-replace-model-toggle:focus{outline:1px solid;outline-offset:-1px}.monaco-tl-row{align-items:center;display:flex;height:100%;position:relative}.monaco-tl-row.disabled{cursor:default}.monaco-tl-indent{height:100%;left:16px;pointer-events:none;position:absolute;top:0}.hide-arrows .monaco-tl-indent{left:12px}.monaco-tl-indent>.indent-guide{border-left:1px solid transparent;box-sizing:border-box;display:inline-block;height:100%}.monaco-workbench:not(.reduce-motion) .monaco-tl-indent>.indent-guide{transition:border-color .1s linear}.monaco-tl-contents,.monaco-tl-twistie{height:100%}.monaco-tl-twistie{align-items:center;display:flex!important;flex-shrink:0;font-size:10px;justify-content:center;padding-right:6px;text-align:right;transform:translateX(3px);width:16px}.monaco-tl-contents{flex:1;overflow:hidden}.monaco-tl-twistie:before{border-radius:20px}.monaco-tl-twistie.collapsed:before{transform:rotate(-90deg)}.monaco-tl-twistie.codicon-tree-item-loading:before{animation:codicon-spin 1.25s steps(30) infinite}.monaco-tree-type-filter{border:1px solid var(--vscode-widget-border);border-bottom-left-radius:4px;border-bottom-right-radius:4px;display:flex;margin:0 10px 0 6px;max-width:200px;padding:3px;position:absolute;right:0;top:0;z-index:100}.monaco-workbench:not(.reduce-motion) .monaco-tree-type-filter{transition:top .3s}.monaco-tree-type-filter.disabled{top:-40px!important}.monaco-tree-type-filter-input{flex:1}.monaco-tree-type-filter-input .monaco-inputbox{height:23px}.monaco-tree-type-filter-input .monaco-inputbox>.ibwrapper>.input,.monaco-tree-type-filter-input .monaco-inputbox>.ibwrapper>.mirror{padding:2px 4px}.monaco-tree-type-filter-input .monaco-findInput>.controls{top:2px}.monaco-tree-type-filter-actionbar{margin-left:4px}.monaco-tree-type-filter-actionbar .monaco-action-bar .action-label{padding:2px}.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container{background-color:var(--vscode-sideBar-background);height:0;left:0;position:absolute;top:0;width:100%;z-index:13}.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row.monaco-list-row{background-color:var(--vscode-sideBar-background);opacity:1!important;overflow:hidden;position:absolute;width:100%}.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-row:hover{background-color:var(--vscode-list-hoverBackground)!important;cursor:pointer}.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container.empty,.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container.empty .monaco-tree-sticky-container-shadow{display:none}.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container .monaco-tree-sticky-container-shadow{bottom:-3px;height:0;left:0;position:absolute;width:100%}.monaco-list .monaco-scrollable-element .monaco-tree-sticky-container[tabindex="0"]:focus{outline:none}
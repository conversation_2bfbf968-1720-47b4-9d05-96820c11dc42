{"name": "windsurf-code-executor", "displayName": "Windsurf Code Executor", "description": "Execute generated code from cascade.", "version": "0.0.1", "publisher": "codeium", "engines": {"vscode": "^1.70.2"}, "activationEvents": [], "api": "none", "main": "./dist/extension.js", "contributes": {"commands": [{"command": "windsurf-code-executor.executeCode", "title": "Execute Code (Windsurf)", "category": "Windsurf"}]}, "prettier": {"singleQuote": true, "useTabs": true, "tabWidth": 4, "trailingComma": "all", "printWidth": 80}}
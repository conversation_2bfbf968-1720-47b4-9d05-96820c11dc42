/* Tooltip styles - aligned with design system */
.label-with-tooltip {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-bottom: 15px;
}

.label-with-tooltip label {
  margin-bottom: 0;
  margin-right: 15px;
}

.tooltip-container {
  position: relative;
  display: inline-block;
  top: -2px;
}

.tooltip-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: transparent;
  color: var(--vscode-foreground);
  font-size: 9px;
  font-weight: 600;
  cursor: pointer;
  user-select: none;
  border: 1px solid var(--vscode-foreground);
  opacity: 0.7;
}

.tooltip-text {
  visibility: hidden;
  position: absolute;
  z-index: 1;
  width: max-content;
  min-width: 120px;
  max-width: 300px;
  background-color: var(--vscode-editorHoverWidget-background);
  color: var(--vscode-editorHoverWidget-foreground);
  text-align: left;
  border-radius: 4px;
  padding: 2px 4px;
  font-size: 12px;
  line-height: 1.5;
  left: 24px;
  opacity: 0;
  transition:
    opacity 0.2s ease,
    visibility 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  pointer-events: none;
  border: 1px solid
    var(--vscode-editorHoverWidget-border, rgba(127, 127, 127, 0.3));
  word-wrap: break-word;
  top: -2px;
}

.tooltip-container:hover .tooltip-text {
  visibility: visible;
  opacity: 1;
}

/* Rule Editor Styles */
.rule-editor {
  display: flex;
  flex-direction: column;
  padding: 20px;
  height: 100vh;
  margin-bottom: 8px;
  box-sizing: border-box;
  color: var(--vscode-editor-foreground);
  font-family: var(--vscode-font-family);
  background-color: var(--vscode-editor-background);
}

.rule-section {
  margin-bottom: 25px;
  box-sizing: border-box;
  width: 100%;
}

select {
  padding: 8px 8px;
  font-size: 14px;
  border: 1px solid var(--vscode-input-border);
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border-radius: 2px;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: bold;
  color: var(--vscode-editor-foreground);
}

input[type='text'] {
  width: 100%;
  padding: 8px 10px;
  font-size: 14px;
  border: 1px solid var(--vscode-input-border);
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  border-radius: 2px;
  font-family: var(--vscode-editor-font-family);
  letter-spacing: -0.5px;
}

textarea {
  width: 100%;
  height: calc(100vh - 120px);
  padding: 10px;
  font-size: 14px;
  border: 1px solid var(--vscode-input-border);
  background-color: var(--vscode-input-background);
  color: var(--vscode-input-foreground);
  font-family: var(--vscode-editor-font-family);
  resize: none;
  border-radius: 2px;
}

input:focus,
textarea:focus {
  outline: 1px solid var(--vscode-focusBorder);
  border-color: var(--vscode-focusBorder);
}

/* Options container */
.options {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 10px;
}

/* Option descriptions container */
.option-descriptions {
  display: flex;
  align-items: center;
  min-height: 36px; /* Match dropdown height */
  padding-left: 0px;
}

/* Option description styling */
.option-description {
  display: none;
  color: var(--vscode-descriptionForeground);
  font-size: 12px;
}

/* Conditional sections */
.conditional-section {
  display: none;
  margin-bottom: 8px;
}

.content {
  display: block;
}

.conditional-content {
  display: none;
}

/* Input error message */
.input-error-message {
  display: none;
  color: var(--vscode-inputValidation-errorForeground, #f14c4c);
  font-size: 12px;
}

.input-messages {
  display: flex;
  flex-direction: column;
  gap: 2px;
  position: relative;
  min-height: 18px;
}

/* Input wrapper for positioning counter */
.input-wrapper {
  position: relative;
  width: 100%;
}

/* Container for label and counter */
.label-counter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 0px;
  margin-bottom: 2px;
  width: 100%; /* Ensure container takes full width */
  box-sizing: border-box; /* Include padding in width calculation */
  padding: 0; /* No padding needed */
}

/* Input character limit styles */
.char-counter {
  text-align: right;
  font-size: 12px;
  color: var(--vscode-descriptionForeground);
  position: absolute;
  right: 20px;
}

/* Display counter values using data attributes */
.char-counter::before {
  content: attr(data-current) '/' attr(data-max);
}

/* Make sure to update inputErrorClassName and charCounterErrorClassName if the class names are updated. */
.char-counter-error {
  color: var(--vscode-inputValidation-errorForeground, #f14c4c);
}

/* Input character limit error style */
.input-char-limit-error {
  outline: 1px solid var(--vscode-inputValidation-errorBorder, #f14c4c) !important;
  border-color: var(--vscode-inputValidation-errorBorder, #f14c4c) !important;
}

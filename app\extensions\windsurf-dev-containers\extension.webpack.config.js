//@ts-check

'use strict';

const copyPlugin = require('copy-webpack-plugin');
const withDefaults = require('../shared.webpack.config');

module.exports = withDefaults({
	context: __dirname,
	entry: {
		extension: './src/extension.ts',
	},
	resolve: {
		mainFields: ['module', 'main'],
	},
	plugins: [
		new copyPlugin({
			patterns: [
				{
					from: 'node_modules/@devcontainers/cli/dist/spec-node/devContainersSpecCLI.js',
					to: '@devcontainers/cli/dist/spec-node/devContainersSpecCLI.js',
				},
				{
					from: 'node_modules/@devcontainers/cli/scripts',
					to: '@devcontainers/cli/scripts',
				},
			],
		}),
	],
});

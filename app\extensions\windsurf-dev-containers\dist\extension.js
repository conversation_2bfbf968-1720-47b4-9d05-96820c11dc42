(()=>{"use strict";var e={619:(e,t,n)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.IterableMessageDone=t.IterableMessageWithNext=t.DeferredPromise=void 0;const r=n(398);t.DeferredPromise=class{get isRejected(){return 1===this.outcome?.outcome}get isResolved(){return 0===this.outcome?.outcome}get isSettled(){return!!this.outcome}get value(){return 0===this.outcome?.outcome?this.outcome?.value:void 0}constructor(){this.p=new Promise(((e,t)=>{this.completeCallback=e,this.errorCallback=t}))}complete(e){return new Promise((t=>{this.completeCallback(e),this.outcome={outcome:0,value:e},t()}))}error(e){return new Promise((t=>{this.errorCallback(e),this.outcome={outcome:1,value:e},t()}))}cancel(){return this.error(new r.CancellationError)}},t.IterableMessageWithNext=class{constructor(e,t){this.done=!1,this.message=e,this.next=t}},t.IterableMessageDone=class{constructor(){this.message=null,this.next=null,this.done=!0}}},721:(e,t)=>{function n(e){for(;e.length;){const t=e.pop();t&&t.dispose()}}Object.defineProperty(t,"__esModule",{value:!0}),t.Disposable=void 0,t.disposeAll=n,t.Disposable=class{constructor(){this._isDisposed=!1,this._disposables=[]}dispose(){this._isDisposed||(this._isDisposed=!0,n(this._disposables))}_register(e){return this._isDisposed?e.dispose():this._disposables.push(e),e}get isDisposed(){return this._isDisposed}}},947:function(e,t,n){var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.Log=void 0,t.initLogger=function(e){return l instanceof c&&l.dispose(),l=new c(e),l},t.getLogger=function(){return l};const a=s(n(398));class c{constructor(e){this.output=a.window.createOutputChannel(e)}data2String(e){return e instanceof Error?e.stack||e.message:!1===e.success&&e.message?e.message:e.toString()}trace(e,t){this.logLevel("Trace",e,t)}info(e,t){this.logLevel("Info",e,t)}error(e,t){this.logLevel("Error",e,t)}raw(e,t){this.output.append(e),t&&this.output.append(this.data2String(t))}logLevel(e,t,n){this.output.appendLine(`[${e}\t- ${this.now()}] ${t}`),n&&this.output.appendLine(this.data2String(n))}now(){const e=new Date;return d(e.getUTCHours()+"",2,"0")+":"+d(e.getMinutes()+"",2,"0")+":"+d(e.getUTCSeconds()+"",2,"0")+"."+e.getMilliseconds()}show(){this.output.show()}dispose(){this.output.dispose()}}function d(e,t,n=" "){return n.repeat(Math.max(0,t-e.length))+e}t.Log=c;let l=new class{trace(e,t){console.log(`[Trace] ${e}`,t||"")}info(e,t){console.log(`[Info] ${e}`,t||"")}error(e,t){console.error(`[Error] ${e}`,t||"")}raw(e,t){console.log(e,t||"")}show(){}dispose(){}}},467:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});class n{constructor(e){this.setting=e}static withDevContainerConfig(e,t){return new n({settingType:"config",workspacePath:e,devcontainerPath:t})}static withContainerId(e){return new n({settingType:"container",containerId:e})}static parse(e){const t=e.split("+"),r=t[t.length-1].split("?");return r[1].endsWith(".json")?new n({settingType:"config",workspacePath:r[0],devcontainerPath:r[1]}):new n({settingType:"container",containerId:r[1]})}getType(){return this.setting.settingType}getContainerSetting(){if("container"!==this.setting.settingType)throw new Error("Dev Container is not a container");return this.setting}getConfigSetting(){if("config"!==this.setting.settingType)throw new Error("Dev Container is not a config");return this.setting}static parseJSON(e){const t=JSON.parse(e);if(t.containerId)return new n({settingType:"container",containerId:t.containerId});if(t.workspacePath&&t.devcontainerPath)return new n({settingType:"config",workspacePath:t.workspacePath,devcontainerPath:t.devcontainerPath});throw new Error(`Invalid DevContainerDestination JSON: ${e}`)}toJSON(){const e="config"===this.setting.settingType?{workspacePath:this.setting.workspacePath,devcontainerPath:this.setting.devcontainerPath}:{containerId:this.setting.containerId};return JSON.stringify(e)}static parseAuthority(e){const t=e.split("+");return n.parseEncoded(t[t.length-1])}static parseEncoded(e){try{const t=Buffer.from(e,"hex").toString();return n.parseJSON(t)}catch{}return n.parse(e.replace(/\\x([0-9a-f]{2})/g,((e,t)=>String.fromCharCode(parseInt(t,16)))))}toEncodedString(){return Buffer.from(this.toJSON()).toString("hex")}}t.default=n},775:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});class n{constructor(e){this.context=e,this.store={},Object.assign(this.store,e.globalState.get(n.STORAGE_KEY)||{})}getLocations(){return Object.keys(this.store)}getDevContainers(e){return Object.keys(this.store[e]||{})}getAuthority(e,t){return this.store[e][t].authority}getWorkspacePath(e,t){return this.store[e][t].workspacePath}async addContainer(e,t,n,r="/"){(this.store[e]=this.store[e]||{})[t]={authority:n,workspacePath:r},await this.save()}async removeContainer(e,t){this.store[e]&&(delete this.store[e][t],0===Object.keys(this.store[e]).length&&delete this.store[e],await this.save())}async save(){await this.context.globalState.update(n.STORAGE_KEY,this.store)}reloadFromStorage(){const e=this.context.globalState.get(n.STORAGE_KEY)||{};for(const e of Object.keys(this.store))delete this.store[e];Object.assign(this.store,e)}}n.STORAGE_KEY="windsurfDevContainersLocationHistory_v0",t.default=n},53:function(e,t,n){var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.DevContainerTreeDataProvider=void 0;const c=s(n(398)),d=s(n(928)),l=a(n(467)),u=n(721),f=a(n(512));class h{constructor(e){this.location=e}}class p{constructor(e,t,n,r){this.location=e,this.devId=t,this.authority=n,this.workspacePath=r}}class g extends u.Disposable{constructor(e){super(),this.history=e,this._onDidChangeTreeData=new c.EventEmitter,this.onDidChangeTreeData=this._onDidChangeTreeData.event,this._register(c.commands.registerCommand("windsurf-dev-containers.explorer.refresh",(()=>this.refresh()))),this._register(c.commands.registerCommand("windsurf-dev-containers.openFromHistory",(e=>this.openFromHistory(e)))),this._register(c.commands.registerCommand("windsurf-dev-containers.explorer.deleteContainer",(async e=>{await this.history.removeContainer(e.location,e.devId),this.refresh()})))}getCurrentHost(){let e=c.env.remoteAuthority;return e?(e.includes("@")&&(e=e.split("@")[1]),e.startsWith("ssh-remote")?(e=e.split("+")[1],f.default.parseEncoded(e).hostname+" (remote)"):"Local"):"Local"}getTreeItem(e){if(e instanceof p){const t=l.default.parseEncoded(e.devId);let n="container";n="config"===t.getType()?("Local"===e.location?d:d.posix).basename(t.getConfigSetting().workspacePath):t.getContainerSetting().containerId;const r=new c.TreeItem(n,c.TreeItemCollapsibleState.None),o=()=>{const n=c.env.remoteAuthority?.includes(e.authority);return"config"===t.getType()?n?new c.ThemeIcon("package",new c.ThemeColor("charts.green")):new c.ThemeIcon("package"):n?new c.ThemeIcon("vm-running",new c.ThemeColor("charts.green")):new c.ThemeIcon("vm-running")};return r.iconPath=o(),r.contextValue="windsurf-dev-containers.container",r.description="config"===t.getType()?("Local"===e.location?d:d.posix).relative(t.getConfigSetting().workspacePath,t.getConfigSetting().devcontainerPath):"",r.tooltip="config"===t.getType()?"Open folder in container":"Attach to container (if running)",r}const t=new c.TreeItem(e.location);return t.collapsibleState=c.TreeItemCollapsibleState.Collapsed,t.iconPath=this.getCurrentHost()===e.location?new c.ThemeIcon("vm-active",new c.ThemeColor("charts.green")):new c.ThemeIcon("vm"),t.contextValue="windsurf-dev-containers.location",t}async getChildren(e){return e?e instanceof h?this.history.getDevContainers(e.location).map((t=>new p(e.location,t,this.history.getAuthority(e.location,t),this.history.getWorkspacePath(e.location,t)))):[]:this.history.getLocations().map((e=>new h(e)))}refresh(){this.history.reloadFromStorage&&this.history.reloadFromStorage(),this._onDidChangeTreeData.fire()}async openFromHistory(e){const t=l.default.parseEncoded(e.devId),n=e.authority;"config"===t.getType()?c.commands.executeCommand("vscode.openFolder",c.Uri.from({scheme:"vscode-remote",authority:n,path:e.workspacePath}),{forceNewWindow:!0}):c.commands.executeCommand("vscode.newWindow",{remoteAuthority:n})}}t.DevContainerTreeDataProvider=g},256:function(e,t,n){var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.activate=function(e){const t=(0,l.initLogger)("Remote - Dev Containers (Windsurf)");if(e.subscriptions.push(t),h.env.remoteAuthority){const e=h.env.remoteAuthority.includes("@ssh-remote+");c.commands.executeCommand("setContext","windsurf.isNestedContainer",e)}const n=new u.DevContainerResolver(e,t);e.subscriptions.push(c.workspace.registerRemoteAuthorityResolver(u.DEV_CONTAINER_AUTHORITY,n)),e.subscriptions.push(n);const r=new m.default(e),o=new E.DevContainerTreeDataProvider(r);e.subscriptions.push(c.window.createTreeView("windsurfDevContainers",{treeDataProvider:o})),e.subscriptions.push(o);const i=c.commands.registerCommand("windsurf-dev-containers.openInContainer",(async()=>{t.info(h.env.remoteName?`Remote name: ${h.env.remoteName}`:"Remote name is not set"),t.info(h.env.remoteAuthority?`Remote authority: ${h.env.remoteAuthority}`:"Remote authority is not set");let n=h.env.remoteAuthority;n?.includes("@")&&(n=n.split("@")[1]),n?.startsWith("dev-container+")&&(n=void 0);const o=n?await c.workspace.getRemoteExecServer(n):new g.LocalExecServer,i=o instanceof g.LocalExecServer;if(!o)return t.info("Exec server is not available"),void c.window.showErrorMessage("Exec server is not available");t.info("Remote authority: "+n);const s=await c.window.showOpenDialog({title:"Select a workspace folder",canSelectMany:!1,defaultUri:o instanceof g.LocalExecServer?c.Uri.file("/"):c.Uri.from({scheme:"vscode-remote",authority:n}),canSelectFolders:!0});if(!s)return void((0,d.isDisposable)(o)&&o.dispose());const a=s[0],l=await c.workspace.findFiles(new c.RelativePattern(a,".devcontainer/**/devcontainer.json"));if(0===l.length)return c.window.showErrorMessage(`No devcontainer.json files found in the selected folder ${i?a.fsPath:a.path}. Please create a devcontainer.json file in the following path: ${a.path}/.devcontainer/devcontainer.json (or the Windows equivalent).`),void((0,d.isDisposable)(o)&&o.dispose());const m=l.map((e=>({label:i?e.fsPath:e.path}))),E=await c.window.showQuickPick(m,{title:"Select a devcontainer.json file"});if(!E)return void((0,d.isDisposable)(o)&&o.dispose());const _=c.Uri.file(E.label);t.info("Checking docker version runs");try{await(0,d.checkDockerVersion)(o)}catch(e){return t.show(),c.window.showErrorMessage(`Error checking Docker version: ${e.message}`),void((0,d.isDisposable)(o)&&o.dispose())}const w=await(0,p.getVSCodeServerConfig)(),R=await(0,d.getDevContainerCliPath)(e,o,w),S=await(0,d.readDevContainerConfig)({workspaceFolder:i?a.fsPath:a.path,includeMergedConfig:!1,devcontainerId:void 0,devcontainerPath:i?_.fsPath:_.path,execServer:o,devcontainerCliPath:R});if(!S)return t.show(),c.window.showErrorMessage("Failed to read devcontainer configuration"),void((0,d.isDisposable)(o)&&o.dispose());const D=f.default.withDevContainerConfig(i?a.fsPath:a.path,i?_.fsPath:_.path),I=n?`${(0,u.getDevContainerAuthority)(D.toEncodedString())}@${n}`:void 0;await r.addContainer(I?v.default.parseEncoded(n.split("+")[1]).hostname+" (remote)":"Local",D.toEncodedString(),I||(0,u.getDevContainerAuthority)(D.toEncodedString()),S.workspace.workspaceFolder),c.commands.executeCommand("vscode.openFolder",c.Uri.from({scheme:"vscode-remote",authority:I||(0,u.getDevContainerAuthority)(D.toEncodedString()),path:S.workspace.workspaceFolder}),{forceNewWindow:!0})}));e.subscriptions.push(i);const s=c.commands.registerCommand("windsurf-dev-containers.reopenInContainer",(async n=>{t.info(h.env.remoteName?`Remote name: ${h.env.remoteName}`:"Remote name is not set"),t.info(h.env.remoteAuthority?`Remote authority: ${h.env.remoteAuthority}`:"Remote authority is not set");let o=h.env.remoteAuthority;o?.includes("@")&&(o=o.split("@")[1]),o?.startsWith("dev-container+")&&(o=void 0);const i=o?await c.workspace.getRemoteExecServer(o):new g.LocalExecServer,s=i instanceof g.LocalExecServer;if(!i)return t.info("Exec server is not available"),void c.window.showErrorMessage("Exec server is not available");t.show();const a=(e=>{if(!e)return"";const t=c.Uri.parse(e);return(s?t.fsPath:t.path).endsWith("devcontainer.json")?s?t.fsPath:t.path:(c.window.showErrorMessage("The selected file is not a devcontainer.json file"),"")})(n)||await(async()=>{const e=await c.workspace.findFiles(".devcontainer/**/devcontainer.json"),t=e.map((e=>({label:s?e.fsPath:e.path,value:s?e.fsPath:e.path})));if(0===t.length)return c.window.showErrorMessage("No devcontainer.json files found"),"";let n,r;if(1===t.length)n=t[0];else{const t=[...e.map((e=>({label:s?e.fsPath:e.path,value:s?e.fsPath:e.path}))),{label:"$(folder) Browse...",description:"Select a devcontainer.json file",value:"browse"}];n=await c.window.showQuickPick(t,{placeHolder:"Select the devcontainer.json file"})}if(!n)return"";if("browse"===n.value){const e=await c.window.showOpenDialog({canSelectFiles:!0,canSelectFolders:!1,canSelectMany:!1,filters:{"DevContainer Config":["json"]},title:"Select devcontainer.json file",openLabel:"Select"});if(!e||0===e.length)return"";r=s?e[0].fsPath:e[0].path}else r=n.value;if(r.endsWith("devcontainer.json"))return r;c.window.showErrorMessage("The selected file is not a devcontainer.json file")})();if(!a)return c.window.showErrorMessage("No devcontainer.json file selected"),void((0,d.isDisposable)(i)&&i.dispose());const l=(e=>{const t=c.workspace.workspaceFolders;if(t&&0!==t.length)return e?t.find((t=>e.startsWith(s?t.uri.fsPath:t.uri.path))):t[0]})(a);if(!l)return c.window.showErrorMessage("The selected devcontainer.json must be within a workspace folder"),void((0,d.isDisposable)(i)&&i.dispose());t.info("Checking docker version runs");try{await(0,d.checkDockerVersion)(i)}catch(e){return t.show(),c.window.showErrorMessage(`Error checking Docker version: ${e.message}`),void((0,d.isDisposable)(i)&&i.dispose())}const m=c.Uri.file(a),E=await(0,p.getVSCodeServerConfig)(),_=await(0,d.getDevContainerCliPath)(e,i,E),w=await(0,d.readDevContainerConfig)({workspaceFolder:s?l.uri.fsPath:l.uri.path,includeMergedConfig:!1,devcontainerId:void 0,devcontainerPath:s?m.fsPath:m.path,execServer:i,devcontainerCliPath:_});if(!w)return t.show(),c.window.showErrorMessage("Failed to read devcontainer configuration."),void((0,d.isDisposable)(i)&&i.dispose());const R=f.default.withDevContainerConfig(s?l.uri.fsPath:l.uri.path,s?m.fsPath:m.path),S=o?`${(0,u.getDevContainerAuthority)(R.toEncodedString())}@${o}`:void 0;await r.addContainer(S?v.default.parseEncoded(o.split("+")[1]).hostname+" (remote)":"Local",R.toEncodedString(),S||(0,u.getDevContainerAuthority)(R.toEncodedString()),w.workspace.workspaceFolder),c.commands.executeCommand("vscode.openFolder",c.Uri.from({scheme:"vscode-remote",authority:S||(0,u.getDevContainerAuthority)(R.toEncodedString()),path:w.workspace.workspaceFolder}))}));e.subscriptions.push(s);const a=c.commands.registerCommand("windsurf-dev-containers.attachToRunningContainer",(async()=>{t.show();let e=h.env.remoteAuthority;e?.includes("@")&&(e=e.split("@")[1]),e?.startsWith("dev-container+")&&(e=void 0);const n=e?await c.workspace.getRemoteExecServer(e):new g.LocalExecServer;if(!n)return t.info("Exec server is not available"),void c.window.showErrorMessage("Exec server is not available");t.info("Checking docker version runs");try{await(0,d.checkDockerVersion)(n)}catch(e){return t.show(),c.window.showErrorMessage(`Error checking Docker version: ${e.message}`),void((0,d.isDisposable)(n)&&n.dispose())}let o;try{o=(await(0,d.executeCommand)("docker",["ps","--format","{{.Names}}\t{{.Image}}"],n,void 0)).stdout.trim()}catch(e){return t.show(),c.window.showErrorMessage(`Failed to list running containers: ${e.message}`),void((0,d.isDisposable)(n)&&n.dispose())}if(!o)return c.window.showErrorMessage("No running containers found!"),void((0,d.isDisposable)(n)&&n.dispose());const i=o.split("\n").map((e=>{const[t,n]=e.split("\t");return{label:t,description:n}})),s=await c.window.showQuickPick(i,{title:"Select a running container"});if(!s)return void((0,d.isDisposable)(n)&&n.dispose());t.info(`Attaching to running container: ${s.label}`);const a=f.default.withContainerId(s.label),l=e?`${(0,u.getDevContainerAuthority)(a.toEncodedString())}@${e}`:void 0;await r.addContainer(l?v.default.parseEncoded(e.split("+")[1]).hostname+" (remote)":"Local",a.toEncodedString(),l||(0,u.getDevContainerAuthority)(a.toEncodedString())),c.commands.executeCommand("vscode.newWindow",{remoteAuthority:l||(0,u.getDevContainerAuthority)(a.toEncodedString())})}));e.subscriptions.push(a);const _=c.commands.registerCommand("windsurf-dev-containers.showLog",(()=>{t.show()}));e.subscriptions.push(_);const w=c.commands.registerCommand("windsurf-dev-containers.reopenFolderLocally",(async()=>{try{const e=(()=>{try{for(const e of c.workspace.workspaceFolders||[])return f.default.parseAuthority(e.uri.authority)}catch(e){t.error("Failed to get Dev Container workspace information:",e),t.show()}})();if(!e)return void c.window.showErrorMessage("Failed to get Dev Container workspace information.");if("config"!==e.getType())return void c.window.showErrorMessage("The container is not linked to any local workspace.");await c.commands.executeCommand("workbench.action.remote.close"),await c.commands.executeCommand("vscode.openFolder",c.Uri.file(e.getConfigSetting().workspacePath))}catch(e){t.error("Failed to reopen folder locally:",e),t.show(),c.window.showErrorMessage("Failed to reopen folder locally.")}}));e.subscriptions.push(w)};const c=s(n(398)),d=n(376),l=n(947),u=n(527),f=a(n(467)),h=n(398),p=n(596),g=n(964),m=a(n(775)),E=n(53),v=a(n(512))},763:function(e,t,n){var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.forwardPort=async function(e,t,n,r,o,i){const s=new AbortController;return{port:await g(t,e,n,r,o,i,s),abort:()=>s.abort()}},t.forwardSocket=async function(e,t,n,r,o,i,s){const a=new AbortController;return await async function(e,t,n,r,o,i,s,a){const c=new Map;(async function(e,t,n,r,o,i,s,a){const c=p(n);try{s.info("Got connection.");const n=["docker","exec","-u",o,"-i",t,"bash","-c",r+' -e "'+c+'"'],l=await i.spawn(n[0],n.slice(1),{env:(0,d.sanitizeEnv)(await(0,d.getEnv)(i))});if(l.onExit.then((()=>{a.forEach((e=>{e.end()}))})),l.stdin&&l.stdout){let t=Buffer.alloc(0),n=E.HeaderId,r=0,o=0,s=0;const c=new Map,d=new Map,f=4,h=4;l.stdout.onDidReceiveMessage((p=>{t=Buffer.concat([t,p]);let g=!1;do{g=!1;let p=0;if(n===E.HeaderId&&r+f<=t.length){o=t.subarray(r,r+f).readUInt32BE(),p=o,r+=f,n=E.HeaderLen;const s=new u.DeferredPromise;if(!c.has(p)){const t=new u.DeferredPromise;c.set(p,t),d.set(p,s),m(i,e).then((e=>{a.set(p,e),e.on("close",(()=>{a.has(p)&&a.delete(p)})),e.on("data",(e=>{const t=Buffer.alloc(f);t.writeUInt32BE(p);const n=Buffer.alloc(h);n.writeUInt32BE(e.length),l.stdin.write(Buffer.concat([t,n,e]))})),s.complete()}))}}if(n===E.HeaderLen&&r+h<=t.length&&(s=t.subarray(r,r+h).readUInt32BE(),r+=h,n=E.Payload),n===E.Payload&&r+s<=t.length){const e=t.subarray(r,r+s);r+=s,n=E.HeaderId,g=!0,p=o,(0===e.length?c.get(p):d.get(p)).p.then((()=>{0===e.length?(a.get(p)?.end(),a.delete(p)):(a.get(p)?.write(e),c.get(p)?.complete())}))}}while(g);r>0&&(t=t.subarray(r),r=0)}))}}catch(e){s.error(`handleSocketForward error: ${e}`)}})(t,e,{socket:n},r,o,s,i,c),i.info("====socketForward=success===="),h(e,i,s,a?.signal).then((()=>{c.forEach((e=>{e.end()}))}))}(e,t,n,r,o,i,s,a),{abort:()=>a.abort()}};const a=n(434),c=s(n(278)),d=n(376),l=n(964),u=n(619);async function f(e,t,n,r,o){try{const{stdout:r}=await(0,d.executeCommand)("docker",["inspect","-f",`{{${t}}}`,e],o,void 0);return r.trim()===n}catch(e){return r.error(`Error getting container status: ${e.message}`),!1}}async function h(e,t,n,r){for(;!r?.aborted;){const o=await f(e,".State.Running","true",t,n),i=await f(e,".State.Restarting","true",t,n);if(!await f(e,".State.Status","created",t,n)&&!i&&!o)break;await new Promise((e=>{const t=setTimeout(e,1e4);r&&r.addEventListener("abort",(()=>{clearTimeout(t),e()}),{once:!0})}))}}function p(e){if(e.socket&&e.port||!e.socket&&!e.port)throw new Error("Invalid arguments, exactly one of socket or port must be provided.");const t=e.port?`{ host: '127.0.0.1', port: ${e.port}}`:`{ path: '${e.socket}'}`,n="\n\t\tconsole.error('--- SCRIPT STARTED ---');\n\t\tconst net = require('net');\n\t\tconst fs = require('fs');\n\t\tconst handleError = (err) => process.stderr.write(err && (err.stack || err.message) || String(err));\n\t\tconst handleClose = (hadError) => {\n\t\t\tconsole.error(hadError ? 'Remote close with error' : 'Remote close');\n\t\t\tprocess.exit(hadError ? 1 : 0);\n\t\t};\n\t\tprocess.stdin.on('close', handleClose);\n\t\tprocess.on('uncaughtException', (err) => {\n\t\t\tfs.writeSync(process.stderr.fd, 'error: ' + (err.stack || err.message) + '\\n');\n\t\t\tprocess.exit(1);\n\t\t});";if(e.port||e.usePortVersionForSocket)return n+`\n\t\tconst Transform = require('stream').Transform;\n\t\tprocess.stdin.pause();\n\t\tconst client = net.createConnection(${t}, () => {\n\t\t\tconsole.error('Connection established');\n\t\t\tconst filterKill = new Transform({\n\t\t\t\ttransform(chunk, _enc, cb) {\n\t\t\t\t\tif (chunk.toString().trim() !== '--kill--') this.push(chunk);\n\t\t\t\t\telse process.exit(0);\n\t\t\t\t\tcb();\n\t\t\t\t}\n\t\t\t});\n\t\t\tclient.pipe(process.stdout);\n\t\t\tprocess.stdin.pipe(filterKill).pipe(client);\n\t\t});\n\t\tclient.on('close', handleClose);\n\t\tclient.on('error', handleError);\n\t\tprocess.stdin.on('data', (data) => {\n\t\t\tif (data.toString().trim() === '--kill--') process.exit(0);\n\t\t});`;if(e.socket)return n+`\n\t\tconst [ID, LEN, PAYLOAD] = [0, 1, 2], [idLen, lenLen] = [4, 4];\n\t\tlet buffer = Buffer.alloc(0), state = ID, parsedId = 0, parsedLen = 0, offset = 0, id = 0;\n\t\tconst socketMap = new Map();\n\t\tconst writeFrame = (socketId, data) => {\n\t\t\tconst idBuf = Buffer.allocUnsafe(idLen), lenBuf = Buffer.allocUnsafe(lenLen);\n\t\t\tidBuf.writeUInt32BE(socketId); lenBuf.writeUInt32BE(data.length);\n\t\t\tprocess.stdout.write(Buffer.concat([idBuf, lenBuf, data]));\n\t\t};\n\t\tconst server = net.createServer((socket) => {\n\t\t\tconsole.error('Connection established');\n\t\t\tconst socketId = id++;\n\t\t\tsocketMap.set(socketId, socket);\n\t\t\tsocket.on('data', (data) => writeFrame(socketId, data));\n\t\t\tconst endSocket = () => {\n\t\t\t\tif (socketMap.has(socketId)) {\n\t\t\t\t\twriteFrame(socketId, Buffer.alloc(0));\n\t\t\t\t\tsocketMap.delete(socketId);\n\t\t\t\t}\n\t\t\t\tsocket.destroy();\n\t\t\t};\n\t\t\tsocket.on('close', endSocket);\n\t\t\tsocket.on('error', (err) => { console.error(err); endSocket(); });\n\t\t\tsocket.on('end', () => socket.end());\n\t\t});\n\t\tprocess.stdin.on('data', (data) => {\n\t\t\tbuffer = Buffer.concat([buffer, data]);\n\t\t\twhile (true) {\n\t\t\t\tif (state === ID && offset + idLen <= buffer.length) {\n\t\t\t\t\tparsedId = buffer.readUInt32BE(offset);\n\t\t\t\t\toffset += idLen; state = LEN;\n\t\t\t\t}\n\t\t\t\tif (state === LEN && offset + lenLen <= buffer.length) {\n\t\t\t\t\tparsedLen = buffer.readUInt32BE(offset);\n\t\t\t\t\toffset += lenLen; state = PAYLOAD;\n\t\t\t\t}\n\t\t\t\tif (state === PAYLOAD && offset + parsedLen <= buffer.length) {\n\t\t\t\t\tconst payload = buffer.subarray(offset, offset + parsedLen);\n\t\t\t\t\toffset += parsedLen; state = ID;\n\t\t\t\t\tsocketMap.get(parsedId)?.write(payload);\n\t\t\t\t}\n\t\t\t\telse break;\n\t\t\t}\n\t\t\tif (offset > 0) { buffer = buffer.subarray(offset); offset = 0; }\n\t\t});\n\t\tserver.listen(${t});\n\t\tserver.on('close', handleClose);\n\t\tserver.on('error', handleError);`;throw new Error("Invalid arguments, exactly one of socket or port must be provided.")}async function g(e,t,n,r,o,i,s){const a=c.createServer((s=>{!async function({socket:e,containerId:t,options:n,remoteServerNodePath:r,remoteUser:o,execServer:i,logger:s}){const a=p(n);try{s.info("Got connection.");const n=["docker","exec","-u",o,"-i",t,"bash","-c",r+' -e "'+a+'"'],c=await i.spawn(n[0],n.slice(1),{env:(0,d.sanitizeEnv)(await(0,d.getEnv)(i))});c.onExit.then((()=>{e.end()})),e.on("close",(()=>{c instanceof l.LocalSpawnedCommand?c.kill():c.stdin.write(Buffer.from("--kill--\n"))})),c.stdin&&c.stdout&&(s.info("Bidirectional pipe set up"),c.stdout.onDidReceiveMessage((t=>{e.write(t)})),e.on("data",(e=>{c.stdin.write(e)})))}catch(t){s.error(`handleClient error: ${t}`),e.end()}}({socket:s,containerId:e,options:{port:t},remoteServerNodePath:n,remoteUser:r,execServer:i,logger:o})}));return new Promise(((t,n)=>{a.on("error",n),a.listen(0,"127.0.0.1",(()=>{const r=a.address();if(r&&"string"!=typeof r){const n=r.port;h(e,o,i,s?.signal).then((()=>{a.close()})),t(n)}else n(new Error("Failed to get server address"))}))}))}async function m(e,t){return e instanceof l.LocalExecServer?c.createConnection(t):function(e){if(e instanceof c.Socket)return e;const t=e,n=new a.EventEmitter;return t.stdout.onDidReceiveMessage((e=>{n.emit("data",Buffer.from(e))})),t.onExit.then((()=>{n.emit("close")})),{write(e){t.stdin.write(Buffer.isBuffer(e)?e:Buffer.from(e))},end(){t.stdin.end(),n.emit("close")},on(e,t){n.on(e,t)}}}(await e.spawn(await(0,d.getNodePath)(e),["-e",p({socket:t,usePortVersionForSocket:!0})]))}var E;!function(e){e[e.HeaderId=0]="HeaderId",e[e.HeaderLen=1]="HeaderLen",e[e.Payload=2]="Payload"}(E||(E={}))},964:function(e,t,n){var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.LocalExecServer=t.LocalSpawnedCommand=void 0;const a=n(317),c=s(n(896)),d=s(n(278)),l=s(n(398)),u=n(23),f=(0,u.promisify)(c.stat),h=(0,u.promisify)(c.mkdir),p=(0,u.promisify)(c.rmdir),g=(0,u.promisify)(c.unlink),m=(0,u.promisify)(c.readdir),E=(0,u.promisify)(c.rename);class v{constructor(){this.dataEmitter=new l.EventEmitter,this.endEmitter=new l.EventEmitter,this.onDidReceiveMessage=this.dataEmitter.event,this.onEnd=new Promise((e=>{this.endEmitter.event((()=>e()))}))}emit(e){this.dataEmitter.fire(e)}dispose(){this.dataEmitter.dispose(),this.endEmitter.dispose()}triggerEnd(){this.endEmitter.fire()}}class _{constructor(e){this.writable=e,this.endEmitter=new l.EventEmitter,this.onEnd=this.endEmitter.event}write(e){this.writable&&this.writable.write(e)}end(){this.writable&&this.writable.end(),this.endEmitter.fire()}dispose(){this.endEmitter.dispose()}}class w{constructor(e){this.stdout=new v,this.stderr=new v,this.childProcess=e,this.pid=e.pid,this.stdin=new _(e.stdin||void 0),this.onExit=new Promise(((e,t)=>{this.exitPromiseResolve=e,this.exitPromiseReject=t})),e.stdout&&e.stdout.on("data",(e=>{this.stdout.emit(e)})),e.stderr&&e.stderr.on("data",(e=>{this.stderr.emit(e)})),e.on("close",(e=>{this.stdout.triggerEnd(),this.stderr.triggerEnd(),this.exitPromiseResolve({status:e||0})})),e.on("error",(e=>{this.exitPromiseReject(e)}))}kill(e){this.childProcess.kill(e)}}t.LocalSpawnedCommand=w;class R{constructor(e){this.dataEmitter=new l.EventEmitter,this.onDidReceiveMessage=this.dataEmitter.event,this.stream=c.createReadStream(e),this.onEnd=new Promise(((e,t)=>{this.stream.on("end",(()=>{e()})),this.stream.on("error",(e=>{t(e)}))})),this.stream.on("data",(e=>{this.dataEmitter.fire(e)}))}dispose(){this.stream.close(),this.dataEmitter.dispose()}}class S{constructor(e){this.endEmitter=new l.EventEmitter,this.onEnd=this.endEmitter.event,this.stream=c.createWriteStream(e),this.stream.on("finish",(()=>{this.endEmitter.fire()})),this.stream.on("error",(e=>{console.error("WriteStream error:",e)}))}write(e){this.stream.write(e)}end(){this.stream.end()}dispose(){this.stream.close(),this.endEmitter.dispose()}}class D{async stat(e){const t=await f(e);return{type:t.isFile()?l.FileType.File:t.isDirectory()?l.FileType.Directory:t.isSymbolicLink()?l.FileType.SymbolicLink:l.FileType.Unknown,ctime:t.ctimeMs,mtime:t.mtimeMs,size:t.size,permissions:t.mode}}async mkdirp(e){await h(e,{recursive:!0})}async rm(e){(await f(e)).isDirectory()?await p(e,{recursive:!0}):await g(e)}async read(e){return new R(e)}async write(e){const t=new S(e),n=new Promise((e=>{t.onEnd((()=>e()))}));return{stream:t,done:n}}async connect(e){throw new Error("Unix domain socket connection not implemented in LocalFileSystem")}async rename(e,t){await E(e,t)}async readdir(e){return(await m(e,{withFileTypes:!0})).map((e=>({name:e.name,type:e.isFile()?l.FileType.File:e.isDirectory()?l.FileType.Directory:e.isSymbolicLink()?l.FileType.SymbolicLink:l.FileType.Unknown})))}}t.LocalExecServer=class{constructor(){this.attempt=0,this.fs=new D}async spawn(e,t,n){return new Promise(((r,o)=>{try{const i=(0,a.spawn)(e,t,{cwd:n?.cwd,env:n?.env?{...process.env,...n.env}:process.env});i.on("error",(e=>{o(e)})),i.on("spawn",(()=>{r(new w(i))}))}catch(e){o(e)}}))}spawnRemoteServerConnector(e,t,n){throw new Error("Remote server connector not implemented in LocalExecServer")}downloadCliExecutable(e,t,n,r){throw new Error("CLI executable download not implemented in LocalExecServer")}async env(){const e={};for(const[t,n]of Object.entries(process.env))void 0!==n&&(e[t]=n);return{env:e,osPlatform:process.platform}}async kill(e){try{process.kill(e)}catch(t){throw new Error(`Failed to kill process ${e}: ${t}`)}}async tcpConnect(e,t){return new Promise(((n,r)=>{const o=new d.Socket;o.connect(t,e,(()=>{const e=new l.EventEmitter,t={write:e=>o.write(e),end:()=>o.end(),onEnd:new Promise((e=>{o.on("end",(()=>e()))})),onDidReceiveMessage:e.event,dispose:()=>{o.destroy(),e.dispose()}},r=new Promise((e=>{o.on("close",(()=>e()))}));o.on("data",(t=>{e.fire(new Uint8Array(t))})),n({stream:t,done:r})})),o.on("error",(e=>{r(e)}))}))}dispose(){}}},527:function(e,t,n){var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t}),a=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.DevContainerResolver=t.DEV_CONTAINER_AUTHORITY=void 0,t.getDevContainerAuthority=function(e){return`${t.DEV_CONTAINER_AUTHORITY}+${e}`};const c=s(n(398)),d=s(n(982)),l=n(376),u=n(763),f=n(596),h=s(n(873)),p=s(n(928)),g=s(n(857)),m=a(n(467)),E=n(582),v=n(964);t.DEV_CONTAINER_AUTHORITY="dev-container",t.DevContainerResolver=class{constructor(e,t){this.context=e,this.logger=t,this.monitoringAbortControllers=[]}resolve(e,t){this.resolverContext=t;const n=void 0!==t.execServer;return this.resolveHelper(e,t).then((({localPort:e,connectionToken:t})=>new c.ResolvedAuthority("127.0.0.1",e,t)),(e=>{throw new Error(n?"Failed to resolve authority: "+e:"Failed to resolve remote server connector: "+e)}))}resolveHelper(e,t){const n=m.default.parseAuthority(e);this.logger.info(`Resolving dev container authority '${e}' (attempt #${t.resolveAttempt}) container '${n.toJSON()}'`);const r=c.workspace.getConfiguration("remote.windsurfSSH.experimental").get("serverDownloadUrlTemplate");return c.window.withProgress({location:c.ProgressLocation.Notification,title:"Setting up Dev Container ([details](command:windsurf-dev-containers.showLog))",cancellable:!1},(async o=>{const i=t.execServer?t.execServer:new v.LocalExecServer,s=await(0,f.getVSCodeServerConfig)(),a=await(0,l.getDevContainerCliPath)(this.context,i,s);let u=!1;return this.logger.info("setup devContainers"),new Promise((async(t,f)=>{o.report({message:"Starting Windsurf Dev Containers..."}),this.logger.info("Starting Windsurf Dev Containers...");const m=e=>{this.logger.error(e),u||(u=!0,this.logger.show(),c.window.showErrorMessage(e,{modal:!0}).then((()=>{f(e)}))),f(e)};let _,w=E.ROOT_USER;this.logger.info("Checking docker version runs");try{await(0,l.checkDockerVersion)(i)}catch(e){return void m(`Failed to check docker version: ${e.message}`)}if("config"===n.getType()){const e=n.getConfigSetting();o.report({message:"Starting Dev Container..."});const[t,r]=await this.startDevContainer(e,a,i);if(r)return void m(r);if(!t?.containerId)return void m("No container ID found in output");_=t.containerId;const s=await(0,l.readDevContainerConfig)({workspaceFolder:e.workspacePath,includeMergedConfig:!1,devcontainerId:_,devcontainerPath:e.devcontainerPath,execServer:i,devcontainerCliPath:a});if(!s)return void m("Failed to read Dev Container configuration. Please ensure the container exists and the configuration is valid.");this.logger.info("Finished Read Dev Container Configuration."),w=await(0,l.findBestRemoteUser)(s,_,i)}else{_=n.getContainerSetting().containerId;try{const e=await(0,l.inspectDockerContainer)(_,i);if("running"!==e.State.Status)return void m(`Failed to start Dev Container: container is not running: ${_}`);w=await(0,l.findBestRemoteUserForAttach)(e,_,i);const[t,n]=await this.setupDevContainer(_,a,i);if(n)return void m(n)}catch(e){return void m(`Failed to start Dev Container: container doesn't exist or isn't running: ${e}`)}}this.logger.info(`Remote user: ${w}`),this.logger.info("Installing remote server in container...");let R=String(d.randomInt(0xffffffffff));const S=d.randomBytes(12).toString("hex");let D=r??s.serverDownloadUrlTemplate??h.getDefaultDownloadUrlTemplate(s.quality);D===h.getDefaultDownloadUrlTemplate("stable")&&"insider"===s.quality&&(D=h.getDefaultDownloadUrlTemplate("insider"));const I={id:S,vscodeVersion:s.vscodeVersion,windsurfVersion:s.windsurfVersion,commit:s.commit,quality:s.quality,release:s.release,extensionIds:[],envVariables:[],useSocketPath:!1,serverApplicationName:s.serverApplicationName,serverDataFolderName:s.serverDataFolderName,serverDownloadUrlTemplate:D,distroId:s.commit+(s.isDevelopment?"-dev":""),disableServerChecksum:c.workspace.getConfiguration("remote.windsurfDevContainers.experimental").get("disableServerChecksum",!1)},$=h.generateBashInstallScript(I,R);this.logger.info(`Bash script:\n${$}`),this.logger.info(`Install options: ${JSON.stringify(I,void 0,2)}`);const O=i instanceof v.LocalExecServer?p.join(g.tmpdir(),`${S}.sh`):p.posix.join("/tmp",`${S}.sh`);let y="",C="";try{await(0,l.setupBashScript)(O,$,i)}catch(e){return void m(`Error writing server installation script: ${e}`)}const P=p.posix.join("/tmp",`${S}.sh`);try{({stdout:y,stderr:C}=await(0,l.executeCommand)("docker",["cp","-a",O,`${_}:${P}`],i,void 0))}catch(e){return void m(`Failed to copy script to container: ${e}\nstdout: ${y}\nstderr: ${C}`)}if(C)return void m(`Error copying script to container: ${C}\nstdout: ${y}`);try{({stdout:y,stderr:C}=await(0,l.executeCommand)("docker",["exec","-u","root",_,"chown",`${w}:`,P],i,void 0))}catch(e){return void m(`Failed to change server installation script owner to ${w}: ${e}\nstdout: ${y}\nstderr: ${C}`)}if(C)return void m(`Failed to change server installation script owner: ${C}\nstdout: ${y}`);try{({stdout:y,stderr:C}=await(0,l.executeCommand)("docker",["exec","-u",w,_,"chmod","+x",P],i,void 0))}catch(e){return void m(`Failed to change server installation script permissions: ${e}\nstdout: ${y}\nstderr: ${C}`)}if(C)return m(`Failed to change server installation script permissions: ${C}\nstdout: ${y}`),void m(C);const b=c.workspace.getConfiguration("remote.windsurfDevContainers").get("enableSSHAgentForwarding")??!0;this.logger.info(`SSH Agent Forwarding: ${b}`);const L=(await(0,l.getEnv)(i)).SSH_AUTH_SOCK,T=p.posix.join("/tmp",`windsurf-remote-ssh-${S}.sock`);try{const e=b&&L?["exec","-e",`SSH_AUTH_SOCK=${T}`,"-u",w,_,"bash",P]:["exec","-u",w,_,"bash",P];({stdout:y,stderr:C}=await(0,l.executeCommand)("docker",e,i,void 0))}catch(e){return void m(`Failed to install remote server in container: ${e}`)}if(C)return void m(C);this.logger.info("Server install output:",y);const k=y;try{({stdout:y,stderr:C}=await(0,l.executeCommand)("docker",["exec","-u",w,_,"rm",P],i,void 0))}catch(e){return void m(`Failed to delete script from container: ${e}`)}if(C)return void m(C);try{await(0,l.deleteFile)(O,i)}catch(e){this.logger.info(`Failed to delete temporary script: ${e}`)}this.logger.info(`Finished installing remote server in container. Output: ${k}`);const A=k.match(/connectionToken==(.+)==/);A&&(R=A[1]);const x=k.match(/listeningOn==(\d+)==/),N=k.match(/exitCode==(\d+)==/);if(N&&"1"===N[1])m(`An error occurred while starting the server, with exit code: ${N[1]}\nMore info can be found in the Output tab.`);else{if(x){const n=parseInt(x[1]);if(isNaN(n))return void m("Failed to parse server port");let r="";try{({stdout:r}=await(0,l.executeCommand)("docker",["inspect","-f","{{ .Config.Image }}",_],i,void 0))}catch(e){return void m(`Failed to get image name from container: ${e}`)}this.logger.info(`Image name: ${r}`);const o=i.remoteAuthority?`@${i.remoteAuthority}`:"";this.labelFormatterDisposable=c.workspace.registerResourceLabelFormatter({scheme:"vscode-remote",authority:e+o,formatting:{label:"${path}",separator:"/",tildify:!0,workspaceSuffix:`Container ${r}`}}),c.WindsurfDevContainers.registerWindsurfDevContainerContext({imageName:r,authority:e+o});const s=this.getRemoteServerNodePath(k);if(null===s)return void m("Failed to find RemoteServerPath in output");if(this.logger.info(`Remote server node path: ${s}`),b&&L){const e=await Promise.race([this.forwardSocket(_,L,T,s,w,i),new Promise((e=>{setTimeout((()=>{e("SSH agent forwarding timed out after 5 seconds")}),5e3)}))]);""!==e&&this.logger.error(`Failed to forward SSH_AUTH_SOCK: ${e}`)}const[a,d]=await this.forwardPortWithRetry(n,_,s,w,void 0,i);return""!==d?void m(d):(this.logger.info(`Forwarded port resolving: ${a}`),this.logger.info(`Connection token: ${R}`),void t({localPort:a,connectionToken:R}))}m("Failed to find server port in output")}}))}))}async startDevContainer(e,t,n){this.logger.info("Start devcontainer up ...");const r=await(0,l.getNodePath)(n);this.logger.info(`Node path: ${r}`),this.logger.info(`Devcontainer CLI path: ${t}`);try{const o=await(0,l.executeCommand)(r,[t,"up",`--workspace-folder=${e.workspacePath}`,`--config=${e.devcontainerPath}`,"--workspace-mount-consistency=cached","--gpu-availability=detect","--log-level=debug","--log-format=text","--default-user-env-probe=loginInteractiveShell","--update-remote-user-uid-default","--include-configuration","--include-merged-configuration","--skip-post-create"],n,{...await(0,l.getEnv)(n),ELECTRON_RUN_AS_NODE:"1"});return this.parseDevContainerOutput(o.stdout)}catch(e){return this.logger.error("Failed to start devcontainer:",e),[null,e.message]}}parseDevContainerOutput(e){try{const t=JSON.parse(e);if(this.logger.info(`Devcontainer output as JSON: ${JSON.stringify(t,null,4)}`),!t||"object"!=typeof t)return[null,"Invalid JSON structure"];const n=t;return"success"!==n.outcome?[n,`Failed to start devcontainer: ${n.message}`]:(this.logger.info(`Found container ID: ${n.containerId}`),[n,""])}catch(e){return this.logger.error("Failed to parse devcontainer output as JSON:",e),[null,"Failed to parse output as JSON"]}}async setupDevContainer(e,t,n){this.logger.info(`Setup devcontainer on docker container ${e} ...`);try{const r=await(0,l.getNodePath)(n),o=await(0,l.executeCommand)(r,[t,"set-up",`--container-id=${e}`,"--log-level=debug","--log-format=text","--default-user-env-probe=loginInteractiveShell","--include-configuration","--include-merged-configuration","--skip-post-create"],n,{...await(0,l.getEnv)(n),ELECTRON_RUN_AS_NODE:"1"});return this.parseDevContainerOutput(o.stdout)}catch(e){return this.logger.error("Failed to setup devcontainer:",e),[null,e.message]}}async forwardPortWithRetry(e,t,n,r,o=3,i){let s=0,a="";for(;s<o;){try{const o=await(0,u.forwardPort)(e,t,n,r,this.logger,i);return this.monitoringAbortControllers.push(o.abort),this.logger.info(`Forwarded port: ${o.port}`),[o.port,""]}catch(e){a=e.message}s++,s<o&&(this.logger.info(`Port forwarding attempt ${s} failed, retrying in 1 second...`),await new Promise((e=>setTimeout(e,1e3))))}return[0,`Port forwarding failed after ${o} attempts. Last error: ${a}`]}async forwardSocket(e,t,n,r,o,i){this.logger.info(`Forwarding socket ${t} to ${n}`);try{const s=await(0,u.forwardSocket)(e,t,n,r,o,this.logger,i);return this.monitoringAbortControllers.push(s.abort),""}catch(e){return e.message}}getRemoteServerNodePath(e){const t=this.getRemoteServerHomeAndCommitHash(e);return null===t?null:`${t.serverHome}/bin/${t.commitHash}/node`}getRemoteServerHomeAndCommitHash(e){const t=e.match(/logFile==(.+?\/\.[^\/]+)\/\.([a-f0-9]{40}(?:-dev)?)\.log==/);return t?{serverHome:t[1],commitHash:t[2]}:null}async getCanonicalURI(e){return c.Uri.file(e.path)}dispose(){for(const e of this.monitoringAbortControllers)try{e()}catch(e){this.logger.info(`Error aborting monitoring process: ${e}`)}this.monitoringAbortControllers=[],this.resolverContext?.execServer&&(0,l.isDisposable)(this.resolverContext.execServer)&&this.resolverContext.execServer.dispose(),this.logger.dispose(),this.labelFormatterDisposable?.dispose()}}},596:function(e,t,n){var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.getVSCodeServerConfig=async function(){const e=await async function(){if(!l){const e=await c.promises.readFile(d.join(a.env.appRoot,"product.json"),"utf8");l=JSON.parse(e)}return l}(),t=a.workspace.getConfiguration("remote.windsurfSSH.experimental").get("serverBinaryName",""),n=a.version.replace("-insider","");let r=e.windsurfVersion,o=e.commit,i=e.quality,s=t||e.serverApplicationName,f=e.serverDataFolderName,h=!1;if(void 0===i){const e=await fetch(u),t=await e.json();r=t.windsurfVersion,o=t.version,i="insider",s="windsurf-server-insiders",f=".windsurf-server-insiders",h=!0}return{vscodeVersion:n,windsurfVersion:r,commit:o,quality:i,release:e.release,serverApplicationName:s,serverDataFolderName:f,serverDownloadUrlTemplate:e.serverDownloadUrlTemplate,isDevelopment:h}};const a=s(n(398)),c=s(n(896)),d=s(n(928));let l;const u="https://windsurf-nightly.corp.exafunction.com/api/update/linux-reh-x64/insider/latest"},873:function(e,t,n){var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.ServerInstallError=void 0,t.getDefaultDownloadUrlTemplate=function(e){return"insider"===e||void 0===e?"https://windsurf-nightly.codeiumdata.com/${os}-reh-${arch}/${quality}/${commit}/windsurf-reh-${os}-${arch}-${windsurfVersion}.tar.gz":"https://windsurf-stable.codeiumdata.com/${os}-reh-${arch}/${quality}/${commit}/windsurf-reh-${os}-${arch}-${windsurfVersion}.tar.gz"},t.getDefaultCliDownloadUrlTemplate=function(e){return"insider"===e||void 0===e?"https://windsurf-nightly.codeiumdata.com/${os}-cli-${arch}/${quality}/${commit}/Windsurf-cli-${os}-${arch}-${windsurfVersion}.tar.gz":"https://windsurf-stable.codeiumdata.com/${os}-cli-${arch}/${quality}/${commit}/Windsurf-cli-${os}-${arch}-${windsurfVersion}.tar.gz"},t.parseServerInstallOutput=function(e,t){const n=`${t}: start`,r=`${t}: end`,o=e.indexOf(n);if(o<0)return;const i=e.indexOf(r,o+n.length);if(i<0)return;const s={},a=e.substring(o+n.length,i).split(/\r?\n/);for(const e of a){const[t,n]=e.split("==");s[t]=n}return s},t.generateBashInstallScript=function({id:e,quality:t,vscodeVersion:n,windsurfVersion:r,commit:o,release:i,extensionIds:s,envVariables:c,useSocketPath:d,serverApplicationName:l,serverDataFolderName:u,serverDownloadUrlTemplate:f,distroId:h,disableServerChecksum:p},g){const m=s.map((e=>"--install-extension "+e)).join(" ");return`\n# Server installation script\nDISABLE_SERVER_CHECKSUM="${p}"\nTMP_DIR="\${XDG_RUNTIME_DIR:-"/tmp"}"\n\nDISTRO_VSCODE_VERSION="${n}"\nDISTRO_WINDSURF_VERSION="${r}"\nDISTRO_COMMIT="${o}"\nDISTRO_ID="${h}"\nDISTRO_QUALITY="${t}"\nDISTRO_VSCODIUM_RELEASE="${i??""}"\n\nSERVER_APP_NAME="${l}"\nSERVER_INITIAL_EXTENSIONS="${m}"\nSERVER_LISTEN_FLAG="${d?`--socket-path="$TMP_DIR/vscode-server-sock-${a.randomUUID()}"`:"--port=0"}"\nSERVER_DATA_DIR="$HOME/${u}"\nSERVER_DIR="$SERVER_DATA_DIR/bin/$DISTRO_ID"\nSERVER_SCRIPT="$SERVER_DIR/bin/$SERVER_APP_NAME"\nSERVER_LOGFILE="$SERVER_DATA_DIR/.$DISTRO_ID.log"\nSERVER_PIDFILE="$SERVER_DATA_DIR/.$DISTRO_ID.pid"\nSERVER_TOKENFILE="$SERVER_DATA_DIR/.$DISTRO_ID.token"\nSERVER_SSH_AGENT_SOCKET="$SERVER_DATA_DIR/.$DISTRO_ID-ssh-auth.sock"\nSERVER_ARCH=\nSERVER_CONNECTION_TOKEN=\nSERVER_DOWNLOAD_URL=\n\nLISTENING_ON=\nOS_RELEASE_ID=\nARCH=\nPLATFORM=\nSERVER_PID=\n\nGLIBC_VERSION_GOOD=\n\n# Add lock mechanism\nLOCK_FILE="$SERVER_DATA_DIR/.installation_lock"\n\n# Function to acquire lock\nacquire_lock() {\n\texec 200>$LOCK_FILE\n\techo "Waiting for lock..."\n\tflock 200\n\techo "Lock acquired, proceeding with installation."\n}\n\n# Function to release lock\nrelease_lock() {\n\tflock -u 200\n\texec 200>&-\n}\n\ntrap release_lock EXIT INT TERM\n\n# Mimic output from logs of remote-ssh extension\nprint_install_results_and_exit() {\n\tif [[ $1 -eq 1 ]]; then\n\t\techo ""\n\t\techo "Error: installation failed."\n\t\tif [[ -f "$SERVER_LOGFILE" ]]; then\n\t\t\techo "Server log:\n $(cat "$SERVER_LOGFILE")\n"\n\t\tfi\n\tfi\n\tif [[ "$GLIBC_VERSION_GOOD" = "false" ]]; then\n\t\techo "Warning: valid glibc version not found. Windsurf only supports remote connections with glibc >= 2.28, such as Ubuntu 20.04, Debian 10, or CentOS 8."\n\t\techo ""\n\tfi\n\techo "${e}: start"\n\techo "exitCode==$1=="\n\techo "listeningOn==$LISTENING_ON=="\n\techo "connectionToken==$SERVER_CONNECTION_TOKEN=="\n\techo "logFile==$SERVER_LOGFILE=="\n\techo "osReleaseId==$OS_RELEASE_ID=="\n\techo "arch==$ARCH=="\n\techo "platform==$PLATFORM=="\n\techo "tmpDir==$TMP_DIR=="\n\t${c.map((e=>`echo "${e}==$${e}=="`)).join("\n")}\n\techo "${e}: end"\n\n\texit 0\n}\n\nprint_install_results_and_wait() {\n\t# Check server is indeed running\n\tif [[ ! -f $SERVER_PIDFILE ]]; then\n\t\tSERVER_PID=$(pgrep -f "$SERVER_SCRIPT")\n\t\tif [[ -n $SERVER_PID ]]; then\n\t\t\ttouch $SERVER_PIDFILE\n\t\t\techo $SERVER_PID > $SERVER_PIDFILE\n\t\tfi\n\tfi\n\tif [[ -f $SERVER_PIDFILE ]]; then\n\t\tSERVER_PID="$(cat $SERVER_PIDFILE)"\n\t\tif [[ -z $SERVER_PID ]]; then\n\t\t\tSERVER_PID=$(pgrep -f "$SERVER_SCRIPT")\n\n\t\t\tif [[ -n $SERVER_PID ]]; then\n\t\t\t\techo $SERVER_PID > $SERVER_PIDFILE\n\t\t\telse\n\t\t\t\tprint_install_results_and_exit 1\n\t\t\tfi\n\t\tfi\n\telse\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\techo "${e}: start"\n\t# pretend exit code is 0\n\techo "exitCode=0"\n\techo "listeningOn==$LISTENING_ON=="\n\techo "connectionToken==$SERVER_CONNECTION_TOKEN=="\n\techo "logFile==$SERVER_LOGFILE=="\n\techo "osReleaseId==$OS_RELEASE_ID=="\n\techo "arch==$ARCH=="\n\techo "platform==$PLATFORM=="\n\techo "tmpDir==$TMP_DIR=="\n\t${c.map((e=>`echo "${e}==$${e}=="`)).join("\n")}\n\techo "${e}: end"\n\n\trelease_lock\n\n\t# Wait for server to exit\n\twhile ps -p $SERVER_PID >/dev/null 2>&1\n\tdo\n\t\tsleep 10\n\tdone\n}\n\ndelete_server() {\n\tif [[ -n "$OLD_DISTRO_ID" ]]; then\n\t\techo "Cleaning up files for $OLD_DISTRO_ID..."\n\t\trm -rf "$SERVER_DATA_DIR/bin/$OLD_DISTRO_ID"\n\t\trm -f "$SERVER_DATA_DIR/.$OLD_DISTRO_ID.pid"\n\t\trm -f "$SERVER_DATA_DIR/.$OLD_DISTRO_ID.log"\n\t\trm -f "$SERVER_DATA_DIR/.$OLD_DISTRO_ID.token"\n\t\trm -f "$SERVER_DATA_DIR/.$OLD_DISTRO_ID-ssh-auth.sock"\n\t\techo "$OLD_DISTRO_ID cleaned up."\n\tfi\n}\n\nclean_up_old_servers() {\n\techo "Cleaning up old server installations..."\n\tif [[ -d "$SERVER_DATA_DIR/bin" ]]; then\n\t\tfor OLD_DISTRO_DIR_FULL_PATH in "$SERVER_DATA_DIR"/bin/*; do\n\t\t\tif [[ -d "$OLD_DISTRO_DIR_FULL_PATH" ]]; then\n\t\t\t\tOLD_DISTRO_ID=$(basename "$OLD_DISTRO_DIR_FULL_PATH")\n\n\t\t\t\tif [[ "$OLD_DISTRO_ID" = "$DISTRO_ID" ]]; then\n\t\t\t\t\tcontinue\n\t\t\t\tfi\n\n\t\t\t\techo "Checking old server: $OLD_DISTRO_ID"\n\t\t\t\tOLD_SERVER_PIDFILE="$SERVER_DATA_DIR/.$OLD_DISTRO_ID.pid"\n\n\t\t\t\techo "Old server PID file: $OLD_SERVER_PIDFILE"\n\n\t\t\t\tNUMBER_OF_EXECUTABLES=$(find "$OLD_DISTRO_DIR_FULL_PATH/bin/" -maxdepth 1 -type f -executable | wc -l)\n\t\t\t\tif (( $NUMBER_OF_EXECUTABLES == 0 )); then\n\t\t\t\t\techo "No executables found in $OLD_DISTRO_DIR_FULL_PATH/bin/"\n\t\t\t\t\tdelete_server\n\t\t\t\t\tcontinue\n\t\t\t\telif (( $NUMBER_OF_EXECUTABLES > 1 )); then\n\t\t\t\t\techo "Multiple executables found in $OLD_DISTRO_DIR_FULL_PATH/bin/, leaving it alone."\n\t\t\t\t\tcontinue\n\t\t\t\telse\n\t\t\t\t\tOLD_SERVER_SCRIPT=$(find "$OLD_DISTRO_DIR_FULL_PATH/bin/" -maxdepth 1 -type f -executable)\n\t\t\t\t\techo "Old server script: $OLD_SERVER_SCRIPT"\n\t\t\t\tfi\n\n\t\t\t\tOLD_SERVER_SCRIPT_PATH=$(readlink -f "$OLD_SERVER_SCRIPT")\n\t\t\t\techo "Old server script path: $OLD_SERVER_SCRIPT_PATH"\n\n\t\t\t\tif [[ -z "$OLD_SERVER_SCRIPT_PATH" ]]; then\n\t\t\t\t\techo "Something went wrong, old server script path is empty. Skipping old server $OLD_DISTRO_ID."\n\t\t\t\t\tcontinue\n\t\t\t\tfi\n\n\t\t\t\tif [[ ! -f "$OLD_SERVER_PIDFILE" ]]; then\n\t\t\t\t\tOLD_PID=$(pgrep -f "$OLD_SERVER_SCRIPT_PATH")\n\t\t\t\t\tif [[ -n "$OLD_PID" ]]; then\n\t\t\t\t\t\ttouch "$OLD_SERVER_PIDFILE"\n\t\t\t\t\t\techo "$OLD_PID" > "$OLD_SERVER_PIDFILE"\n\t\t\t\t\t\techo "Wrote to PID file for $OLD_DISTRO_ID: $OLD_SERVER_PIDFILE"\n\t\t\t\t\tfi\n\t\t\t\tfi\n\n\t\t\t\tif [[ -f "$OLD_SERVER_PIDFILE" ]]; then\n\t\t\t\t\tOLD_PID=$(cat "$OLD_SERVER_PIDFILE")\n\t\t\t\t\techo "Old server PID: $OLD_PID"\n\t\t\t\t\tif [[ -z "$OLD_PID" ]]; then\n\t\t\t\t\t\tOLD_PID=$(pgrep -f "$OLD_SERVER_SCRIPT_PATH")\n\t\t\t\t\t\tif [[ -n "$OLD_PID" ]]; then\n\t\t\t\t\t\t\techo "$OLD_PID" > "$OLD_SERVER_PIDFILE"\n\t\t\t\t\t\t\techo "Restored PID file for $OLD_DISTRO_ID: $OLD_SERVER_PIDFILE."\n\t\t\t\t\t\tfi\n\t\t\t\t\tfi\n\t\t\t\t\t# Check if PID is non-empty and if process with OLD_PID is running\n\t\t\t\t\tif [[ -n "$OLD_PID" ]] && ps -p "$OLD_PID" > /dev/null 2>&1; then\n\t\t\t\t\t\tOLD_RUNNING_PROCESS="$(ps -o pid,args -p $OLD_PID | grep -F $OLD_SERVER_SCRIPT_PATH)"\n\t\t\t\t\t\tif [[ -n "$OLD_RUNNING_PROCESS" ]]; then\n\t\t\t\t\t\t\techo "Old server $OLD_DISTRO_ID (PID $OLD_PID) is running."\n\t\t\t\t\t\tfi\n\t\t\t\t\telse\n\t\t\t\t\t\techo "Old server $OLD_DISTRO_ID (PID $OLD_PID) is not running."\n\t\t\t\t\t\tdelete_server\n\t\t\t\t\t\tcontinue\n\t\t\t\t\tfi\n\t\t\t\telse\n\t\t\t\t\techo "No PID file found for old server $OLD_DISTRO_ID ($OLD_SERVER_PIDFILE) and no processes matching the script path."\n\t\t\t\t\tdelete_server\n\t\t\t\t\tcontinue\n\t\t\t\tfi\n\t\t\tfi\n\t\tdone\n\t\techo "Finished cleaning up old server installations."\n\telse\n\t\techo "No server bin directory found at $SERVER_DATA_DIR/bin. Skipping cleanup of old servers."\n\tfi\n}\n\nterminateTree() {\n\tfor cpid in $(/usr/bin/pgrep -P $1); do\n\t\tterminateTree $cpid\n\tdone\n\tkill -9 $1 > /dev/null 2>&1\n}\n\nkill_running_server() {\n\tif [[ -n "$1" ]]; then\n\t\techo "Killing server process with PID $1 (and all its children)"\n\t\tterminateTree $1\n\tfi\n}\n\n# Check if platform is supported\nKERNEL="$(uname -s)"\ncase $KERNEL in\n\tDarwin)\n\t\tPLATFORM="darwin"\n\t\t;;\n\tLinux)\n\t\tPLATFORM="linux"\n\t\t;;\n\tFreeBSD)\n\t\tPLATFORM="freebsd"\n\t\t;;\n\tDragonFly)\n\t\tPLATFORM="dragonfly"\n\t\t;;\n\t*)\n\t\techo "Error platform not supported: $KERNEL"\n\t\tprint_install_results_and_exit 1\n\t\t;;\nesac\n\n# Check machine architecture\nARCH="$(uname -m)"\ncase $ARCH in\n\tx86_64 | amd64)\n\t\tSERVER_ARCH="x64"\n\t\t;;\n\tarmv7l | armv8l)\n\t\tSERVER_ARCH="armhf"\n\t\t;;\n\tarm64 | aarch64)\n\t\tSERVER_ARCH="arm64"\n\t\t;;\n\tppc64le)\n\t\tSERVER_ARCH="ppc64le"\n\t\t;;\n\triscv64)\n\t\tSERVER_ARCH="riscv64"\n\t\t;;\n\t*)\n\t\techo "Error architecture not supported: $ARCH"\n\t\tprint_install_results_and_exit 1\n\t\t;;\nesac\n\n# Attempt to get checksum from manifest\n\nif [[ "$DISTRO_QUALITY" = "insider" ]]; then\n\tMANIFEST_URL="https://windsurf-nightly.codeiumdata.com/\${PLATFORM}-reh-\${SERVER_ARCH}/\${DISTRO_QUALITY}/manifest-\${DISTRO_COMMIT}.json"\nelse\n\tMANIFEST_URL="https://windsurf-stable.codeiumdata.com/\${PLATFORM}-reh-\${SERVER_ARCH}/\${DISTRO_QUALITY}/manifest-\${DISTRO_COMMIT}.json"\nfi\n\nif [[ $DISABLE_SERVER_CHECKSUM = "true" ]]; then\n\tSHA256_HASH=""\nelse\n\t# Curl the manifest URL, and get the sha256 hash\n\tSHA256_HASH="$(curl -s "$MANIFEST_URL" --max-time 5 | grep -o '"sha256hash": "[^"]*"' | sed 's/"sha256hash": "\\(.*\\)"/\\1/')"\n\n\tif [[ $? -ne 0 || -z $SHA256_HASH ]]; then\n\t\techo "Warning: could not get sha256 hash from manifest: will not verify checksum."\n\t\tSHA256_HASH=""\n\tfi\n\n\tif ! sha256sum --version > /dev/null 2>&1; then\n\t\techo "Warning: sha256sum could not be found: will not verify checksum."\n\t\tSHA256_HASH=""\n\tfi\nfi\n\n# https://www.freedesktop.org/software/systemd/man/os-release.html\nOS_RELEASE_ID="$(grep -i '^ID=' /etc/os-release 2>/dev/null | sed 's/^ID=//gi' | sed 's/"//g')"\nif [ -z $OS_RELEASE_ID ]; then\n\tOS_RELEASE_ID="$(grep -i '^ID=' /usr/lib/os-release 2>/dev/null | sed 's/^ID=//gi' | sed 's/"//g')"\n\tif [ -z $OS_RELEASE_ID ]; then\n\t\tOS_RELEASE_ID="unknown"\n\tfi\nfi\n\n# Create installation folder\nif [ ! -d $SERVER_DIR ]; then\n\tmkdir -p $SERVER_DIR\n\tif (( $? > 0 )); then\n\t\techo "Error creating server install directory"\n\t\tprint_install_results_and_exit 1\n\tfi\nfi\n\n# Acquire lock at the beginning of the script\nacquire_lock\n\n# Add trap to release lock on exit\ntrap release_lock EXIT\n\nclean_up_old_servers\n\nif [ -n "$SSH_AUTH_SOCK" ]; then\n\tln -s -f $SSH_AUTH_SOCK $SERVER_SSH_AGENT_SOCKET\nfi\nexport SSH_AUTH_SOCK=$SERVER_SSH_AGENT_SOCKET\n\n\nSERVER_DOWNLOAD_URL="$(echo "${f.replace(/\$\{/g,"\\${")}" | sed "s/\\\${quality}/$DISTRO_QUALITY/g" | sed "s/\\\${vscodeVersion}/$DISTRO_VSCODE_VERSION/g" | sed "s/\\\${commit}/$DISTRO_COMMIT/g" | sed "s/\\\${os}/$PLATFORM/g" | sed "s/\\\${arch}/$SERVER_ARCH/g" | sed "s/\\\${release}/$DISTRO_VSCODIUM_RELEASE/g" | sed "s/\\\${windsurfVersion}/$DISTRO_WINDSURF_VERSION/g")"\n\nif [[ "$PLATFORM" == "linux" ]]; then\n\t# Check ldd version based on format "ldd (.*) 2.28"\n\tversion=$(ldd --version | head -n 1 | grep -oE '[0-9]+.[0-9]+$')\n\tif (( $? > 0 )); then\n\t\techo "Warning: ldd not found"\n\t\tGLIBC_VERSION_GOOD="false"\n\telse\n\t\tmajor=$(echo "$version" | cut -d '.' -f 1)\n\t\tminor=$(echo "$version" | cut -d '.' -f 2)\n\n\t\tif [[ "$major" -eq 2 && "$minor" -ge 28 ]]; then\n\t\t\tGLIBC_VERSION_GOOD="true"\n\t\telse\n\t\t\tGLIBC_VERSION_GOOD="false"\n\t\tfi\n\tfi\n\n\tif [[ "$GLIBC_VERSION_GOOD" = "false" ]]; then\n\t\techo "Warning: valid glibc version not found. Windsurf only supports remote connections with glibc >= 2.28, such as Ubuntu 20.04, Debian 10, or CentOS 8."\n\tfi\nfi\n\n# Check if server script is already installed\nif [ ! -f $SERVER_SCRIPT ]; then\n\tif [ "$PLATFORM" != "darwin" ] && [ "$PLATFORM" != "linux" ]; then\n\t\techo "Error "$PLATFORM" needs manual installation of remote extension host"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tpushd $SERVER_DIR > /dev/null\n\n\ttemp_file=$(mktemp)\n\tif [ ! -z $(which curl) ]; then\n\t\tcurl --retry 3 --connect-timeout 10 --location --show-error --silent --output $temp_file $SERVER_DOWNLOAD_URL\n\telif [ ! -z $(which wget) ]; then\n\t\twget --tries=3 --timeout=10 --continue --quiet -O $temp_file $SERVER_DOWNLOAD_URL\n\telse\n\t\techo "Error no tool to download server binary"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tif (( $? > 0 )); then\n\t\techo "Error downloading server from $SERVER_DOWNLOAD_URL"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tmv $temp_file vscode-server.tar.gz\n\n\t# Before extracting it, check the checksum\n\tif [[ ! -z $SHA256_HASH ]]; then\n\t\t# Calculate the checksum of the downloaded file\n\t\tCALCULATED_HASH="$(sha256sum vscode-server.tar.gz | cut -d ' ' -f 1)"\n\t\t\n\t\t# Compare with the expected hash\n\t\tif [[ "$CALCULATED_HASH" != "$SHA256_HASH" ]]; then\n\t\t\techo "Error: Checksum verification failed: this usually means the server failed to download correctly."\n\t\t\techo "Expected: $SHA256_HASH"\n\t\t\techo "Got: $CALCULATED_HASH"\n\n\t\t\trm -f vscode-server.tar.gz\n\n\t\t\tprint_install_results_and_exit 1\n\t\telse\n\t\t\techo "Checksum verification passed: $CALCULATED_HASH"\n\t\tfi\n\telse \n\t\techo "Skipping checksum verification"\n\tfi\n\n\ttar -xf vscode-server.tar.gz --strip-components 1\n\tif (( $? > 0 )); then\n\t\techo "Error while extracting server contents"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tif [ ! -f $SERVER_SCRIPT ]; then\n\t\techo "Error server contents are corrupted"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\trm -f vscode-server.tar.gz\n\n\tpopd > /dev/null\nelse\n\techo "Server script already installed in $SERVER_SCRIPT"\nfi\n\n# Try to find if server is already running\nif [[ -f $SERVER_PIDFILE ]]; then\n\tSERVER_PID="$(cat $SERVER_PIDFILE)"\n\tif [[ -z $SERVER_PID ]]; then\n\t\tSERVER_PID=$(pgrep -f "$SERVER_SCRIPT")\n\t\tif [[ -n $SERVER_PID ]]; then\n\t\t\ttouch $SERVER_PIDFILE\n\t\t\techo $SERVER_PID > $SERVER_PIDFILE\n\t\tfi\n\tfi\n\tif [[ -n $SERVER_PID ]]; then\n\t\tif [[ -n $(ps -o pid,args -p $SERVER_PID | grep $SERVER_SCRIPT) ]]; then\n\t\t\tSERVER_RUNNING_PROCESS="$SERVER_PID"\n\t\t\techo "Found running server process: $SERVER_RUNNING_PROCESS"\n\t\tfi\n\tfi\nelse\n\tSERVER_RUNNING_PROCESS="$(pgrep -f "$SERVER_SCRIPT")"\n\tif [[ -z $SERVER_RUNNING_PROCESS ]]; then\n\t\tSERVER_PID=\n\telse\n\t\tSERVER_PID=$SERVER_RUNNING_PROCESS\n\t\ttouch $SERVER_PIDFILE\n\t\techo $SERVER_PID > $SERVER_PIDFILE\n\tfi\nfi\n\nif [ -z "$SERVER_RUNNING_PROCESS" ]; then\n\tif [ -f "$SERVER_LOGFILE" ]; then\n\t\trm $SERVER_LOGFILE\n\tfi\n\tif [ -f "$SERVER_TOKENFILE" ]; then\n\t\trm $SERVER_TOKENFILE\n\tfi\n\n\ttouch $SERVER_TOKENFILE\n\tchmod 600 $SERVER_TOKENFILE\n\tSERVER_CONNECTION_TOKEN="${g}"\n\techo $SERVER_CONNECTION_TOKEN > $SERVER_TOKENFILE\n\n\texport REMOTE_CONTAINERS=true\n\n\t$SERVER_SCRIPT --start-server --host=127.0.0.1 $SERVER_LISTEN_FLAG $SERVER_INITIAL_EXTENSIONS --connection-token-file $SERVER_TOKENFILE --telemetry-level off --enable-remote-auto-shutdown --accept-server-license-terms &> $SERVER_LOGFILE & disown\n\techo $! > $SERVER_PIDFILE\n\tSERVER_PID=$(cat $SERVER_PIDFILE)\nelse\n\techo "Server script is already running $SERVER_SCRIPT"\nfi\n\nif [[ -f $SERVER_TOKENFILE && -n $(cat $SERVER_TOKENFILE) ]]; then\n\tSERVER_CONNECTION_TOKEN="$(cat $SERVER_TOKENFILE)"\nelse\n\techo "Error server token file not found $SERVER_TOKENFILE"\n\tif [[ -n $SERVER_PID ]]; then\n\t\tkill_running_server $SERVER_PID\n\tfi\n\tprint_install_results_and_exit 1\nfi\n\nif [ -f "$SERVER_LOGFILE" ]; then\n\tfor i in {1..5}; do\n\t\tLISTENING_ON="$(cat $SERVER_LOGFILE | grep -E 'Extension host agent listening on .+' | sed 's/Extension host agent listening on //')"\n\t\tif [ -n "$LISTENING_ON" ]; then\n\t\t\tbreak\n\t\tfi\n\t\tsleep 0.5\n\tdone\n\n\tif [ -z "$LISTENING_ON" ]; then\n\t\techo "Error server did not start sucessfully"\n\t\tif [[ -n $SERVER_PID ]]; then\n\t\t\tkill_running_server $SERVER_PID\n\t\tfi\n\t\tprint_install_results_and_exit 1\n\tfi\nelse\n\techo "Error server log file not found $SERVER_LOGFILE"\n\tif [[ -n $SERVER_PID ]]; then\n\t\tkill_running_server $SERVER_PID\n\tfi\n\tprint_install_results_and_exit 1\nfi\n\n# Finish server setup\nprint_install_results_and_exit 0\n`};const a=s(n(982));class c extends Error{constructor(e){super(e)}}t.ServerInstallError=c},512:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0});class n{constructor(e,t,n){this.hostname=e,this.user=t,this.port=n}static parse(e){let t;const r=e.lastIndexOf("@");let o;-1!==r&&(t=e.substring(0,r));const i=e.lastIndexOf(":");-1!==i&&(o=parseInt(e.substring(i+1),10));const s=-1!==r?r+1:0,a=-1!==i?i:e.length,c=e.substring(s,a);return new n(c,t,o)}toString(){let e=this.hostname;return this.user&&(e=`${this.user}@`+e),this.port&&(e+=`:${this.port}`),e}static parseEncoded(e){try{const t=JSON.parse(Buffer.from(e,"hex").toString());return new n(t.hostName,t.user,t.port)}catch{}return n.parse(e.replace(/\\x([0-9a-f]{2})/g,((e,t)=>String.fromCharCode(parseInt(t,16)))))}toEncodedString(){return this.toString().replace(/[A-Z]/g,(e=>`\\x${e.charCodeAt(0).toString(16).toLowerCase()}`))}}t.default=n},582:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.ROOT_USER=void 0,t.ROOT_USER="root"},376:function(e,t,n){var r,o=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),i=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),s=this&&this.__importStar||(r=function(e){return r=Object.getOwnPropertyNames||function(e){var t=[];for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[t.length]=n);return t},r(e)},function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n=r(e),s=0;s<n.length;s++)"default"!==n[s]&&o(t,e,n[s]);return i(t,e),t});Object.defineProperty(t,"__esModule",{value:!0}),t.findRandomPort=function(){return new Promise(((e,t)=>{const n=a.createServer({pauseOnConnect:!0});n.on("error",t),n.on("listening",(()=>{const t=n.address().port;n.close((()=>e(t)))})),n.listen(0,"127.0.0.1")}))},t.sanitizeEnv=g,t.executeCommand=m,t.readDevContainerConfig=async function({workspaceFolder:e,includeMergedConfig:t,devcontainerId:n,devcontainerPath:r,execServer:o,devcontainerCliPath:i}){const s=(0,u.getLogger)();try{if(!n&&!r)return s.error("devcontainerId or devcontainerPath is required"),null;s.info(`Devcontainer CLI path: ${i}`);const a=await S(o),c=[i,"read-configuration","--workspace-folder",e];t&&c.push("--include-merged-config"),n&&c.push("--container-id",n),r&&c.push("--config",r),s.info(`Reading devcontainer configuration with command: ${a} ${c.join(" ")}`);const{stdout:d}=await m(a,c,o,{...await D(o),ELECTRON_RUN_AS_NODE:"1"});return JSON.parse(d)}catch(e){return s.error("Failed to read devcontainer configuration:",e),null}},t.getDevContainerCliPath=async function(e,t,n){return t instanceof h.LocalExecServer?process.execArgv.some((e=>"--inspect"===e||"--inspect-brk"===e))?d.join(e.extensionPath,"node_modules","@devcontainers","cli","dist","spec-node","devContainersSpecCLI.js"):d.join(e.extensionPath,"dist","@devcontainers","cli","dist","spec-node","devContainersSpecCLI.js"):d.posix.join(await _(t),n.serverDataFolderName,"bin",n.commit+(n.isDevelopment?"-dev":""),"extensions","windsurf-dev-containers","dist","@devcontainers","cli","dist","spec-node","devContainersSpecCLI.js")},t.findBestRemoteUser=async function(e,t,n){if(e.configuration.remoteUser)return e.configuration.remoteUser;if(e.configuration.containerUser)return e.configuration.containerUser;const r=await E(t,n);if(r)return r;const o=(0,c.userInfo)().username;return await v(o,t,n)?o:l.ROOT_USER},t.findBestRemoteUserForAttach=async function(e,t,n){const r=await E(t,n);if(r)return r;if(e.Config.User)return e.Config.User;const o=(0,c.userInfo)().username;return await v(o,t,n)?o:l.ROOT_USER},t.checkDockerVersion=async function(e){const t=(0,u.getLogger)(),{stdout:n}=await m("docker",["version"],e,void 0);t.info(`Docker version: ${n}`)},t.inspectDockerContainer=async function(e,t){const n=["inspect","--type","container",e];(0,u.getLogger)().info(`Inspecting Docker container with command: docker ${n.join(" ")}`);const{stdout:r}=await m("docker",n,t,void 0),o=JSON.parse(r);if(!Array.isArray(o)||0===o.length)throw new Error("Invalid container data: Expected a non-empty array.");return o[0]},t.getRemoteHomeDir=_,t.deleteFile=async function(e,t){return t instanceof h.LocalExecServer?p.existsSync(e)?p.promises.rm(e):void 0:t.fs.rm(e)},t.setupBashScript=async function(e,t,n){await w(n,e,t),await R(n,e,"755")},t.writeRemoteFile=w,t.chmodFile=R,t.getNodePath=S,t.getEnv=D,t.isDisposable=function(e){return e&&"function"==typeof e.dispose};const a=s(n(278)),c=n(857),d=s(n(928)),l=n(582),u=n(947),f=n(596),h=n(964),p=s(n(896));function g(e){const t={};for(const[n,r]of Object.entries(e))void 0!==r&&(t[n]=r);return t}async function m(e,t,n,r){const o=(0,u.getLogger)();r||(r=await D(n));const i=g(r);try{const r=await n.spawn(e,t,{env:i});let o="",s="";return r.stdout.onDidReceiveMessage((e=>{o+=e})),r.stderr.onDidReceiveMessage((e=>{s+=e})),await r.onExit,{stdout:o,stderr:s}}catch(n){const r=`${e} ${t.join(" ")}`;throw o.error(`Error executing command: ${n.message}\nCommand: ${r}`),n.stdout&&o.error(`stdout: ${n.stdout}`),n.stderr&&o.error(`stderr: ${n.stderr}`),new Error(n.message)}}async function E(e,t){try{const n=["inspect","-f",'{{ index .Config.Labels "devcontainer.metadata" }}',e],{stdout:r}=await m("docker",n,t,void 0);if(r){const e=JSON.parse(r);if(Array.isArray(e))for(const t of e)if(t.remoteUser)return t.remoteUser}return}catch(e){return}}async function v(e,t,n){const r=["exec",t,"bash","-c",`command -v getent >/dev/null 2>&1 && getent passwd '${e}' || grep -E '^${e}|^[^:]*:[^:]*:${e}:' /etc/passwd || true`];(0,u.getLogger)().info(`Checking if host user ${e} exists in container ${t}: docker ${r.join(" ")}`);try{const{stdout:e}=await m("docker",r,n,void 0);return""!==e.trim()}catch(e){return!1}}async function _(e){return(await e.env()).env.HOME}async function w(e,t,n){if(e instanceof h.LocalExecServer)return p.promises.writeFile(t,n);const{stream:r,done:o}=await e.fs.write(t);return r.write(Buffer.from(n)),r.end(),o}async function R(e,t,n){if(e instanceof h.LocalExecServer)return p.promises.chmod(t,parseInt(n,8));await m("chmod",[n,t],e,void 0)}async function S(e){if(e instanceof h.LocalExecServer)return process.execPath;const t=await(0,f.getVSCodeServerConfig)(),n=await _(e);if(!n)throw new Error("Could not determine remote home directory");return d.posix.join(n,t.serverDataFolderName,"bin",t.commit+(t.isDevelopment?"-dev":""),"node")}function D(e){return new Promise((t=>{e.env().then((e=>{t(e.env)}))}))}},398:e=>{e.exports=require("vscode")},317:e=>{e.exports=require("child_process")},982:e=>{e.exports=require("crypto")},434:e=>{e.exports=require("events")},896:e=>{e.exports=require("fs")},278:e=>{e.exports=require("net")},857:e=>{e.exports=require("os")},928:e=>{e.exports=require("path")},23:e=>{e.exports=require("util")}},t={},n=function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}(256),r=exports;for(var o in n)r[o]=n[o];n.__esModule&&Object.defineProperty(r,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/b8f002c02d165600299a109bf21d02d139c52644/extensions/windsurf-dev-containers/dist/extension.js.map
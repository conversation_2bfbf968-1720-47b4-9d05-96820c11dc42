{"$schema": "http://json-schema.org/draft-07/schema#", "$id": "https://windsurf.com/schemas/mcp_config.json", "type": "object", "properties": {"mcpServers": {"type": "object", "additionalProperties": {"type": "object", "additionalProperties": false, "properties": {"command": {"type": "string"}, "args": {"type": "array", "items": {"type": "string"}}, "env": {"type": "object", "additionalProperties": {"type": "string"}}, "serverUrl": {"type": "string"}, "disabled": {"type": "boolean"}, "disabledTools": {"type": "array", "items": {"type": "string"}}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}}}}}}
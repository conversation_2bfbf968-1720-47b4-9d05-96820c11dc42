{"name": "windsurf-dev-containers", "displayName": "Windsurf Remote - Dev Containers", "description": "Use devcontainers within Windsurf", "version": "0.0.1", "publisher": "Codeium", "engines": {"vscode": "^1.25.0"}, "categories": ["Other"], "activationEvents": ["onStartupFinished", "onResolveRemoteAuthority:dev-container", "onCommand:windsurf-dev-containers.reopenInContainer", "onCommand:windsurf-dev-containers.showLog", "onCommand:windsurf-dev-containers.openInContainer", "onCommand:windsurf-dev-containers.attachToRunningContainer"], "main": "./dist/extension.js", "contributes": {"configuration": {"title": "Windsurf Remote - Dev Containers", "properties": {"remote.windsurfDevContainers.enableSSHAgentForwarding": {"type": "boolean", "default": true, "description": "Enable SSH agent forwarding when connecting to devcontainers."}, "remote.windsurfDevContainers.experimental.disableServerChecksum": {"type": "boolean", "scope": "application", "default": false, "description": "Disable the server checksum verification. This is only recommended for development and testing."}}}, "resourceLabelFormatters": [{"scheme": "vscode-remote", "authority": "dev-container+*", "formatting": {"label": "${path}", "separator": "/", "tildify": true, "workspaceSuffix": "[<PERSON>er]", "workspaceTooltip": "Connect to a development container"}}], "views": {"remote": [{"id": "windsurfDevContainers", "name": "Dev Containers (Windsurf)", "group": "targets@1", "remoteName": "dev-container"}]}, "commands": [{"command": "windsurf-dev-containers.explorer.refresh", "title": "Refresh Dev Containers", "category": "Dev Containers", "icon": "$(refresh)"}, {"command": "windsurf-dev-containers.reopenInContainer", "title": "Reopen in Container", "category": "Dev Containers", "when": "!remoteName || remoteName =~ /^ssh-remote$/ || remoteName =~ /^dev-container$/"}, {"command": "windsurf-dev-containers.showLog", "title": "Show Windsurf Dev Containers Log", "category": "Dev Containers"}, {"command": "windsurf-dev-containers.openInContainer", "title": "Open Folder in Container", "category": "Dev Containers", "when": "!remoteName || remoteName =~ /^ssh-remote$/"}, {"command": "windsurf-dev-containers.reopenFolderLocally", "title": "Reopen Folder Locally", "category": "Dev Containers", "when": "remoteName =~ /^dev-container$/ && !windsurf.isNestedContainer"}, {"command": "windsurf-dev-containers.attachToRunningContainer", "title": "Attach to Running Container", "category": "Dev Containers", "when": "!remoteName || remoteName =~ /^ssh-remote$/"}, {"command": "windsurf-dev-containers.explorer.deleteContainer", "title": "<PERSON><PERSON><PERSON>tainer from History", "category": "Dev Containers", "icon": "$(trash)"}, {"command": "windsurf-dev-containers.openFromHistory", "title": "Open Container", "category": "Dev Containers", "icon": "$(arrow-right)"}], "menus": {"view/title": [{"command": "windsurf-dev-containers.explorer.refresh", "when": "view == windsurfDevContainers", "group": "navigation@1", "icon": "$(refresh)"}], "view/item/context": [{"command": "windsurf-dev-containers.explorer.deleteContainer", "when": "view == windsurfDevContainers && viewItem == windsurf-dev-containers.container", "group": "inline@1"}, {"command": "windsurf-dev-containers.openFromHistory", "when": "view == windsurfDevContainers && viewItem == windsurf-dev-containers.container", "group": "inline@2"}], "commandPalette": [{"command": "windsurf-dev-containers.reopenInContainer", "when": "(!remoteName || remoteName =~ /^ssh-remote$/) && workspaceFolderCount > 0"}, {"command": "windsurf-dev-containers.openInContainer", "when": "!remoteName || remoteName =~ /^ssh-remote$/"}, {"command": "windsurf-dev-containers.reopenFolderLocally", "when": "remoteName =~ /^dev-container$/ && workspaceFolderCount > 0 && !windsurf.isNestedContainer"}, {"command": "windsurf-dev-containers.attachToRunningContainer", "when": "!remoteName || remoteName =~ /^ssh-remote$/ || remoteName =~ /^dev-container$/"}, {"command": "windsurf-dev-containers.explorer.deleteContainer", "when": "false"}, {"command": "windsurf-dev-containers.openFromHistory", "when": "false"}, {"command": "windsurf-dev-containers.explorer.refresh", "when": "false"}], "statusBar/remoteIndicator": [{"command": "windsurf-dev-containers.openInContainer", "when": "remoteName =~ /^dev-container$/ && remoteConnectionState == connected && !windsurf.isNestedContainer", "group": "remote_11_devcontainers_2general@1"}, {"command": "windsurf-dev-containers.attachToRunningContainer", "when": "remoteName =~ /^dev-container$/ && remoteConnectionState == connected", "group": "remote_11_devcontainers_2general@2"}, {"command": "windsurf-dev-containers.reopenFolderLocally", "when": "remoteName =~ /^dev-container$/ && remoteConnectionState == connected && workspaceFolderCount > 0 && !windsurf.isNestedContainer", "group": "remote_11_devcontainers_2general@1"}, {"command": "windsurf-dev-containers.reopenInContainer", "when": "remoteName =~ /^dev-container$/ && remoteConnectionState == connected && workspaceFolderCount > 0 && !windsurf.isNestedContainer", "group": "remote_11_devcontainers_2general@2"}, {"command": "windsurf-dev-containers.showLog", "when": "remoteName =~ /^dev-container$/ && remoteConnectionState == connected", "group": "remote_11_devcontainers_2general@4"}, {"command": "windsurf-dev-containers.openInContainer", "when": "remoteConnectionState == disconnected", "group": "remote_11_devcontainers_4local@1"}, {"command": "windsurf-dev-containers.attachToRunningContainer", "when": "remoteConnectionState == disconnected", "group": "remote_11_devcontainers_4local@2"}, {"command": "windsurf-dev-containers.reopenInContainer", "when": "remoteConnectionState == disconnected && workspaceFolderCount > 0", "group": "remote_11_devcontainers_4local@3"}, {"command": "windsurf-dev-containers.openInContainer", "when": "!remoteName && !virtualWorkspace", "group": "remote_11_devcontainers_4local@5"}, {"command": "windsurf-dev-containers.attachToRunningContainer", "when": "!remoteName && !virtualWorkspace", "group": "remote_11_devcontainers_4local@6"}, {"command": "windsurf-dev-containers.reopenInContainer", "when": "!remoteName && !virtualWorkspace && workspaceFolderCount > 0", "group": "remote_11_devcontainers_4local@7"}]}}, "capabilities": {"untrustedWorkspaces": {"supported": true}, "virtualWorkspaces": true}, "repository": {"type": "git", "url": "https://github.com/Exafunction/windsurf"}, "prettier": {"singleQuote": true, "useTabs": true, "tabWidth": 4}, "extensionKind": ["ui"], "enabledApiProposals": ["resolvers", "contribViewsRemote"]}
/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/var $bb=class{constructor(){this.b=[],this.a=function(e){setTimeout(()=>{throw e.stack?$ub.isErrorNoTelemetry(e)?new $ub(e.message+`

`+e.stack):new Error(e.message+`

`+e.stack):e},0)}}addListener(e){return this.b.push(e),()=>{this.d(e)}}c(e){this.b.forEach(t=>{t(e)})}d(e){this.b.splice(this.b.indexOf(e),1)}setUnexpectedErrorHandler(e){this.a=e}getUnexpectedErrorHandler(){return this.a}onUnexpectedError(e){this.a(e),this.c(e)}onUnexpectedExternalError(e){this.a(e)}},$cb=new $bb;function $gb(e){$kb(e)||$cb.onUnexpectedError(e)}function $ib(e){if(e instanceof Error){const{name:t,message:n,cause:r}=e,s=e.stacktrace||e.stack;return{$isError:!0,name:t,message:n,stack:s,noTelemetry:$ub.isErrorNoTelemetry(e),cause:r?$ib(r):void 0,code:e.code}}return e}var canceledName="Canceled";function $kb(e){return e instanceof $lb?!0:e instanceof Error&&e.name===canceledName&&e.message===canceledName}var $lb=class extends Error{constructor(){super(canceledName),this.name=this.message}},$ub=class we extends Error{constructor(t){super(t),this.name="CodeExpectedError"}static fromError(t){if(t instanceof we)return t;const n=new we;return n.message=t.message,n.stack=t.stack,n}static isErrorNoTelemetry(t){return t.name==="CodeExpectedError"}},$vb=class Re extends Error{constructor(t){super(t||"An unexpected bug occurred."),Object.setPrototypeOf(this,Re.prototype)}},_a;function $a(e,t){const n=Object.create(null);for(const r of e){const s=t(r);let i=n[s];i||(i=n[s]=[]),i.push(r)}return n}var $e=class{static{_a=Symbol.toStringTag}constructor(e,t){this.b=t,this.a=new Map,this[_a]="SetWithKey";for(const n of e)this.add(n)}get size(){return this.a.size}add(e){const t=this.b(e);return this.a.set(t,e),this}delete(e){return this.a.delete(this.b(e))}has(e){return this.a.has(this.b(e))}*entries(){for(const e of this.a.values())yield[e,e]}keys(){return this.values()}*values(){for(const e of this.a.values())yield e}clear(){this.a.clear()}forEach(e,t){this.a.forEach(n=>e.call(t,n,n,this))}[Symbol.iterator](){return this.values()}};function $Pc(e,t){const n=this;let r=!1,s;return function(){if(r)return s;if(r=!0,t)try{s=e.apply(n,arguments)}finally{t()}else s=e.apply(n,arguments);return s}}function $3(e,t){const n=$4(e,t);return n===-1?void 0:e[n]}function $4(e,t,n=0,r=e.length){let s=n,i=r;for(;s<i;){const a=Math.floor((s+i)/2);t(e[a])?s=a+1:i=a}return s-1}function $5(e,t){const n=$6(e,t);return n===e.length?void 0:e[n]}function $6(e,t,n=0,r=e.length){let s=n,i=r;for(;s<i;){const a=Math.floor((s+i)/2);t(e[a])?i=a:s=a+1}return s}var $8=class Ee{static{this.assertInvariants=!1}constructor(t){this.e=t,this.c=0}findLastMonotonous(t){if(Ee.assertInvariants){if(this.d){for(const r of this.e)if(this.d(r)&&!t(r))throw new Error("MonotonousArray: current predicate must be weaker than (or equal to) the previous predicate.")}this.d=t}const n=$4(this.e,t,this.c);return this.c=n+1,n===-1?void 0:this.e[n]}};function $yb(e,t,n=(r,s)=>r===s){if(e===t)return!0;if(!e||!t||e.length!==t.length)return!1;for(let r=0,s=e.length;r<s;r++)if(!n(e[r],t[r]))return!1;return!0}function*$Eb(e,t){let n,r;for(const s of e)r!==void 0&&t(r,s)?n.push(s):(n&&(yield n),n=[s]),r=s;n&&(yield n)}function $Fb(e,t){for(let n=0;n<=e.length;n++)t(n===0?void 0:e[n-1],n===e.length?void 0:e[n])}function $Gb(e,t){for(let n=0;n<e.length;n++)t(n===0?void 0:e[n-1],e[n],n+1===e.length?void 0:e[n+1])}function $2b(e,t){for(const n of t)e.push(n)}var CompareResult;(function(e){function t(i){return i<0}e.isLessThan=t;function n(i){return i<=0}e.isLessThanOrEqual=n;function r(i){return i>0}e.isGreaterThan=r;function s(i){return i===0}e.isNeitherLessOrGreaterThan=s,e.greaterThan=1,e.lessThan=-1,e.neitherLessOrGreaterThan=0})(CompareResult||(CompareResult={}));function $9b(e,t){return(n,r)=>t(e(n),e(r))}var $$b=(e,t)=>e-t;function $ac(e){return(t,n)=>-e(t,n)}var $dc=class he{static{this.empty=new he(t=>{})}constructor(t){this.iterate=t}forEach(t){this.iterate(n=>(t(n),!0))}toArray(){const t=[];return this.iterate(n=>(t.push(n),!0)),t}filter(t){return new he(n=>this.iterate(r=>t(r)?n(r):!0))}map(t){return new he(n=>this.iterate(r=>n(t(r))))}some(t){let n=!1;return this.iterate(r=>(n=t(r),!n)),n}findFirst(t){let n;return this.iterate(r=>t(r)?(n=r,!1):!0),n}findLast(t){let n;return this.iterate(r=>(t(r)&&(n=r),!0)),n}findLastMaxBy(t){let n,r=!0;return this.iterate(s=>((r||CompareResult.isGreaterThan(t(s,n)))&&(r=!1,n=s),!0)),n}},_a2,_b,_c,ResourceMapEntry=class{constructor(e,t){this.uri=e,this.value=t}};function isEntries(e){return Array.isArray(e)}var $Fc=class ne{static{this.c=t=>t.toString()}constructor(t,n){if(this[_a2]="ResourceMap",t instanceof ne)this.d=new Map(t.d),this.e=n??ne.c;else if(isEntries(t)){this.d=new Map,this.e=n??ne.c;for(const[r,s]of t)this.set(r,s)}else this.d=new Map,this.e=t??ne.c}set(t,n){return this.d.set(this.e(t),new ResourceMapEntry(t,n)),this}get(t){return this.d.get(this.e(t))?.value}has(t){return this.d.has(this.e(t))}get size(){return this.d.size}clear(){this.d.clear()}delete(t){return this.d.delete(this.e(t))}forEach(t,n){typeof n<"u"&&(t=t.bind(n));for(const[r,s]of this.d)t(s.value,s.uri,this)}*values(){for(const t of this.d.values())yield t.value}*keys(){for(const t of this.d.values())yield t.uri}*entries(){for(const t of this.d.values())yield[t.uri,t.value]}*[(_a2=Symbol.toStringTag,Symbol.iterator)](){for(const[,t]of this.d)yield[t.uri,t.value]}},$Gc=class{constructor(e,t){this[_b]="ResourceSet",!e||typeof e=="function"?this.c=new $Fc(e):(this.c=new $Fc(t),e.forEach(this.add,this))}get size(){return this.c.size}add(e){return this.c.set(e,e),this}clear(){this.c.clear()}delete(e){return this.c.delete(e)}forEach(e,t){this.c.forEach((n,r)=>e.call(t,r,r,this))}has(e){return this.c.has(e)}entries(){return this.c.entries()}keys(){return this.c.keys()}values(){return this.c.keys()}[(_b=Symbol.toStringTag,Symbol.iterator)](){return this.keys()}},Touch;(function(e){e[e.None=0]="None",e[e.AsOld=1]="AsOld",e[e.AsNew=2]="AsNew"})(Touch||(Touch={}));var $Hc=class{constructor(){this[_c]="LinkedMap",this.c=new Map,this.d=void 0,this.e=void 0,this.f=0,this.g=0}clear(){this.c.clear(),this.d=void 0,this.e=void 0,this.f=0,this.g++}isEmpty(){return!this.d&&!this.e}get size(){return this.f}get first(){return this.d?.value}get last(){return this.e?.value}has(e){return this.c.has(e)}get(e,t=0){const n=this.c.get(e);if(n)return t!==0&&this.n(n,t),n.value}set(e,t,n=0){let r=this.c.get(e);if(r)r.value=t,n!==0&&this.n(r,n);else{switch(r={key:e,value:t,next:void 0,previous:void 0},n){case 0:this.l(r);break;case 1:this.k(r);break;case 2:this.l(r);break;default:this.l(r);break}this.c.set(e,r),this.f++}return this}delete(e){return!!this.remove(e)}remove(e){const t=this.c.get(e);if(t)return this.c.delete(e),this.m(t),this.f--,t.value}shift(){if(!this.d&&!this.e)return;if(!this.d||!this.e)throw new Error("Invalid list");const e=this.d;return this.c.delete(e.key),this.m(e),this.f--,e.value}forEach(e,t){const n=this.g;let r=this.d;for(;r;){if(t?e.bind(t)(r.value,r.key,this):e(r.value,r.key,this),this.g!==n)throw new Error("LinkedMap got modified during iteration.");r=r.next}}keys(){const e=this,t=this.g;let n=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const s={value:n.key,done:!1};return n=n.next,s}else return{value:void 0,done:!0}}};return r}values(){const e=this,t=this.g;let n=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const s={value:n.value,done:!1};return n=n.next,s}else return{value:void 0,done:!0}}};return r}entries(){const e=this,t=this.g;let n=this.d;const r={[Symbol.iterator](){return r},next(){if(e.g!==t)throw new Error("LinkedMap got modified during iteration.");if(n){const s={value:[n.key,n.value],done:!1};return n=n.next,s}else return{value:void 0,done:!0}}};return r}[(_c=Symbol.toStringTag,Symbol.iterator)](){return this.entries()}h(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.d,n=this.size;for(;t&&n>e;)this.c.delete(t.key),t=t.next,n--;this.d=t,this.f=n,t&&(t.previous=void 0),this.g++}j(e){if(e>=this.size)return;if(e===0){this.clear();return}let t=this.e,n=this.size;for(;t&&n>e;)this.c.delete(t.key),t=t.previous,n--;this.e=t,this.f=n,t&&(t.next=void 0),this.g++}k(e){if(!this.d&&!this.e)this.e=e;else if(this.d)e.next=this.d,this.d.previous=e;else throw new Error("Invalid list");this.d=e,this.g++}l(e){if(!this.d&&!this.e)this.d=e;else if(this.e)e.previous=this.e,this.e.next=e;else throw new Error("Invalid list");this.e=e,this.g++}m(e){if(e===this.d&&e===this.e)this.d=void 0,this.e=void 0;else if(e===this.d){if(!e.next)throw new Error("Invalid list");e.next.previous=void 0,this.d=e.next}else if(e===this.e){if(!e.previous)throw new Error("Invalid list");e.previous.next=void 0,this.e=e.previous}else{const t=e.next,n=e.previous;if(!t||!n)throw new Error("Invalid list");t.previous=n,n.next=t}e.next=void 0,e.previous=void 0,this.g++}n(e,t){if(!this.d||!this.e)throw new Error("Invalid list");if(!(t!==1&&t!==2)){if(t===1){if(e===this.d)return;const n=e.next,r=e.previous;e===this.e?(r.next=void 0,this.e=r):(n.previous=r,r.next=n),e.previous=void 0,e.next=this.d,this.d.previous=e,this.d=e,this.g++}else if(t===2){if(e===this.e)return;const n=e.next,r=e.previous;e===this.d?(n.previous=void 0,this.d=n):(n.previous=r,r.next=n),e.next=void 0,e.previous=this.e,this.e.next=e,this.e=e,this.g++}}}toJSON(){const e=[];return this.forEach((t,n)=>{e.push([n,t])}),e}fromJSON(e){this.clear();for(const[t,n]of e)this.set(t,n)}},Cache=class extends $Hc{constructor(e,t=1){super(),this.o=e,this.p=Math.min(Math.max(0,t),1)}get limit(){return this.o}set limit(e){this.o=e,this.q()}get ratio(){return this.p}set ratio(e){this.p=Math.min(Math.max(0,e),1),this.q()}get(e,t=2){return super.get(e,t)}peek(e){return super.get(e,0)}set(e,t){return super.set(e,t,2),this}q(){this.size>this.o&&this.r(Math.round(this.o*this.p))}},$Ic=class extends Cache{constructor(e,t=1){super(e,t)}r(e){this.h(e)}set(e,t){return super.set(e,t),this.q(),this}},$Mc=class{constructor(){this.c=new Map}add(e,t){let n=this.c.get(e);n||(n=new Set,this.c.set(e,n)),n.add(t)}delete(e,t){const n=this.c.get(e);n&&(n.delete(t),n.size===0&&this.c.delete(e))}forEach(e,t){const n=this.c.get(e);n&&n.forEach(t)}get(e){const t=this.c.get(e);return t||new Set}};function $Rc(e,t="Unreachable"){throw new Error(t)}function $Sc(e,t="unexpected state"){if(!e)throw typeof t=="string"?new $vb(`Assertion Failed: ${t}`):t}function $Uc(e){if(!e()){debugger;e(),$gb(new $vb("Assertion Failed"))}}function $Vc(e,t){let n=0;for(;n<e.length-1;){const r=e[n],s=e[n+1];if(!t(r,s))return!1;n++}return!0}function $Wc(e){return typeof e=="string"}function $2c(e){return!!e&&typeof e[Symbol.iterator]=="function"}var Iterable;(function(e){function t(b){return b&&typeof b=="object"&&typeof b[Symbol.iterator]=="function"}e.is=t;const n=Object.freeze([]);function r(){return n}e.empty=r;function*s(b){yield b}e.single=s;function i(b){return t(b)?b:s(b)}e.wrap=i;function a(b){return b||n}e.from=a;function*l(b){for(let w=b.length-1;w>=0;w--)yield b[w]}e.reverse=l;function u(b){return!b||b[Symbol.iterator]().next().done===!0}e.isEmpty=u;function o(b){return b[Symbol.iterator]().next().value}e.first=o;function h(b,w){let R=0;for(const F of b)if(w(F,R++))return!0;return!1}e.some=h;function c(b,w){for(const R of b)if(w(R))return R}e.find=c;function*f(b,w){for(const R of b)w(R)&&(yield R)}e.filter=f;function*g(b,w){let R=0;for(const F of b)yield w(F,R++)}e.map=g;function*m(b,w){let R=0;for(const F of b)yield*w(F,R++)}e.flatMap=m;function*N(...b){for(const w of b)$2c(w)?yield*w:yield w}e.concat=N;function p(b,w,R){let F=R;for(const $ of b)F=w(F,$);return F}e.reduce=p;function d(b){let w=0;for(const R of b)w++;return w}e.length=d;function*A(b,w,R=b.length){for(w<-b.length&&(w=0),w<0&&(w+=b.length),R<0?R+=b.length:R>b.length&&(R=b.length);w<R;w++)yield b[w]}e.slice=A;function v(b,w=Number.POSITIVE_INFINITY){const R=[];if(w===0)return[R,b];const F=b[Symbol.iterator]();for(let $=0;$<w;$++){const B=F.next();if(B.done)return[R,e.empty()];R.push(B.value)}return[R,{[Symbol.iterator](){return F}}]}e.consume=v;async function L(b){const w=[];for await(const R of b)w.push(R);return Promise.resolve(w)}e.asyncToArray=L})(Iterable||(Iterable={}));var TRACK_DISPOSABLES=!1,disposableTracker=null,$hd=class xe{constructor(){this.b=new Map}static{this.a=0}c(t){let n=this.b.get(t);return n||(n={parent:null,source:null,isSingleton:!1,value:t,idx:xe.a++},this.b.set(t,n)),n}trackDisposable(t){const n=this.c(t);n.source||(n.source=new Error().stack)}setParent(t,n){const r=this.c(t);r.parent=n}markAsDisposed(t){this.b.delete(t)}markAsSingleton(t){this.c(t).isSingleton=!0}f(t,n){const r=n.get(t);if(r)return r;const s=t.parent?this.f(this.c(t.parent),n):t;return n.set(t,s),s}getTrackedDisposables(){const t=new Map;return[...this.b.entries()].filter(([,r])=>r.source!==null&&!this.f(r,t).isSingleton).flatMap(([r])=>r)}computeLeakingDisposables(t=10,n){let r;if(n)r=n;else{const u=new Map,o=[...this.b.values()].filter(c=>c.source!==null&&!this.f(c,u).isSingleton);if(o.length===0)return;const h=new Set(o.map(c=>c.value));if(r=o.filter(c=>!(c.parent&&h.has(c.parent))),r.length===0)throw new Error("There are cyclic diposable chains!")}if(!r)return;function s(u){function o(c,f){for(;c.length>0&&f.some(g=>typeof g=="string"?g===c[0]:c[0].match(g));)c.shift()}const h=u.source.split(`
`).map(c=>c.trim().replace("at ","")).filter(c=>c!=="");return o(h,["Error",/^trackDisposable \(.*\)$/,/^DisposableTracker.trackDisposable \(.*\)$/]),h.reverse()}const i=new $Mc;for(const u of r){const o=s(u);for(let h=0;h<=o.length;h++)i.add(o.slice(0,h).join(`
`),u)}r.sort($9b(u=>u.idx,$$b));let a="",l=0;for(const u of r.slice(0,t)){l++;const o=s(u),h=[];for(let c=0;c<o.length;c++){let f=o[c];f=`(shared with ${i.get(o.slice(0,c+1).join(`
`)).size}/${r.length} leaks) at ${f}`;const m=i.get(o.slice(0,c).join(`
`)),N=$a([...m].map(p=>s(p)[c]),p=>p);delete N[o[c]];for(const[p,d]of Object.entries(N))h.unshift(`    - stacktraces of ${d.length} other leaks continue with ${p}`);h.unshift(f)}a+=`


==================== Leaking disposable ${l}/${r.length}: ${u.value.constructor.name} ====================
${h.join(`
`)}
============================================================

`}return r.length>t&&(a+=`


... and ${r.length-t} more leaking disposables

`),{leaks:r,details:a}}};function $id(e){disposableTracker=e}if(TRACK_DISPOSABLES){const e="__is_disposable_tracked__";$id(new class{trackDisposable(t){const n=new Error("Potentially leaked disposable").stack;setTimeout(()=>{t[e]||console.log(n)},3e3)}setParent(t,n){if(t&&t!==$sd.None)try{t[e]=!0}catch{}}markAsDisposed(t){if(t&&t!==$sd.None)try{t[e]=!0}catch{}}markAsSingleton(t){}})}function $jd(e){return disposableTracker?.trackDisposable(e),e}function $kd(e){disposableTracker?.markAsDisposed(e)}function setParentOfDisposable(e,t){disposableTracker?.setParent(e,t)}function setParentOfDisposables(e,t){if(disposableTracker)for(const n of e)disposableTracker.setParent(n,t)}function $nd(e){if(Iterable.is(e)){const t=[];for(const n of e)if(n)try{n.dispose()}catch(r){t.push(r)}if(t.length===1)throw t[0];if(t.length>1)throw new AggregateError(t,"Encountered errors while disposing of store");return Array.isArray(e)?[]:e}else if(e)return e.dispose(),e}function $pd(...e){const t=$qd(()=>$nd(e));return setParentOfDisposables(e,t),t}function $qd(e){const t=$jd({dispose:$Pc(()=>{$kd(t),e()})});return t}var $rd=class ke{static{this.DISABLE_DISPOSED_WARNING=!1}constructor(){this.f=new Set,this.g=!1,$jd(this)}dispose(){this.g||($kd(this),this.g=!0,this.clear())}get isDisposed(){return this.g}clear(){if(this.f.size!==0)try{$nd(this.f)}finally{this.f.clear()}}add(t){if(!t)return t;if(t===this)throw new Error("Cannot register a disposable on itself!");return setParentOfDisposable(t,this),this.g?ke.DISABLE_DISPOSED_WARNING||console.warn(new Error("Trying to add a disposable to a DisposableStore that has already been disposed of. The added object will be leaked!").stack):this.f.add(t),t}delete(t){if(t){if(t===this)throw new Error("Cannot dispose a disposable on itself!");this.f.delete(t),t.dispose()}}deleteAndLeak(t){t&&this.f.has(t)&&(this.f.delete(t),setParentOfDisposable(t,null))}},$sd=class{static{this.None=Object.freeze({dispose(){}})}constructor(){this.q=new $rd,$jd(this),setParentOfDisposable(this.q,this)}dispose(){$kd(this),this.q.dispose()}B(e){if(e===this)throw new Error("Cannot register a disposable on itself!");return this.q.add(e)}},Node=class fe{static{this.Undefined=new fe(void 0)}constructor(t){this.element=t,this.next=fe.Undefined,this.prev=fe.Undefined}},$Fd=class{constructor(){this.a=Node.Undefined,this.b=Node.Undefined,this.c=0}get size(){return this.c}isEmpty(){return this.a===Node.Undefined}clear(){let e=this.a;for(;e!==Node.Undefined;){const t=e.next;e.prev=Node.Undefined,e.next=Node.Undefined,e=t}this.a=Node.Undefined,this.b=Node.Undefined,this.c=0}unshift(e){return this.d(e,!1)}push(e){return this.d(e,!0)}d(e,t){const n=new Node(e);if(this.a===Node.Undefined)this.a=n,this.b=n;else if(t){const s=this.b;this.b=n,n.prev=s,s.next=n}else{const s=this.a;this.a=n,n.next=s,s.prev=n}this.c+=1;let r=!1;return()=>{r||(r=!0,this.e(n))}}shift(){if(this.a!==Node.Undefined){const e=this.a.element;return this.e(this.a),e}}pop(){if(this.b!==Node.Undefined){const e=this.b.element;return this.e(this.b),e}}e(e){if(e.prev!==Node.Undefined&&e.next!==Node.Undefined){const t=e.prev;t.next=e.next,e.next.prev=t}else e.prev===Node.Undefined&&e.next===Node.Undefined?(this.a=Node.Undefined,this.b=Node.Undefined):e.next===Node.Undefined?(this.b=this.b.prev,this.b.next=Node.Undefined):e.prev===Node.Undefined&&(this.a=this.a.next,this.a.prev=Node.Undefined);this.c-=1}*[Symbol.iterator](){let e=this.a;for(;e!==Node.Undefined;)yield e.element,e=e.next}},hasPerformanceNow=globalThis.performance&&typeof globalThis.performance.now=="function",$4e=class Me{static create(t){return new Me(t)}constructor(t){this.c=hasPerformanceNow&&t===!1?Date.now:globalThis.performance.now.bind(globalThis.performance),this.a=this.c(),this.b=-1}stop(){this.b=this.c()}reset(){this.a=this.c(),this.b=-1}elapsed(){return this.b!==-1?this.b-this.a:this.c()-this.a}},_enableDisposeWithListenerWarning=!1,_enableSnapshotPotentialLeakWarning=!1,Event;(function(e){e.None=()=>$sd.None;function t(M){if(_enableSnapshotPotentialLeakWarning){const{onDidAddListener:E}=M,k=Stacktrace.create();let x=0;M.onDidAddListener=()=>{++x===2&&(console.warn("snapshotted emitter LIKELY used public and SHOULD HAVE BEEN created with DisposableStore. snapshotted here"),k.print()),E?.()}}}function n(M,E){return g(M,()=>{},0,void 0,!0,void 0,E)}e.defer=n;function r(M){return(E,k=null,x)=>{let P=!1,D;return D=M(_=>{if(!P)return D?D.dispose():P=!0,E.call(k,_)},null,x),P&&D.dispose(),D}}e.once=r;function s(M,E){return e.once(e.filter(M,E))}e.onceIf=s;function i(M,E,k){return c((x,P=null,D)=>M(_=>x.call(P,E(_)),null,D),k)}e.map=i;function a(M,E,k){return c((x,P=null,D)=>M(_=>{E(_),x.call(P,_)},null,D),k)}e.forEach=a;function l(M,E,k){return c((x,P=null,D)=>M(_=>E(_)&&x.call(P,_),null,D),k)}e.filter=l;function u(M){return M}e.signal=u;function o(...M){return(E,k=null,x)=>{const P=$pd(...M.map(D=>D(_=>E.call(k,_))));return f(P,x)}}e.any=o;function h(M,E,k,x){let P=k;return i(M,D=>(P=E(P,D),P),x)}e.reduce=h;function c(M,E){let k;const x={onWillAddFirstListener(){k=M(P.fire,P)},onDidRemoveLastListener(){k?.dispose()}};E||t(x);const P=new $0e(x);return E?.add(P),P.event}function f(M,E){return E instanceof Array?E.push(M):E&&E.add(M),M}function g(M,E,k=100,x=!1,P=!1,D,_){let q,I,J,oe=0,te;const Ae={leakWarningThreshold:D,onWillAddFirstListener(){q=M(Ie=>{oe++,I=E(I,Ie),x&&!J&&(ce.fire(I),I=void 0),te=()=>{const Ue=I;I=void 0,J=void 0,(!x||oe>1)&&ce.fire(Ue),oe=0},typeof k=="number"?(clearTimeout(J),J=setTimeout(te,k)):J===void 0&&(J=0,queueMicrotask(te))})},onWillRemoveListener(){P&&oe>0&&te?.()},onDidRemoveLastListener(){te=void 0,q.dispose()}};_||t(Ae);const ce=new $0e(Ae);return _?.add(ce),ce.event}e.debounce=g;function m(M,E=0,k){return e.debounce(M,(x,P)=>x?(x.push(P),x):[P],E,void 0,!0,void 0,k)}e.accumulate=m;function N(M,E=(x,P)=>x===P,k){let x=!0,P;return l(M,D=>{const _=x||!E(D,P);return x=!1,P=D,_},k)}e.latch=N;function p(M,E,k){return[e.filter(M,E,k),e.filter(M,x=>!E(x),k)]}e.split=p;function d(M,E=!1,k=[],x){let P=k.slice(),D=M(I=>{P?P.push(I):q.fire(I)});x&&x.add(D);const _=()=>{P?.forEach(I=>q.fire(I)),P=null},q=new $0e({onWillAddFirstListener(){D||(D=M(I=>q.fire(I)),x&&x.add(D))},onDidAddFirstListener(){P&&(E?setTimeout(_):_())},onDidRemoveLastListener(){D&&D.dispose(),D=null}});return x&&x.add(q),q.event}e.buffer=d;function A(M,E){return(x,P,D)=>{const _=E(new L);return M(function(q){const I=_.evaluate(q);I!==v&&x.call(P,I)},void 0,D)}}e.chain=A;const v=Symbol("HaltChainable");class L{constructor(){this.f=[]}map(E){return this.f.push(E),this}forEach(E){return this.f.push(k=>(E(k),k)),this}filter(E){return this.f.push(k=>E(k)?k:v),this}reduce(E,k){let x=k;return this.f.push(P=>(x=E(x,P),x)),this}latch(E=(k,x)=>k===x){let k=!0,x;return this.f.push(P=>{const D=k||!E(P,x);return k=!1,x=P,D?P:v}),this}evaluate(E){for(const k of this.f)if(E=k(E),E===v)break;return E}}function b(M,E,k=x=>x){const x=(...q)=>_.fire(k(...q)),P=()=>M.on(E,x),D=()=>M.removeListener(E,x),_=new $0e({onWillAddFirstListener:P,onDidRemoveLastListener:D});return _.event}e.fromNodeEventEmitter=b;function w(M,E,k=x=>x){const x=(...q)=>_.fire(k(...q)),P=()=>M.addEventListener(E,x),D=()=>M.removeEventListener(E,x),_=new $0e({onWillAddFirstListener:P,onDidRemoveLastListener:D});return _.event}e.fromDOMEventEmitter=w;function R(M,E){return new Promise(k=>r(M)(k,null,E))}e.toPromise=R;function F(M){const E=new $0e;return M.then(k=>{E.fire(k)},()=>{E.fire(void 0)}).finally(()=>{E.dispose()}),E.event}e.fromPromise=F;function $(M,E){return M(k=>E.fire(k))}e.forward=$;function B(M,E,k){return E(k),M(x=>E(x))}e.runAndSubscribe=B;class T{constructor(E,k){this._observable=E,this.f=0,this.g=!1;const x={onWillAddFirstListener:()=>{E.addObserver(this),this._observable.reportChanges()},onDidRemoveLastListener:()=>{E.removeObserver(this)}};k||t(x),this.emitter=new $0e(x),k&&k.add(this.emitter)}beginUpdate(E){this.f++}handlePossibleChange(E){}handleChange(E,k){this.g=!0}endUpdate(E){this.f--,this.f===0&&(this._observable.reportChanges(),this.g&&(this.g=!1,this.emitter.fire(this._observable.get())))}}function y(M,E){return new T(M,E).emitter.event}e.fromObservable=y;function O(M){return(E,k,x)=>{let P=0,D=!1;const _={beginUpdate(){P++},endUpdate(){P--,P===0&&(M.reportChanges(),D&&(D=!1,E.call(k)))},handlePossibleChange(){},handleChange(){D=!0}};M.addObserver(_),M.reportChanges();const q={dispose(){M.removeObserver(_)}};return x instanceof $rd?x.add(q):Array.isArray(x)&&x.push(q),q}}e.fromObservableLight=O})(Event||(Event={}));var $6e=class ve{static{this.all=new Set}static{this.f=0}constructor(t){this.listenerCount=0,this.invocationCount=0,this.elapsedOverall=0,this.durations=[],this.name=`${t}_${ve.f++}`,ve.all.add(this)}start(t){this.g=new $4e,this.listenerCount=t}stop(){if(this.g){const t=this.g.elapsed();this.durations.push(t),this.elapsedOverall+=t,this.invocationCount+=1,this.g=void 0}}},_globalLeakWarningThreshold=-1,LeakageMonitor=class Fe{static{this.f=1}constructor(t,n,r=(Fe.f++).toString(16).padStart(3,"0")){this.j=t,this.threshold=n,this.name=r,this.h=0}dispose(){this.g?.clear()}check(t,n){const r=this.threshold;if(r<=0||n<r)return;this.g||(this.g=new Map);const s=this.g.get(t.value)||0;if(this.g.set(t.value,s+1),this.h-=1,this.h<=0){this.h=r*.5;const[i,a]=this.getMostFrequentStack(),l=`[${this.name}] potential listener LEAK detected, having ${n} listeners already. MOST frequent listener (${a}):`;console.warn(l),console.warn(i);const u=new $8e(l,i);this.j(u)}return()=>{const i=this.g.get(t.value)||0;this.g.set(t.value,i-1)}}getMostFrequentStack(){if(!this.g)return;let t,n=0;for(const[r,s]of this.g)(!t||n<s)&&(t=[r,s],n=s);return t}},Stacktrace=class Pe{static create(){const t=new Error;return new Pe(t.stack??"")}constructor(t){this.value=t}print(){console.warn(this.value.split(`
`).slice(2).join(`
`))}},$8e=class extends Error{constructor(e,t){super(e),this.name="ListenerLeakError",this.stack=t}},$9e=class extends Error{constructor(e,t){super(e),this.name="ListenerRefusalError",this.stack=t}},id=0,UniqueContainer=class{constructor(e){this.value=e,this.id=id++}},compactionThreshold=2,forEachListener=(e,t)=>{if(e instanceof UniqueContainer)t(e);else for(let n=0;n<e.length;n++){const r=e[n];r&&t(r)}},$0e=class{constructor(e){this.z=0,this.f=e,this.g=_globalLeakWarningThreshold>0||this.f?.leakWarningThreshold?new LeakageMonitor(e?.onListenerError??$gb,this.f?.leakWarningThreshold??_globalLeakWarningThreshold):void 0,this.j=this.f?._profName?new $6e(this.f._profName):void 0,this.w=this.f?.deliveryQueue}dispose(){if(!this.m){if(this.m=!0,this.w?.current===this&&this.w.reset(),this.u){if(_enableDisposeWithListenerWarning){const e=this.u;queueMicrotask(()=>{forEachListener(e,t=>t.stack?.print())})}this.u=void 0,this.z=0}this.f?.onDidRemoveLastListener?.(),this.g?.dispose()}}get event(){return this.q??=(e,t,n)=>{if(this.g&&this.z>this.g.threshold**2){const l=`[${this.g.name}] REFUSES to accept new listeners because it exceeded its threshold by far (${this.z} vs ${this.g.threshold})`;console.warn(l);const u=this.g.getMostFrequentStack()??["UNKNOWN stack",-1],o=new $9e(`${l}. HINT: Stack shows most frequent listener (${u[1]}-times)`,u[0]);return(this.f?.onListenerError||$gb)(o),$sd.None}if(this.m)return $sd.None;t&&(e=e.bind(t));const r=new UniqueContainer(e);let s,i;this.g&&this.z>=Math.ceil(this.g.threshold*.2)&&(r.stack=Stacktrace.create(),s=this.g.check(r.stack,this.z+1)),_enableDisposeWithListenerWarning&&(r.stack=i??Stacktrace.create()),this.u?this.u instanceof UniqueContainer?(this.w??=new EventDeliveryQueuePrivate,this.u=[this.u,r]):this.u.push(r):(this.f?.onWillAddFirstListener?.(this),this.u=r,this.f?.onDidAddFirstListener?.(this)),this.f?.onDidAddListener?.(this),this.z++;const a=$qd(()=>{s?.(),this.A(r)});return n instanceof $rd?n.add(a):Array.isArray(n)&&n.push(a),a},this.q}A(e){if(this.f?.onWillRemoveListener?.(this),!this.u)return;if(this.z===1){this.u=void 0,this.f?.onDidRemoveLastListener?.(this),this.z=0;return}const t=this.u,n=t.indexOf(e);if(n===-1)throw console.log("disposed?",this.m),console.log("size?",this.z),console.log("arr?",JSON.stringify(this.u)),new Error("Attempted to dispose unknown listener");this.z--,t[n]=void 0;const r=this.w.current===this;if(this.z*compactionThreshold<=t.length){let s=0;for(let i=0;i<t.length;i++)t[i]?t[s++]=t[i]:r&&s<this.w.end&&(this.w.end--,s<this.w.i&&this.w.i--);t.length=s}}B(e,t){if(!e)return;const n=this.f?.onListenerError||$gb;if(!n){e.value(t);return}try{e.value(t)}catch(r){n(r)}}C(e){const t=e.current.u;for(;e.i<e.end;)this.B(t[e.i++],e.value);e.reset()}fire(e){if(this.w?.current&&(this.C(this.w),this.j?.stop()),this.j?.start(this.z),this.u)if(this.u instanceof UniqueContainer)this.B(this.u,e);else{const t=this.w;t.enqueue(this,e,this.u.length),this.C(t)}this.j?.stop()}hasListeners(){return this.z>0}},EventDeliveryQueuePrivate=class{constructor(){this.i=-1,this.end=0}enqueue(e,t,n){this.i=0,this.end=n,this.current=e,this.value=t}reset(){this.i=this.end,this.current=void 0,this.value=void 0}};function $f(){return globalThis._VSCODE_NLS_MESSAGES}function $g(){return globalThis._VSCODE_NLS_LANGUAGE}var isPseudo=$g()==="pseudo"||typeof document<"u"&&document.location&&typeof document.location.hash=="string"&&document.location.hash.indexOf("pseudo=true")>=0;function _format(e,t){let n;return t.length===0?n=e:n=e.replace(/\{(\d+)\}/g,(r,s)=>{const i=t[parseInt(s)];let a=r;return typeof i=="string"?a=i:(typeof i=="number"||typeof i=="boolean"||i===void 0||i===null)&&(a=String(i)),a}),isPseudo&&(n="\uFF3B"+n.replace(/[aouei]/g,"$&$&")+"\uFF3D"),n}function localize(e,t,...n){return _format(typeof e=="number"?lookupMessage(e,t):t,n)}function lookupMessage(e,t){const n=$f()?.[e];if(typeof n!="string"){if(typeof t=="string")return t;throw new Error(`!!! NLS MISSING: ${e} !!!`)}return n}var $j="en",_isWindows=!1,_isMacintosh=!1,_isLinux=!1,_isLinuxSnap=!1,_isNative=!1,_isWeb=!1,_isElectron=!1,_isIOS=!1,_isCI=!1,_isMobile=!1,_locale=void 0,_language=$j,_platformLocale=$j,_translationsConfigFile=void 0,_userAgent=void 0,$globalThis=globalThis,nodeProcess=void 0;typeof $globalThis.vscode<"u"&&typeof $globalThis.vscode.process<"u"?nodeProcess=$globalThis.vscode.process:typeof process<"u"&&typeof process?.versions?.node=="string"&&(nodeProcess=process);var isElectronProcess=typeof nodeProcess?.versions?.electron=="string",isElectronRenderer=isElectronProcess&&nodeProcess?.type==="renderer";if(typeof nodeProcess=="object"){_isWindows=nodeProcess.platform==="win32",_isMacintosh=nodeProcess.platform==="darwin",_isLinux=nodeProcess.platform==="linux",_isLinuxSnap=_isLinux&&!!nodeProcess.env.SNAP&&!!nodeProcess.env.SNAP_REVISION,_isElectron=isElectronProcess,_isCI=!!nodeProcess.env.CI||!!nodeProcess.env.BUILD_ARTIFACTSTAGINGDIRECTORY,_locale=$j,_language=$j;const e=nodeProcess.env.VSCODE_NLS_CONFIG;if(e)try{const t=JSON.parse(e);_locale=t.userLocale,_platformLocale=t.osLocale,_language=t.resolvedLanguage||$j,_translationsConfigFile=t.languagePack?.translationsConfigFile}catch{}_isNative=!0}else typeof navigator=="object"&&!isElectronRenderer?(_userAgent=navigator.userAgent,_isWindows=_userAgent.indexOf("Windows")>=0,_isMacintosh=_userAgent.indexOf("Macintosh")>=0,_isIOS=(_userAgent.indexOf("Macintosh")>=0||_userAgent.indexOf("iPad")>=0||_userAgent.indexOf("iPhone")>=0)&&!!navigator.maxTouchPoints&&navigator.maxTouchPoints>0,_isLinux=_userAgent.indexOf("Linux")>=0,_isMobile=_userAgent?.indexOf("Mobi")>=0,_isWeb=!0,_language=$g()||$j,_locale=navigator.language.toLowerCase(),_platformLocale=_locale):console.error("Unable to resolve platform.");var Platform;(function(e){e[e.Web=0]="Web",e[e.Mac=1]="Mac",e[e.Linux=2]="Linux",e[e.Windows=3]="Windows"})(Platform||(Platform={}));var _platform=0;_isMacintosh?_platform=1:_isWindows?_platform=3:_isLinux&&(_platform=2);var $l=_isWindows,$m=_isMacintosh,$n=_isLinux,$p=_isNative,$r=_isWeb,$s=_isWeb&&typeof $globalThis.importScripts=="function",$t=$s?$globalThis.origin:void 0,$y=_userAgent,$z=_language,Language;(function(e){function t(){return $z}e.value=t;function n(){return $z.length===2?$z==="en":$z.length>=3?$z[0]==="e"&&$z[1]==="n"&&$z[2]==="-":!1}e.isDefaultVariant=n;function r(){return $z==="en"}e.isDefault=r})(Language||(Language={}));var $D=typeof $globalThis.postMessage=="function"&&!$globalThis.importScripts,$E=(()=>{if($D){const e=[];$globalThis.addEventListener("message",n=>{if(n.data&&n.data.vscodeScheduleAsyncWork)for(let r=0,s=e.length;r<s;r++){const i=e[r];if(i.id===n.data.vscodeScheduleAsyncWork){e.splice(r,1),i.callback();return}}});let t=0;return n=>{const r=++t;e.push({id:r,callback:n}),$globalThis.postMessage({vscodeScheduleAsyncWork:r},"*")}}return e=>setTimeout(e)})(),OperatingSystem;(function(e){e[e.Windows=1]="Windows",e[e.Macintosh=2]="Macintosh",e[e.Linux=3]="Linux"})(OperatingSystem||(OperatingSystem={}));var $H=!!($y&&$y.indexOf("Chrome")>=0),$I=!!($y&&$y.indexOf("Firefox")>=0),$J=!!(!$H&&$y&&$y.indexOf("Safari")>=0),$K=!!($y&&$y.indexOf("Edg/")>=0),$L=!!($y&&$y.indexOf("Android")>=0),shortcutEvent=Object.freeze(function(e,t){const n=setTimeout(e.bind(t),0);return{dispose(){clearTimeout(n)}}}),CancellationToken;(function(e){function t(n){return n===e.None||n===e.Cancelled||n instanceof MutableToken?!0:!n||typeof n!="object"?!1:typeof n.isCancellationRequested=="boolean"&&typeof n.onCancellationRequested=="function"}e.isCancellationToken=t,e.None=Object.freeze({isCancellationRequested:!1,onCancellationRequested:Event.None}),e.Cancelled=Object.freeze({isCancellationRequested:!0,onCancellationRequested:shortcutEvent})})(CancellationToken||(CancellationToken={}));var MutableToken=class{constructor(){this.a=!1,this.b=null}cancel(){this.a||(this.a=!0,this.b&&(this.b.fire(void 0),this.dispose()))}get isCancellationRequested(){return this.a}get onCancellationRequested(){return this.a?shortcutEvent:(this.b||(this.b=new $0e),this.b.event)}dispose(){this.b&&(this.b.dispose(),this.b=null)}},$Dd=class{constructor(e){this.f=void 0,this.g=void 0,this.g=e&&e.onCancellationRequested(this.cancel,this)}get token(){return this.f||(this.f=new MutableToken),this.f}cancel(){this.f?this.f instanceof MutableToken&&this.f.cancel():this.f=CancellationToken.Cancelled}dispose(e=!1){e&&this.cancel(),this.g?.dispose(),this.f?this.f instanceof MutableToken&&this.f.dispose():this.f=CancellationToken.None}};function $Zf(e){return e}var $1f=class{constructor(e,t){this.a=void 0,this.b=void 0,typeof e=="function"?(this.c=e,this.d=$Zf):(this.c=t,this.d=e.getCacheKey)}get(e){const t=this.d(e);return this.b!==t&&(this.b=t,this.a=this.c(e)),this.a}},$3f=class{constructor(e){this.d=e,this.a=!1}get hasValue(){return this.a}get value(){if(!this.a)try{this.b=this.d()}catch(e){this.c=e}finally{this.a=!0}if(this.c)throw this.c;return this.b}get rawValue(){return this.b}};function $$f(e){return e.replace(/[\\\{\}\*\+\?\|\^\$\.\[\]\(\)]/g,"\\$&")}function $kg(e){return e.split(/\r\n|\r|\n/)}function $mg(e){for(let t=0,n=e.length;t<n;t++){const r=e.charCodeAt(t);if(r!==32&&r!==9)return t}return-1}function $og(e,t=e.length-1){for(let n=t;n>=0;n--){const r=e.charCodeAt(n);if(r!==32&&r!==9)return n}return-1}function $rg(e,t){return e<t?-1:e>t?1:0}function $sg(e,t,n=0,r=e.length,s=0,i=t.length){for(;n<r&&s<i;n++,s++){const u=e.charCodeAt(n),o=t.charCodeAt(s);if(u<o)return-1;if(u>o)return 1}const a=r-n,l=i-s;return a<l?-1:a>l?1:0}function $ug(e,t,n=0,r=e.length,s=0,i=t.length){for(;n<r&&s<i;n++,s++){let u=e.charCodeAt(n),o=t.charCodeAt(s);if(u===o)continue;if(u>=128||o>=128)return $sg(e.toLowerCase(),t.toLowerCase(),n,r,s,i);$wg(u)&&(u-=32),$wg(o)&&(o-=32);const h=u-o;if(h!==0)return h}const a=r-n,l=i-s;return a<l?-1:a>l?1:0}function $wg(e){return e>=97&&e<=122}function $xg(e){return e>=65&&e<=90}function $yg(e,t){return e.length===t.length&&$ug(e,t)===0}function $zg(e,t){const n=t.length;return t.length>e.length?!1:$ug(e,t,0,n)===0}function $Ag(e,t){const n=Math.min(e.length,t.length);let r;for(r=0;r<n;r++)if(e.charCodeAt(r)!==t.charCodeAt(r))return r;return n}function $Bg(e,t){const n=Math.min(e.length,t.length);let r;const s=e.length-1,i=t.length-1;for(r=0;r<n;r++)if(e.charCodeAt(s-r)!==t.charCodeAt(i-r))return r;return n}function $Cg(e){return 55296<=e&&e<=56319}function $Dg(e){return 56320<=e&&e<=57343}function $Eg(e,t){return(e-55296<<10)+(t-56320)+65536}function $Fg(e,t,n){const r=e.charCodeAt(n);if($Cg(r)&&n+1<t){const s=e.charCodeAt(n+1);if($Dg(s))return $Eg(r,s)}return r}var IS_BASIC_ASCII=/^[\t\n\r\x20-\x7E]*$/;function $Ng(e){return IS_BASIC_ASCII.test(e)}var CSI_SEQUENCE=/(?:\x1b\[|\x9b)[=?>!]?[\d;:]*["$#'* ]?[a-zA-Z@^`{}|~]/,OSC_SEQUENCE=/(?:\x1b\]|\x9d).*?(?:\x1b\\|\x07|\x9c)/,ESC_SEQUENCE=/\x1b(?:[ #%\(\)\*\+\-\.\/]?[a-zA-Z0-9\|}~@])/,CONTROL_SEQUENCES=new RegExp("(?:"+[CSI_SEQUENCE.source,OSC_SEQUENCE.source,ESC_SEQUENCE.source].join("|")+")","g"),$Xg="\uFEFF",GraphemeBreakType;(function(e){e[e.Other=0]="Other",e[e.Prepend=1]="Prepend",e[e.CR=2]="CR",e[e.LF=3]="LF",e[e.Control=4]="Control",e[e.Extend=5]="Extend",e[e.Regional_Indicator=6]="Regional_Indicator",e[e.SpacingMark=7]="SpacingMark",e[e.L=8]="L",e[e.V=9]="V",e[e.T=10]="T",e[e.LV=11]="LV",e[e.LVT=12]="LVT",e[e.ZWJ=13]="ZWJ",e[e.Extended_Pictographic=14]="Extended_Pictographic"})(GraphemeBreakType||(GraphemeBreakType={}));var GraphemeBreakTree=class re{static{this.c=null}static getInstance(){return re.c||(re.c=new re),re.c}constructor(){this.d=getGraphemeBreakRawData()}getGraphemeBreakType(t){if(t<32)return t===10?3:t===13?2:4;if(t<127)return 0;const n=this.d,r=n.length/3;let s=1;for(;s<=r;)if(t<n[3*s])s=2*s;else if(t>n[3*s+1])s=2*s+1;else return n[3*s+2];return 0}};function getGraphemeBreakRawData(){return JSON.parse("[0,0,0,51229,51255,12,44061,44087,12,127462,127487,6,7083,7085,5,47645,47671,12,54813,54839,12,128678,128678,14,3270,3270,5,9919,9923,14,45853,45879,12,49437,49463,12,53021,53047,12,71216,71218,7,128398,128399,14,129360,129374,14,2519,2519,5,4448,4519,9,9742,9742,14,12336,12336,14,44957,44983,12,46749,46775,12,48541,48567,12,50333,50359,12,52125,52151,12,53917,53943,12,69888,69890,5,73018,73018,5,127990,127990,14,128558,128559,14,128759,128760,14,129653,129655,14,2027,2035,5,2891,2892,7,3761,3761,5,6683,6683,5,8293,8293,4,9825,9826,14,9999,9999,14,43452,43453,5,44509,44535,12,45405,45431,12,46301,46327,12,47197,47223,12,48093,48119,12,48989,49015,12,49885,49911,12,50781,50807,12,51677,51703,12,52573,52599,12,53469,53495,12,54365,54391,12,65279,65279,4,70471,70472,7,72145,72147,7,119173,119179,5,127799,127818,14,128240,128244,14,128512,128512,14,128652,128652,14,128721,128722,14,129292,129292,14,129445,129450,14,129734,129743,14,1476,1477,5,2366,2368,7,2750,2752,7,3076,3076,5,3415,3415,5,4141,4144,5,6109,6109,5,6964,6964,5,7394,7400,5,9197,9198,14,9770,9770,14,9877,9877,14,9968,9969,14,10084,10084,14,43052,43052,5,43713,43713,5,44285,44311,12,44733,44759,12,45181,45207,12,45629,45655,12,46077,46103,12,46525,46551,12,46973,46999,12,47421,47447,12,47869,47895,12,48317,48343,12,48765,48791,12,49213,49239,12,49661,49687,12,50109,50135,12,50557,50583,12,51005,51031,12,51453,51479,12,51901,51927,12,52349,52375,12,52797,52823,12,53245,53271,12,53693,53719,12,54141,54167,12,54589,54615,12,55037,55063,12,69506,69509,5,70191,70193,5,70841,70841,7,71463,71467,5,72330,72342,5,94031,94031,5,123628,123631,5,127763,127765,14,127941,127941,14,128043,128062,14,128302,128317,14,128465,128467,14,128539,128539,14,128640,128640,14,128662,128662,14,128703,128703,14,128745,128745,14,129004,129007,14,129329,129330,14,129402,129402,14,129483,129483,14,129686,129704,14,130048,131069,14,173,173,4,1757,1757,1,2200,2207,5,2434,2435,7,2631,2632,5,2817,2817,5,3008,3008,5,3201,3201,5,3387,3388,5,3542,3542,5,3902,3903,7,4190,4192,5,6002,6003,5,6439,6440,5,6765,6770,7,7019,7027,5,7154,7155,7,8205,8205,13,8505,8505,14,9654,9654,14,9757,9757,14,9792,9792,14,9852,9853,14,9890,9894,14,9937,9937,14,9981,9981,14,10035,10036,14,11035,11036,14,42654,42655,5,43346,43347,7,43587,43587,5,44006,44007,7,44173,44199,12,44397,44423,12,44621,44647,12,44845,44871,12,45069,45095,12,45293,45319,12,45517,45543,12,45741,45767,12,45965,45991,12,46189,46215,12,46413,46439,12,46637,46663,12,46861,46887,12,47085,47111,12,47309,47335,12,47533,47559,12,47757,47783,12,47981,48007,12,48205,48231,12,48429,48455,12,48653,48679,12,48877,48903,12,49101,49127,12,49325,49351,12,49549,49575,12,49773,49799,12,49997,50023,12,50221,50247,12,50445,50471,12,50669,50695,12,50893,50919,12,51117,51143,12,51341,51367,12,51565,51591,12,51789,51815,12,52013,52039,12,52237,52263,12,52461,52487,12,52685,52711,12,52909,52935,12,53133,53159,12,53357,53383,12,53581,53607,12,53805,53831,12,54029,54055,12,54253,54279,12,54477,54503,12,54701,54727,12,54925,54951,12,55149,55175,12,68101,68102,5,69762,69762,7,70067,70069,7,70371,70378,5,70720,70721,7,71087,71087,5,71341,71341,5,71995,71996,5,72249,72249,7,72850,72871,5,73109,73109,5,118576,118598,5,121505,121519,5,127245,127247,14,127568,127569,14,127777,127777,14,127872,127891,14,127956,127967,14,128015,128016,14,128110,128172,14,128259,128259,14,128367,128368,14,128424,128424,14,128488,128488,14,128530,128532,14,128550,128551,14,128566,128566,14,128647,128647,14,128656,128656,14,128667,128673,14,128691,128693,14,128715,128715,14,128728,128732,14,128752,128752,14,128765,128767,14,129096,129103,14,129311,129311,14,129344,129349,14,129394,129394,14,129413,129425,14,129466,129471,14,129511,129535,14,129664,129666,14,129719,129722,14,129760,129767,14,917536,917631,5,13,13,2,1160,1161,5,1564,1564,4,1807,1807,1,2085,2087,5,2307,2307,7,2382,2383,7,2497,2500,5,2563,2563,7,2677,2677,5,2763,2764,7,2879,2879,5,2914,2915,5,3021,3021,5,3142,3144,5,3263,3263,5,3285,3286,5,3398,3400,7,3530,3530,5,3633,3633,5,3864,3865,5,3974,3975,5,4155,4156,7,4229,4230,5,5909,5909,7,6078,6085,7,6277,6278,5,6451,6456,7,6744,6750,5,6846,6846,5,6972,6972,5,7074,7077,5,7146,7148,7,7222,7223,5,7416,7417,5,8234,8238,4,8417,8417,5,9000,9000,14,9203,9203,14,9730,9731,14,9748,9749,14,9762,9763,14,9776,9783,14,9800,9811,14,9831,9831,14,9872,9873,14,9882,9882,14,9900,9903,14,9929,9933,14,9941,9960,14,9974,9974,14,9989,9989,14,10006,10006,14,10062,10062,14,10160,10160,14,11647,11647,5,12953,12953,14,43019,43019,5,43232,43249,5,43443,43443,5,43567,43568,7,43696,43696,5,43765,43765,7,44013,44013,5,44117,44143,12,44229,44255,12,44341,44367,12,44453,44479,12,44565,44591,12,44677,44703,12,44789,44815,12,44901,44927,12,45013,45039,12,45125,45151,12,45237,45263,12,45349,45375,12,45461,45487,12,45573,45599,12,45685,45711,12,45797,45823,12,45909,45935,12,46021,46047,12,46133,46159,12,46245,46271,12,46357,46383,12,46469,46495,12,46581,46607,12,46693,46719,12,46805,46831,12,46917,46943,12,47029,47055,12,47141,47167,12,47253,47279,12,47365,47391,12,47477,47503,12,47589,47615,12,47701,47727,12,47813,47839,12,47925,47951,12,48037,48063,12,48149,48175,12,48261,48287,12,48373,48399,12,48485,48511,12,48597,48623,12,48709,48735,12,48821,48847,12,48933,48959,12,49045,49071,12,49157,49183,12,49269,49295,12,49381,49407,12,49493,49519,12,49605,49631,12,49717,49743,12,49829,49855,12,49941,49967,12,50053,50079,12,50165,50191,12,50277,50303,12,50389,50415,12,50501,50527,12,50613,50639,12,50725,50751,12,50837,50863,12,50949,50975,12,51061,51087,12,51173,51199,12,51285,51311,12,51397,51423,12,51509,51535,12,51621,51647,12,51733,51759,12,51845,51871,12,51957,51983,12,52069,52095,12,52181,52207,12,52293,52319,12,52405,52431,12,52517,52543,12,52629,52655,12,52741,52767,12,52853,52879,12,52965,52991,12,53077,53103,12,53189,53215,12,53301,53327,12,53413,53439,12,53525,53551,12,53637,53663,12,53749,53775,12,53861,53887,12,53973,53999,12,54085,54111,12,54197,54223,12,54309,54335,12,54421,54447,12,54533,54559,12,54645,54671,12,54757,54783,12,54869,54895,12,54981,55007,12,55093,55119,12,55243,55291,10,66045,66045,5,68325,68326,5,69688,69702,5,69817,69818,5,69957,69958,7,70089,70092,5,70198,70199,5,70462,70462,5,70502,70508,5,70750,70750,5,70846,70846,7,71100,71101,5,71230,71230,7,71351,71351,5,71737,71738,5,72000,72000,7,72160,72160,5,72273,72278,5,72752,72758,5,72882,72883,5,73031,73031,5,73461,73462,7,94192,94193,7,119149,119149,7,121403,121452,5,122915,122916,5,126980,126980,14,127358,127359,14,127535,127535,14,127759,127759,14,127771,127771,14,127792,127793,14,127825,127867,14,127897,127899,14,127945,127945,14,127985,127986,14,128000,128007,14,128021,128021,14,128066,128100,14,128184,128235,14,128249,128252,14,128266,128276,14,128335,128335,14,128379,128390,14,128407,128419,14,128444,128444,14,128481,128481,14,128499,128499,14,128526,128526,14,128536,128536,14,128543,128543,14,128556,128556,14,128564,128564,14,128577,128580,14,128643,128645,14,128649,128649,14,128654,128654,14,128660,128660,14,128664,128664,14,128675,128675,14,128686,128689,14,128695,128696,14,128705,128709,14,128717,128719,14,128725,128725,14,128736,128741,14,128747,128748,14,128755,128755,14,128762,128762,14,128981,128991,14,129009,129023,14,129160,129167,14,129296,129304,14,129320,129327,14,129340,129342,14,129356,129356,14,129388,129392,14,129399,129400,14,129404,129407,14,129432,129442,14,129454,129455,14,129473,129474,14,129485,129487,14,129648,129651,14,129659,129660,14,129671,129679,14,129709,129711,14,129728,129730,14,129751,129753,14,129776,129782,14,917505,917505,4,917760,917999,5,10,10,3,127,159,4,768,879,5,1471,1471,5,1536,1541,1,1648,1648,5,1767,1768,5,1840,1866,5,2070,2073,5,2137,2139,5,2274,2274,1,2363,2363,7,2377,2380,7,2402,2403,5,2494,2494,5,2507,2508,7,2558,2558,5,2622,2624,7,2641,2641,5,2691,2691,7,2759,2760,5,2786,2787,5,2876,2876,5,2881,2884,5,2901,2902,5,3006,3006,5,3014,3016,7,3072,3072,5,3134,3136,5,3157,3158,5,3260,3260,5,3266,3266,5,3274,3275,7,3328,3329,5,3391,3392,7,3405,3405,5,3457,3457,5,3536,3537,7,3551,3551,5,3636,3642,5,3764,3772,5,3895,3895,5,3967,3967,7,3993,4028,5,4146,4151,5,4182,4183,7,4226,4226,5,4253,4253,5,4957,4959,5,5940,5940,7,6070,6070,7,6087,6088,7,6158,6158,4,6432,6434,5,6448,6449,7,6679,6680,5,6742,6742,5,6754,6754,5,6783,6783,5,6912,6915,5,6966,6970,5,6978,6978,5,7042,7042,7,7080,7081,5,7143,7143,7,7150,7150,7,7212,7219,5,7380,7392,5,7412,7412,5,8203,8203,4,8232,8232,4,8265,8265,14,8400,8412,5,8421,8432,5,8617,8618,14,9167,9167,14,9200,9200,14,9410,9410,14,9723,9726,14,9733,9733,14,9745,9745,14,9752,9752,14,9760,9760,14,9766,9766,14,9774,9774,14,9786,9786,14,9794,9794,14,9823,9823,14,9828,9828,14,9833,9850,14,9855,9855,14,9875,9875,14,9880,9880,14,9885,9887,14,9896,9897,14,9906,9916,14,9926,9927,14,9935,9935,14,9939,9939,14,9962,9962,14,9972,9972,14,9978,9978,14,9986,9986,14,9997,9997,14,10002,10002,14,10017,10017,14,10055,10055,14,10071,10071,14,10133,10135,14,10548,10549,14,11093,11093,14,12330,12333,5,12441,12442,5,42608,42610,5,43010,43010,5,43045,43046,5,43188,43203,7,43302,43309,5,43392,43394,5,43446,43449,5,43493,43493,5,43571,43572,7,43597,43597,7,43703,43704,5,43756,43757,5,44003,44004,7,44009,44010,7,44033,44059,12,44089,44115,12,44145,44171,12,44201,44227,12,44257,44283,12,44313,44339,12,44369,44395,12,44425,44451,12,44481,44507,12,44537,44563,12,44593,44619,12,44649,44675,12,44705,44731,12,44761,44787,12,44817,44843,12,44873,44899,12,44929,44955,12,44985,45011,12,45041,45067,12,45097,45123,12,45153,45179,12,45209,45235,12,45265,45291,12,45321,45347,12,45377,45403,12,45433,45459,12,45489,45515,12,45545,45571,12,45601,45627,12,45657,45683,12,45713,45739,12,45769,45795,12,45825,45851,12,45881,45907,12,45937,45963,12,45993,46019,12,46049,46075,12,46105,46131,12,46161,46187,12,46217,46243,12,46273,46299,12,46329,46355,12,46385,46411,12,46441,46467,12,46497,46523,12,46553,46579,12,46609,46635,12,46665,46691,12,46721,46747,12,46777,46803,12,46833,46859,12,46889,46915,12,46945,46971,12,47001,47027,12,47057,47083,12,47113,47139,12,47169,47195,12,47225,47251,12,47281,47307,12,47337,47363,12,47393,47419,12,47449,47475,12,47505,47531,12,47561,47587,12,47617,47643,12,47673,47699,12,47729,47755,12,47785,47811,12,47841,47867,12,47897,47923,12,47953,47979,12,48009,48035,12,48065,48091,12,48121,48147,12,48177,48203,12,48233,48259,12,48289,48315,12,48345,48371,12,48401,48427,12,48457,48483,12,48513,48539,12,48569,48595,12,48625,48651,12,48681,48707,12,48737,48763,12,48793,48819,12,48849,48875,12,48905,48931,12,48961,48987,12,49017,49043,12,49073,49099,12,49129,49155,12,49185,49211,12,49241,49267,12,49297,49323,12,49353,49379,12,49409,49435,12,49465,49491,12,49521,49547,12,49577,49603,12,49633,49659,12,49689,49715,12,49745,49771,12,49801,49827,12,49857,49883,12,49913,49939,12,49969,49995,12,50025,50051,12,50081,50107,12,50137,50163,12,50193,50219,12,50249,50275,12,50305,50331,12,50361,50387,12,50417,50443,12,50473,50499,12,50529,50555,12,50585,50611,12,50641,50667,12,50697,50723,12,50753,50779,12,50809,50835,12,50865,50891,12,50921,50947,12,50977,51003,12,51033,51059,12,51089,51115,12,51145,51171,12,51201,51227,12,51257,51283,12,51313,51339,12,51369,51395,12,51425,51451,12,51481,51507,12,51537,51563,12,51593,51619,12,51649,51675,12,51705,51731,12,51761,51787,12,51817,51843,12,51873,51899,12,51929,51955,12,51985,52011,12,52041,52067,12,52097,52123,12,52153,52179,12,52209,52235,12,52265,52291,12,52321,52347,12,52377,52403,12,52433,52459,12,52489,52515,12,52545,52571,12,52601,52627,12,52657,52683,12,52713,52739,12,52769,52795,12,52825,52851,12,52881,52907,12,52937,52963,12,52993,53019,12,53049,53075,12,53105,53131,12,53161,53187,12,53217,53243,12,53273,53299,12,53329,53355,12,53385,53411,12,53441,53467,12,53497,53523,12,53553,53579,12,53609,53635,12,53665,53691,12,53721,53747,12,53777,53803,12,53833,53859,12,53889,53915,12,53945,53971,12,54001,54027,12,54057,54083,12,54113,54139,12,54169,54195,12,54225,54251,12,54281,54307,12,54337,54363,12,54393,54419,12,54449,54475,12,54505,54531,12,54561,54587,12,54617,54643,12,54673,54699,12,54729,54755,12,54785,54811,12,54841,54867,12,54897,54923,12,54953,54979,12,55009,55035,12,55065,55091,12,55121,55147,12,55177,55203,12,65024,65039,5,65520,65528,4,66422,66426,5,68152,68154,5,69291,69292,5,69633,69633,5,69747,69748,5,69811,69814,5,69826,69826,5,69932,69932,7,70016,70017,5,70079,70080,7,70095,70095,5,70196,70196,5,70367,70367,5,70402,70403,7,70464,70464,5,70487,70487,5,70709,70711,7,70725,70725,7,70833,70834,7,70843,70844,7,70849,70849,7,71090,71093,5,71103,71104,5,71227,71228,7,71339,71339,5,71344,71349,5,71458,71461,5,71727,71735,5,71985,71989,7,71998,71998,5,72002,72002,7,72154,72155,5,72193,72202,5,72251,72254,5,72281,72283,5,72344,72345,5,72766,72766,7,72874,72880,5,72885,72886,5,73023,73029,5,73104,73105,5,73111,73111,5,92912,92916,5,94095,94098,5,113824,113827,4,119142,119142,7,119155,119162,4,119362,119364,5,121476,121476,5,122888,122904,5,123184,123190,5,125252,125258,5,127183,127183,14,127340,127343,14,127377,127386,14,127491,127503,14,127548,127551,14,127744,127756,14,127761,127761,14,127769,127769,14,127773,127774,14,127780,127788,14,127796,127797,14,127820,127823,14,127869,127869,14,127894,127895,14,127902,127903,14,127943,127943,14,127947,127950,14,127972,127972,14,127988,127988,14,127992,127994,14,128009,128011,14,128019,128019,14,128023,128041,14,128064,128064,14,128102,128107,14,128174,128181,14,128238,128238,14,128246,128247,14,128254,128254,14,128264,128264,14,128278,128299,14,128329,128330,14,128348,128359,14,128371,128377,14,128392,128393,14,128401,128404,14,128421,128421,14,128433,128434,14,128450,128452,14,128476,128478,14,128483,128483,14,128495,128495,14,128506,128506,14,128519,128520,14,128528,128528,14,128534,128534,14,128538,128538,14,128540,128542,14,128544,128549,14,128552,128555,14,128557,128557,14,128560,128563,14,128565,128565,14,128567,128576,14,128581,128591,14,128641,128642,14,128646,128646,14,128648,128648,14,128650,128651,14,128653,128653,14,128655,128655,14,128657,128659,14,128661,128661,14,128663,128663,14,128665,128666,14,128674,128674,14,128676,128677,14,128679,128685,14,128690,128690,14,128694,128694,14,128697,128702,14,128704,128704,14,128710,128714,14,128716,128716,14,128720,128720,14,128723,128724,14,128726,128727,14,128733,128735,14,128742,128744,14,128746,128746,14,128749,128751,14,128753,128754,14,128756,128758,14,128761,128761,14,128763,128764,14,128884,128895,14,128992,129003,14,129008,129008,14,129036,129039,14,129114,129119,14,129198,129279,14,129293,129295,14,129305,129310,14,129312,129319,14,129328,129328,14,129331,129338,14,129343,129343,14,129351,129355,14,129357,129359,14,129375,129387,14,129393,129393,14,129395,129398,14,129401,129401,14,129403,129403,14,129408,129412,14,129426,129431,14,129443,129444,14,129451,129453,14,129456,129465,14,129472,129472,14,129475,129482,14,129484,129484,14,129488,129510,14,129536,129647,14,129652,129652,14,129656,129658,14,129661,129663,14,129667,129670,14,129680,129685,14,129705,129708,14,129712,129718,14,129723,129727,14,129731,129733,14,129744,129750,14,129754,129759,14,129768,129775,14,129783,129791,14,917504,917504,4,917506,917535,4,917632,917759,4,918000,921599,4,0,9,4,11,12,4,14,31,4,169,169,14,174,174,14,1155,1159,5,1425,1469,5,1473,1474,5,1479,1479,5,1552,1562,5,1611,1631,5,1750,1756,5,1759,1764,5,1770,1773,5,1809,1809,5,1958,1968,5,2045,2045,5,2075,2083,5,2089,2093,5,2192,2193,1,2250,2273,5,2275,2306,5,2362,2362,5,2364,2364,5,2369,2376,5,2381,2381,5,2385,2391,5,2433,2433,5,2492,2492,5,2495,2496,7,2503,2504,7,2509,2509,5,2530,2531,5,2561,2562,5,2620,2620,5,2625,2626,5,2635,2637,5,2672,2673,5,2689,2690,5,2748,2748,5,2753,2757,5,2761,2761,7,2765,2765,5,2810,2815,5,2818,2819,7,2878,2878,5,2880,2880,7,2887,2888,7,2893,2893,5,2903,2903,5,2946,2946,5,3007,3007,7,3009,3010,7,3018,3020,7,3031,3031,5,3073,3075,7,3132,3132,5,3137,3140,7,3146,3149,5,3170,3171,5,3202,3203,7,3262,3262,7,3264,3265,7,3267,3268,7,3271,3272,7,3276,3277,5,3298,3299,5,3330,3331,7,3390,3390,5,3393,3396,5,3402,3404,7,3406,3406,1,3426,3427,5,3458,3459,7,3535,3535,5,3538,3540,5,3544,3550,7,3570,3571,7,3635,3635,7,3655,3662,5,3763,3763,7,3784,3789,5,3893,3893,5,3897,3897,5,3953,3966,5,3968,3972,5,3981,3991,5,4038,4038,5,4145,4145,7,4153,4154,5,4157,4158,5,4184,4185,5,4209,4212,5,4228,4228,7,4237,4237,5,4352,4447,8,4520,4607,10,5906,5908,5,5938,5939,5,5970,5971,5,6068,6069,5,6071,6077,5,6086,6086,5,6089,6099,5,6155,6157,5,6159,6159,5,6313,6313,5,6435,6438,7,6441,6443,7,6450,6450,5,6457,6459,5,6681,6682,7,6741,6741,7,6743,6743,7,6752,6752,5,6757,6764,5,6771,6780,5,6832,6845,5,6847,6862,5,6916,6916,7,6965,6965,5,6971,6971,7,6973,6977,7,6979,6980,7,7040,7041,5,7073,7073,7,7078,7079,7,7082,7082,7,7142,7142,5,7144,7145,5,7149,7149,5,7151,7153,5,7204,7211,7,7220,7221,7,7376,7378,5,7393,7393,7,7405,7405,5,7415,7415,7,7616,7679,5,8204,8204,5,8206,8207,4,8233,8233,4,8252,8252,14,8288,8292,4,8294,8303,4,8413,8416,5,8418,8420,5,8482,8482,14,8596,8601,14,8986,8987,14,9096,9096,14,9193,9196,14,9199,9199,14,9201,9202,14,9208,9210,14,9642,9643,14,9664,9664,14,9728,9729,14,9732,9732,14,9735,9741,14,9743,9744,14,9746,9746,14,9750,9751,14,9753,9756,14,9758,9759,14,9761,9761,14,9764,9765,14,9767,9769,14,9771,9773,14,9775,9775,14,9784,9785,14,9787,9791,14,9793,9793,14,9795,9799,14,9812,9822,14,9824,9824,14,9827,9827,14,9829,9830,14,9832,9832,14,9851,9851,14,9854,9854,14,9856,9861,14,9874,9874,14,9876,9876,14,9878,9879,14,9881,9881,14,9883,9884,14,9888,9889,14,9895,9895,14,9898,9899,14,9904,9905,14,9917,9918,14,9924,9925,14,9928,9928,14,9934,9934,14,9936,9936,14,9938,9938,14,9940,9940,14,9961,9961,14,9963,9967,14,9970,9971,14,9973,9973,14,9975,9977,14,9979,9980,14,9982,9985,14,9987,9988,14,9992,9996,14,9998,9998,14,10000,10001,14,10004,10004,14,10013,10013,14,10024,10024,14,10052,10052,14,10060,10060,14,10067,10069,14,10083,10083,14,10085,10087,14,10145,10145,14,10175,10175,14,11013,11015,14,11088,11088,14,11503,11505,5,11744,11775,5,12334,12335,5,12349,12349,14,12951,12951,14,42607,42607,5,42612,42621,5,42736,42737,5,43014,43014,5,43043,43044,7,43047,43047,7,43136,43137,7,43204,43205,5,43263,43263,5,43335,43345,5,43360,43388,8,43395,43395,7,43444,43445,7,43450,43451,7,43454,43456,7,43561,43566,5,43569,43570,5,43573,43574,5,43596,43596,5,43644,43644,5,43698,43700,5,43710,43711,5,43755,43755,7,43758,43759,7,43766,43766,5,44005,44005,5,44008,44008,5,44012,44012,7,44032,44032,11,44060,44060,11,44088,44088,11,44116,44116,11,44144,44144,11,44172,44172,11,44200,44200,11,44228,44228,11,44256,44256,11,44284,44284,11,44312,44312,11,44340,44340,11,44368,44368,11,44396,44396,11,44424,44424,11,44452,44452,11,44480,44480,11,44508,44508,11,44536,44536,11,44564,44564,11,44592,44592,11,44620,44620,11,44648,44648,11,44676,44676,11,44704,44704,11,44732,44732,11,44760,44760,11,44788,44788,11,44816,44816,11,44844,44844,11,44872,44872,11,44900,44900,11,44928,44928,11,44956,44956,11,44984,44984,11,45012,45012,11,45040,45040,11,45068,45068,11,45096,45096,11,45124,45124,11,45152,45152,11,45180,45180,11,45208,45208,11,45236,45236,11,45264,45264,11,45292,45292,11,45320,45320,11,45348,45348,11,45376,45376,11,45404,45404,11,45432,45432,11,45460,45460,11,45488,45488,11,45516,45516,11,45544,45544,11,45572,45572,11,45600,45600,11,45628,45628,11,45656,45656,11,45684,45684,11,45712,45712,11,45740,45740,11,45768,45768,11,45796,45796,11,45824,45824,11,45852,45852,11,45880,45880,11,45908,45908,11,45936,45936,11,45964,45964,11,45992,45992,11,46020,46020,11,46048,46048,11,46076,46076,11,46104,46104,11,46132,46132,11,46160,46160,11,46188,46188,11,46216,46216,11,46244,46244,11,46272,46272,11,46300,46300,11,46328,46328,11,46356,46356,11,46384,46384,11,46412,46412,11,46440,46440,11,46468,46468,11,46496,46496,11,46524,46524,11,46552,46552,11,46580,46580,11,46608,46608,11,46636,46636,11,46664,46664,11,46692,46692,11,46720,46720,11,46748,46748,11,46776,46776,11,46804,46804,11,46832,46832,11,46860,46860,11,46888,46888,11,46916,46916,11,46944,46944,11,46972,46972,11,47000,47000,11,47028,47028,11,47056,47056,11,47084,47084,11,47112,47112,11,47140,47140,11,47168,47168,11,47196,47196,11,47224,47224,11,47252,47252,11,47280,47280,11,47308,47308,11,47336,47336,11,47364,47364,11,47392,47392,11,47420,47420,11,47448,47448,11,47476,47476,11,47504,47504,11,47532,47532,11,47560,47560,11,47588,47588,11,47616,47616,11,47644,47644,11,47672,47672,11,47700,47700,11,47728,47728,11,47756,47756,11,47784,47784,11,47812,47812,11,47840,47840,11,47868,47868,11,47896,47896,11,47924,47924,11,47952,47952,11,47980,47980,11,48008,48008,11,48036,48036,11,48064,48064,11,48092,48092,11,48120,48120,11,48148,48148,11,48176,48176,11,48204,48204,11,48232,48232,11,48260,48260,11,48288,48288,11,48316,48316,11,48344,48344,11,48372,48372,11,48400,48400,11,48428,48428,11,48456,48456,11,48484,48484,11,48512,48512,11,48540,48540,11,48568,48568,11,48596,48596,11,48624,48624,11,48652,48652,11,48680,48680,11,48708,48708,11,48736,48736,11,48764,48764,11,48792,48792,11,48820,48820,11,48848,48848,11,48876,48876,11,48904,48904,11,48932,48932,11,48960,48960,11,48988,48988,11,49016,49016,11,49044,49044,11,49072,49072,11,49100,49100,11,49128,49128,11,49156,49156,11,49184,49184,11,49212,49212,11,49240,49240,11,49268,49268,11,49296,49296,11,49324,49324,11,49352,49352,11,49380,49380,11,49408,49408,11,49436,49436,11,49464,49464,11,49492,49492,11,49520,49520,11,49548,49548,11,49576,49576,11,49604,49604,11,49632,49632,11,49660,49660,11,49688,49688,11,49716,49716,11,49744,49744,11,49772,49772,11,49800,49800,11,49828,49828,11,49856,49856,11,49884,49884,11,49912,49912,11,49940,49940,11,49968,49968,11,49996,49996,11,50024,50024,11,50052,50052,11,50080,50080,11,50108,50108,11,50136,50136,11,50164,50164,11,50192,50192,11,50220,50220,11,50248,50248,11,50276,50276,11,50304,50304,11,50332,50332,11,50360,50360,11,50388,50388,11,50416,50416,11,50444,50444,11,50472,50472,11,50500,50500,11,50528,50528,11,50556,50556,11,50584,50584,11,50612,50612,11,50640,50640,11,50668,50668,11,50696,50696,11,50724,50724,11,50752,50752,11,50780,50780,11,50808,50808,11,50836,50836,11,50864,50864,11,50892,50892,11,50920,50920,11,50948,50948,11,50976,50976,11,51004,51004,11,51032,51032,11,51060,51060,11,51088,51088,11,51116,51116,11,51144,51144,11,51172,51172,11,51200,51200,11,51228,51228,11,51256,51256,11,51284,51284,11,51312,51312,11,51340,51340,11,51368,51368,11,51396,51396,11,51424,51424,11,51452,51452,11,51480,51480,11,51508,51508,11,51536,51536,11,51564,51564,11,51592,51592,11,51620,51620,11,51648,51648,11,51676,51676,11,51704,51704,11,51732,51732,11,51760,51760,11,51788,51788,11,51816,51816,11,51844,51844,11,51872,51872,11,51900,51900,11,51928,51928,11,51956,51956,11,51984,51984,11,52012,52012,11,52040,52040,11,52068,52068,11,52096,52096,11,52124,52124,11,52152,52152,11,52180,52180,11,52208,52208,11,52236,52236,11,52264,52264,11,52292,52292,11,52320,52320,11,52348,52348,11,52376,52376,11,52404,52404,11,52432,52432,11,52460,52460,11,52488,52488,11,52516,52516,11,52544,52544,11,52572,52572,11,52600,52600,11,52628,52628,11,52656,52656,11,52684,52684,11,52712,52712,11,52740,52740,11,52768,52768,11,52796,52796,11,52824,52824,11,52852,52852,11,52880,52880,11,52908,52908,11,52936,52936,11,52964,52964,11,52992,52992,11,53020,53020,11,53048,53048,11,53076,53076,11,53104,53104,11,53132,53132,11,53160,53160,11,53188,53188,11,53216,53216,11,53244,53244,11,53272,53272,11,53300,53300,11,53328,53328,11,53356,53356,11,53384,53384,11,53412,53412,11,53440,53440,11,53468,53468,11,53496,53496,11,53524,53524,11,53552,53552,11,53580,53580,11,53608,53608,11,53636,53636,11,53664,53664,11,53692,53692,11,53720,53720,11,53748,53748,11,53776,53776,11,53804,53804,11,53832,53832,11,53860,53860,11,53888,53888,11,53916,53916,11,53944,53944,11,53972,53972,11,54000,54000,11,54028,54028,11,54056,54056,11,54084,54084,11,54112,54112,11,54140,54140,11,54168,54168,11,54196,54196,11,54224,54224,11,54252,54252,11,54280,54280,11,54308,54308,11,54336,54336,11,54364,54364,11,54392,54392,11,54420,54420,11,54448,54448,11,54476,54476,11,54504,54504,11,54532,54532,11,54560,54560,11,54588,54588,11,54616,54616,11,54644,54644,11,54672,54672,11,54700,54700,11,54728,54728,11,54756,54756,11,54784,54784,11,54812,54812,11,54840,54840,11,54868,54868,11,54896,54896,11,54924,54924,11,54952,54952,11,54980,54980,11,55008,55008,11,55036,55036,11,55064,55064,11,55092,55092,11,55120,55120,11,55148,55148,11,55176,55176,11,55216,55238,9,64286,64286,5,65056,65071,5,65438,65439,5,65529,65531,4,66272,66272,5,68097,68099,5,68108,68111,5,68159,68159,5,68900,68903,5,69446,69456,5,69632,69632,7,69634,69634,7,69744,69744,5,69759,69761,5,69808,69810,7,69815,69816,7,69821,69821,1,69837,69837,1,69927,69931,5,69933,69940,5,70003,70003,5,70018,70018,7,70070,70078,5,70082,70083,1,70094,70094,7,70188,70190,7,70194,70195,7,70197,70197,7,70206,70206,5,70368,70370,7,70400,70401,5,70459,70460,5,70463,70463,7,70465,70468,7,70475,70477,7,70498,70499,7,70512,70516,5,70712,70719,5,70722,70724,5,70726,70726,5,70832,70832,5,70835,70840,5,70842,70842,5,70845,70845,5,70847,70848,5,70850,70851,5,71088,71089,7,71096,71099,7,71102,71102,7,71132,71133,5,71219,71226,5,71229,71229,5,71231,71232,5,71340,71340,7,71342,71343,7,71350,71350,7,71453,71455,5,71462,71462,7,71724,71726,7,71736,71736,7,71984,71984,5,71991,71992,7,71997,71997,7,71999,71999,1,72001,72001,1,72003,72003,5,72148,72151,5,72156,72159,7,72164,72164,7,72243,72248,5,72250,72250,1,72263,72263,5,72279,72280,7,72324,72329,1,72343,72343,7,72751,72751,7,72760,72765,5,72767,72767,5,72873,72873,7,72881,72881,7,72884,72884,7,73009,73014,5,73020,73021,5,73030,73030,1,73098,73102,7,73107,73108,7,73110,73110,7,73459,73460,5,78896,78904,4,92976,92982,5,94033,94087,7,94180,94180,5,113821,113822,5,118528,118573,5,119141,119141,5,119143,119145,5,119150,119154,5,119163,119170,5,119210,119213,5,121344,121398,5,121461,121461,5,121499,121503,5,122880,122886,5,122907,122913,5,122918,122922,5,123566,123566,5,125136,125142,5,126976,126979,14,126981,127182,14,127184,127231,14,127279,127279,14,127344,127345,14,127374,127374,14,127405,127461,14,127489,127490,14,127514,127514,14,127538,127546,14,127561,127567,14,127570,127743,14,127757,127758,14,127760,127760,14,127762,127762,14,127766,127768,14,127770,127770,14,127772,127772,14,127775,127776,14,127778,127779,14,127789,127791,14,127794,127795,14,127798,127798,14,127819,127819,14,127824,127824,14,127868,127868,14,127870,127871,14,127892,127893,14,127896,127896,14,127900,127901,14,127904,127940,14,127942,127942,14,127944,127944,14,127946,127946,14,127951,127955,14,127968,127971,14,127973,127984,14,127987,127987,14,127989,127989,14,127991,127991,14,127995,127999,5,128008,128008,14,128012,128014,14,128017,128018,14,128020,128020,14,128022,128022,14,128042,128042,14,128063,128063,14,128065,128065,14,128101,128101,14,128108,128109,14,128173,128173,14,128182,128183,14,128236,128237,14,128239,128239,14,128245,128245,14,128248,128248,14,128253,128253,14,128255,128258,14,128260,128263,14,128265,128265,14,128277,128277,14,128300,128301,14,128326,128328,14,128331,128334,14,128336,128347,14,128360,128366,14,128369,128370,14,128378,128378,14,128391,128391,14,128394,128397,14,128400,128400,14,128405,128406,14,128420,128420,14,128422,128423,14,128425,128432,14,128435,128443,14,128445,128449,14,128453,128464,14,128468,128475,14,128479,128480,14,128482,128482,14,128484,128487,14,128489,128494,14,128496,128498,14,128500,128505,14,128507,128511,14,128513,128518,14,128521,128525,14,128527,128527,14,128529,128529,14,128533,128533,14,128535,128535,14,128537,128537,14]")}var CodePoint;(function(e){e[e.zwj=8205]="zwj",e[e.emojiVariantSelector=65039]="emojiVariantSelector",e[e.enclosingKeyCap=8419]="enclosingKeyCap",e[e.space=32]="space"})(CodePoint||(CodePoint={}));var $9g=class se{static{this.c=new $3f(()=>JSON.parse('{"_common":[8232,32,8233,32,5760,32,8192,32,8193,32,8194,32,8195,32,8196,32,8197,32,8198,32,8200,32,8201,32,8202,32,8287,32,8199,32,8239,32,2042,95,65101,95,65102,95,65103,95,8208,45,8209,45,8210,45,65112,45,1748,45,8259,45,727,45,8722,45,10134,45,11450,45,1549,44,1643,44,184,44,42233,44,894,59,2307,58,2691,58,1417,58,1795,58,1796,58,5868,58,65072,58,6147,58,6153,58,8282,58,1475,58,760,58,42889,58,8758,58,720,58,42237,58,451,33,11601,33,660,63,577,63,2429,63,5038,63,42731,63,119149,46,8228,46,1793,46,1794,46,42510,46,68176,46,1632,46,1776,46,42232,46,1373,96,65287,96,8219,96,1523,96,8242,96,1370,96,8175,96,65344,96,900,96,8189,96,8125,96,8127,96,8190,96,697,96,884,96,712,96,714,96,715,96,756,96,699,96,701,96,700,96,702,96,42892,96,1497,96,2036,96,2037,96,5194,96,5836,96,94033,96,94034,96,65339,91,10088,40,10098,40,12308,40,64830,40,65341,93,10089,41,10099,41,12309,41,64831,41,10100,123,119060,123,10101,125,65342,94,8270,42,1645,42,8727,42,66335,42,5941,47,8257,47,8725,47,8260,47,9585,47,10187,47,10744,47,119354,47,12755,47,12339,47,11462,47,20031,47,12035,47,65340,92,65128,92,8726,92,10189,92,10741,92,10745,92,119311,92,119355,92,12756,92,20022,92,12034,92,42872,38,708,94,710,94,5869,43,10133,43,66203,43,8249,60,10094,60,706,60,119350,60,5176,60,5810,60,5120,61,11840,61,12448,61,42239,61,8250,62,10095,62,707,62,119351,62,5171,62,94015,62,8275,126,732,126,8128,126,8764,126,65372,124,65293,45,118002,50,120784,50,120794,50,120804,50,120814,50,120824,50,130034,50,42842,50,423,50,1000,50,42564,50,5311,50,42735,50,119302,51,118003,51,120785,51,120795,51,120805,51,120815,51,120825,51,130035,51,42923,51,540,51,439,51,42858,51,11468,51,1248,51,94011,51,71882,51,118004,52,120786,52,120796,52,120806,52,120816,52,120826,52,130036,52,5070,52,71855,52,118005,53,120787,53,120797,53,120807,53,120817,53,120827,53,130037,53,444,53,71867,53,118006,54,120788,54,120798,54,120808,54,120818,54,120828,54,130038,54,11474,54,5102,54,71893,54,119314,55,118007,55,120789,55,120799,55,120809,55,120819,55,120829,55,130039,55,66770,55,71878,55,2819,56,2538,56,2666,56,125131,56,118008,56,120790,56,120800,56,120810,56,120820,56,120830,56,130040,56,547,56,546,56,66330,56,2663,57,2920,57,2541,57,3437,57,118009,57,120791,57,120801,57,120811,57,120821,57,120831,57,130041,57,42862,57,11466,57,71884,57,71852,57,71894,57,9082,97,65345,97,119834,97,119886,97,119938,97,119990,97,120042,97,120094,97,120146,97,120198,97,120250,97,120302,97,120354,97,120406,97,120458,97,593,97,945,97,120514,97,120572,97,120630,97,120688,97,120746,97,65313,65,117974,65,119808,65,119860,65,119912,65,119964,65,120016,65,120068,65,120120,65,120172,65,120224,65,120276,65,120328,65,120380,65,120432,65,913,65,120488,65,120546,65,120604,65,120662,65,120720,65,5034,65,5573,65,42222,65,94016,65,66208,65,119835,98,119887,98,119939,98,119991,98,120043,98,120095,98,120147,98,120199,98,120251,98,120303,98,120355,98,120407,98,120459,98,388,98,5071,98,5234,98,5551,98,65314,66,8492,66,117975,66,119809,66,119861,66,119913,66,120017,66,120069,66,120121,66,120173,66,120225,66,120277,66,120329,66,120381,66,120433,66,42932,66,914,66,120489,66,120547,66,120605,66,120663,66,120721,66,5108,66,5623,66,42192,66,66178,66,66209,66,66305,66,65347,99,8573,99,119836,99,119888,99,119940,99,119992,99,120044,99,120096,99,120148,99,120200,99,120252,99,120304,99,120356,99,120408,99,120460,99,7428,99,1010,99,11429,99,43951,99,66621,99,128844,67,71913,67,71922,67,65315,67,8557,67,8450,67,8493,67,117976,67,119810,67,119862,67,119914,67,119966,67,120018,67,120174,67,120226,67,120278,67,120330,67,120382,67,120434,67,1017,67,11428,67,5087,67,42202,67,66210,67,66306,67,66581,67,66844,67,8574,100,8518,100,119837,100,119889,100,119941,100,119993,100,120045,100,120097,100,120149,100,120201,100,120253,100,120305,100,120357,100,120409,100,120461,100,1281,100,5095,100,5231,100,42194,100,8558,68,8517,68,117977,68,119811,68,119863,68,119915,68,119967,68,120019,68,120071,68,120123,68,120175,68,120227,68,120279,68,120331,68,120383,68,120435,68,5024,68,5598,68,5610,68,42195,68,8494,101,65349,101,8495,101,8519,101,119838,101,119890,101,119942,101,120046,101,120098,101,120150,101,120202,101,120254,101,120306,101,120358,101,120410,101,120462,101,43826,101,1213,101,8959,69,65317,69,8496,69,117978,69,119812,69,119864,69,119916,69,120020,69,120072,69,120124,69,120176,69,120228,69,120280,69,120332,69,120384,69,120436,69,917,69,120492,69,120550,69,120608,69,120666,69,120724,69,11577,69,5036,69,42224,69,71846,69,71854,69,66182,69,119839,102,119891,102,119943,102,119995,102,120047,102,120099,102,120151,102,120203,102,120255,102,120307,102,120359,102,120411,102,120463,102,43829,102,42905,102,383,102,7837,102,1412,102,119315,70,8497,70,117979,70,119813,70,119865,70,119917,70,120021,70,120073,70,120125,70,120177,70,120229,70,120281,70,120333,70,120385,70,120437,70,42904,70,988,70,120778,70,5556,70,42205,70,71874,70,71842,70,66183,70,66213,70,66853,70,65351,103,8458,103,119840,103,119892,103,119944,103,120048,103,120100,103,120152,103,120204,103,120256,103,120308,103,120360,103,120412,103,120464,103,609,103,7555,103,397,103,1409,103,117980,71,119814,71,119866,71,119918,71,119970,71,120022,71,120074,71,120126,71,120178,71,120230,71,120282,71,120334,71,120386,71,120438,71,1292,71,5056,71,5107,71,42198,71,65352,104,8462,104,119841,104,119945,104,119997,104,120049,104,120101,104,120153,104,120205,104,120257,104,120309,104,120361,104,120413,104,120465,104,1211,104,1392,104,5058,104,65320,72,8459,72,8460,72,8461,72,117981,72,119815,72,119867,72,119919,72,120023,72,120179,72,120231,72,120283,72,120335,72,120387,72,120439,72,919,72,120494,72,120552,72,120610,72,120668,72,120726,72,11406,72,5051,72,5500,72,42215,72,66255,72,731,105,9075,105,65353,105,8560,105,8505,105,8520,105,119842,105,119894,105,119946,105,119998,105,120050,105,120102,105,120154,105,120206,105,120258,105,120310,105,120362,105,120414,105,120466,105,120484,105,618,105,617,105,953,105,8126,105,890,105,120522,105,120580,105,120638,105,120696,105,120754,105,1110,105,42567,105,1231,105,43893,105,5029,105,71875,105,65354,106,8521,106,119843,106,119895,106,119947,106,119999,106,120051,106,120103,106,120155,106,120207,106,120259,106,120311,106,120363,106,120415,106,120467,106,1011,106,1112,106,65322,74,117983,74,119817,74,119869,74,119921,74,119973,74,120025,74,120077,74,120129,74,120181,74,120233,74,120285,74,120337,74,120389,74,120441,74,42930,74,895,74,1032,74,5035,74,5261,74,42201,74,119844,107,119896,107,119948,107,120000,107,120052,107,120104,107,120156,107,120208,107,120260,107,120312,107,120364,107,120416,107,120468,107,8490,75,65323,75,117984,75,119818,75,119870,75,119922,75,119974,75,120026,75,120078,75,120130,75,120182,75,120234,75,120286,75,120338,75,120390,75,120442,75,922,75,120497,75,120555,75,120613,75,120671,75,120729,75,11412,75,5094,75,5845,75,42199,75,66840,75,1472,108,8739,73,9213,73,65512,73,1633,108,1777,73,66336,108,125127,108,118001,108,120783,73,120793,73,120803,73,120813,73,120823,73,130033,73,65321,73,8544,73,8464,73,8465,73,117982,108,119816,73,119868,73,119920,73,120024,73,120128,73,120180,73,120232,73,120284,73,120336,73,120388,73,120440,73,65356,108,8572,73,8467,108,119845,108,119897,108,119949,108,120001,108,120053,108,120105,73,120157,73,120209,73,120261,73,120313,73,120365,73,120417,73,120469,73,448,73,120496,73,120554,73,120612,73,120670,73,120728,73,11410,73,1030,73,1216,73,1493,108,1503,108,1575,108,126464,108,126592,108,65166,108,65165,108,1994,108,11599,73,5825,73,42226,73,93992,73,66186,124,66313,124,119338,76,8556,76,8466,76,117985,76,119819,76,119871,76,119923,76,120027,76,120079,76,120131,76,120183,76,120235,76,120287,76,120339,76,120391,76,120443,76,11472,76,5086,76,5290,76,42209,76,93974,76,71843,76,71858,76,66587,76,66854,76,65325,77,8559,77,8499,77,117986,77,119820,77,119872,77,119924,77,120028,77,120080,77,120132,77,120184,77,120236,77,120288,77,120340,77,120392,77,120444,77,924,77,120499,77,120557,77,120615,77,120673,77,120731,77,1018,77,11416,77,5047,77,5616,77,5846,77,42207,77,66224,77,66321,77,119847,110,119899,110,119951,110,120003,110,120055,110,120107,110,120159,110,120211,110,120263,110,120315,110,120367,110,120419,110,120471,110,1400,110,1404,110,65326,78,8469,78,117987,78,119821,78,119873,78,119925,78,119977,78,120029,78,120081,78,120185,78,120237,78,120289,78,120341,78,120393,78,120445,78,925,78,120500,78,120558,78,120616,78,120674,78,120732,78,11418,78,42208,78,66835,78,3074,111,3202,111,3330,111,3458,111,2406,111,2662,111,2790,111,3046,111,3174,111,3302,111,3430,111,3664,111,3792,111,4160,111,1637,111,1781,111,65359,111,8500,111,119848,111,119900,111,119952,111,120056,111,120108,111,120160,111,120212,111,120264,111,120316,111,120368,111,120420,111,120472,111,7439,111,7441,111,43837,111,959,111,120528,111,120586,111,120644,111,120702,111,120760,111,963,111,120532,111,120590,111,120648,111,120706,111,120764,111,11423,111,4351,111,1413,111,1505,111,1607,111,126500,111,126564,111,126596,111,65259,111,65260,111,65258,111,65257,111,1726,111,64428,111,64429,111,64427,111,64426,111,1729,111,64424,111,64425,111,64423,111,64422,111,1749,111,3360,111,4125,111,66794,111,71880,111,71895,111,66604,111,1984,79,2534,79,2918,79,12295,79,70864,79,71904,79,118000,79,120782,79,120792,79,120802,79,120812,79,120822,79,130032,79,65327,79,117988,79,119822,79,119874,79,119926,79,119978,79,120030,79,120082,79,120134,79,120186,79,120238,79,120290,79,120342,79,120394,79,120446,79,927,79,120502,79,120560,79,120618,79,120676,79,120734,79,11422,79,1365,79,11604,79,4816,79,2848,79,66754,79,42227,79,71861,79,66194,79,66219,79,66564,79,66838,79,9076,112,65360,112,119849,112,119901,112,119953,112,120005,112,120057,112,120109,112,120161,112,120213,112,120265,112,120317,112,120369,112,120421,112,120473,112,961,112,120530,112,120544,112,120588,112,120602,112,120646,112,120660,112,120704,112,120718,112,120762,112,120776,112,11427,112,65328,80,8473,80,117989,80,119823,80,119875,80,119927,80,119979,80,120031,80,120083,80,120187,80,120239,80,120291,80,120343,80,120395,80,120447,80,929,80,120504,80,120562,80,120620,80,120678,80,120736,80,11426,80,5090,80,5229,80,42193,80,66197,80,119850,113,119902,113,119954,113,120006,113,120058,113,120110,113,120162,113,120214,113,120266,113,120318,113,120370,113,120422,113,120474,113,1307,113,1379,113,1382,113,8474,81,117990,81,119824,81,119876,81,119928,81,119980,81,120032,81,120084,81,120188,81,120240,81,120292,81,120344,81,120396,81,120448,81,11605,81,119851,114,119903,114,119955,114,120007,114,120059,114,120111,114,120163,114,120215,114,120267,114,120319,114,120371,114,120423,114,120475,114,43847,114,43848,114,7462,114,11397,114,43905,114,119318,82,8475,82,8476,82,8477,82,117991,82,119825,82,119877,82,119929,82,120033,82,120189,82,120241,82,120293,82,120345,82,120397,82,120449,82,422,82,5025,82,5074,82,66740,82,5511,82,42211,82,94005,82,65363,115,119852,115,119904,115,119956,115,120008,115,120060,115,120112,115,120164,115,120216,115,120268,115,120320,115,120372,115,120424,115,120476,115,42801,115,445,115,1109,115,43946,115,71873,115,66632,115,65331,83,117992,83,119826,83,119878,83,119930,83,119982,83,120034,83,120086,83,120138,83,120190,83,120242,83,120294,83,120346,83,120398,83,120450,83,1029,83,1359,83,5077,83,5082,83,42210,83,94010,83,66198,83,66592,83,119853,116,119905,116,119957,116,120009,116,120061,116,120113,116,120165,116,120217,116,120269,116,120321,116,120373,116,120425,116,120477,116,8868,84,10201,84,128872,84,65332,84,117993,84,119827,84,119879,84,119931,84,119983,84,120035,84,120087,84,120139,84,120191,84,120243,84,120295,84,120347,84,120399,84,120451,84,932,84,120507,84,120565,84,120623,84,120681,84,120739,84,11430,84,5026,84,42196,84,93962,84,71868,84,66199,84,66225,84,66325,84,119854,117,119906,117,119958,117,120010,117,120062,117,120114,117,120166,117,120218,117,120270,117,120322,117,120374,117,120426,117,120478,117,42911,117,7452,117,43854,117,43858,117,651,117,965,117,120534,117,120592,117,120650,117,120708,117,120766,117,1405,117,66806,117,71896,117,8746,85,8899,85,117994,85,119828,85,119880,85,119932,85,119984,85,120036,85,120088,85,120140,85,120192,85,120244,85,120296,85,120348,85,120400,85,120452,85,1357,85,4608,85,66766,85,5196,85,42228,85,94018,85,71864,85,8744,118,8897,118,65366,118,8564,118,119855,118,119907,118,119959,118,120011,118,120063,118,120115,118,120167,118,120219,118,120271,118,120323,118,120375,118,120427,118,120479,118,7456,118,957,118,120526,118,120584,118,120642,118,120700,118,120758,118,1141,118,1496,118,71430,118,43945,118,71872,118,119309,86,1639,86,1783,86,8548,86,117995,86,119829,86,119881,86,119933,86,119985,86,120037,86,120089,86,120141,86,120193,86,120245,86,120297,86,120349,86,120401,86,120453,86,1140,86,11576,86,5081,86,5167,86,42719,86,42214,86,93960,86,71840,86,66845,86,623,119,119856,119,119908,119,119960,119,120012,119,120064,119,120116,119,120168,119,120220,119,120272,119,120324,119,120376,119,120428,119,120480,119,7457,119,1121,119,1309,119,1377,119,71434,119,71438,119,71439,119,43907,119,71910,87,71919,87,117996,87,119830,87,119882,87,119934,87,119986,87,120038,87,120090,87,120142,87,120194,87,120246,87,120298,87,120350,87,120402,87,120454,87,1308,87,5043,87,5076,87,42218,87,5742,120,10539,120,10540,120,10799,120,65368,120,8569,120,119857,120,119909,120,119961,120,120013,120,120065,120,120117,120,120169,120,120221,120,120273,120,120325,120,120377,120,120429,120,120481,120,5441,120,5501,120,5741,88,9587,88,66338,88,71916,88,65336,88,8553,88,117997,88,119831,88,119883,88,119935,88,119987,88,120039,88,120091,88,120143,88,120195,88,120247,88,120299,88,120351,88,120403,88,120455,88,42931,88,935,88,120510,88,120568,88,120626,88,120684,88,120742,88,11436,88,11613,88,5815,88,42219,88,66192,88,66228,88,66327,88,66855,88,611,121,7564,121,65369,121,119858,121,119910,121,119962,121,120014,121,120066,121,120118,121,120170,121,120222,121,120274,121,120326,121,120378,121,120430,121,120482,121,655,121,7935,121,43866,121,947,121,8509,121,120516,121,120574,121,120632,121,120690,121,120748,121,1199,121,4327,121,71900,121,65337,89,117998,89,119832,89,119884,89,119936,89,119988,89,120040,89,120092,89,120144,89,120196,89,120248,89,120300,89,120352,89,120404,89,120456,89,933,89,978,89,120508,89,120566,89,120624,89,120682,89,120740,89,11432,89,1198,89,5033,89,5053,89,42220,89,94019,89,71844,89,66226,89,119859,122,119911,122,119963,122,120015,122,120067,122,120119,122,120171,122,120223,122,120275,122,120327,122,120379,122,120431,122,120483,122,7458,122,43923,122,71876,122,71909,90,66293,90,65338,90,8484,90,8488,90,117999,90,119833,90,119885,90,119937,90,119989,90,120041,90,120197,90,120249,90,120301,90,120353,90,120405,90,120457,90,918,90,120493,90,120551,90,120609,90,120667,90,120725,90,5059,90,42204,90,71849,90,65282,34,65283,35,65284,36,65285,37,65286,38,65290,42,65291,43,65294,46,65295,47,65296,48,65298,50,65299,51,65300,52,65301,53,65302,54,65303,55,65304,56,65305,57,65308,60,65309,61,65310,62,65312,64,65316,68,65318,70,65319,71,65324,76,65329,81,65330,82,65333,85,65334,86,65335,87,65343,95,65346,98,65348,100,65350,102,65355,107,65357,109,65358,110,65361,113,65362,114,65364,116,65365,117,65367,119,65370,122,65371,123,65373,125,119846,109],"_default":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8217,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"cs":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"de":[65374,126,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"es":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"fr":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"it":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ja":[8211,45,8218,44,65281,33,8216,96,8245,96,180,96,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65292,44,65297,49,65307,59],"ko":[8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pl":[65374,126,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"pt-BR":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"qps-ploc":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"ru":[65374,126,8218,44,65306,58,65281,33,8216,96,8245,96,180,96,12494,47,305,105,921,73,1009,112,215,120,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"tr":[160,32,8211,45,65374,126,8218,44,65306,58,65281,33,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65288,40,65289,41,65292,44,65297,49,65307,59,65311,63],"zh-hans":[160,32,65374,126,8218,44,8245,96,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89,65297,49],"zh-hant":[8211,45,65374,126,8218,44,180,96,12494,47,1047,51,1073,54,1072,97,1040,65,1068,98,1042,66,1089,99,1057,67,1077,101,1045,69,1053,72,305,105,1050,75,921,73,1052,77,1086,111,1054,79,1009,112,1088,112,1056,80,1075,114,1058,84,215,120,1093,120,1061,88,1091,121,1059,89]}'))}static{this.d=new $1f({getCacheKey:JSON.stringify},t=>{function n(h){const c=new Map;for(let f=0;f<h.length;f+=2)c.set(h[f],h[f+1]);return c}function r(h,c){const f=new Map(h);for(const[g,m]of c)f.set(g,m);return f}function s(h,c){if(!h)return c;const f=new Map;for(const[g,m]of h)c.has(g)&&f.set(g,m);return f}const i=this.c.value;let a=t.filter(h=>!h.startsWith("_")&&h in i);a.length===0&&(a=["_default"]);let l;for(const h of a){const c=n(i[h]);l=s(l,c)}const u=n(i._common),o=r(u,l);return new se(o)})}static getInstance(t){return se.d.get(Array.from(t))}static{this.e=new $3f(()=>Object.keys(se.c.value).filter(t=>!t.startsWith("_")))}static getLocales(){return se.e.value}constructor(t){this.f=t}isAmbiguous(t){return this.f.has(t)}containsAmbiguousCharacter(t){for(let n=0;n<t.length;n++){const r=t.codePointAt(n);if(typeof r=="number"&&this.isAmbiguous(r))return!0}return!1}getPrimaryConfusable(t){return this.f.get(t)}getConfusableCodePoints(){return new Set(this.f.keys())}},$0g=class ie{static c(){return JSON.parse('{"_common":[11,12,13,127,847,1564,4447,4448,6068,6069,6155,6156,6157,6158,7355,7356,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8204,8205,8206,8207,8234,8235,8236,8237,8238,8239,8287,8288,8289,8290,8291,8292,8293,8294,8295,8296,8297,8298,8299,8300,8301,8302,8303,10240,12644,65024,65025,65026,65027,65028,65029,65030,65031,65032,65033,65034,65035,65036,65037,65038,65039,65279,65440,65520,65521,65522,65523,65524,65525,65526,65527,65528,65532,78844,119155,119156,119157,119158,119159,119160,119161,119162,917504,917505,917506,917507,917508,917509,917510,917511,917512,917513,917514,917515,917516,917517,917518,917519,917520,917521,917522,917523,917524,917525,917526,917527,917528,917529,917530,917531,917532,917533,917534,917535,917536,917537,917538,917539,917540,917541,917542,917543,917544,917545,917546,917547,917548,917549,917550,917551,917552,917553,917554,917555,917556,917557,917558,917559,917560,917561,917562,917563,917564,917565,917566,917567,917568,917569,917570,917571,917572,917573,917574,917575,917576,917577,917578,917579,917580,917581,917582,917583,917584,917585,917586,917587,917588,917589,917590,917591,917592,917593,917594,917595,917596,917597,917598,917599,917600,917601,917602,917603,917604,917605,917606,917607,917608,917609,917610,917611,917612,917613,917614,917615,917616,917617,917618,917619,917620,917621,917622,917623,917624,917625,917626,917627,917628,917629,917630,917631,917760,917761,917762,917763,917764,917765,917766,917767,917768,917769,917770,917771,917772,917773,917774,917775,917776,917777,917778,917779,917780,917781,917782,917783,917784,917785,917786,917787,917788,917789,917790,917791,917792,917793,917794,917795,917796,917797,917798,917799,917800,917801,917802,917803,917804,917805,917806,917807,917808,917809,917810,917811,917812,917813,917814,917815,917816,917817,917818,917819,917820,917821,917822,917823,917824,917825,917826,917827,917828,917829,917830,917831,917832,917833,917834,917835,917836,917837,917838,917839,917840,917841,917842,917843,917844,917845,917846,917847,917848,917849,917850,917851,917852,917853,917854,917855,917856,917857,917858,917859,917860,917861,917862,917863,917864,917865,917866,917867,917868,917869,917870,917871,917872,917873,917874,917875,917876,917877,917878,917879,917880,917881,917882,917883,917884,917885,917886,917887,917888,917889,917890,917891,917892,917893,917894,917895,917896,917897,917898,917899,917900,917901,917902,917903,917904,917905,917906,917907,917908,917909,917910,917911,917912,917913,917914,917915,917916,917917,917918,917919,917920,917921,917922,917923,917924,917925,917926,917927,917928,917929,917930,917931,917932,917933,917934,917935,917936,917937,917938,917939,917940,917941,917942,917943,917944,917945,917946,917947,917948,917949,917950,917951,917952,917953,917954,917955,917956,917957,917958,917959,917960,917961,917962,917963,917964,917965,917966,917967,917968,917969,917970,917971,917972,917973,917974,917975,917976,917977,917978,917979,917980,917981,917982,917983,917984,917985,917986,917987,917988,917989,917990,917991,917992,917993,917994,917995,917996,917997,917998,917999],"cs":[173,8203,12288],"de":[173,8203,12288],"es":[8203,12288],"fr":[173,8203,12288],"it":[160,173,12288],"ja":[173],"ko":[173,12288],"pl":[173,8203,12288],"pt-BR":[173,8203,12288],"qps-ploc":[160,173,8203,12288],"ru":[173,12288],"tr":[160,173,8203,12288],"zh-hans":[160,173,8203,12288],"zh-hant":[173,12288]}')}static{this.d=void 0}static e(){return this.d||(this.d=new Set([...Object.values(ie.c())].flat())),this.d}static isInvisibleCharacter(t){return ie.e().has(t)}static containsInvisibleCharacter(t){for(let n=0;n<t.length;n++){const r=t.codePointAt(n);if(typeof r=="number"&&(ie.isInvisibleCharacter(r)||r===32))return!0}return!1}static get codePoints(){return ie.e()}},DEFAULT_CHANNEL="default",INITIALIZE="$initialize",MessageType;(function(e){e[e.Request=0]="Request",e[e.Reply=1]="Reply",e[e.SubscribeEvent=2]="SubscribeEvent",e[e.Event=3]="Event",e[e.UnsubscribeEvent=4]="UnsubscribeEvent"})(MessageType||(MessageType={}));var RequestMessage=class{constructor(e,t,n,r,s){this.vsWorker=e,this.req=t,this.channel=n,this.method=r,this.args=s,this.type=0}},ReplyMessage=class{constructor(e,t,n,r){this.vsWorker=e,this.seq=t,this.res=n,this.err=r,this.type=1}},SubscribeEventMessage=class{constructor(e,t,n,r,s){this.vsWorker=e,this.req=t,this.channel=n,this.eventName=r,this.arg=s,this.type=2}},EventMessage=class{constructor(e,t,n){this.vsWorker=e,this.req=t,this.event=n,this.type=3}},UnsubscribeEventMessage=class{constructor(e,t){this.vsWorker=e,this.req=t,this.type=4}},WebWorkerProtocol=class{constructor(e){this.a=-1,this.g=e,this.b=0,this.c=Object.create(null),this.d=new Map,this.f=new Map}setWorkerId(e){this.a=e}sendMessage(e,t,n){const r=String(++this.b);return new Promise((s,i)=>{this.c[r]={resolve:s,reject:i},this.o(new RequestMessage(this.a,r,e,t,n))})}listen(e,t,n){let r=null;const s=new $0e({onWillAddFirstListener:()=>{r=String(++this.b),this.d.set(r,s),this.o(new SubscribeEventMessage(this.a,r,e,t,n))},onDidRemoveLastListener:()=>{this.d.delete(r),this.o(new UnsubscribeEventMessage(this.a,r)),r=null}});return s.event}handleMessage(e){!e||!e.vsWorker||this.a!==-1&&e.vsWorker!==this.a||this.h(e)}createProxyToRemoteChannel(e,t){const n={get:(r,s)=>(typeof s=="string"&&!r[s]&&(propertyIsDynamicEvent(s)?r[s]=i=>this.listen(e,s,i):propertyIsEvent(s)?r[s]=this.listen(e,s,void 0):s.charCodeAt(0)===36&&(r[s]=async(...i)=>(await t?.(),this.sendMessage(e,s,i)))),r[s])};return new Proxy(Object.create(null),n)}h(e){switch(e.type){case 1:return this.j(e);case 0:return this.k(e);case 2:return this.l(e);case 3:return this.m(e);case 4:return this.n(e)}}j(e){if(!this.c[e.seq]){console.warn("Got reply to unknown seq");return}const t=this.c[e.seq];if(delete this.c[e.seq],e.err){let n=e.err;e.err.$isError&&(n=new Error,n.name=e.err.name,n.message=e.err.message,n.stack=e.err.stack),t.reject(n);return}t.resolve(e.res)}k(e){const t=e.req;this.g.handleMessage(e.channel,e.method,e.args).then(r=>{this.o(new ReplyMessage(this.a,t,r,void 0))},r=>{r.detail instanceof Error&&(r.detail=$ib(r.detail)),this.o(new ReplyMessage(this.a,t,void 0,$ib(r)))})}l(e){const t=e.req,n=this.g.handleEvent(e.channel,e.eventName,e.arg)(r=>{this.o(new EventMessage(this.a,t,r))});this.f.set(t,n)}m(e){if(!this.d.has(e.req)){console.warn("Got event for unknown req");return}this.d.get(e.req).fire(e.event)}n(e){if(!this.f.has(e.req)){console.warn("Got unsubscribe for unknown req");return}this.f.get(e.req).dispose(),this.f.delete(e.req)}o(e){const t=[];if(e.type===0)for(let n=0;n<e.args.length;n++)e.args[n]instanceof ArrayBuffer&&t.push(e.args[n]);else e.type===1&&e.res instanceof ArrayBuffer&&t.push(e.res);this.g.sendMessage(e,t)}};function propertyIsEvent(e){return e[0]==="o"&&e[1]==="n"&&$xg(e.charCodeAt(2))}function propertyIsDynamicEvent(e){return/^onDynamic/.test(e)&&$xg(e.charCodeAt(9))}var $pjb=class{constructor(e,t){this.b=new Map,this.c=new Map,this.a=new WebWorkerProtocol({sendMessage:(n,r)=>{e(n,r)},handleMessage:(n,r,s)=>this.d(n,r,s),handleEvent:(n,r,s)=>this.f(n,r,s)}),this.requestHandler=t(this)}onmessage(e){this.a.handleMessage(e)}d(e,t,n){if(e===DEFAULT_CHANNEL&&t===INITIALIZE)return this.g(n[0]);const r=e===DEFAULT_CHANNEL?this.requestHandler:this.b.get(e);if(!r)return Promise.reject(new Error(`Missing channel ${e} on worker thread`));if(typeof r[t]!="function")return Promise.reject(new Error(`Missing method ${t} on worker thread channel ${e}`));try{return Promise.resolve(r[t].apply(r,n))}catch(s){return Promise.reject(s)}}f(e,t,n){const r=e===DEFAULT_CHANNEL?this.requestHandler:this.b.get(e);if(!r)throw new Error(`Missing channel ${e} on worker thread`);if(propertyIsDynamicEvent(t)){const s=r[t].call(r,n);if(typeof s!="function")throw new Error(`Missing dynamic event ${t} on request handler.`);return s}if(propertyIsEvent(t)){const s=r[t];if(typeof s!="function")throw new Error(`Missing event ${t} on request handler.`);return s}throw new Error(`Malformed event name ${t}`)}setChannel(e,t){this.b.set(e,t)}getChannel(e){if(!this.c.has(e)){const t=this.a.createProxyToRemoteChannel(e);this.c.set(e,t)}return this.c.get(e)}async g(e){this.a.setWorkerId(e)}},initialized=!1;function $Etb(e){if(initialized)throw new Error("WebWorker already initialized!");initialized=!0;const t=new $pjb(n=>globalThis.postMessage(n),n=>e(n));return globalThis.onmessage=n=>{t.onmessage(n.data)},t}function $Ftb(e){globalThis.onmessage=t=>{initialized||$Etb(e)}}var $gV=class{constructor(e,t,n,r){this.originalStart=e,this.originalLength=t,this.modifiedStart=n,this.modifiedLength=r}getOriginalEnd(){return this.originalStart+this.originalLength}getModifiedEnd(){return this.modifiedStart+this.modifiedLength}},indexOfTable=new $3f(()=>new Uint8Array(256));function $hj(e,t){return(t<<5)-t+e|0}function $ij(e,t){t=$hj(149417,t);for(let n=0,r=e.length;n<r;n++)t=$hj(e.charCodeAt(n),t);return t}var SHA1Constant;(function(e){e[e.BLOCK_SIZE=64]="BLOCK_SIZE",e[e.UNICODE_REPLACEMENT=65533]="UNICODE_REPLACEMENT"})(SHA1Constant||(SHA1Constant={}));function leftRotate(e,t,n=32){const r=n-t,s=~((1<<r)-1);return(e<<t|(s&e)>>>r)>>>0}function toHexString(e,t=32){return e instanceof ArrayBuffer?Array.from(new Uint8Array(e)).map(n=>n.toString(16).padStart(2,"0")).join(""):(e>>>0).toString(16).padStart(t/4,"0")}var $kj=class ye{static{this.g=new DataView(new ArrayBuffer(320))}constructor(){this.h=1732584193,this.l=4023233417,this.m=2562383102,this.n=271733878,this.o=3285377520,this.p=new Uint8Array(67),this.q=new DataView(this.p.buffer),this.r=0,this.t=0,this.u=0,this.v=!1}update(t){const n=t.length;if(n===0)return;const r=this.p;let s=this.r,i=this.u,a,l;for(i!==0?(a=i,l=-1,i=0):(a=t.charCodeAt(0),l=0);;){let u=a;if($Cg(a))if(l+1<n){const o=t.charCodeAt(l+1);$Dg(o)?(l++,u=$Eg(a,o)):u=65533}else{i=a;break}else $Dg(a)&&(u=65533);if(s=this.w(r,s,u),l++,l<n)a=t.charCodeAt(l);else break}this.r=s,this.u=i}w(t,n,r){return r<128?t[n++]=r:r<2048?(t[n++]=192|(r&1984)>>>6,t[n++]=128|(r&63)>>>0):r<65536?(t[n++]=224|(r&61440)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0):(t[n++]=240|(r&1835008)>>>18,t[n++]=128|(r&258048)>>>12,t[n++]=128|(r&4032)>>>6,t[n++]=128|(r&63)>>>0),n>=64&&(this.y(),n-=64,this.t+=64,t[0]=t[64],t[1]=t[65],t[2]=t[66]),n}digest(){return this.v||(this.v=!0,this.u&&(this.u=0,this.r=this.w(this.p,this.r,65533)),this.t+=this.r,this.x()),toHexString(this.h)+toHexString(this.l)+toHexString(this.m)+toHexString(this.n)+toHexString(this.o)}x(){this.p[this.r++]=128,this.p.subarray(this.r).fill(0),this.r>56&&(this.y(),this.p.fill(0));const t=8*this.t;this.q.setUint32(56,Math.floor(t/4294967296),!1),this.q.setUint32(60,t%4294967296,!1),this.y()}y(){const t=ye.g,n=this.q;for(let c=0;c<64;c+=4)t.setUint32(c,n.getUint32(c,!1),!1);for(let c=64;c<320;c+=4)t.setUint32(c,leftRotate(t.getUint32(c-12,!1)^t.getUint32(c-32,!1)^t.getUint32(c-56,!1)^t.getUint32(c-64,!1),1),!1);let r=this.h,s=this.l,i=this.m,a=this.n,l=this.o,u,o,h;for(let c=0;c<80;c++)c<20?(u=s&i|~s&a,o=1518500249):c<40?(u=s^i^a,o=1859775393):c<60?(u=s&i|s&a|i&a,o=2400959708):(u=s^i^a,o=3395469782),h=leftRotate(r,5)+u+l+o+t.getUint32(c*4,!1)&4294967295,l=a,a=i,i=leftRotate(s,30),s=r,r=h;this.h=this.h+r&4294967295,this.l=this.l+s&4294967295,this.m=this.m+i&4294967295,this.n=this.n+a&4294967295,this.o=this.o+l&4294967295}},$hV=class{constructor(e){this.a=e}getElements(){const e=this.a,t=new Int32Array(e.length);for(let n=0,r=e.length;n<r;n++)t[n]=e.charCodeAt(n);return t}};function $iV(e,t,n){return new $jV(new $hV(e),new $hV(t)).ComputeDiff(n).changes}var Debug=class{static Assert(e,t){if(!e)throw new Error(t)}},MyArray=class{static Copy(e,t,n,r,s){for(let i=0;i<s;i++)n[r+i]=e[t+i]}static Copy2(e,t,n,r,s){for(let i=0;i<s;i++)n[r+i]=e[t+i]}},LocalConstants;(function(e){e[e.MaxDifferencesHistory=1447]="MaxDifferencesHistory"})(LocalConstants||(LocalConstants={}));var DiffChangeHelper=class{constructor(){this.a=[],this.b=1073741824,this.c=1073741824,this.d=0,this.e=0}MarkNextChange(){(this.d>0||this.e>0)&&this.a.push(new $gV(this.b,this.d,this.c,this.e)),this.d=0,this.e=0,this.b=1073741824,this.c=1073741824}AddOriginalElement(e,t){this.b=Math.min(this.b,e),this.c=Math.min(this.c,t),this.d++}AddModifiedElement(e,t){this.b=Math.min(this.b,e),this.c=Math.min(this.c,t),this.e++}getChanges(){return(this.d>0||this.e>0)&&this.MarkNextChange(),this.a}getReverseChanges(){return(this.d>0||this.e>0)&&this.MarkNextChange(),this.a.reverse(),this.a}},$jV=class Z{constructor(t,n,r=null){this.a=r,this.b=t,this.c=n;const[s,i,a]=Z.p(t),[l,u,o]=Z.p(n);this.d=a&&o,this.e=s,this.f=i,this.g=l,this.h=u,this.m=[],this.n=[]}static o(t){return t.length>0&&typeof t[0]=="string"}static p(t){const n=t.getElements();if(Z.o(n)){const r=new Int32Array(n.length);for(let s=0,i=n.length;s<i;s++)r[s]=$ij(n[s],0);return[n,r,!0]}return n instanceof Int32Array?[[],n,!1]:[[],new Int32Array(n),!1]}q(t,n){return this.f[t]!==this.h[n]?!1:this.d?this.e[t]===this.g[n]:!0}r(t,n){if(!this.q(t,n))return!1;const r=Z.s(this.b,t),s=Z.s(this.c,n);return r===s}static s(t,n){return typeof t.getStrictElement=="function"?t.getStrictElement(n):null}u(t,n){return this.f[t]!==this.f[n]?!1:this.d?this.e[t]===this.e[n]:!0}v(t,n){return this.h[t]!==this.h[n]?!1:this.d?this.g[t]===this.g[n]:!0}ComputeDiff(t){return this.w(0,this.f.length-1,0,this.h.length-1,t)}w(t,n,r,s,i){const a=[!1];let l=this.x(t,n,r,s,a);return i&&(l=this.A(l)),{quitEarly:a[0],changes:l}}x(t,n,r,s,i){for(i[0]=!1;t<=n&&r<=s&&this.q(t,r);)t++,r++;for(;n>=t&&s>=r&&this.q(n,s);)n--,s--;if(t>n||r>s){let c;return r<=s?(Debug.Assert(t===n+1,"originalStart should only be one more than originalEnd"),c=[new $gV(t,0,r,s-r+1)]):t<=n?(Debug.Assert(r===s+1,"modifiedStart should only be one more than modifiedEnd"),c=[new $gV(t,n-t+1,r,0)]):(Debug.Assert(t===n+1,"originalStart should only be one more than originalEnd"),Debug.Assert(r===s+1,"modifiedStart should only be one more than modifiedEnd"),c=[]),c}const a=[0],l=[0],u=this.z(t,n,r,s,a,l,i),o=a[0],h=l[0];if(u!==null)return u;if(!i[0]){const c=this.x(t,o,r,h,i);let f=[];return i[0]?f=[new $gV(o+1,n-(o+1)+1,h+1,s-(h+1)+1)]:f=this.x(o+1,n,h+1,s,i),this.I(c,f)}return[new $gV(t,n-t+1,r,s-r+1)]}y(t,n,r,s,i,a,l,u,o,h,c,f,g,m,N,p,d,A){let v=null,L=null,b=new DiffChangeHelper,w=n,R=r,F=g[0]-p[0]-s,$=-1073741824,B=this.m.length-1;do{const T=F+t;T===w||T<R&&o[T-1]<o[T+1]?(c=o[T+1],m=c-F-s,c<$&&b.MarkNextChange(),$=c,b.AddModifiedElement(c+1,m),F=T+1-t):(c=o[T-1]+1,m=c-F-s,c<$&&b.MarkNextChange(),$=c-1,b.AddOriginalElement(c,m+1),F=T-1-t),B>=0&&(o=this.m[B],t=o[0],w=1,R=o.length-1)}while(--B>=-1);if(v=b.getReverseChanges(),A[0]){let T=g[0]+1,y=p[0]+1;if(v!==null&&v.length>0){const O=v[v.length-1];T=Math.max(T,O.getOriginalEnd()),y=Math.max(y,O.getModifiedEnd())}L=[new $gV(T,f-T+1,y,N-y+1)]}else{b=new DiffChangeHelper,w=a,R=l,F=g[0]-p[0]-u,$=1073741824,B=d?this.n.length-1:this.n.length-2;do{const T=F+i;T===w||T<R&&h[T-1]>=h[T+1]?(c=h[T+1]-1,m=c-F-u,c>$&&b.MarkNextChange(),$=c+1,b.AddOriginalElement(c+1,m+1),F=T+1-i):(c=h[T-1],m=c-F-u,c>$&&b.MarkNextChange(),$=c,b.AddModifiedElement(c+1,m+1),F=T-1-i),B>=0&&(h=this.n[B],i=h[0],w=1,R=h.length-1)}while(--B>=-1);L=b.getChanges()}return this.I(v,L)}z(t,n,r,s,i,a,l){let u=0,o=0,h=0,c=0,f=0,g=0;t--,r--,i[0]=0,a[0]=0,this.m=[],this.n=[];const m=n-t+(s-r),N=m+1,p=new Int32Array(N),d=new Int32Array(N),A=s-r,v=n-t,L=t-r,b=n-s,R=(v-A)%2===0;p[A]=t,d[v]=n,l[0]=!1;for(let F=1;F<=m/2+1;F++){let $=0,B=0;h=this.K(A-F,F,A,N),c=this.K(A+F,F,A,N);for(let y=h;y<=c;y+=2){y===h||y<c&&p[y-1]<p[y+1]?u=p[y+1]:u=p[y-1]+1,o=u-(y-A)-L;const O=u;for(;u<n&&o<s&&this.q(u+1,o+1);)u++,o++;if(p[y]=u,u+o>$+B&&($=u,B=o),!R&&Math.abs(y-v)<=F-1&&u>=d[y])return i[0]=u,a[0]=o,O<=d[y]&&F<=1448?this.y(A,h,c,L,v,f,g,b,p,d,u,n,i,o,s,a,R,l):null}const T=($-t+(B-r)-F)/2;if(this.a!==null&&!this.a($,T))return l[0]=!0,i[0]=$,a[0]=B,T>0&&F<=1448?this.y(A,h,c,L,v,f,g,b,p,d,u,n,i,o,s,a,R,l):(t++,r++,[new $gV(t,n-t+1,r,s-r+1)]);f=this.K(v-F,F,v,N),g=this.K(v+F,F,v,N);for(let y=f;y<=g;y+=2){y===f||y<g&&d[y-1]>=d[y+1]?u=d[y+1]-1:u=d[y-1],o=u-(y-v)-b;const O=u;for(;u>t&&o>r&&this.q(u,o);)u--,o--;if(d[y]=u,R&&Math.abs(y-A)<=F&&u<=p[y])return i[0]=u,a[0]=o,O>=p[y]&&F<=1448?this.y(A,h,c,L,v,f,g,b,p,d,u,n,i,o,s,a,R,l):null}if(F<=1447){let y=new Int32Array(c-h+2);y[0]=A-h+1,MyArray.Copy2(p,h,y,1,c-h+1),this.m.push(y),y=new Int32Array(g-f+2),y[0]=v-f+1,MyArray.Copy2(d,f,y,1,g-f+1),this.n.push(y)}}return this.y(A,h,c,L,v,f,g,b,p,d,u,n,i,o,s,a,R,l)}A(t){for(let n=0;n<t.length;n++){const r=t[n],s=n<t.length-1?t[n+1].originalStart:this.f.length,i=n<t.length-1?t[n+1].modifiedStart:this.h.length,a=r.originalLength>0,l=r.modifiedLength>0;for(;r.originalStart+r.originalLength<s&&r.modifiedStart+r.modifiedLength<i&&(!a||this.u(r.originalStart,r.originalStart+r.originalLength))&&(!l||this.v(r.modifiedStart,r.modifiedStart+r.modifiedLength));){const o=this.r(r.originalStart,r.modifiedStart);if(this.r(r.originalStart+r.originalLength,r.modifiedStart+r.modifiedLength)&&!o)break;r.originalStart++,r.modifiedStart++}const u=[null];if(n<t.length-1&&this.J(t[n],t[n+1],u)){t[n]=u[0],t.splice(n+1,1),n--;continue}}for(let n=t.length-1;n>=0;n--){const r=t[n];let s=0,i=0;if(n>0){const c=t[n-1];s=c.originalStart+c.originalLength,i=c.modifiedStart+c.modifiedLength}const a=r.originalLength>0,l=r.modifiedLength>0;let u=0,o=this.H(r.originalStart,r.originalLength,r.modifiedStart,r.modifiedLength);for(let c=1;;c++){const f=r.originalStart-c,g=r.modifiedStart-c;if(f<s||g<i||a&&!this.u(f,f+r.originalLength)||l&&!this.v(g,g+r.modifiedLength))break;const N=(f===s&&g===i?5:0)+this.H(f,r.originalLength,g,r.modifiedLength);N>o&&(o=N,u=c)}r.originalStart-=u,r.modifiedStart-=u;const h=[null];if(n>0&&this.J(t[n-1],t[n],h)){t[n-1]=h[0],t.splice(n,1),n++;continue}}if(this.d)for(let n=1,r=t.length;n<r;n++){const s=t[n-1],i=t[n],a=i.originalStart-s.originalStart-s.originalLength,l=s.originalStart,u=i.originalStart+i.originalLength,o=u-l,h=s.modifiedStart,c=i.modifiedStart+i.modifiedLength,f=c-h;if(a<5&&o<20&&f<20){const g=this.B(l,o,h,f,a);if(g){const[m,N]=g;(m!==s.originalStart+s.originalLength||N!==s.modifiedStart+s.modifiedLength)&&(s.originalLength=m-s.originalStart,s.modifiedLength=N-s.modifiedStart,i.originalStart=m+a,i.modifiedStart=N+a,i.originalLength=u-i.originalStart,i.modifiedLength=c-i.modifiedStart)}}}return t}B(t,n,r,s,i){if(n<i||s<i)return null;const a=t+n-i+1,l=r+s-i+1;let u=0,o=0,h=0;for(let c=t;c<a;c++)for(let f=r;f<l;f++){const g=this.C(c,f,i);g>0&&g>u&&(u=g,o=c,h=f)}return u>0?[o,h]:null}C(t,n,r){let s=0;for(let i=0;i<r;i++){if(!this.q(t+i,n+i))return 0;s+=this.e[t+i].length}return s}D(t){return t<=0||t>=this.f.length-1?!0:this.d&&/^\s*$/.test(this.e[t])}E(t,n){if(this.D(t)||this.D(t-1))return!0;if(n>0){const r=t+n;if(this.D(r-1)||this.D(r))return!0}return!1}F(t){return t<=0||t>=this.h.length-1?!0:this.d&&/^\s*$/.test(this.g[t])}G(t,n){if(this.F(t)||this.F(t-1))return!0;if(n>0){const r=t+n;if(this.F(r-1)||this.F(r))return!0}return!1}H(t,n,r,s){const i=this.E(t,n)?1:0,a=this.G(r,s)?1:0;return i+a}I(t,n){const r=[];if(t.length===0||n.length===0)return n.length>0?n:t;if(this.J(t[t.length-1],n[0],r)){const s=new Array(t.length+n.length-1);return MyArray.Copy(t,0,s,0,t.length-1),s[t.length-1]=r[0],MyArray.Copy(n,1,s,t.length,n.length-1),s}else{const s=new Array(t.length+n.length);return MyArray.Copy(t,0,s,0,t.length),MyArray.Copy(n,0,s,t.length,n.length),s}}J(t,n,r){if(Debug.Assert(t.originalStart<=n.originalStart,"Left change is not less than or equal to right change"),Debug.Assert(t.modifiedStart<=n.modifiedStart,"Left change is not less than or equal to right change"),t.originalStart+t.originalLength>=n.originalStart||t.modifiedStart+t.modifiedLength>=n.modifiedStart){const s=t.originalStart;let i=t.originalLength;const a=t.modifiedStart;let l=t.modifiedLength;return t.originalStart+t.originalLength>=n.originalStart&&(i=n.originalStart+n.originalLength-t.originalStart),t.modifiedStart+t.modifiedLength>=n.modifiedStart&&(l=n.modifiedStart+n.modifiedLength-t.modifiedStart),r[0]=new $gV(s,i,a,l),!0}else return r[0]=null,!1}K(t,n,r,s){if(t>=0&&t<s)return t;const i=r,a=s-r-1,l=n%2===0;if(t<0){const u=i%2===0;return l===u?0:1}else{const u=a%2===0;return l===u?s-1:s-2}}},precomputedEqualityArray=new Uint32Array(65536),$lV=class X{constructor(t,n){this.lineNumber=t,this.column=n}with(t=this.lineNumber,n=this.column){return t===this.lineNumber&&n===this.column?this:new X(t,n)}delta(t=0,n=0){return this.with(Math.max(1,this.lineNumber+t),Math.max(1,this.column+n))}equals(t){return X.equals(this,t)}static equals(t,n){return!t&&!n?!0:!!t&&!!n&&t.lineNumber===n.lineNumber&&t.column===n.column}isBefore(t){return X.isBefore(this,t)}static isBefore(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<n.column}isBeforeOrEqual(t){return X.isBeforeOrEqual(this,t)}static isBeforeOrEqual(t,n){return t.lineNumber<n.lineNumber?!0:n.lineNumber<t.lineNumber?!1:t.column<=n.column}static compare(t,n){const r=t.lineNumber|0,s=n.lineNumber|0;if(r===s){const i=t.column|0,a=n.column|0;return i-a}return r-s}clone(){return new X(this.lineNumber,this.column)}toString(){return"("+this.lineNumber+","+this.column+")"}static lift(t){return new X(t.lineNumber,t.column)}static isIPosition(t){return t&&typeof t.lineNumber=="number"&&typeof t.column=="number"}toJSON(){return{lineNumber:this.lineNumber,column:this.column}}},$mV=class S{constructor(t,n,r,s){t>r||t===r&&n>s?(this.startLineNumber=r,this.startColumn=s,this.endLineNumber=t,this.endColumn=n):(this.startLineNumber=t,this.startColumn=n,this.endLineNumber=r,this.endColumn=s)}isEmpty(){return S.isEmpty(this)}static isEmpty(t){return t.startLineNumber===t.endLineNumber&&t.startColumn===t.endColumn}containsPosition(t){return S.containsPosition(this,t)}static containsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<t.startColumn||n.lineNumber===t.endLineNumber&&n.column>t.endColumn)}static strictContainsPosition(t,n){return!(n.lineNumber<t.startLineNumber||n.lineNumber>t.endLineNumber||n.lineNumber===t.startLineNumber&&n.column<=t.startColumn||n.lineNumber===t.endLineNumber&&n.column>=t.endColumn)}containsRange(t){return S.containsRange(this,t)}static containsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>t.endColumn)}strictContainsRange(t){return S.strictContainsRange(this,t)}static strictContainsRange(t,n){return!(n.startLineNumber<t.startLineNumber||n.endLineNumber<t.startLineNumber||n.startLineNumber>t.endLineNumber||n.endLineNumber>t.endLineNumber||n.startLineNumber===t.startLineNumber&&n.startColumn<=t.startColumn||n.endLineNumber===t.endLineNumber&&n.endColumn>=t.endColumn)}plusRange(t){return S.plusRange(this,t)}static plusRange(t,n){let r,s,i,a;return n.startLineNumber<t.startLineNumber?(r=n.startLineNumber,s=n.startColumn):n.startLineNumber===t.startLineNumber?(r=n.startLineNumber,s=Math.min(n.startColumn,t.startColumn)):(r=t.startLineNumber,s=t.startColumn),n.endLineNumber>t.endLineNumber?(i=n.endLineNumber,a=n.endColumn):n.endLineNumber===t.endLineNumber?(i=n.endLineNumber,a=Math.max(n.endColumn,t.endColumn)):(i=t.endLineNumber,a=t.endColumn),new S(r,s,i,a)}intersectRanges(t){return S.intersectRanges(this,t)}static intersectRanges(t,n){let r=t.startLineNumber,s=t.startColumn,i=t.endLineNumber,a=t.endColumn;const l=n.startLineNumber,u=n.startColumn,o=n.endLineNumber,h=n.endColumn;return r<l?(r=l,s=u):r===l&&(s=Math.max(s,u)),i>o?(i=o,a=h):i===o&&(a=Math.min(a,h)),r>i||r===i&&s>a?null:new S(r,s,i,a)}equalsRange(t){return S.equalsRange(this,t)}static equalsRange(t,n){return!t&&!n?!0:!!t&&!!n&&t.startLineNumber===n.startLineNumber&&t.startColumn===n.startColumn&&t.endLineNumber===n.endLineNumber&&t.endColumn===n.endColumn}getEndPosition(){return S.getEndPosition(this)}static getEndPosition(t){return new $lV(t.endLineNumber,t.endColumn)}getStartPosition(){return S.getStartPosition(this)}static getStartPosition(t){return new $lV(t.startLineNumber,t.startColumn)}toString(){return"["+this.startLineNumber+","+this.startColumn+" -> "+this.endLineNumber+","+this.endColumn+"]"}setEndPosition(t,n){return new S(this.startLineNumber,this.startColumn,t,n)}setStartPosition(t,n){return new S(t,n,this.endLineNumber,this.endColumn)}collapseToStart(){return S.collapseToStart(this)}static collapseToStart(t){return new S(t.startLineNumber,t.startColumn,t.startLineNumber,t.startColumn)}collapseToEnd(){return S.collapseToEnd(this)}static collapseToEnd(t){return new S(t.endLineNumber,t.endColumn,t.endLineNumber,t.endColumn)}delta(t){return new S(this.startLineNumber+t,this.startColumn,this.endLineNumber+t,this.endColumn)}isSingleLine(){return this.startLineNumber===this.endLineNumber}static fromPositions(t,n=t){return new S(t.lineNumber,t.column,n.lineNumber,n.column)}static lift(t){return t?new S(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):null}static isIRange(t){return t&&typeof t.startLineNumber=="number"&&typeof t.startColumn=="number"&&typeof t.endLineNumber=="number"&&typeof t.endColumn=="number"}static areIntersectingOrTouching(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn)}static areIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber||t.endLineNumber===n.startLineNumber&&t.endColumn<=n.startColumn||n.endLineNumber<t.startLineNumber||n.endLineNumber===t.startLineNumber&&n.endColumn<=t.startColumn)}static areOnlyIntersecting(t,n){return!(t.endLineNumber<n.startLineNumber-1||t.endLineNumber===n.startLineNumber&&t.endColumn<n.startColumn-1||n.endLineNumber<t.startLineNumber-1||n.endLineNumber===t.startLineNumber&&n.endColumn<t.startColumn-1)}static compareRangesUsingStarts(t,n){if(t&&n){const i=t.startLineNumber|0,a=n.startLineNumber|0;if(i===a){const l=t.startColumn|0,u=n.startColumn|0;if(l===u){const o=t.endLineNumber|0,h=n.endLineNumber|0;if(o===h){const c=t.endColumn|0,f=n.endColumn|0;return c-f}return o-h}return l-u}return i-a}return(t?1:0)-(n?1:0)}static compareRangesUsingEnds(t,n){return t.endLineNumber===n.endLineNumber?t.endColumn===n.endColumn?t.startLineNumber===n.startLineNumber?t.startColumn-n.startColumn:t.startLineNumber-n.startLineNumber:t.endColumn-n.endColumn:t.endLineNumber-n.endLineNumber}static spansMultipleLines(t){return t.endLineNumber>t.startLineNumber}toJSON(){return this}},Constants;(function(e){e[e.MAX_SAFE_SMALL_INTEGER=1073741824]="MAX_SAFE_SMALL_INTEGER",e[e.MIN_SAFE_SMALL_INTEGER=-1073741824]="MIN_SAFE_SMALL_INTEGER",e[e.MAX_UINT_8=255]="MAX_UINT_8",e[e.MAX_UINT_16=65535]="MAX_UINT_16",e[e.MAX_UINT_32=4294967295]="MAX_UINT_32",e[e.UNICODE_SUPPLEMENTARY_PLANE_BEGIN=65536]="UNICODE_SUPPLEMENTARY_PLANE_BEGIN"})(Constants||(Constants={}));function $4f(e){return e<0?0:e>255?255:e|0}function $5f(e){return e<0?0:e>4294967295?4294967295:e|0}var $9V=class De{constructor(t){const n=$4f(t);this.c=n,this.a=De.d(n),this.b=new Map}static d(t){const n=new Uint8Array(256);return n.fill(t),n}set(t,n){const r=$4f(n);t>=0&&t<256?this.a[t]=r:this.b.set(t,r)}get(t){return t>=0&&t<256?this.a[t]:this.b.get(t)||this.c}clear(){this.a.fill(this.c),this.b.clear()}},Boolean2;(function(e){e[e.False=0]="False",e[e.True=1]="True"})(Boolean2||(Boolean2={}));var State;(function(e){e[e.Invalid=0]="Invalid",e[e.Start=1]="Start",e[e.H=2]="H",e[e.HT=3]="HT",e[e.HTT=4]="HTT",e[e.HTTP=5]="HTTP",e[e.F=6]="F",e[e.FI=7]="FI",e[e.FIL=8]="FIL",e[e.BeforeColon=9]="BeforeColon",e[e.AfterColon=10]="AfterColon",e[e.AlmostThere=11]="AlmostThere",e[e.End=12]="End",e[e.Accept=13]="Accept",e[e.LastKnownState=14]="LastKnownState"})(State||(State={}));var Uint8Matrix=class{constructor(e,t,n){const r=new Uint8Array(e*t);for(let s=0,i=e*t;s<i;s++)r[s]=n;this.a=r,this.rows=e,this.cols=t}get(e,t){return this.a[e*this.cols+t]}set(e,t,n){this.a[e*this.cols+t]=n}},$Szb=class{constructor(e){let t=0,n=0;for(let s=0,i=e.length;s<i;s++){const[a,l,u]=e[s];l>t&&(t=l),a>n&&(n=a),u>n&&(n=u)}t++,n++;const r=new Uint8Matrix(n,t,0);for(let s=0,i=e.length;s<i;s++){const[a,l,u]=e[s];r.set(a,l,u)}this.a=r,this.b=t}nextState(e,t){return t<0||t>=this.b?0:this.a.get(e,t)}},_stateMachine=null;function getStateMachine(){return _stateMachine===null&&(_stateMachine=new $Szb([[1,104,2],[1,72,2],[1,102,6],[1,70,6],[2,116,3],[2,84,3],[3,116,4],[3,84,4],[4,112,5],[4,80,5],[5,115,9],[5,83,9],[5,58,10],[6,105,7],[6,73,7],[7,108,8],[7,76,8],[8,101,9],[8,69,9],[9,58,10],[10,47,11],[11,47,12]])),_stateMachine}var CharacterClass;(function(e){e[e.None=0]="None",e[e.ForceTermination=1]="ForceTermination",e[e.CannotEndIn=2]="CannotEndIn"})(CharacterClass||(CharacterClass={}));var _classifier=null;function getClassifier(){if(_classifier===null){_classifier=new $9V(0);const e=` 	<>'"\u3001\u3002\uFF61\uFF64\uFF0C\uFF0E\uFF1A\uFF1B\u2018\u3008\u300C\u300E\u3014\uFF08\uFF3B\uFF5B\uFF62\uFF63\uFF5D\uFF3D\uFF09\u3015\u300F\u300D\u3009\u2019\uFF40\uFF5E\u2026`;for(let n=0;n<e.length;n++)_classifier.set(e.charCodeAt(n),1);const t=".,;:";for(let n=0;n<t.length;n++)_classifier.set(t.charCodeAt(n),2)}return _classifier}var $Tzb=class pe{static a(t,n,r,s,i){let a=i-1;do{const l=n.charCodeAt(a);if(t.get(l)!==2)break;a--}while(a>s);if(s>0){const l=n.charCodeAt(s-1),u=n.charCodeAt(a);(l===40&&u===41||l===91&&u===93||l===123&&u===125)&&a--}return{range:{startLineNumber:r,startColumn:s+1,endLineNumber:r,endColumn:a+2},url:n.substring(s,a+1)}}static computeLinks(t,n=getStateMachine()){const r=getClassifier(),s=[];for(let i=1,a=t.getLineCount();i<=a;i++){const l=t.getLineContent(i),u=l.length;let o=0,h=0,c=0,f=1,g=!1,m=!1,N=!1,p=!1;for(;o<u;){let d=!1;const A=l.charCodeAt(o);if(f===13){let v;switch(A){case 40:g=!0,v=0;break;case 41:v=g?0:1;break;case 91:N=!0,m=!0,v=0;break;case 93:N=!1,v=m?0:1;break;case 123:p=!0,v=0;break;case 125:v=p?0:1;break;case 39:case 34:case 96:c===A?v=1:c===39||c===34||c===96?v=0:v=1;break;case 42:v=c===42?1:0;break;case 124:v=c===124?1:0;break;case 32:v=N?0:1;break;default:v=r.get(A)}v===1&&(s.push(pe.a(r,l,i,h,o)),d=!0)}else if(f===12){let v;A===91?(m=!0,v=0):v=r.get(A),v===1?d=!0:f=13}else f=n.nextState(f,A),f===0&&(d=!0);d&&(f=1,g=!1,m=!1,p=!1,h=o+1,c=A),o++}f===13&&s.push(pe.a(r,l,i,h,u))}return s}};function $Uzb(e){return!e||typeof e.getLineCount!="function"||typeof e.getLineContent!="function"?[]:$Tzb.computeLinks(e)}var $Vzb=class Te{constructor(){this.c=[["true","false"],["True","False"],["Private","Public","Friend","ReadOnly","Partial","Protected","WriteOnly"],["public","protected","private"]]}static{this.INSTANCE=new Te}navigateValueSet(t,n,r,s,i){if(t&&n){const a=this.a(n,i);if(a)return{range:t,value:a}}if(r&&s){const a=this.a(s,i);if(a)return{range:r,value:a}}return null}a(t,n){const r=this.b(t,n);return r!==null?r:this.d(t,n)}b(t,n){const r=Math.pow(10,t.length-(t.lastIndexOf(".")+1));let s=Number(t);const i=parseFloat(t);return!isNaN(s)&&!isNaN(i)&&s===i?s===0&&!n?null:(s=Math.floor(s*r),s+=n?r:-r,String(s/r)):null}d(t,n){return this.e(this.c,t,n)}e(t,n,r){let s=null;for(let i=0,a=t.length;s===null&&i<a;i++)s=this.f(t[i],n,r);return s}f(t,n,r){let s=t.indexOf(n);return s>=0?(s+=r?1:-1,s<0?s=t.length-1:s%=t.length,t[s]):null}},KeyCode;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.F20=78]="F20",e[e.F21=79]="F21",e[e.F22=80]="F22",e[e.F23=81]="F23",e[e.F24=82]="F24",e[e.NumLock=83]="NumLock",e[e.ScrollLock=84]="ScrollLock",e[e.Semicolon=85]="Semicolon",e[e.Equal=86]="Equal",e[e.Comma=87]="Comma",e[e.Minus=88]="Minus",e[e.Period=89]="Period",e[e.Slash=90]="Slash",e[e.Backquote=91]="Backquote",e[e.BracketLeft=92]="BracketLeft",e[e.Backslash=93]="Backslash",e[e.BracketRight=94]="BracketRight",e[e.Quote=95]="Quote",e[e.OEM_8=96]="OEM_8",e[e.IntlBackslash=97]="IntlBackslash",e[e.Numpad0=98]="Numpad0",e[e.Numpad1=99]="Numpad1",e[e.Numpad2=100]="Numpad2",e[e.Numpad3=101]="Numpad3",e[e.Numpad4=102]="Numpad4",e[e.Numpad5=103]="Numpad5",e[e.Numpad6=104]="Numpad6",e[e.Numpad7=105]="Numpad7",e[e.Numpad8=106]="Numpad8",e[e.Numpad9=107]="Numpad9",e[e.NumpadMultiply=108]="NumpadMultiply",e[e.NumpadAdd=109]="NumpadAdd",e[e.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=111]="NumpadSubtract",e[e.NumpadDecimal=112]="NumpadDecimal",e[e.NumpadDivide=113]="NumpadDivide",e[e.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",e[e.ABNT_C1=115]="ABNT_C1",e[e.ABNT_C2=116]="ABNT_C2",e[e.AudioVolumeMute=117]="AudioVolumeMute",e[e.AudioVolumeUp=118]="AudioVolumeUp",e[e.AudioVolumeDown=119]="AudioVolumeDown",e[e.BrowserSearch=120]="BrowserSearch",e[e.BrowserHome=121]="BrowserHome",e[e.BrowserBack=122]="BrowserBack",e[e.BrowserForward=123]="BrowserForward",e[e.MediaTrackNext=124]="MediaTrackNext",e[e.MediaTrackPrevious=125]="MediaTrackPrevious",e[e.MediaStop=126]="MediaStop",e[e.MediaPlayPause=127]="MediaPlayPause",e[e.LaunchMediaPlayer=128]="LaunchMediaPlayer",e[e.LaunchMail=129]="LaunchMail",e[e.LaunchApp2=130]="LaunchApp2",e[e.Clear=131]="Clear",e[e.MAX_VALUE=132]="MAX_VALUE"})(KeyCode||(KeyCode={}));var ScanCode;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.None=0]="None",e[e.Hyper=1]="Hyper",e[e.Super=2]="Super",e[e.Fn=3]="Fn",e[e.FnLock=4]="FnLock",e[e.Suspend=5]="Suspend",e[e.Resume=6]="Resume",e[e.Turbo=7]="Turbo",e[e.Sleep=8]="Sleep",e[e.WakeUp=9]="WakeUp",e[e.KeyA=10]="KeyA",e[e.KeyB=11]="KeyB",e[e.KeyC=12]="KeyC",e[e.KeyD=13]="KeyD",e[e.KeyE=14]="KeyE",e[e.KeyF=15]="KeyF",e[e.KeyG=16]="KeyG",e[e.KeyH=17]="KeyH",e[e.KeyI=18]="KeyI",e[e.KeyJ=19]="KeyJ",e[e.KeyK=20]="KeyK",e[e.KeyL=21]="KeyL",e[e.KeyM=22]="KeyM",e[e.KeyN=23]="KeyN",e[e.KeyO=24]="KeyO",e[e.KeyP=25]="KeyP",e[e.KeyQ=26]="KeyQ",e[e.KeyR=27]="KeyR",e[e.KeyS=28]="KeyS",e[e.KeyT=29]="KeyT",e[e.KeyU=30]="KeyU",e[e.KeyV=31]="KeyV",e[e.KeyW=32]="KeyW",e[e.KeyX=33]="KeyX",e[e.KeyY=34]="KeyY",e[e.KeyZ=35]="KeyZ",e[e.Digit1=36]="Digit1",e[e.Digit2=37]="Digit2",e[e.Digit3=38]="Digit3",e[e.Digit4=39]="Digit4",e[e.Digit5=40]="Digit5",e[e.Digit6=41]="Digit6",e[e.Digit7=42]="Digit7",e[e.Digit8=43]="Digit8",e[e.Digit9=44]="Digit9",e[e.Digit0=45]="Digit0",e[e.Enter=46]="Enter",e[e.Escape=47]="Escape",e[e.Backspace=48]="Backspace",e[e.Tab=49]="Tab",e[e.Space=50]="Space",e[e.Minus=51]="Minus",e[e.Equal=52]="Equal",e[e.BracketLeft=53]="BracketLeft",e[e.BracketRight=54]="BracketRight",e[e.Backslash=55]="Backslash",e[e.IntlHash=56]="IntlHash",e[e.Semicolon=57]="Semicolon",e[e.Quote=58]="Quote",e[e.Backquote=59]="Backquote",e[e.Comma=60]="Comma",e[e.Period=61]="Period",e[e.Slash=62]="Slash",e[e.CapsLock=63]="CapsLock",e[e.F1=64]="F1",e[e.F2=65]="F2",e[e.F3=66]="F3",e[e.F4=67]="F4",e[e.F5=68]="F5",e[e.F6=69]="F6",e[e.F7=70]="F7",e[e.F8=71]="F8",e[e.F9=72]="F9",e[e.F10=73]="F10",e[e.F11=74]="F11",e[e.F12=75]="F12",e[e.PrintScreen=76]="PrintScreen",e[e.ScrollLock=77]="ScrollLock",e[e.Pause=78]="Pause",e[e.Insert=79]="Insert",e[e.Home=80]="Home",e[e.PageUp=81]="PageUp",e[e.Delete=82]="Delete",e[e.End=83]="End",e[e.PageDown=84]="PageDown",e[e.ArrowRight=85]="ArrowRight",e[e.ArrowLeft=86]="ArrowLeft",e[e.ArrowDown=87]="ArrowDown",e[e.ArrowUp=88]="ArrowUp",e[e.NumLock=89]="NumLock",e[e.NumpadDivide=90]="NumpadDivide",e[e.NumpadMultiply=91]="NumpadMultiply",e[e.NumpadSubtract=92]="NumpadSubtract",e[e.NumpadAdd=93]="NumpadAdd",e[e.NumpadEnter=94]="NumpadEnter",e[e.Numpad1=95]="Numpad1",e[e.Numpad2=96]="Numpad2",e[e.Numpad3=97]="Numpad3",e[e.Numpad4=98]="Numpad4",e[e.Numpad5=99]="Numpad5",e[e.Numpad6=100]="Numpad6",e[e.Numpad7=101]="Numpad7",e[e.Numpad8=102]="Numpad8",e[e.Numpad9=103]="Numpad9",e[e.Numpad0=104]="Numpad0",e[e.NumpadDecimal=105]="NumpadDecimal",e[e.IntlBackslash=106]="IntlBackslash",e[e.ContextMenu=107]="ContextMenu",e[e.Power=108]="Power",e[e.NumpadEqual=109]="NumpadEqual",e[e.F13=110]="F13",e[e.F14=111]="F14",e[e.F15=112]="F15",e[e.F16=113]="F16",e[e.F17=114]="F17",e[e.F18=115]="F18",e[e.F19=116]="F19",e[e.F20=117]="F20",e[e.F21=118]="F21",e[e.F22=119]="F22",e[e.F23=120]="F23",e[e.F24=121]="F24",e[e.Open=122]="Open",e[e.Help=123]="Help",e[e.Select=124]="Select",e[e.Again=125]="Again",e[e.Undo=126]="Undo",e[e.Cut=127]="Cut",e[e.Copy=128]="Copy",e[e.Paste=129]="Paste",e[e.Find=130]="Find",e[e.AudioVolumeMute=131]="AudioVolumeMute",e[e.AudioVolumeUp=132]="AudioVolumeUp",e[e.AudioVolumeDown=133]="AudioVolumeDown",e[e.NumpadComma=134]="NumpadComma",e[e.IntlRo=135]="IntlRo",e[e.KanaMode=136]="KanaMode",e[e.IntlYen=137]="IntlYen",e[e.Convert=138]="Convert",e[e.NonConvert=139]="NonConvert",e[e.Lang1=140]="Lang1",e[e.Lang2=141]="Lang2",e[e.Lang3=142]="Lang3",e[e.Lang4=143]="Lang4",e[e.Lang5=144]="Lang5",e[e.Abort=145]="Abort",e[e.Props=146]="Props",e[e.NumpadParenLeft=147]="NumpadParenLeft",e[e.NumpadParenRight=148]="NumpadParenRight",e[e.NumpadBackspace=149]="NumpadBackspace",e[e.NumpadMemoryStore=150]="NumpadMemoryStore",e[e.NumpadMemoryRecall=151]="NumpadMemoryRecall",e[e.NumpadMemoryClear=152]="NumpadMemoryClear",e[e.NumpadMemoryAdd=153]="NumpadMemoryAdd",e[e.NumpadMemorySubtract=154]="NumpadMemorySubtract",e[e.NumpadClear=155]="NumpadClear",e[e.NumpadClearEntry=156]="NumpadClearEntry",e[e.ControlLeft=157]="ControlLeft",e[e.ShiftLeft=158]="ShiftLeft",e[e.AltLeft=159]="AltLeft",e[e.MetaLeft=160]="MetaLeft",e[e.ControlRight=161]="ControlRight",e[e.ShiftRight=162]="ShiftRight",e[e.AltRight=163]="AltRight",e[e.MetaRight=164]="MetaRight",e[e.BrightnessUp=165]="BrightnessUp",e[e.BrightnessDown=166]="BrightnessDown",e[e.MediaPlay=167]="MediaPlay",e[e.MediaRecord=168]="MediaRecord",e[e.MediaFastForward=169]="MediaFastForward",e[e.MediaRewind=170]="MediaRewind",e[e.MediaTrackNext=171]="MediaTrackNext",e[e.MediaTrackPrevious=172]="MediaTrackPrevious",e[e.MediaStop=173]="MediaStop",e[e.Eject=174]="Eject",e[e.MediaPlayPause=175]="MediaPlayPause",e[e.MediaSelect=176]="MediaSelect",e[e.LaunchMail=177]="LaunchMail",e[e.LaunchApp2=178]="LaunchApp2",e[e.LaunchApp1=179]="LaunchApp1",e[e.SelectTask=180]="SelectTask",e[e.LaunchScreenSaver=181]="LaunchScreenSaver",e[e.BrowserSearch=182]="BrowserSearch",e[e.BrowserHome=183]="BrowserHome",e[e.BrowserBack=184]="BrowserBack",e[e.BrowserForward=185]="BrowserForward",e[e.BrowserStop=186]="BrowserStop",e[e.BrowserRefresh=187]="BrowserRefresh",e[e.BrowserFavorites=188]="BrowserFavorites",e[e.ZoomToggle=189]="ZoomToggle",e[e.MailReply=190]="MailReply",e[e.MailForward=191]="MailForward",e[e.MailSend=192]="MailSend",e[e.MAX_VALUE=193]="MAX_VALUE"})(ScanCode||(ScanCode={}));var KeyCodeStrMap=class{constructor(){this._keyCodeToStr=[],this._strToKeyCode=Object.create(null)}define(e,t){this._keyCodeToStr[e]=t,this._strToKeyCode[t.toLowerCase()]=e}keyCodeToStr(e){return this._keyCodeToStr[e]}strToKeyCode(e){return this._strToKeyCode[e.toLowerCase()]||0}},uiMap=new KeyCodeStrMap,userSettingsUSMap=new KeyCodeStrMap,userSettingsGeneralMap=new KeyCodeStrMap,$Df=new Array(230),$Ef={},scanCodeIntToStr=[],scanCodeStrToInt=Object.create(null),scanCodeLowerCaseStrToInt=Object.create(null),$Gf=[],$Hf=[];for(let e=0;e<=193;e++)$Gf[e]=-1;for(let e=0;e<=132;e++)$Hf[e]=-1;(function(){const e="",t=[[1,0,"None",0,"unknown",0,"VK_UNKNOWN",e,e],[1,1,"Hyper",0,e,0,e,e,e],[1,2,"Super",0,e,0,e,e,e],[1,3,"Fn",0,e,0,e,e,e],[1,4,"FnLock",0,e,0,e,e,e],[1,5,"Suspend",0,e,0,e,e,e],[1,6,"Resume",0,e,0,e,e,e],[1,7,"Turbo",0,e,0,e,e,e],[1,8,"Sleep",0,e,0,"VK_SLEEP",e,e],[1,9,"WakeUp",0,e,0,e,e,e],[0,10,"KeyA",31,"A",65,"VK_A",e,e],[0,11,"KeyB",32,"B",66,"VK_B",e,e],[0,12,"KeyC",33,"C",67,"VK_C",e,e],[0,13,"KeyD",34,"D",68,"VK_D",e,e],[0,14,"KeyE",35,"E",69,"VK_E",e,e],[0,15,"KeyF",36,"F",70,"VK_F",e,e],[0,16,"KeyG",37,"G",71,"VK_G",e,e],[0,17,"KeyH",38,"H",72,"VK_H",e,e],[0,18,"KeyI",39,"I",73,"VK_I",e,e],[0,19,"KeyJ",40,"J",74,"VK_J",e,e],[0,20,"KeyK",41,"K",75,"VK_K",e,e],[0,21,"KeyL",42,"L",76,"VK_L",e,e],[0,22,"KeyM",43,"M",77,"VK_M",e,e],[0,23,"KeyN",44,"N",78,"VK_N",e,e],[0,24,"KeyO",45,"O",79,"VK_O",e,e],[0,25,"KeyP",46,"P",80,"VK_P",e,e],[0,26,"KeyQ",47,"Q",81,"VK_Q",e,e],[0,27,"KeyR",48,"R",82,"VK_R",e,e],[0,28,"KeyS",49,"S",83,"VK_S",e,e],[0,29,"KeyT",50,"T",84,"VK_T",e,e],[0,30,"KeyU",51,"U",85,"VK_U",e,e],[0,31,"KeyV",52,"V",86,"VK_V",e,e],[0,32,"KeyW",53,"W",87,"VK_W",e,e],[0,33,"KeyX",54,"X",88,"VK_X",e,e],[0,34,"KeyY",55,"Y",89,"VK_Y",e,e],[0,35,"KeyZ",56,"Z",90,"VK_Z",e,e],[0,36,"Digit1",22,"1",49,"VK_1",e,e],[0,37,"Digit2",23,"2",50,"VK_2",e,e],[0,38,"Digit3",24,"3",51,"VK_3",e,e],[0,39,"Digit4",25,"4",52,"VK_4",e,e],[0,40,"Digit5",26,"5",53,"VK_5",e,e],[0,41,"Digit6",27,"6",54,"VK_6",e,e],[0,42,"Digit7",28,"7",55,"VK_7",e,e],[0,43,"Digit8",29,"8",56,"VK_8",e,e],[0,44,"Digit9",30,"9",57,"VK_9",e,e],[0,45,"Digit0",21,"0",48,"VK_0",e,e],[1,46,"Enter",3,"Enter",13,"VK_RETURN",e,e],[1,47,"Escape",9,"Escape",27,"VK_ESCAPE",e,e],[1,48,"Backspace",1,"Backspace",8,"VK_BACK",e,e],[1,49,"Tab",2,"Tab",9,"VK_TAB",e,e],[1,50,"Space",10,"Space",32,"VK_SPACE",e,e],[0,51,"Minus",88,"-",189,"VK_OEM_MINUS","-","OEM_MINUS"],[0,52,"Equal",86,"=",187,"VK_OEM_PLUS","=","OEM_PLUS"],[0,53,"BracketLeft",92,"[",219,"VK_OEM_4","[","OEM_4"],[0,54,"BracketRight",94,"]",221,"VK_OEM_6","]","OEM_6"],[0,55,"Backslash",93,"\\",220,"VK_OEM_5","\\","OEM_5"],[0,56,"IntlHash",0,e,0,e,e,e],[0,57,"Semicolon",85,";",186,"VK_OEM_1",";","OEM_1"],[0,58,"Quote",95,"'",222,"VK_OEM_7","'","OEM_7"],[0,59,"Backquote",91,"`",192,"VK_OEM_3","`","OEM_3"],[0,60,"Comma",87,",",188,"VK_OEM_COMMA",",","OEM_COMMA"],[0,61,"Period",89,".",190,"VK_OEM_PERIOD",".","OEM_PERIOD"],[0,62,"Slash",90,"/",191,"VK_OEM_2","/","OEM_2"],[1,63,"CapsLock",8,"CapsLock",20,"VK_CAPITAL",e,e],[1,64,"F1",59,"F1",112,"VK_F1",e,e],[1,65,"F2",60,"F2",113,"VK_F2",e,e],[1,66,"F3",61,"F3",114,"VK_F3",e,e],[1,67,"F4",62,"F4",115,"VK_F4",e,e],[1,68,"F5",63,"F5",116,"VK_F5",e,e],[1,69,"F6",64,"F6",117,"VK_F6",e,e],[1,70,"F7",65,"F7",118,"VK_F7",e,e],[1,71,"F8",66,"F8",119,"VK_F8",e,e],[1,72,"F9",67,"F9",120,"VK_F9",e,e],[1,73,"F10",68,"F10",121,"VK_F10",e,e],[1,74,"F11",69,"F11",122,"VK_F11",e,e],[1,75,"F12",70,"F12",123,"VK_F12",e,e],[1,76,"PrintScreen",0,e,0,e,e,e],[1,77,"ScrollLock",84,"ScrollLock",145,"VK_SCROLL",e,e],[1,78,"Pause",7,"PauseBreak",19,"VK_PAUSE",e,e],[1,79,"Insert",19,"Insert",45,"VK_INSERT",e,e],[1,80,"Home",14,"Home",36,"VK_HOME",e,e],[1,81,"PageUp",11,"PageUp",33,"VK_PRIOR",e,e],[1,82,"Delete",20,"Delete",46,"VK_DELETE",e,e],[1,83,"End",13,"End",35,"VK_END",e,e],[1,84,"PageDown",12,"PageDown",34,"VK_NEXT",e,e],[1,85,"ArrowRight",17,"RightArrow",39,"VK_RIGHT","Right",e],[1,86,"ArrowLeft",15,"LeftArrow",37,"VK_LEFT","Left",e],[1,87,"ArrowDown",18,"DownArrow",40,"VK_DOWN","Down",e],[1,88,"ArrowUp",16,"UpArrow",38,"VK_UP","Up",e],[1,89,"NumLock",83,"NumLock",144,"VK_NUMLOCK",e,e],[1,90,"NumpadDivide",113,"NumPad_Divide",111,"VK_DIVIDE",e,e],[1,91,"NumpadMultiply",108,"NumPad_Multiply",106,"VK_MULTIPLY",e,e],[1,92,"NumpadSubtract",111,"NumPad_Subtract",109,"VK_SUBTRACT",e,e],[1,93,"NumpadAdd",109,"NumPad_Add",107,"VK_ADD",e,e],[1,94,"NumpadEnter",3,e,0,e,e,e],[1,95,"Numpad1",99,"NumPad1",97,"VK_NUMPAD1",e,e],[1,96,"Numpad2",100,"NumPad2",98,"VK_NUMPAD2",e,e],[1,97,"Numpad3",101,"NumPad3",99,"VK_NUMPAD3",e,e],[1,98,"Numpad4",102,"NumPad4",100,"VK_NUMPAD4",e,e],[1,99,"Numpad5",103,"NumPad5",101,"VK_NUMPAD5",e,e],[1,100,"Numpad6",104,"NumPad6",102,"VK_NUMPAD6",e,e],[1,101,"Numpad7",105,"NumPad7",103,"VK_NUMPAD7",e,e],[1,102,"Numpad8",106,"NumPad8",104,"VK_NUMPAD8",e,e],[1,103,"Numpad9",107,"NumPad9",105,"VK_NUMPAD9",e,e],[1,104,"Numpad0",98,"NumPad0",96,"VK_NUMPAD0",e,e],[1,105,"NumpadDecimal",112,"NumPad_Decimal",110,"VK_DECIMAL",e,e],[0,106,"IntlBackslash",97,"OEM_102",226,"VK_OEM_102",e,e],[1,107,"ContextMenu",58,"ContextMenu",93,e,e,e],[1,108,"Power",0,e,0,e,e,e],[1,109,"NumpadEqual",0,e,0,e,e,e],[1,110,"F13",71,"F13",124,"VK_F13",e,e],[1,111,"F14",72,"F14",125,"VK_F14",e,e],[1,112,"F15",73,"F15",126,"VK_F15",e,e],[1,113,"F16",74,"F16",127,"VK_F16",e,e],[1,114,"F17",75,"F17",128,"VK_F17",e,e],[1,115,"F18",76,"F18",129,"VK_F18",e,e],[1,116,"F19",77,"F19",130,"VK_F19",e,e],[1,117,"F20",78,"F20",131,"VK_F20",e,e],[1,118,"F21",79,"F21",132,"VK_F21",e,e],[1,119,"F22",80,"F22",133,"VK_F22",e,e],[1,120,"F23",81,"F23",134,"VK_F23",e,e],[1,121,"F24",82,"F24",135,"VK_F24",e,e],[1,122,"Open",0,e,0,e,e,e],[1,123,"Help",0,e,0,e,e,e],[1,124,"Select",0,e,0,e,e,e],[1,125,"Again",0,e,0,e,e,e],[1,126,"Undo",0,e,0,e,e,e],[1,127,"Cut",0,e,0,e,e,e],[1,128,"Copy",0,e,0,e,e,e],[1,129,"Paste",0,e,0,e,e,e],[1,130,"Find",0,e,0,e,e,e],[1,131,"AudioVolumeMute",117,"AudioVolumeMute",173,"VK_VOLUME_MUTE",e,e],[1,132,"AudioVolumeUp",118,"AudioVolumeUp",175,"VK_VOLUME_UP",e,e],[1,133,"AudioVolumeDown",119,"AudioVolumeDown",174,"VK_VOLUME_DOWN",e,e],[1,134,"NumpadComma",110,"NumPad_Separator",108,"VK_SEPARATOR",e,e],[0,135,"IntlRo",115,"ABNT_C1",193,"VK_ABNT_C1",e,e],[1,136,"KanaMode",0,e,0,e,e,e],[0,137,"IntlYen",0,e,0,e,e,e],[1,138,"Convert",0,e,0,e,e,e],[1,139,"NonConvert",0,e,0,e,e,e],[1,140,"Lang1",0,e,0,e,e,e],[1,141,"Lang2",0,e,0,e,e,e],[1,142,"Lang3",0,e,0,e,e,e],[1,143,"Lang4",0,e,0,e,e,e],[1,144,"Lang5",0,e,0,e,e,e],[1,145,"Abort",0,e,0,e,e,e],[1,146,"Props",0,e,0,e,e,e],[1,147,"NumpadParenLeft",0,e,0,e,e,e],[1,148,"NumpadParenRight",0,e,0,e,e,e],[1,149,"NumpadBackspace",0,e,0,e,e,e],[1,150,"NumpadMemoryStore",0,e,0,e,e,e],[1,151,"NumpadMemoryRecall",0,e,0,e,e,e],[1,152,"NumpadMemoryClear",0,e,0,e,e,e],[1,153,"NumpadMemoryAdd",0,e,0,e,e,e],[1,154,"NumpadMemorySubtract",0,e,0,e,e,e],[1,155,"NumpadClear",131,"Clear",12,"VK_CLEAR",e,e],[1,156,"NumpadClearEntry",0,e,0,e,e,e],[1,0,e,5,"Ctrl",17,"VK_CONTROL",e,e],[1,0,e,4,"Shift",16,"VK_SHIFT",e,e],[1,0,e,6,"Alt",18,"VK_MENU",e,e],[1,0,e,57,"Meta",91,"VK_COMMAND",e,e],[1,157,"ControlLeft",5,e,0,"VK_LCONTROL",e,e],[1,158,"ShiftLeft",4,e,0,"VK_LSHIFT",e,e],[1,159,"AltLeft",6,e,0,"VK_LMENU",e,e],[1,160,"MetaLeft",57,e,0,"VK_LWIN",e,e],[1,161,"ControlRight",5,e,0,"VK_RCONTROL",e,e],[1,162,"ShiftRight",4,e,0,"VK_RSHIFT",e,e],[1,163,"AltRight",6,e,0,"VK_RMENU",e,e],[1,164,"MetaRight",57,e,0,"VK_RWIN",e,e],[1,165,"BrightnessUp",0,e,0,e,e,e],[1,166,"BrightnessDown",0,e,0,e,e,e],[1,167,"MediaPlay",0,e,0,e,e,e],[1,168,"MediaRecord",0,e,0,e,e,e],[1,169,"MediaFastForward",0,e,0,e,e,e],[1,170,"MediaRewind",0,e,0,e,e,e],[1,171,"MediaTrackNext",124,"MediaTrackNext",176,"VK_MEDIA_NEXT_TRACK",e,e],[1,172,"MediaTrackPrevious",125,"MediaTrackPrevious",177,"VK_MEDIA_PREV_TRACK",e,e],[1,173,"MediaStop",126,"MediaStop",178,"VK_MEDIA_STOP",e,e],[1,174,"Eject",0,e,0,e,e,e],[1,175,"MediaPlayPause",127,"MediaPlayPause",179,"VK_MEDIA_PLAY_PAUSE",e,e],[1,176,"MediaSelect",128,"LaunchMediaPlayer",181,"VK_MEDIA_LAUNCH_MEDIA_SELECT",e,e],[1,177,"LaunchMail",129,"LaunchMail",180,"VK_MEDIA_LAUNCH_MAIL",e,e],[1,178,"LaunchApp2",130,"LaunchApp2",183,"VK_MEDIA_LAUNCH_APP2",e,e],[1,179,"LaunchApp1",0,e,0,"VK_MEDIA_LAUNCH_APP1",e,e],[1,180,"SelectTask",0,e,0,e,e,e],[1,181,"LaunchScreenSaver",0,e,0,e,e,e],[1,182,"BrowserSearch",120,"BrowserSearch",170,"VK_BROWSER_SEARCH",e,e],[1,183,"BrowserHome",121,"BrowserHome",172,"VK_BROWSER_HOME",e,e],[1,184,"BrowserBack",122,"BrowserBack",166,"VK_BROWSER_BACK",e,e],[1,185,"BrowserForward",123,"BrowserForward",167,"VK_BROWSER_FORWARD",e,e],[1,186,"BrowserStop",0,e,0,"VK_BROWSER_STOP",e,e],[1,187,"BrowserRefresh",0,e,0,"VK_BROWSER_REFRESH",e,e],[1,188,"BrowserFavorites",0,e,0,"VK_BROWSER_FAVORITES",e,e],[1,189,"ZoomToggle",0,e,0,e,e,e],[1,190,"MailReply",0,e,0,e,e,e],[1,191,"MailForward",0,e,0,e,e,e],[1,192,"MailSend",0,e,0,e,e,e],[1,0,e,114,"KeyInComposition",229,e,e,e],[1,0,e,116,"ABNT_C2",194,"VK_ABNT_C2",e,e],[1,0,e,96,"OEM_8",223,"VK_OEM_8",e,e],[1,0,e,0,e,0,"VK_KANA",e,e],[1,0,e,0,e,0,"VK_HANGUL",e,e],[1,0,e,0,e,0,"VK_JUNJA",e,e],[1,0,e,0,e,0,"VK_FINAL",e,e],[1,0,e,0,e,0,"VK_HANJA",e,e],[1,0,e,0,e,0,"VK_KANJI",e,e],[1,0,e,0,e,0,"VK_CONVERT",e,e],[1,0,e,0,e,0,"VK_NONCONVERT",e,e],[1,0,e,0,e,0,"VK_ACCEPT",e,e],[1,0,e,0,e,0,"VK_MODECHANGE",e,e],[1,0,e,0,e,0,"VK_SELECT",e,e],[1,0,e,0,e,0,"VK_PRINT",e,e],[1,0,e,0,e,0,"VK_EXECUTE",e,e],[1,0,e,0,e,0,"VK_SNAPSHOT",e,e],[1,0,e,0,e,0,"VK_HELP",e,e],[1,0,e,0,e,0,"VK_APPS",e,e],[1,0,e,0,e,0,"VK_PROCESSKEY",e,e],[1,0,e,0,e,0,"VK_PACKET",e,e],[1,0,e,0,e,0,"VK_DBE_SBCSCHAR",e,e],[1,0,e,0,e,0,"VK_DBE_DBCSCHAR",e,e],[1,0,e,0,e,0,"VK_ATTN",e,e],[1,0,e,0,e,0,"VK_CRSEL",e,e],[1,0,e,0,e,0,"VK_EXSEL",e,e],[1,0,e,0,e,0,"VK_EREOF",e,e],[1,0,e,0,e,0,"VK_PLAY",e,e],[1,0,e,0,e,0,"VK_ZOOM",e,e],[1,0,e,0,e,0,"VK_NONAME",e,e],[1,0,e,0,e,0,"VK_PA1",e,e],[1,0,e,0,e,0,"VK_OEM_CLEAR",e,e]],n=[],r=[];for(const s of t){const[i,a,l,u,o,h,c,f,g]=s;if(r[a]||(r[a]=!0,scanCodeIntToStr[a]=l,scanCodeStrToInt[l]=a,scanCodeLowerCaseStrToInt[l.toLowerCase()]=a,i&&($Gf[a]=u,u!==0&&u!==3&&u!==5&&u!==4&&u!==6&&u!==57&&($Hf[u]=a))),!n[u]){if(n[u]=!0,!o)throw new Error(`String representation missing for key code ${u} around scan code ${l}`);uiMap.define(u,o),userSettingsUSMap.define(u,f||o),userSettingsGeneralMap.define(u,g||f||o)}h&&($Df[h]=u),c&&($Ef[c]=u)}$Hf[3]=46})();var KeyCodeUtils;(function(e){function t(l){return uiMap.keyCodeToStr(l)}e.toString=t;function n(l){return uiMap.strToKeyCode(l)}e.fromString=n;function r(l){return userSettingsUSMap.keyCodeToStr(l)}e.toUserSettingsUS=r;function s(l){return userSettingsGeneralMap.keyCodeToStr(l)}e.toUserSettingsGeneral=s;function i(l){return userSettingsUSMap.strToKeyCode(l)||userSettingsGeneralMap.strToKeyCode(l)}e.fromUserSettings=i;function a(l){if(l>=98&&l<=113)return null;switch(l){case 16:return"Up";case 18:return"Down";case 15:return"Left";case 17:return"Right"}return uiMap.keyCodeToStr(l)}e.toElectronAccelerator=a})(KeyCodeUtils||(KeyCodeUtils={}));var KeyMod;(function(e){e[e.CtrlCmd=2048]="CtrlCmd",e[e.Shift=1024]="Shift",e[e.Alt=512]="Alt",e[e.WinCtrl=256]="WinCtrl"})(KeyMod||(KeyMod={}));function $If(e,t){const n=(t&65535)<<16>>>0;return(e|n)>>>0}var safeProcess,vscodeGlobal=globalThis.vscode;if(typeof vscodeGlobal<"u"&&typeof vscodeGlobal.process<"u"){const e=vscodeGlobal.process;safeProcess={get platform(){return e.platform},get arch(){return e.arch},get env(){return e.env},cwd(){return e.cwd()}}}else typeof process<"u"&&typeof process?.versions?.node=="string"?safeProcess={get platform(){return process.platform},get arch(){return process.arch},get env(){return process.env},cwd(){return process.env.VSCODE_CWD||process.cwd()}}:safeProcess={get platform(){return $l?"win32":$m?"darwin":"linux"},get arch(){},get env(){return{}},cwd(){return"/"}};var cwd=safeProcess.cwd,env=safeProcess.env,$ic=safeProcess.platform,$jc=safeProcess.arch,CHAR_UPPERCASE_A=65,CHAR_LOWERCASE_A=97,CHAR_UPPERCASE_Z=90,CHAR_LOWERCASE_Z=122,CHAR_DOT=46,CHAR_FORWARD_SLASH=47,CHAR_BACKWARD_SLASH=92,CHAR_COLON=58,CHAR_QUESTION_MARK=63,ErrorInvalidArgType=class extends Error{constructor(e,t,n){let r;typeof t=="string"&&t.indexOf("not ")===0?(r="must not be",t=t.replace(/^not /,"")):r="must be";const s=e.indexOf(".")!==-1?"property":"argument";let i=`The "${e}" ${s} ${r} of type ${t}`;i+=`. Received type ${typeof n}`,super(i),this.code="ERR_INVALID_ARG_TYPE"}};function validateObject(e,t){if(e===null||typeof e!="object")throw new ErrorInvalidArgType(t,"Object",e)}function validateString(e,t){if(typeof e!="string")throw new ErrorInvalidArgType(t,"string",e)}var platformIsWin32=$ic==="win32";function isPathSeparator(e){return e===CHAR_FORWARD_SLASH||e===CHAR_BACKWARD_SLASH}function isPosixPathSeparator(e){return e===CHAR_FORWARD_SLASH}function isWindowsDeviceRoot(e){return e>=CHAR_UPPERCASE_A&&e<=CHAR_UPPERCASE_Z||e>=CHAR_LOWERCASE_A&&e<=CHAR_LOWERCASE_Z}function normalizeString(e,t,n,r){let s="",i=0,a=-1,l=0,u=0;for(let o=0;o<=e.length;++o){if(o<e.length)u=e.charCodeAt(o);else{if(r(u))break;u=CHAR_FORWARD_SLASH}if(r(u)){if(!(a===o-1||l===1))if(l===2){if(s.length<2||i!==2||s.charCodeAt(s.length-1)!==CHAR_DOT||s.charCodeAt(s.length-2)!==CHAR_DOT){if(s.length>2){const h=s.lastIndexOf(n);h===-1?(s="",i=0):(s=s.slice(0,h),i=s.length-1-s.lastIndexOf(n)),a=o,l=0;continue}else if(s.length!==0){s="",i=0,a=o,l=0;continue}}t&&(s+=s.length>0?`${n}..`:"..",i=2)}else s.length>0?s+=`${n}${e.slice(a+1,o)}`:s=e.slice(a+1,o),i=o-a-1;a=o,l=0}else u===CHAR_DOT&&l!==-1?++l:l=-1}return s}function formatExt(e){return e?`${e[0]==="."?"":"."}${e}`:""}function _format2(e,t){validateObject(t,"pathObject");const n=t.dir||t.root,r=t.base||`${t.name||""}${formatExt(t.ext)}`;return n?n===t.root?`${n}${r}`:`${n}${e}${r}`:r}var $kc={resolve(...e){let t="",n="",r=!1;for(let s=e.length-1;s>=-1;s--){let i;if(s>=0){if(i=e[s],validateString(i,`paths[${s}]`),i.length===0)continue}else t.length===0?i=cwd():(i=env[`=${t}`]||cwd(),(i===void 0||i.slice(0,2).toLowerCase()!==t.toLowerCase()&&i.charCodeAt(2)===CHAR_BACKWARD_SLASH)&&(i=`${t}\\`));const a=i.length;let l=0,u="",o=!1;const h=i.charCodeAt(0);if(a===1)isPathSeparator(h)&&(l=1,o=!0);else if(isPathSeparator(h))if(o=!0,isPathSeparator(i.charCodeAt(1))){let c=2,f=c;for(;c<a&&!isPathSeparator(i.charCodeAt(c));)c++;if(c<a&&c!==f){const g=i.slice(f,c);for(f=c;c<a&&isPathSeparator(i.charCodeAt(c));)c++;if(c<a&&c!==f){for(f=c;c<a&&!isPathSeparator(i.charCodeAt(c));)c++;(c===a||c!==f)&&(u=`\\\\${g}\\${i.slice(f,c)}`,l=c)}}}else l=1;else isWindowsDeviceRoot(h)&&i.charCodeAt(1)===CHAR_COLON&&(u=i.slice(0,2),l=2,a>2&&isPathSeparator(i.charCodeAt(2))&&(o=!0,l=3));if(u.length>0)if(t.length>0){if(u.toLowerCase()!==t.toLowerCase())continue}else t=u;if(r){if(t.length>0)break}else if(n=`${i.slice(l)}\\${n}`,r=o,o&&t.length>0)break}return n=normalizeString(n,!r,"\\",isPathSeparator),r?`${t}\\${n}`:`${t}${n}`||"."},normalize(e){validateString(e,"path");const t=e.length;if(t===0)return".";let n=0,r,s=!1;const i=e.charCodeAt(0);if(t===1)return isPosixPathSeparator(i)?"\\":e;if(isPathSeparator(i))if(s=!0,isPathSeparator(e.charCodeAt(1))){let l=2,u=l;for(;l<t&&!isPathSeparator(e.charCodeAt(l));)l++;if(l<t&&l!==u){const o=e.slice(u,l);for(u=l;l<t&&isPathSeparator(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&!isPathSeparator(e.charCodeAt(l));)l++;if(l===t)return`\\\\${o}\\${e.slice(u)}\\`;l!==u&&(r=`\\\\${o}\\${e.slice(u,l)}`,n=l)}}}else n=1;else isWindowsDeviceRoot(i)&&e.charCodeAt(1)===CHAR_COLON&&(r=e.slice(0,2),n=2,t>2&&isPathSeparator(e.charCodeAt(2))&&(s=!0,n=3));let a=n<t?normalizeString(e.slice(n),!s,"\\",isPathSeparator):"";if(a.length===0&&!s&&(a="."),a.length>0&&isPathSeparator(e.charCodeAt(t-1))&&(a+="\\"),!s&&r===void 0&&e.includes(":")){if(a.length>=2&&isWindowsDeviceRoot(a.charCodeAt(0))&&a.charCodeAt(1)===CHAR_COLON)return`.\\${a}`;let l=e.indexOf(":");do if(l===t-1||isPathSeparator(e.charCodeAt(l+1)))return`.\\${a}`;while((l=e.indexOf(":",l+1))!==-1)}return r===void 0?s?`\\${a}`:a:s?`${r}\\${a}`:`${r}${a}`},isAbsolute(e){validateString(e,"path");const t=e.length;if(t===0)return!1;const n=e.charCodeAt(0);return isPathSeparator(n)||t>2&&isWindowsDeviceRoot(n)&&e.charCodeAt(1)===CHAR_COLON&&isPathSeparator(e.charCodeAt(2))},join(...e){if(e.length===0)return".";let t,n;for(let i=0;i<e.length;++i){const a=e[i];validateString(a,"path"),a.length>0&&(t===void 0?t=n=a:t+=`\\${a}`)}if(t===void 0)return".";let r=!0,s=0;if(typeof n=="string"&&isPathSeparator(n.charCodeAt(0))){++s;const i=n.length;i>1&&isPathSeparator(n.charCodeAt(1))&&(++s,i>2&&(isPathSeparator(n.charCodeAt(2))?++s:r=!1))}if(r){for(;s<t.length&&isPathSeparator(t.charCodeAt(s));)s++;s>=2&&(t=`\\${t.slice(s)}`)}return $kc.normalize(t)},relative(e,t){if(validateString(e,"from"),validateString(t,"to"),e===t)return"";const n=$kc.resolve(e),r=$kc.resolve(t);if(n===r||(e=n.toLowerCase(),t=r.toLowerCase(),e===t))return"";if(n.length!==e.length||r.length!==t.length){const m=n.split("\\"),N=r.split("\\");m[m.length-1]===""&&m.pop(),N[N.length-1]===""&&N.pop();const p=m.length,d=N.length,A=p<d?p:d;let v;for(v=0;v<A&&m[v].toLowerCase()===N[v].toLowerCase();v++);return v===0?r:v===A?d>A?N.slice(v).join("\\"):p>A?"..\\".repeat(p-1-v)+"..":"":"..\\".repeat(p-v)+N.slice(v).join("\\")}let s=0;for(;s<e.length&&e.charCodeAt(s)===CHAR_BACKWARD_SLASH;)s++;let i=e.length;for(;i-1>s&&e.charCodeAt(i-1)===CHAR_BACKWARD_SLASH;)i--;const a=i-s;let l=0;for(;l<t.length&&t.charCodeAt(l)===CHAR_BACKWARD_SLASH;)l++;let u=t.length;for(;u-1>l&&t.charCodeAt(u-1)===CHAR_BACKWARD_SLASH;)u--;const o=u-l,h=a<o?a:o;let c=-1,f=0;for(;f<h;f++){const m=e.charCodeAt(s+f);if(m!==t.charCodeAt(l+f))break;m===CHAR_BACKWARD_SLASH&&(c=f)}if(f!==h){if(c===-1)return r}else{if(o>h){if(t.charCodeAt(l+f)===CHAR_BACKWARD_SLASH)return r.slice(l+f+1);if(f===2)return r.slice(l+f)}a>h&&(e.charCodeAt(s+f)===CHAR_BACKWARD_SLASH?c=f:f===2&&(c=3)),c===-1&&(c=0)}let g="";for(f=s+c+1;f<=i;++f)(f===i||e.charCodeAt(f)===CHAR_BACKWARD_SLASH)&&(g+=g.length===0?"..":"\\..");return l+=c,g.length>0?`${g}${r.slice(l,u)}`:(r.charCodeAt(l)===CHAR_BACKWARD_SLASH&&++l,r.slice(l,u))},toNamespacedPath(e){if(typeof e!="string"||e.length===0)return e;const t=$kc.resolve(e);if(t.length<=2)return e;if(t.charCodeAt(0)===CHAR_BACKWARD_SLASH){if(t.charCodeAt(1)===CHAR_BACKWARD_SLASH){const n=t.charCodeAt(2);if(n!==CHAR_QUESTION_MARK&&n!==CHAR_DOT)return`\\\\?\\UNC\\${t.slice(2)}`}}else if(isWindowsDeviceRoot(t.charCodeAt(0))&&t.charCodeAt(1)===CHAR_COLON&&t.charCodeAt(2)===CHAR_BACKWARD_SLASH)return`\\\\?\\${t}`;return t},dirname(e){validateString(e,"path");const t=e.length;if(t===0)return".";let n=-1,r=0;const s=e.charCodeAt(0);if(t===1)return isPathSeparator(s)?e:".";if(isPathSeparator(s)){if(n=r=1,isPathSeparator(e.charCodeAt(1))){let l=2,u=l;for(;l<t&&!isPathSeparator(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&isPathSeparator(e.charCodeAt(l));)l++;if(l<t&&l!==u){for(u=l;l<t&&!isPathSeparator(e.charCodeAt(l));)l++;if(l===t)return e;l!==u&&(n=r=l+1)}}}}else isWindowsDeviceRoot(s)&&e.charCodeAt(1)===CHAR_COLON&&(n=t>2&&isPathSeparator(e.charCodeAt(2))?3:2,r=n);let i=-1,a=!0;for(let l=t-1;l>=r;--l)if(isPathSeparator(e.charCodeAt(l))){if(!a){i=l;break}}else a=!1;if(i===-1){if(n===-1)return".";i=n}return e.slice(0,i)},basename(e,t){t!==void 0&&validateString(t,"suffix"),validateString(e,"path");let n=0,r=-1,s=!0,i;if(e.length>=2&&isWindowsDeviceRoot(e.charCodeAt(0))&&e.charCodeAt(1)===CHAR_COLON&&(n=2),t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,l=-1;for(i=e.length-1;i>=n;--i){const u=e.charCodeAt(i);if(isPathSeparator(u)){if(!s){n=i+1;break}}else l===-1&&(s=!1,l=i+1),a>=0&&(u===t.charCodeAt(a)?--a===-1&&(r=i):(a=-1,r=l))}return n===r?r=l:r===-1&&(r=e.length),e.slice(n,r)}for(i=e.length-1;i>=n;--i)if(isPathSeparator(e.charCodeAt(i))){if(!s){n=i+1;break}}else r===-1&&(s=!1,r=i+1);return r===-1?"":e.slice(n,r)},extname(e){validateString(e,"path");let t=0,n=-1,r=0,s=-1,i=!0,a=0;e.length>=2&&e.charCodeAt(1)===CHAR_COLON&&isWindowsDeviceRoot(e.charCodeAt(0))&&(t=r=2);for(let l=e.length-1;l>=t;--l){const u=e.charCodeAt(l);if(isPathSeparator(u)){if(!i){r=l+1;break}continue}s===-1&&(i=!1,s=l+1),u===CHAR_DOT?n===-1?n=l:a!==1&&(a=1):n!==-1&&(a=-1)}return n===-1||s===-1||a===0||a===1&&n===s-1&&n===r+1?"":e.slice(n,s)},format:_format2.bind(null,"\\"),parse(e){validateString(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.length;let r=0,s=e.charCodeAt(0);if(n===1)return isPathSeparator(s)?(t.root=t.dir=e,t):(t.base=t.name=e,t);if(isPathSeparator(s)){if(r=1,isPathSeparator(e.charCodeAt(1))){let c=2,f=c;for(;c<n&&!isPathSeparator(e.charCodeAt(c));)c++;if(c<n&&c!==f){for(f=c;c<n&&isPathSeparator(e.charCodeAt(c));)c++;if(c<n&&c!==f){for(f=c;c<n&&!isPathSeparator(e.charCodeAt(c));)c++;c===n?r=c:c!==f&&(r=c+1)}}}}else if(isWindowsDeviceRoot(s)&&e.charCodeAt(1)===CHAR_COLON){if(n<=2)return t.root=t.dir=e,t;if(r=2,isPathSeparator(e.charCodeAt(2))){if(n===3)return t.root=t.dir=e,t;r=3}}r>0&&(t.root=e.slice(0,r));let i=-1,a=r,l=-1,u=!0,o=e.length-1,h=0;for(;o>=r;--o){if(s=e.charCodeAt(o),isPathSeparator(s)){if(!u){a=o+1;break}continue}l===-1&&(u=!1,l=o+1),s===CHAR_DOT?i===-1?i=o:h!==1&&(h=1):i!==-1&&(h=-1)}return l!==-1&&(i===-1||h===0||h===1&&i===l-1&&i===a+1?t.base=t.name=e.slice(a,l):(t.name=e.slice(a,i),t.base=e.slice(a,l),t.ext=e.slice(i,l))),a>0&&a!==r?t.dir=e.slice(0,a-1):t.dir=t.root,t},sep:"\\",delimiter:";",win32:null,posix:null},posixCwd=(()=>{if(platformIsWin32){const e=/\\/g;return()=>{const t=cwd().replace(e,"/");return t.slice(t.indexOf("/"))}}return()=>cwd()})(),$lc={resolve(...e){let t="",n=!1;for(let r=e.length-1;r>=0&&!n;r--){const s=e[r];validateString(s,`paths[${r}]`),s.length!==0&&(t=`${s}/${t}`,n=s.charCodeAt(0)===CHAR_FORWARD_SLASH)}if(!n){const r=posixCwd();t=`${r}/${t}`,n=r.charCodeAt(0)===CHAR_FORWARD_SLASH}return t=normalizeString(t,!n,"/",isPosixPathSeparator),n?`/${t}`:t.length>0?t:"."},normalize(e){if(validateString(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===CHAR_FORWARD_SLASH,n=e.charCodeAt(e.length-1)===CHAR_FORWARD_SLASH;return e=normalizeString(e,!t,"/",isPosixPathSeparator),e.length===0?t?"/":n?"./":".":(n&&(e+="/"),t?`/${e}`:e)},isAbsolute(e){return validateString(e,"path"),e.length>0&&e.charCodeAt(0)===CHAR_FORWARD_SLASH},join(...e){if(e.length===0)return".";const t=[];for(let n=0;n<e.length;++n){const r=e[n];validateString(r,"path"),r.length>0&&t.push(r)}return t.length===0?".":$lc.normalize(t.join("/"))},relative(e,t){if(validateString(e,"from"),validateString(t,"to"),e===t||(e=$lc.resolve(e),t=$lc.resolve(t),e===t))return"";const n=1,r=e.length,s=r-n,i=1,a=t.length-i,l=s<a?s:a;let u=-1,o=0;for(;o<l;o++){const c=e.charCodeAt(n+o);if(c!==t.charCodeAt(i+o))break;c===CHAR_FORWARD_SLASH&&(u=o)}if(o===l)if(a>l){if(t.charCodeAt(i+o)===CHAR_FORWARD_SLASH)return t.slice(i+o+1);if(o===0)return t.slice(i+o)}else s>l&&(e.charCodeAt(n+o)===CHAR_FORWARD_SLASH?u=o:o===0&&(u=0));let h="";for(o=n+u+1;o<=r;++o)(o===r||e.charCodeAt(o)===CHAR_FORWARD_SLASH)&&(h+=h.length===0?"..":"/..");return`${h}${t.slice(i+u)}`},toNamespacedPath(e){return e},dirname(e){if(validateString(e,"path"),e.length===0)return".";const t=e.charCodeAt(0)===CHAR_FORWARD_SLASH;let n=-1,r=!0;for(let s=e.length-1;s>=1;--s)if(e.charCodeAt(s)===CHAR_FORWARD_SLASH){if(!r){n=s;break}}else r=!1;return n===-1?t?"/":".":t&&n===1?"//":e.slice(0,n)},basename(e,t){t!==void 0&&validateString(t,"suffix"),validateString(e,"path");let n=0,r=-1,s=!0,i;if(t!==void 0&&t.length>0&&t.length<=e.length){if(t===e)return"";let a=t.length-1,l=-1;for(i=e.length-1;i>=0;--i){const u=e.charCodeAt(i);if(u===CHAR_FORWARD_SLASH){if(!s){n=i+1;break}}else l===-1&&(s=!1,l=i+1),a>=0&&(u===t.charCodeAt(a)?--a===-1&&(r=i):(a=-1,r=l))}return n===r?r=l:r===-1&&(r=e.length),e.slice(n,r)}for(i=e.length-1;i>=0;--i)if(e.charCodeAt(i)===CHAR_FORWARD_SLASH){if(!s){n=i+1;break}}else r===-1&&(s=!1,r=i+1);return r===-1?"":e.slice(n,r)},extname(e){validateString(e,"path");let t=-1,n=0,r=-1,s=!0,i=0;for(let a=e.length-1;a>=0;--a){const l=e[a];if(l==="/"){if(!s){n=a+1;break}continue}r===-1&&(s=!1,r=a+1),l==="."?t===-1?t=a:i!==1&&(i=1):t!==-1&&(i=-1)}return t===-1||r===-1||i===0||i===1&&t===r-1&&t===n+1?"":e.slice(t,r)},format:_format2.bind(null,"/"),parse(e){validateString(e,"path");const t={root:"",dir:"",base:"",ext:"",name:""};if(e.length===0)return t;const n=e.charCodeAt(0)===CHAR_FORWARD_SLASH;let r;n?(t.root="/",r=1):r=0;let s=-1,i=0,a=-1,l=!0,u=e.length-1,o=0;for(;u>=r;--u){const h=e.charCodeAt(u);if(h===CHAR_FORWARD_SLASH){if(!l){i=u+1;break}continue}a===-1&&(l=!1,a=u+1),h===CHAR_DOT?s===-1?s=u:o!==1&&(o=1):s!==-1&&(o=-1)}if(a!==-1){const h=i===0&&n?1:i;s===-1||o===0||o===1&&s===a-1&&s===i+1?t.base=t.name=e.slice(h,a):(t.name=e.slice(h,s),t.base=e.slice(h,a),t.ext=e.slice(s,a))}return i>0?t.dir=e.slice(0,i-1):n&&(t.dir="/"),t},sep:"/",delimiter:":",win32:null,posix:null};$lc.win32=$kc.win32=$kc,$lc.posix=$kc.posix=$lc;var $mc=platformIsWin32?$kc.normalize:$lc.normalize,$nc=platformIsWin32?$kc.isAbsolute:$lc.isAbsolute,$oc=platformIsWin32?$kc.join:$lc.join,$pc=platformIsWin32?$kc.resolve:$lc.resolve,$qc=platformIsWin32?$kc.relative:$lc.relative,$rc=platformIsWin32?$kc.dirname:$lc.dirname,$sc=platformIsWin32?$kc.basename:$lc.basename,$tc=platformIsWin32?$kc.extname:$lc.extname,$uc=platformIsWin32?$kc.format:$lc.format,$vc=platformIsWin32?$kc.parse:$lc.parse,$wc=platformIsWin32?$kc.toNamespacedPath:$lc.toNamespacedPath,sep=platformIsWin32?$kc.sep:$lc.sep,$yc=platformIsWin32?$kc.delimiter:$lc.delimiter,_schemePattern=/^\w[\w\d+.-]*$/,_singleSlashStart=/^\//,_doubleSlashStart=/^\/\//;function _validateUri(e,t){if(!e.scheme&&t)throw new Error(`[UriError]: Scheme is missing: {scheme: "", authority: "${e.authority}", path: "${e.path}", query: "${e.query}", fragment: "${e.fragment}"}`);if(e.scheme&&!_schemePattern.test(e.scheme))throw new Error("[UriError]: Scheme contains illegal characters.");if(e.path){if(e.authority){if(!_singleSlashStart.test(e.path))throw new Error('[UriError]: If a URI contains an authority component, then the path component must either be empty or begin with a slash ("/") character')}else if(_doubleSlashStart.test(e.path))throw new Error('[UriError]: If a URI does not contain an authority component, then the path cannot begin with two slash characters ("//")')}}function _schemeFix(e,t){return!e&&!t?"file":e}function _referenceResolution(e,t){switch(e){case"https":case"http":case"file":t?t[0]!==_slash&&(t=_slash+t):t=_slash;break}return t}var _empty="",_slash="/",_regexp=/^(([^:/?#]+?):)?(\/\/([^/?#]*))?([^?#]*)(\?([^#]*))?(#(.*))?/,URI=class ge{static isUri(t){return t instanceof ge?!0:t?typeof t.authority=="string"&&typeof t.fragment=="string"&&typeof t.path=="string"&&typeof t.query=="string"&&typeof t.scheme=="string"&&typeof t.fsPath=="string"&&typeof t.with=="function"&&typeof t.toString=="function":!1}constructor(t,n,r,s,i,a=!1){typeof t=="object"?(this.scheme=t.scheme||_empty,this.authority=t.authority||_empty,this.path=t.path||_empty,this.query=t.query||_empty,this.fragment=t.fragment||_empty):(this.scheme=_schemeFix(t,a),this.authority=n||_empty,this.path=_referenceResolution(this.scheme,r||_empty),this.query=s||_empty,this.fragment=i||_empty,_validateUri(this,a))}get fsPath(){return $Bc(this,!1)}with(t){if(!t)return this;let{scheme:n,authority:r,path:s,query:i,fragment:a}=t;return n===void 0?n=this.scheme:n===null&&(n=_empty),r===void 0?r=this.authority:r===null&&(r=_empty),s===void 0?s=this.path:s===null&&(s=_empty),i===void 0?i=this.query:i===null&&(i=_empty),a===void 0?a=this.fragment:a===null&&(a=_empty),n===this.scheme&&r===this.authority&&s===this.path&&i===this.query&&a===this.fragment?this:new Uri(n,r,s,i,a)}static parse(t,n=!1){const r=_regexp.exec(t);return r?new Uri(r[2]||_empty,percentDecode(r[4]||_empty),percentDecode(r[5]||_empty),percentDecode(r[7]||_empty),percentDecode(r[9]||_empty),n):new Uri(_empty,_empty,_empty,_empty,_empty)}static file(t){let n=_empty;if($l&&(t=t.replace(/\\/g,_slash)),t[0]===_slash&&t[1]===_slash){const r=t.indexOf(_slash,2);r===-1?(n=t.substring(2),t=_slash):(n=t.substring(2,r),t=t.substring(r)||_slash)}return new Uri("file",n,t,_empty,_empty)}static from(t,n){return new Uri(t.scheme,t.authority,t.path,t.query,t.fragment,n)}static joinPath(t,...n){if(!t.path)throw new Error("[UriError]: cannot call joinPath on URI without path");let r;return $l&&t.scheme==="file"?r=ge.file($kc.join($Bc(t,!0),...n)).path:r=$lc.join(t.path,...n),t.with({path:r})}toString(t=!1){return _asFormatted(this,t)}toJSON(){return this}static revive(t){if(t){if(t instanceof ge)return t;{const n=new Uri(t);return n._formatted=t.external??null,n._fsPath=t._sep===_pathSepMarker?t.fsPath??null:null,n}}else return t}[Symbol.for("debug.description")](){return`URI(${this.toString()})`}},_pathSepMarker=$l?1:void 0,Uri=class extends URI{constructor(){super(...arguments),this._formatted=null,this._fsPath=null}get fsPath(){return this._fsPath||(this._fsPath=$Bc(this,!1)),this._fsPath}toString(e=!1){return e?_asFormatted(this,!0):(this._formatted||(this._formatted=_asFormatted(this,!1)),this._formatted)}toJSON(){const e={$mid:1};return this._fsPath&&(e.fsPath=this._fsPath,e._sep=_pathSepMarker),this._formatted&&(e.external=this._formatted),this.path&&(e.path=this.path),this.scheme&&(e.scheme=this.scheme),this.authority&&(e.authority=this.authority),this.query&&(e.query=this.query),this.fragment&&(e.fragment=this.fragment),e}},encodeTable={58:"%3A",47:"%2F",63:"%3F",35:"%23",91:"%5B",93:"%5D",64:"%40",33:"%21",36:"%24",38:"%26",39:"%27",40:"%28",41:"%29",42:"%2A",43:"%2B",44:"%2C",59:"%3B",61:"%3D",32:"%20"};function encodeURIComponentFast(e,t,n){let r,s=-1;for(let i=0;i<e.length;i++){const a=e.charCodeAt(i);if(a>=97&&a<=122||a>=65&&a<=90||a>=48&&a<=57||a===45||a===46||a===95||a===126||t&&a===47||n&&a===91||n&&a===93||n&&a===58)s!==-1&&(r+=encodeURIComponent(e.substring(s,i)),s=-1),r!==void 0&&(r+=e.charAt(i));else{r===void 0&&(r=e.substr(0,i));const l=encodeTable[a];l!==void 0?(s!==-1&&(r+=encodeURIComponent(e.substring(s,i)),s=-1),r+=l):s===-1&&(s=i)}}return s!==-1&&(r+=encodeURIComponent(e.substring(s))),r!==void 0?r:e}function encodeURIComponentMinimal(e){let t;for(let n=0;n<e.length;n++){const r=e.charCodeAt(n);r===35||r===63?(t===void 0&&(t=e.substr(0,n)),t+=encodeTable[r]):t!==void 0&&(t+=e[n])}return t!==void 0?t:e}function $Bc(e,t){let n;return e.authority&&e.path.length>1&&e.scheme==="file"?n=`//${e.authority}${e.path}`:e.path.charCodeAt(0)===47&&(e.path.charCodeAt(1)>=65&&e.path.charCodeAt(1)<=90||e.path.charCodeAt(1)>=97&&e.path.charCodeAt(1)<=122)&&e.path.charCodeAt(2)===58?t?n=e.path.substr(1):n=e.path[1].toLowerCase()+e.path.substr(2):n=e.path,$l&&(n=n.replace(/\//g,"\\")),n}function _asFormatted(e,t){const n=t?encodeURIComponentMinimal:encodeURIComponentFast;let r="",{scheme:s,authority:i,path:a,query:l,fragment:u}=e;if(s&&(r+=s,r+=":"),(i||s==="file")&&(r+=_slash,r+=_slash),i){let o=i.indexOf("@");if(o!==-1){const h=i.substr(0,o);i=i.substr(o+1),o=h.lastIndexOf(":"),o===-1?r+=n(h,!1,!1):(r+=n(h.substr(0,o),!1,!1),r+=":",r+=n(h.substr(o+1),!1,!0)),r+="@"}i=i.toLowerCase(),o=i.lastIndexOf(":"),o===-1?r+=n(i,!1,!0):(r+=n(i.substr(0,o),!1,!0),r+=i.substr(o))}if(a){if(a.length>=3&&a.charCodeAt(0)===47&&a.charCodeAt(2)===58){const o=a.charCodeAt(1);o>=65&&o<=90&&(a=`/${String.fromCharCode(o+32)}:${a.substr(3)}`)}else if(a.length>=2&&a.charCodeAt(1)===58){const o=a.charCodeAt(0);o>=65&&o<=90&&(a=`${String.fromCharCode(o+32)}:${a.substr(2)}`)}r+=n(a,!0,!1)}return l&&(r+="?",r+=n(l,!1,!1)),u&&(r+="#",r+=t?u:encodeURIComponentFast(u,!1,!1)),r}function decodeURIComponentGraceful(e){try{return decodeURIComponent(e)}catch{return e.length>3?e.substr(0,3)+decodeURIComponentGraceful(e.substr(3)):e}}var _rEncodedAsHex=/(%[0-9A-Za-z][0-9A-Za-z])+/g;function percentDecode(e){return e.match(_rEncodedAsHex)?e.replace(_rEncodedAsHex,t=>decodeURIComponentGraceful(t)):e}var SelectionDirection;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(SelectionDirection||(SelectionDirection={}));var $2V=class j extends $mV{constructor(t,n,r,s){super(t,n,r,s),this.selectionStartLineNumber=t,this.selectionStartColumn=n,this.positionLineNumber=r,this.positionColumn=s}toString(){return"["+this.selectionStartLineNumber+","+this.selectionStartColumn+" -> "+this.positionLineNumber+","+this.positionColumn+"]"}equalsSelection(t){return j.selectionsEqual(this,t)}static selectionsEqual(t,n){return t.selectionStartLineNumber===n.selectionStartLineNumber&&t.selectionStartColumn===n.selectionStartColumn&&t.positionLineNumber===n.positionLineNumber&&t.positionColumn===n.positionColumn}getDirection(){return this.selectionStartLineNumber===this.startLineNumber&&this.selectionStartColumn===this.startColumn?0:1}setEndPosition(t,n){return this.getDirection()===0?new j(this.startLineNumber,this.startColumn,t,n):new j(t,n,this.startLineNumber,this.startColumn)}getPosition(){return new $lV(this.positionLineNumber,this.positionColumn)}getSelectionStart(){return new $lV(this.selectionStartLineNumber,this.selectionStartColumn)}setStartPosition(t,n){return this.getDirection()===0?new j(t,n,this.endLineNumber,this.endColumn):new j(this.endLineNumber,this.endColumn,t,n)}static fromPositions(t,n=t){return new j(t.lineNumber,t.column,n.lineNumber,n.column)}static fromRange(t,n){return n===0?new j(t.startLineNumber,t.startColumn,t.endLineNumber,t.endColumn):new j(t.endLineNumber,t.endColumn,t.startLineNumber,t.startColumn)}static liftSelection(t){return new j(t.selectionStartLineNumber,t.selectionStartColumn,t.positionLineNumber,t.positionColumn)}static selectionsArrEqual(t,n){if(t&&!n||!t&&n)return!1;if(!t&&!n)return!0;if(t.length!==n.length)return!1;for(let r=0,s=t.length;r<s;r++)if(!this.selectionsEqual(t[r],n[r]))return!1;return!0}static isISelection(t){return t&&typeof t.selectionStartLineNumber=="number"&&typeof t.selectionStartColumn=="number"&&typeof t.positionLineNumber=="number"&&typeof t.positionColumn=="number"}static createWithDirection(t,n,r,s,i){return i===0?new j(t,n,r,s):new j(r,s,t,n)}},_codiconFontCharacters=Object.create(null);function $vm(e,t){if($Wc(t)){const n=_codiconFontCharacters[t];if(n===void 0)throw new Error(`${e} references an unknown codicon: ${t}`);t=n}return _codiconFontCharacters[e]=t,{id:e}}var $xm={add:$vm("add",6e4),plus:$vm("plus",6e4),gistNew:$vm("gist-new",6e4),repoCreate:$vm("repo-create",6e4),lightbulb:$vm("lightbulb",60001),lightBulb:$vm("light-bulb",60001),repo:$vm("repo",60002),repoDelete:$vm("repo-delete",60002),gistFork:$vm("gist-fork",60003),repoForked:$vm("repo-forked",60003),gitPullRequest:$vm("git-pull-request",60004),gitPullRequestAbandoned:$vm("git-pull-request-abandoned",60004),recordKeys:$vm("record-keys",60005),keyboard:$vm("keyboard",60005),tag:$vm("tag",60006),gitPullRequestLabel:$vm("git-pull-request-label",60006),tagAdd:$vm("tag-add",60006),tagRemove:$vm("tag-remove",60006),person:$vm("person",60007),personFollow:$vm("person-follow",60007),personOutline:$vm("person-outline",60007),personFilled:$vm("person-filled",60007),gitBranch:$vm("git-branch",60008),gitBranchCreate:$vm("git-branch-create",60008),gitBranchDelete:$vm("git-branch-delete",60008),sourceControl:$vm("source-control",60008),mirror:$vm("mirror",60009),mirrorPublic:$vm("mirror-public",60009),star:$vm("star",60010),starAdd:$vm("star-add",60010),starDelete:$vm("star-delete",60010),starEmpty:$vm("star-empty",60010),comment:$vm("comment",60011),commentAdd:$vm("comment-add",60011),alert:$vm("alert",60012),warning:$vm("warning",60012),search:$vm("search",60013),searchSave:$vm("search-save",60013),logOut:$vm("log-out",60014),signOut:$vm("sign-out",60014),logIn:$vm("log-in",60015),signIn:$vm("sign-in",60015),eye:$vm("eye",60016),eyeUnwatch:$vm("eye-unwatch",60016),eyeWatch:$vm("eye-watch",60016),circleFilled:$vm("circle-filled",60017),primitiveDot:$vm("primitive-dot",60017),closeDirty:$vm("close-dirty",60017),debugBreakpoint:$vm("debug-breakpoint",60017),debugBreakpointDisabled:$vm("debug-breakpoint-disabled",60017),debugHint:$vm("debug-hint",60017),terminalDecorationSuccess:$vm("terminal-decoration-success",60017),primitiveSquare:$vm("primitive-square",60018),edit:$vm("edit",60019),pencil:$vm("pencil",60019),info:$vm("info",60020),issueOpened:$vm("issue-opened",60020),gistPrivate:$vm("gist-private",60021),gitForkPrivate:$vm("git-fork-private",60021),lock:$vm("lock",60021),mirrorPrivate:$vm("mirror-private",60021),close:$vm("close",60022),removeClose:$vm("remove-close",60022),x:$vm("x",60022),repoSync:$vm("repo-sync",60023),sync:$vm("sync",60023),clone:$vm("clone",60024),desktopDownload:$vm("desktop-download",60024),beaker:$vm("beaker",60025),microscope:$vm("microscope",60025),vm:$vm("vm",60026),deviceDesktop:$vm("device-desktop",60026),file:$vm("file",60027),fileText:$vm("file-text",60027),more:$vm("more",60028),ellipsis:$vm("ellipsis",60028),kebabHorizontal:$vm("kebab-horizontal",60028),mailReply:$vm("mail-reply",60029),reply:$vm("reply",60029),organization:$vm("organization",60030),organizationFilled:$vm("organization-filled",60030),organizationOutline:$vm("organization-outline",60030),newFile:$vm("new-file",60031),fileAdd:$vm("file-add",60031),newFolder:$vm("new-folder",60032),fileDirectoryCreate:$vm("file-directory-create",60032),trash:$vm("trash",60033),trashcan:$vm("trashcan",60033),history:$vm("history",60034),clock:$vm("clock",60034),folder:$vm("folder",60035),fileDirectory:$vm("file-directory",60035),symbolFolder:$vm("symbol-folder",60035),logoGithub:$vm("logo-github",60036),markGithub:$vm("mark-github",60036),github:$vm("github",60036),terminal:$vm("terminal",60037),console:$vm("console",60037),repl:$vm("repl",60037),zap:$vm("zap",60038),symbolEvent:$vm("symbol-event",60038),error:$vm("error",60039),stop:$vm("stop",60039),variable:$vm("variable",60040),symbolVariable:$vm("symbol-variable",60040),array:$vm("array",60042),symbolArray:$vm("symbol-array",60042),symbolModule:$vm("symbol-module",60043),symbolPackage:$vm("symbol-package",60043),symbolNamespace:$vm("symbol-namespace",60043),symbolObject:$vm("symbol-object",60043),symbolMethod:$vm("symbol-method",60044),symbolFunction:$vm("symbol-function",60044),symbolConstructor:$vm("symbol-constructor",60044),symbolBoolean:$vm("symbol-boolean",60047),symbolNull:$vm("symbol-null",60047),symbolNumeric:$vm("symbol-numeric",60048),symbolNumber:$vm("symbol-number",60048),symbolStructure:$vm("symbol-structure",60049),symbolStruct:$vm("symbol-struct",60049),symbolParameter:$vm("symbol-parameter",60050),symbolTypeParameter:$vm("symbol-type-parameter",60050),symbolKey:$vm("symbol-key",60051),symbolText:$vm("symbol-text",60051),symbolReference:$vm("symbol-reference",60052),goToFile:$vm("go-to-file",60052),symbolEnum:$vm("symbol-enum",60053),symbolValue:$vm("symbol-value",60053),symbolRuler:$vm("symbol-ruler",60054),symbolUnit:$vm("symbol-unit",60054),activateBreakpoints:$vm("activate-breakpoints",60055),archive:$vm("archive",60056),arrowBoth:$vm("arrow-both",60057),arrowDown:$vm("arrow-down",60058),arrowLeft:$vm("arrow-left",60059),arrowRight:$vm("arrow-right",60060),arrowSmallDown:$vm("arrow-small-down",60061),arrowSmallLeft:$vm("arrow-small-left",60062),arrowSmallRight:$vm("arrow-small-right",60063),arrowSmallUp:$vm("arrow-small-up",60064),arrowUp:$vm("arrow-up",60065),bell:$vm("bell",60066),bold:$vm("bold",60067),book:$vm("book",60068),bookmark:$vm("bookmark",60069),debugBreakpointConditionalUnverified:$vm("debug-breakpoint-conditional-unverified",60070),debugBreakpointConditional:$vm("debug-breakpoint-conditional",60071),debugBreakpointConditionalDisabled:$vm("debug-breakpoint-conditional-disabled",60071),debugBreakpointDataUnverified:$vm("debug-breakpoint-data-unverified",60072),debugBreakpointData:$vm("debug-breakpoint-data",60073),debugBreakpointDataDisabled:$vm("debug-breakpoint-data-disabled",60073),debugBreakpointLogUnverified:$vm("debug-breakpoint-log-unverified",60074),debugBreakpointLog:$vm("debug-breakpoint-log",60075),debugBreakpointLogDisabled:$vm("debug-breakpoint-log-disabled",60075),briefcase:$vm("briefcase",60076),broadcast:$vm("broadcast",60077),browser:$vm("browser",60078),bug:$vm("bug",60079),calendar:$vm("calendar",60080),caseSensitive:$vm("case-sensitive",60081),check:$vm("check",60082),checklist:$vm("checklist",60083),chevronDown:$vm("chevron-down",60084),chevronLeft:$vm("chevron-left",60085),chevronRight:$vm("chevron-right",60086),chevronUp:$vm("chevron-up",60087),chromeClose:$vm("chrome-close",60088),chromeMaximize:$vm("chrome-maximize",60089),chromeMinimize:$vm("chrome-minimize",60090),chromeRestore:$vm("chrome-restore",60091),circleOutline:$vm("circle-outline",60092),circle:$vm("circle",60092),debugBreakpointUnverified:$vm("debug-breakpoint-unverified",60092),terminalDecorationIncomplete:$vm("terminal-decoration-incomplete",60092),circleSlash:$vm("circle-slash",60093),circuitBoard:$vm("circuit-board",60094),clearAll:$vm("clear-all",60095),clippy:$vm("clippy",60096),closeAll:$vm("close-all",60097),cloudDownload:$vm("cloud-download",60098),cloudUpload:$vm("cloud-upload",60099),code:$vm("code",60100),collapseAll:$vm("collapse-all",60101),colorMode:$vm("color-mode",60102),commentDiscussion:$vm("comment-discussion",60103),creditCard:$vm("credit-card",60105),dash:$vm("dash",60108),dashboard:$vm("dashboard",60109),database:$vm("database",60110),debugContinue:$vm("debug-continue",60111),debugDisconnect:$vm("debug-disconnect",60112),debugPause:$vm("debug-pause",60113),debugRestart:$vm("debug-restart",60114),debugStart:$vm("debug-start",60115),debugStepInto:$vm("debug-step-into",60116),debugStepOut:$vm("debug-step-out",60117),debugStepOver:$vm("debug-step-over",60118),debugStop:$vm("debug-stop",60119),debug:$vm("debug",60120),deviceCameraVideo:$vm("device-camera-video",60121),deviceCamera:$vm("device-camera",60122),deviceMobile:$vm("device-mobile",60123),diffAdded:$vm("diff-added",60124),diffIgnored:$vm("diff-ignored",60125),diffModified:$vm("diff-modified",60126),diffRemoved:$vm("diff-removed",60127),diffRenamed:$vm("diff-renamed",60128),diff:$vm("diff",60129),diffSidebyside:$vm("diff-sidebyside",60129),discard:$vm("discard",60130),editorLayout:$vm("editor-layout",60131),emptyWindow:$vm("empty-window",60132),exclude:$vm("exclude",60133),extensions:$vm("extensions",60134),eyeClosed:$vm("eye-closed",60135),fileBinary:$vm("file-binary",60136),fileCode:$vm("file-code",60137),fileMedia:$vm("file-media",60138),filePdf:$vm("file-pdf",60139),fileSubmodule:$vm("file-submodule",60140),fileSymlinkDirectory:$vm("file-symlink-directory",60141),fileSymlinkFile:$vm("file-symlink-file",60142),fileZip:$vm("file-zip",60143),files:$vm("files",60144),filter:$vm("filter",60145),flame:$vm("flame",60146),foldDown:$vm("fold-down",60147),foldUp:$vm("fold-up",60148),fold:$vm("fold",60149),folderActive:$vm("folder-active",60150),folderOpened:$vm("folder-opened",60151),gear:$vm("gear",60152),gift:$vm("gift",60153),gistSecret:$vm("gist-secret",60154),gist:$vm("gist",60155),gitCommit:$vm("git-commit",60156),gitCompare:$vm("git-compare",60157),compareChanges:$vm("compare-changes",60157),gitMerge:$vm("git-merge",60158),githubAction:$vm("github-action",60159),githubAlt:$vm("github-alt",60160),globe:$vm("globe",60161),grabber:$vm("grabber",60162),graph:$vm("graph",60163),gripper:$vm("gripper",60164),heart:$vm("heart",60165),home:$vm("home",60166),horizontalRule:$vm("horizontal-rule",60167),hubot:$vm("hubot",60168),inbox:$vm("inbox",60169),issueReopened:$vm("issue-reopened",60171),issues:$vm("issues",60172),italic:$vm("italic",60173),jersey:$vm("jersey",60174),json:$vm("json",60175),kebabVertical:$vm("kebab-vertical",60176),key:$vm("key",60177),law:$vm("law",60178),lightbulbAutofix:$vm("lightbulb-autofix",60179),linkExternal:$vm("link-external",60180),link:$vm("link",60181),listOrdered:$vm("list-ordered",60182),listUnordered:$vm("list-unordered",60183),liveShare:$vm("live-share",60184),loading:$vm("loading",60185),location:$vm("location",60186),mailRead:$vm("mail-read",60187),mail:$vm("mail",60188),markdown:$vm("markdown",60189),megaphone:$vm("megaphone",60190),mention:$vm("mention",60191),milestone:$vm("milestone",60192),gitPullRequestMilestone:$vm("git-pull-request-milestone",60192),mortarBoard:$vm("mortar-board",60193),move:$vm("move",60194),multipleWindows:$vm("multiple-windows",60195),mute:$vm("mute",60196),noNewline:$vm("no-newline",60197),note:$vm("note",60198),octoface:$vm("octoface",60199),openPreview:$vm("open-preview",60200),package:$vm("package",60201),paintcan:$vm("paintcan",60202),pin:$vm("pin",60203),play:$vm("play",60204),run:$vm("run",60204),plug:$vm("plug",60205),preserveCase:$vm("preserve-case",60206),preview:$vm("preview",60207),project:$vm("project",60208),pulse:$vm("pulse",60209),question:$vm("question",60210),quote:$vm("quote",60211),radioTower:$vm("radio-tower",60212),reactions:$vm("reactions",60213),references:$vm("references",60214),refresh:$vm("refresh",60215),regex:$vm("regex",60216),remoteExplorer:$vm("remote-explorer",60217),remote:$vm("remote",60218),remove:$vm("remove",60219),replaceAll:$vm("replace-all",60220),replace:$vm("replace",60221),repoClone:$vm("repo-clone",60222),repoForcePush:$vm("repo-force-push",60223),repoPull:$vm("repo-pull",60224),repoPush:$vm("repo-push",60225),report:$vm("report",60226),requestChanges:$vm("request-changes",60227),rocket:$vm("rocket",60228),rootFolderOpened:$vm("root-folder-opened",60229),rootFolder:$vm("root-folder",60230),rss:$vm("rss",60231),ruby:$vm("ruby",60232),saveAll:$vm("save-all",60233),saveAs:$vm("save-as",60234),save:$vm("save",60235),screenFull:$vm("screen-full",60236),screenNormal:$vm("screen-normal",60237),searchStop:$vm("search-stop",60238),server:$vm("server",60240),settingsGear:$vm("settings-gear",60241),settings:$vm("settings",60242),shield:$vm("shield",60243),smiley:$vm("smiley",60244),sortPrecedence:$vm("sort-precedence",60245),splitHorizontal:$vm("split-horizontal",60246),splitVertical:$vm("split-vertical",60247),squirrel:$vm("squirrel",60248),starFull:$vm("star-full",60249),starHalf:$vm("star-half",60250),symbolClass:$vm("symbol-class",60251),symbolColor:$vm("symbol-color",60252),symbolConstant:$vm("symbol-constant",60253),symbolEnumMember:$vm("symbol-enum-member",60254),symbolField:$vm("symbol-field",60255),symbolFile:$vm("symbol-file",60256),symbolInterface:$vm("symbol-interface",60257),symbolKeyword:$vm("symbol-keyword",60258),symbolMisc:$vm("symbol-misc",60259),symbolOperator:$vm("symbol-operator",60260),symbolProperty:$vm("symbol-property",60261),wrench:$vm("wrench",60261),wrenchSubaction:$vm("wrench-subaction",60261),symbolSnippet:$vm("symbol-snippet",60262),tasklist:$vm("tasklist",60263),telescope:$vm("telescope",60264),textSize:$vm("text-size",60265),threeBars:$vm("three-bars",60266),thumbsdown:$vm("thumbsdown",60267),thumbsup:$vm("thumbsup",60268),tools:$vm("tools",60269),triangleDown:$vm("triangle-down",60270),triangleLeft:$vm("triangle-left",60271),triangleRight:$vm("triangle-right",60272),triangleUp:$vm("triangle-up",60273),twitter:$vm("twitter",60274),unfold:$vm("unfold",60275),unlock:$vm("unlock",60276),unmute:$vm("unmute",60277),unverified:$vm("unverified",60278),verified:$vm("verified",60279),versions:$vm("versions",60280),vmActive:$vm("vm-active",60281),vmOutline:$vm("vm-outline",60282),vmRunning:$vm("vm-running",60283),watch:$vm("watch",60284),whitespace:$vm("whitespace",60285),wholeWord:$vm("whole-word",60286),window:$vm("window",60287),wordWrap:$vm("word-wrap",60288),zoomIn:$vm("zoom-in",60289),zoomOut:$vm("zoom-out",60290),listFilter:$vm("list-filter",60291),listFlat:$vm("list-flat",60292),listSelection:$vm("list-selection",60293),selection:$vm("selection",60293),listTree:$vm("list-tree",60294),debugBreakpointFunctionUnverified:$vm("debug-breakpoint-function-unverified",60295),debugBreakpointFunction:$vm("debug-breakpoint-function",60296),debugBreakpointFunctionDisabled:$vm("debug-breakpoint-function-disabled",60296),debugStackframeActive:$vm("debug-stackframe-active",60297),circleSmallFilled:$vm("circle-small-filled",60298),debugStackframeDot:$vm("debug-stackframe-dot",60298),terminalDecorationMark:$vm("terminal-decoration-mark",60298),debugStackframe:$vm("debug-stackframe",60299),debugStackframeFocused:$vm("debug-stackframe-focused",60299),debugBreakpointUnsupported:$vm("debug-breakpoint-unsupported",60300),symbolString:$vm("symbol-string",60301),debugReverseContinue:$vm("debug-reverse-continue",60302),debugStepBack:$vm("debug-step-back",60303),debugRestartFrame:$vm("debug-restart-frame",60304),debugAlt:$vm("debug-alt",60305),callIncoming:$vm("call-incoming",60306),callOutgoing:$vm("call-outgoing",60307),menu:$vm("menu",60308),expandAll:$vm("expand-all",60309),feedback:$vm("feedback",60310),gitPullRequestReviewer:$vm("git-pull-request-reviewer",60310),groupByRefType:$vm("group-by-ref-type",60311),ungroupByRefType:$vm("ungroup-by-ref-type",60312),account:$vm("account",60313),gitPullRequestAssignee:$vm("git-pull-request-assignee",60313),bellDot:$vm("bell-dot",60314),debugConsole:$vm("debug-console",60315),library:$vm("library",60316),output:$vm("output",60317),runAll:$vm("run-all",60318),syncIgnored:$vm("sync-ignored",60319),pinned:$vm("pinned",60320),githubInverted:$vm("github-inverted",60321),serverProcess:$vm("server-process",60322),serverEnvironment:$vm("server-environment",60323),pass:$vm("pass",60324),issueClosed:$vm("issue-closed",60324),stopCircle:$vm("stop-circle",60325),playCircle:$vm("play-circle",60326),record:$vm("record",60327),debugAltSmall:$vm("debug-alt-small",60328),vmConnect:$vm("vm-connect",60329),cloud:$vm("cloud",60330),merge:$vm("merge",60331),export:$vm("export",60332),graphLeft:$vm("graph-left",60333),magnet:$vm("magnet",60334),notebook:$vm("notebook",60335),redo:$vm("redo",60336),checkAll:$vm("check-all",60337),pinnedDirty:$vm("pinned-dirty",60338),passFilled:$vm("pass-filled",60339),circleLargeFilled:$vm("circle-large-filled",60340),circleLarge:$vm("circle-large",60341),circleLargeOutline:$vm("circle-large-outline",60341),combine:$vm("combine",60342),gather:$vm("gather",60342),table:$vm("table",60343),variableGroup:$vm("variable-group",60344),typeHierarchy:$vm("type-hierarchy",60345),typeHierarchySub:$vm("type-hierarchy-sub",60346),typeHierarchySuper:$vm("type-hierarchy-super",60347),gitPullRequestCreate:$vm("git-pull-request-create",60348),runAbove:$vm("run-above",60349),runBelow:$vm("run-below",60350),notebookTemplate:$vm("notebook-template",60351),debugRerun:$vm("debug-rerun",60352),workspaceTrusted:$vm("workspace-trusted",60353),workspaceUntrusted:$vm("workspace-untrusted",60354),workspaceUnknown:$vm("workspace-unknown",60355),terminalCmd:$vm("terminal-cmd",60356),terminalDebian:$vm("terminal-debian",60357),terminalLinux:$vm("terminal-linux",60358),terminalPowershell:$vm("terminal-powershell",60359),terminalTmux:$vm("terminal-tmux",60360),terminalUbuntu:$vm("terminal-ubuntu",60361),terminalBash:$vm("terminal-bash",60362),arrowSwap:$vm("arrow-swap",60363),copy:$vm("copy",60364),personAdd:$vm("person-add",60365),filterFilled:$vm("filter-filled",60366),wand:$vm("wand",60367),debugLineByLine:$vm("debug-line-by-line",60368),inspect:$vm("inspect",60369),layers:$vm("layers",60370),layersDot:$vm("layers-dot",60371),layersActive:$vm("layers-active",60372),compass:$vm("compass",60373),compassDot:$vm("compass-dot",60374),compassActive:$vm("compass-active",60375),azure:$vm("azure",60376),issueDraft:$vm("issue-draft",60377),gitPullRequestClosed:$vm("git-pull-request-closed",60378),gitPullRequestDraft:$vm("git-pull-request-draft",60379),debugAll:$vm("debug-all",60380),debugCoverage:$vm("debug-coverage",60381),runErrors:$vm("run-errors",60382),folderLibrary:$vm("folder-library",60383),debugContinueSmall:$vm("debug-continue-small",60384),beakerStop:$vm("beaker-stop",60385),graphLine:$vm("graph-line",60386),graphScatter:$vm("graph-scatter",60387),pieChart:$vm("pie-chart",60388),bracket:$vm("bracket",60175),bracketDot:$vm("bracket-dot",60389),bracketError:$vm("bracket-error",60390),lockSmall:$vm("lock-small",60391),azureDevops:$vm("azure-devops",60392),verifiedFilled:$vm("verified-filled",60393),newline:$vm("newline",60394),layout:$vm("layout",60395),layoutActivitybarLeft:$vm("layout-activitybar-left",60396),layoutActivitybarRight:$vm("layout-activitybar-right",60397),layoutPanelLeft:$vm("layout-panel-left",60398),layoutPanelCenter:$vm("layout-panel-center",60399),layoutPanelJustify:$vm("layout-panel-justify",60400),layoutPanelRight:$vm("layout-panel-right",60401),layoutPanel:$vm("layout-panel",60402),layoutSidebarLeft:$vm("layout-sidebar-left",60403),layoutSidebarRight:$vm("layout-sidebar-right",60404),layoutStatusbar:$vm("layout-statusbar",60405),layoutMenubar:$vm("layout-menubar",60406),layoutCentered:$vm("layout-centered",60407),target:$vm("target",60408),indent:$vm("indent",60409),recordSmall:$vm("record-small",60410),errorSmall:$vm("error-small",60411),terminalDecorationError:$vm("terminal-decoration-error",60411),arrowCircleDown:$vm("arrow-circle-down",60412),arrowCircleLeft:$vm("arrow-circle-left",60413),arrowCircleRight:$vm("arrow-circle-right",60414),arrowCircleUp:$vm("arrow-circle-up",60415),layoutSidebarRightOff:$vm("layout-sidebar-right-off",60416),layoutPanelOff:$vm("layout-panel-off",60417),layoutSidebarLeftOff:$vm("layout-sidebar-left-off",60418),blank:$vm("blank",60419),heartFilled:$vm("heart-filled",60420),map:$vm("map",60421),mapHorizontal:$vm("map-horizontal",60421),foldHorizontal:$vm("fold-horizontal",60421),mapFilled:$vm("map-filled",60422),mapHorizontalFilled:$vm("map-horizontal-filled",60422),foldHorizontalFilled:$vm("fold-horizontal-filled",60422),circleSmall:$vm("circle-small",60423),bellSlash:$vm("bell-slash",60424),bellSlashDot:$vm("bell-slash-dot",60425),commentUnresolved:$vm("comment-unresolved",60426),gitPullRequestGoToChanges:$vm("git-pull-request-go-to-changes",60427),gitPullRequestNewChanges:$vm("git-pull-request-new-changes",60428),searchFuzzy:$vm("search-fuzzy",60429),commentDraft:$vm("comment-draft",60430),send:$vm("send",60431),sparkle:$vm("sparkle",60432),insert:$vm("insert",60433),mic:$vm("mic",60434),thumbsdownFilled:$vm("thumbsdown-filled",60435),thumbsupFilled:$vm("thumbsup-filled",60436),coffee:$vm("coffee",60437),snake:$vm("snake",60438),game:$vm("game",60439),vr:$vm("vr",60440),chip:$vm("chip",60441),piano:$vm("piano",60442),music:$vm("music",60443),micFilled:$vm("mic-filled",60444),repoFetch:$vm("repo-fetch",60445),copilot:$vm("copilot",60446),lightbulbSparkle:$vm("lightbulb-sparkle",60447),robot:$vm("robot",60448),sparkleFilled:$vm("sparkle-filled",60449),diffSingle:$vm("diff-single",60450),diffMultiple:$vm("diff-multiple",60451),surroundWith:$vm("surround-with",60452),share:$vm("share",60453),gitStash:$vm("git-stash",60454),gitStashApply:$vm("git-stash-apply",60455),gitStashPop:$vm("git-stash-pop",60456),vscode:$vm("vscode",60457),vscodeInsiders:$vm("vscode-insiders",60458),codeOss:$vm("code-oss",60459),runCoverage:$vm("run-coverage",60460),runAllCoverage:$vm("run-all-coverage",60461),coverage:$vm("coverage",60462),githubProject:$vm("github-project",60463),mapVertical:$vm("map-vertical",60464),foldVertical:$vm("fold-vertical",60464),mapVerticalFilled:$vm("map-vertical-filled",60465),foldVerticalFilled:$vm("fold-vertical-filled",60465),goToSearch:$vm("go-to-search",60466),percentage:$vm("percentage",60467),sortPercentage:$vm("sort-percentage",60467),attach:$vm("attach",60468),goToEditingSession:$vm("go-to-editing-session",60469),editSession:$vm("edit-session",60470),codeReview:$vm("code-review",60471),copilotWarning:$vm("copilot-warning",60472),python:$vm("python",60473),copilotLarge:$vm("copilot-large",60474),copilotWarningLarge:$vm("copilot-warning-large",60475),keyboardTab:$vm("keyboard-tab",60476),copilotBlocked:$vm("copilot-blocked",60477),copilotNotConnected:$vm("copilot-not-connected",60478),flag:$vm("flag",60479),lightbulbEmpty:$vm("lightbulb-empty",60480),symbolMethodArrow:$vm("symbol-method-arrow",60481),copilotUnavailable:$vm("copilot-unavailable",60482),repoPinned:$vm("repo-pinned",60483),keyboardTabAbove:$vm("keyboard-tab-above",60484),keyboardTabBelow:$vm("keyboard-tab-below",60485),gitPullRequestDone:$vm("git-pull-request-done",60486),mcp:$vm("mcp",60487),extensionsLarge:$vm("extensions-large",60488),layoutPanelDock:$vm("layout-panel-dock",60489),layoutSidebarLeftDock:$vm("layout-sidebar-left-dock",60490),layoutSidebarRightDock:$vm("layout-sidebar-right-dock",60491),cascade:$vm("cascade",63742),cascadeBrowser:$vm("cascade-browser",63743)},$zm={dialogError:$vm("dialog-error","error"),dialogWarning:$vm("dialog-warning","warning"),dialogInfo:$vm("dialog-info","info"),dialogClose:$vm("dialog-close","close"),treeItemExpanded:$vm("tree-item-expanded","chevron-down"),treeFilterOnTypeOn:$vm("tree-filter-on-type-on","list-filter"),treeFilterOnTypeOff:$vm("tree-filter-on-type-off","list-selection"),treeFilterClear:$vm("tree-filter-clear","close"),treeItemLoading:$vm("tree-item-loading","loading"),menuSelection:$vm("menu-selection","check"),menuSubmenu:$vm("menu-submenu","chevron-right"),menuBarMore:$vm("menubar-more","more"),scrollbarButtonLeft:$vm("scrollbar-button-left","triangle-left"),scrollbarButtonRight:$vm("scrollbar-button-right","triangle-right"),scrollbarButtonUp:$vm("scrollbar-button-up","triangle-up"),scrollbarButtonDown:$vm("scrollbar-button-down","triangle-down"),toolBarMore:$vm("toolbar-more","more"),quickInputBack:$vm("quick-input-back","arrow-left"),dropDownButton:$vm("drop-down-button",60084),symbolCustomColor:$vm("symbol-customcolor",60252),exportIcon:$vm("export",60332),workspaceUnspecified:$vm("workspace-unspecified",60355),newLine:$vm("newline",60394),thumbsDownFilled:$vm("thumbsdown-filled",60435),thumbsUpFilled:$vm("thumbsup-filled",60436),gitFetch:$vm("git-fetch",60445),lightbulbSparkleAutofix:$vm("lightbulb-sparkle-autofix",60447),debugBreakpointPending:$vm("debug-breakpoint-pending",60377)},$Am={...$xm,...$zm},$jW=class{constructor(){this.a=new Map,this.b=new Map,this.c=new $0e,this.onDidChange=this.c.event,this.d=null}handleChange(e){this.c.fire({changedLanguages:e,changedColorMap:!1})}register(e,t){return this.a.set(e,t),this.handleChange([e]),$qd(()=>{this.a.get(e)===t&&(this.a.delete(e),this.handleChange([e]))})}get(e){return this.a.get(e)||null}registerFactory(e,t){this.b.get(e)?.dispose();const n=new TokenizationSupportFactoryData(this,e,t);return this.b.set(e,n),$qd(()=>{const r=this.b.get(e);!r||r!==n||(this.b.delete(e),r.dispose())})}async getOrCreate(e){const t=this.get(e);if(t)return t;const n=this.b.get(e);return!n||n.isResolved?null:(await n.resolve(),this.get(e))}isResolved(e){if(this.get(e))return!0;const n=this.b.get(e);return!!(!n||n.isResolved)}setColorMap(e){this.d=e,this.c.fire({changedLanguages:Array.from(this.a.keys()),changedColorMap:!0})}getColorMap(){return this.d}getDefaultBackground(){return this.d&&this.d.length>2?this.d[2]:null}},TokenizationSupportFactoryData=class extends $sd{get isResolved(){return this.c}constructor(e,t,n){super(),this.f=e,this.g=t,this.h=n,this.a=!1,this.b=null,this.c=!1}dispose(){this.a=!0,super.dispose()}async resolve(){return this.b||(this.b=this.j()),this.b}async j(){const e=await this.h.tokenizationSupport;this.c=!0,e&&!this.a&&this.B(this.f.register(this.g,e))}},$RW=class{constructor(e,t,n){this.offset=e,this.type=t,this.language=n,this._tokenBrand=void 0}toString(){return"("+this.offset+", "+this.type+")"}},HoverVerbosityAction;(function(e){e[e.Increase=0]="Increase",e[e.Decrease=1]="Decrease"})(HoverVerbosityAction||(HoverVerbosityAction={}));var CompletionItemKind;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"})(CompletionItemKind||(CompletionItemKind={}));var CompletionItemKinds;(function(e){const t=new Map;t.set(0,$Am.symbolMethod),t.set(1,$Am.symbolFunction),t.set(2,$Am.symbolConstructor),t.set(3,$Am.symbolField),t.set(4,$Am.symbolVariable),t.set(5,$Am.symbolClass),t.set(6,$Am.symbolStruct),t.set(7,$Am.symbolInterface),t.set(8,$Am.symbolModule),t.set(9,$Am.symbolProperty),t.set(10,$Am.symbolEvent),t.set(11,$Am.symbolOperator),t.set(12,$Am.symbolUnit),t.set(13,$Am.symbolValue),t.set(15,$Am.symbolEnum),t.set(14,$Am.symbolConstant),t.set(15,$Am.symbolEnum),t.set(16,$Am.symbolEnumMember),t.set(17,$Am.symbolKeyword),t.set(27,$Am.symbolSnippet),t.set(18,$Am.symbolText),t.set(19,$Am.symbolColor),t.set(20,$Am.symbolFile),t.set(21,$Am.symbolReference),t.set(22,$Am.symbolCustomColor),t.set(23,$Am.symbolFolder),t.set(24,$Am.symbolTypeParameter),t.set(25,$Am.account),t.set(26,$Am.issues);function n(a){let l=t.get(a);return l||(console.info("No codicon found for CompletionItemKind "+a),l=$Am.symbolProperty),l}e.toIcon=n;function r(a){switch(a){case 0:return localize(825,null);case 1:return localize(826,null);case 2:return localize(827,null);case 3:return localize(828,null);case 4:return localize(829,null);case 5:return localize(830,null);case 6:return localize(831,null);case 7:return localize(832,null);case 8:return localize(833,null);case 9:return localize(834,null);case 10:return localize(835,null);case 11:return localize(836,null);case 12:return localize(837,null);case 13:return localize(838,null);case 14:return localize(839,null);case 15:return localize(840,null);case 16:return localize(841,null);case 17:return localize(842,null);case 18:return localize(843,null);case 19:return localize(844,null);case 20:return localize(845,null);case 21:return localize(846,null);case 22:return localize(847,null);case 23:return localize(848,null);case 24:return localize(849,null);case 25:return localize(850,null);case 26:return localize(851,null);case 27:return localize(852,null);default:return""}}e.toLabel=r;const s=new Map;s.set("method",0),s.set("function",1),s.set("constructor",2),s.set("field",3),s.set("variable",4),s.set("class",5),s.set("struct",6),s.set("interface",7),s.set("module",8),s.set("property",9),s.set("event",10),s.set("operator",11),s.set("unit",12),s.set("value",13),s.set("constant",14),s.set("enum",15),s.set("enum-member",16),s.set("enumMember",16),s.set("keyword",17),s.set("snippet",27),s.set("text",18),s.set("color",19),s.set("file",20),s.set("reference",21),s.set("customcolor",22),s.set("folder",23),s.set("type-parameter",24),s.set("typeParameter",24),s.set("account",25),s.set("issue",26);function i(a,l){let u=s.get(a);return typeof u>"u"&&!l&&(u=9),u}e.fromString=i})(CompletionItemKinds||(CompletionItemKinds={}));var CompletionItemTag;(function(e){e[e.Deprecated=1]="Deprecated"})(CompletionItemTag||(CompletionItemTag={}));var CompletionItemInsertTextRule;(function(e){e[e.None=0]="None",e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(CompletionItemInsertTextRule||(CompletionItemInsertTextRule={}));var PartialAcceptTriggerKind;(function(e){e[e.Word=0]="Word",e[e.Line=1]="Line",e[e.Suggest=2]="Suggest"})(PartialAcceptTriggerKind||(PartialAcceptTriggerKind={}));var CompletionTriggerKind;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(CompletionTriggerKind||(CompletionTriggerKind={}));var InlineCompletionTriggerKind;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit",e[e.WithCursorInSuggestedActionRange=2]="WithCursorInSuggestedActionRange"})(InlineCompletionTriggerKind||(InlineCompletionTriggerKind={}));var CodeActionTriggerType;(function(e){e[e.Invoke=1]="Invoke",e[e.Auto=2]="Auto"})(CodeActionTriggerType||(CodeActionTriggerType={}));var DocumentPasteTriggerKind;(function(e){e[e.Automatic=0]="Automatic",e[e.PasteAs=1]="PasteAs"})(DocumentPasteTriggerKind||(DocumentPasteTriggerKind={}));var SignatureHelpTriggerKind;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(SignatureHelpTriggerKind||(SignatureHelpTriggerKind={}));var DocumentHighlightKind;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(DocumentHighlightKind||(DocumentHighlightKind={}));var SymbolKind;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})(SymbolKind||(SymbolKind={}));var $YW={17:localize(853,null),16:localize(854,null),4:localize(855,null),13:localize(856,null),8:localize(857,null),9:localize(858,null),21:localize(859,null),23:localize(860,null),7:localize(861,null),0:localize(862,null),11:localize(863,null),10:localize(864,null),19:localize(865,null),5:localize(866,null),1:localize(867,null),2:localize(868,null),20:localize(869,null),15:localize(870,null),18:localize(871,null),24:localize(872,null),3:localize(873,null),6:localize(874,null),14:localize(875,null),22:localize(876,null),25:localize(877,null),12:localize(878,null)},SymbolTag;(function(e){e[e.Deprecated=1]="Deprecated"})(SymbolTag||(SymbolTag={}));var SymbolKinds;(function(e){const t=new Map;t.set(0,$Am.symbolFile),t.set(1,$Am.symbolModule),t.set(2,$Am.symbolNamespace),t.set(3,$Am.symbolPackage),t.set(4,$Am.symbolClass),t.set(5,$Am.symbolMethod),t.set(6,$Am.symbolProperty),t.set(7,$Am.symbolField),t.set(8,$Am.symbolConstructor),t.set(9,$Am.symbolEnum),t.set(10,$Am.symbolInterface),t.set(11,$Am.symbolFunction),t.set(12,$Am.symbolVariable),t.set(13,$Am.symbolConstant),t.set(14,$Am.symbolString),t.set(15,$Am.symbolNumber),t.set(16,$Am.symbolBoolean),t.set(17,$Am.symbolArray),t.set(18,$Am.symbolObject),t.set(19,$Am.symbolKey),t.set(20,$Am.symbolNull),t.set(21,$Am.symbolEnumMember),t.set(22,$Am.symbolStruct),t.set(23,$Am.symbolEvent),t.set(24,$Am.symbolOperator),t.set(25,$Am.symbolTypeParameter);function n(i){let a=t.get(i);return a||(console.info("No codicon found for SymbolKind "+i),a=$Am.symbolProperty),a}e.toIcon=n;const r=new Map;r.set(0,20),r.set(1,8),r.set(2,8),r.set(3,8),r.set(4,5),r.set(5,0),r.set(6,9),r.set(7,3),r.set(8,2),r.set(9,15),r.set(10,7),r.set(11,1),r.set(12,4),r.set(13,14),r.set(14,18),r.set(15,13),r.set(16,13),r.set(17,13),r.set(18,13),r.set(19,17),r.set(20,13),r.set(21,16),r.set(22,6),r.set(23,10),r.set(24,11),r.set(25,24);function s(i){let a=r.get(i);return a===void 0&&(console.info("No completion kind found for SymbolKind "+i),a=20),a}e.toCompletionKind=s})(SymbolKinds||(SymbolKinds={}));var $2W=class C{static{this.Comment=new C("comment")}static{this.Imports=new C("imports")}static{this.Region=new C("region")}static fromValue(t){switch(t){case"comment":return C.Comment;case"imports":return C.Imports;case"region":return C.Region}return new C(t)}constructor(t){this.value=t}},NewSymbolNameTag;(function(e){e[e.AIGenerated=1]="AIGenerated"})(NewSymbolNameTag||(NewSymbolNameTag={}));var NewSymbolNameTriggerKind;(function(e){e[e.Invoke=0]="Invoke",e[e.Automatic=1]="Automatic"})(NewSymbolNameTriggerKind||(NewSymbolNameTriggerKind={}));var Command;(function(e){function t(n){return!n||typeof n!="object"?!1:typeof n.id=="string"&&typeof n.title=="string"}e.is=t})(Command||(Command={}));var CommentThreadCollapsibleState;(function(e){e[e.Collapsed=0]="Collapsed",e[e.Expanded=1]="Expanded"})(CommentThreadCollapsibleState||(CommentThreadCollapsibleState={}));var CommentThreadState;(function(e){e[e.Unresolved=0]="Unresolved",e[e.Resolved=1]="Resolved"})(CommentThreadState||(CommentThreadState={}));var CommentThreadApplicability;(function(e){e[e.Current=0]="Current",e[e.Outdated=1]="Outdated"})(CommentThreadApplicability||(CommentThreadApplicability={}));var CommentMode;(function(e){e[e.Editing=0]="Editing",e[e.Preview=1]="Preview"})(CommentMode||(CommentMode={}));var CommentState;(function(e){e[e.Published=0]="Published",e[e.Draft=1]="Draft"})(CommentState||(CommentState={}));var InlayHintKind;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(InlayHintKind||(InlayHintKind={}));var $4W=new $jW,$5W=new $jW,ExternalUriOpenerPriority;(function(e){e[e.None=0]="None",e[e.Option=1]="Option",e[e.Default=2]="Default",e[e.Preferred=3]="Preferred"})(ExternalUriOpenerPriority||(ExternalUriOpenerPriority={}));var InlineEditTriggerKind;(function(e){e[e.Invoke=0]="Invoke",e[e.Automatic=1]="Automatic"})(InlineEditTriggerKind||(InlineEditTriggerKind={}));var AccessibilitySupport;(function(e){e[e.Unknown=0]="Unknown",e[e.Disabled=1]="Disabled",e[e.Enabled=2]="Enabled"})(AccessibilitySupport||(AccessibilitySupport={}));var CodeActionTriggerType2;(function(e){e[e.Invoke=1]="Invoke",e[e.Auto=2]="Auto"})(CodeActionTriggerType2||(CodeActionTriggerType2={}));var CompletionItemInsertTextRule2;(function(e){e[e.None=0]="None",e[e.KeepWhitespace=1]="KeepWhitespace",e[e.InsertAsSnippet=4]="InsertAsSnippet"})(CompletionItemInsertTextRule2||(CompletionItemInsertTextRule2={}));var CompletionItemKind2;(function(e){e[e.Method=0]="Method",e[e.Function=1]="Function",e[e.Constructor=2]="Constructor",e[e.Field=3]="Field",e[e.Variable=4]="Variable",e[e.Class=5]="Class",e[e.Struct=6]="Struct",e[e.Interface=7]="Interface",e[e.Module=8]="Module",e[e.Property=9]="Property",e[e.Event=10]="Event",e[e.Operator=11]="Operator",e[e.Unit=12]="Unit",e[e.Value=13]="Value",e[e.Constant=14]="Constant",e[e.Enum=15]="Enum",e[e.EnumMember=16]="EnumMember",e[e.Keyword=17]="Keyword",e[e.Text=18]="Text",e[e.Color=19]="Color",e[e.File=20]="File",e[e.Reference=21]="Reference",e[e.Customcolor=22]="Customcolor",e[e.Folder=23]="Folder",e[e.TypeParameter=24]="TypeParameter",e[e.User=25]="User",e[e.Issue=26]="Issue",e[e.Snippet=27]="Snippet"})(CompletionItemKind2||(CompletionItemKind2={}));var CompletionItemTag2;(function(e){e[e.Deprecated=1]="Deprecated"})(CompletionItemTag2||(CompletionItemTag2={}));var CompletionTriggerKind2;(function(e){e[e.Invoke=0]="Invoke",e[e.TriggerCharacter=1]="TriggerCharacter",e[e.TriggerForIncompleteCompletions=2]="TriggerForIncompleteCompletions"})(CompletionTriggerKind2||(CompletionTriggerKind2={}));var ContentWidgetPositionPreference;(function(e){e[e.EXACT=0]="EXACT",e[e.ABOVE=1]="ABOVE",e[e.BELOW=2]="BELOW"})(ContentWidgetPositionPreference||(ContentWidgetPositionPreference={}));var CursorChangeReason;(function(e){e[e.NotSet=0]="NotSet",e[e.ContentFlush=1]="ContentFlush",e[e.RecoverFromMarkers=2]="RecoverFromMarkers",e[e.Explicit=3]="Explicit",e[e.Paste=4]="Paste",e[e.Undo=5]="Undo",e[e.Redo=6]="Redo"})(CursorChangeReason||(CursorChangeReason={}));var DefaultEndOfLine;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(DefaultEndOfLine||(DefaultEndOfLine={}));var DocumentHighlightKind2;(function(e){e[e.Text=0]="Text",e[e.Read=1]="Read",e[e.Write=2]="Write"})(DocumentHighlightKind2||(DocumentHighlightKind2={}));var EditorAutoIndentStrategy;(function(e){e[e.None=0]="None",e[e.Keep=1]="Keep",e[e.Brackets=2]="Brackets",e[e.Advanced=3]="Advanced",e[e.Full=4]="Full"})(EditorAutoIndentStrategy||(EditorAutoIndentStrategy={}));var EditorOption;(function(e){e[e.acceptSuggestionOnCommitCharacter=0]="acceptSuggestionOnCommitCharacter",e[e.acceptSuggestionOnEnter=1]="acceptSuggestionOnEnter",e[e.accessibilitySupport=2]="accessibilitySupport",e[e.accessibilityPageSize=3]="accessibilityPageSize",e[e.ariaLabel=4]="ariaLabel",e[e.ariaRequired=5]="ariaRequired",e[e.autoClosingBrackets=6]="autoClosingBrackets",e[e.autoClosingComments=7]="autoClosingComments",e[e.screenReaderAnnounceInlineSuggestion=8]="screenReaderAnnounceInlineSuggestion",e[e.autoClosingDelete=9]="autoClosingDelete",e[e.autoClosingOvertype=10]="autoClosingOvertype",e[e.autoClosingQuotes=11]="autoClosingQuotes",e[e.autoIndent=12]="autoIndent",e[e.automaticLayout=13]="automaticLayout",e[e.autoSurround=14]="autoSurround",e[e.bracketPairColorization=15]="bracketPairColorization",e[e.guides=16]="guides",e[e.codeLens=17]="codeLens",e[e.codeLensFontFamily=18]="codeLensFontFamily",e[e.codeLensFontSize=19]="codeLensFontSize",e[e.colorDecorators=20]="colorDecorators",e[e.colorDecoratorsLimit=21]="colorDecoratorsLimit",e[e.columnSelection=22]="columnSelection",e[e.comments=23]="comments",e[e.contextmenu=24]="contextmenu",e[e.copyWithSyntaxHighlighting=25]="copyWithSyntaxHighlighting",e[e.cursorBlinking=26]="cursorBlinking",e[e.cursorSmoothCaretAnimation=27]="cursorSmoothCaretAnimation",e[e.cursorStyle=28]="cursorStyle",e[e.cursorSurroundingLines=29]="cursorSurroundingLines",e[e.cursorSurroundingLinesStyle=30]="cursorSurroundingLinesStyle",e[e.cursorWidth=31]="cursorWidth",e[e.disableLayerHinting=32]="disableLayerHinting",e[e.disableMonospaceOptimizations=33]="disableMonospaceOptimizations",e[e.domReadOnly=34]="domReadOnly",e[e.dragAndDrop=35]="dragAndDrop",e[e.dropIntoEditor=36]="dropIntoEditor",e[e.experimentalEditContextEnabled=37]="experimentalEditContextEnabled",e[e.emptySelectionClipboard=38]="emptySelectionClipboard",e[e.experimentalGpuAcceleration=39]="experimentalGpuAcceleration",e[e.experimentalWhitespaceRendering=40]="experimentalWhitespaceRendering",e[e.extraEditorClassName=41]="extraEditorClassName",e[e.fastScrollSensitivity=42]="fastScrollSensitivity",e[e.find=43]="find",e[e.fixedOverflowWidgets=44]="fixedOverflowWidgets",e[e.folding=45]="folding",e[e.foldingStrategy=46]="foldingStrategy",e[e.foldingHighlight=47]="foldingHighlight",e[e.foldingImportsByDefault=48]="foldingImportsByDefault",e[e.foldingMaximumRegions=49]="foldingMaximumRegions",e[e.unfoldOnClickAfterEndOfLine=50]="unfoldOnClickAfterEndOfLine",e[e.fontFamily=51]="fontFamily",e[e.fontInfo=52]="fontInfo",e[e.fontLigatures=53]="fontLigatures",e[e.fontSize=54]="fontSize",e[e.fontWeight=55]="fontWeight",e[e.fontVariations=56]="fontVariations",e[e.formatOnPaste=57]="formatOnPaste",e[e.formatOnType=58]="formatOnType",e[e.glyphMargin=59]="glyphMargin",e[e.gotoLocation=60]="gotoLocation",e[e.hideCursorInOverviewRuler=61]="hideCursorInOverviewRuler",e[e.hover=62]="hover",e[e.inDiffEditor=63]="inDiffEditor",e[e.inlineSuggest=64]="inlineSuggest",e[e.letterSpacing=65]="letterSpacing",e[e.lightbulb=66]="lightbulb",e[e.lineDecorationsWidth=67]="lineDecorationsWidth",e[e.lineHeight=68]="lineHeight",e[e.lineNumbers=69]="lineNumbers",e[e.lineNumbersMinChars=70]="lineNumbersMinChars",e[e.linkedEditing=71]="linkedEditing",e[e.links=72]="links",e[e.matchBrackets=73]="matchBrackets",e[e.minimap=74]="minimap",e[e.mouseStyle=75]="mouseStyle",e[e.mouseWheelScrollSensitivity=76]="mouseWheelScrollSensitivity",e[e.mouseWheelZoom=77]="mouseWheelZoom",e[e.multiCursorMergeOverlapping=78]="multiCursorMergeOverlapping",e[e.multiCursorModifier=79]="multiCursorModifier",e[e.multiCursorPaste=80]="multiCursorPaste",e[e.multiCursorLimit=81]="multiCursorLimit",e[e.occurrencesHighlight=82]="occurrencesHighlight",e[e.occurrencesHighlightDelay=83]="occurrencesHighlightDelay",e[e.overtypeCursorStyle=84]="overtypeCursorStyle",e[e.overtypeOnPaste=85]="overtypeOnPaste",e[e.overviewRulerBorder=86]="overviewRulerBorder",e[e.overviewRulerLanes=87]="overviewRulerLanes",e[e.padding=88]="padding",e[e.pasteAs=89]="pasteAs",e[e.parameterHints=90]="parameterHints",e[e.peekWidgetDefaultFocus=91]="peekWidgetDefaultFocus",e[e.placeholder=92]="placeholder",e[e.definitionLinkOpensInPeek=93]="definitionLinkOpensInPeek",e[e.quickSuggestions=94]="quickSuggestions",e[e.quickSuggestionsDelay=95]="quickSuggestionsDelay",e[e.readOnly=96]="readOnly",e[e.readOnlyMessage=97]="readOnlyMessage",e[e.renameOnType=98]="renameOnType",e[e.renderControlCharacters=99]="renderControlCharacters",e[e.renderFinalNewline=100]="renderFinalNewline",e[e.renderLineHighlight=101]="renderLineHighlight",e[e.renderLineHighlightOnlyWhenFocus=102]="renderLineHighlightOnlyWhenFocus",e[e.renderValidationDecorations=103]="renderValidationDecorations",e[e.renderWhitespace=104]="renderWhitespace",e[e.revealHorizontalRightPadding=105]="revealHorizontalRightPadding",e[e.roundedSelection=106]="roundedSelection",e[e.rulers=107]="rulers",e[e.scrollbar=108]="scrollbar",e[e.scrollBeyondLastColumn=109]="scrollBeyondLastColumn",e[e.scrollBeyondLastLine=110]="scrollBeyondLastLine",e[e.scrollPredominantAxis=111]="scrollPredominantAxis",e[e.selectionClipboard=112]="selectionClipboard",e[e.selectionHighlight=113]="selectionHighlight",e[e.selectOnLineNumbers=114]="selectOnLineNumbers",e[e.showFoldingControls=115]="showFoldingControls",e[e.showUnused=116]="showUnused",e[e.snippetSuggestions=117]="snippetSuggestions",e[e.smartSelect=118]="smartSelect",e[e.smoothScrolling=119]="smoothScrolling",e[e.stickyScroll=120]="stickyScroll",e[e.stickyTabStops=121]="stickyTabStops",e[e.stopRenderingLineAfter=122]="stopRenderingLineAfter",e[e.suggest=123]="suggest",e[e.suggestFontSize=124]="suggestFontSize",e[e.suggestLineHeight=125]="suggestLineHeight",e[e.suggestOnTriggerCharacters=126]="suggestOnTriggerCharacters",e[e.suggestSelection=127]="suggestSelection",e[e.tabCompletion=128]="tabCompletion",e[e.tabIndex=129]="tabIndex",e[e.unicodeHighlighting=130]="unicodeHighlighting",e[e.unusualLineTerminators=131]="unusualLineTerminators",e[e.useShadowDOM=132]="useShadowDOM",e[e.useTabStops=133]="useTabStops",e[e.wordBreak=134]="wordBreak",e[e.wordSegmenterLocales=135]="wordSegmenterLocales",e[e.wordSeparators=136]="wordSeparators",e[e.wordWrap=137]="wordWrap",e[e.wordWrapBreakAfterCharacters=138]="wordWrapBreakAfterCharacters",e[e.wordWrapBreakBeforeCharacters=139]="wordWrapBreakBeforeCharacters",e[e.wordWrapColumn=140]="wordWrapColumn",e[e.wordWrapOverride1=141]="wordWrapOverride1",e[e.wordWrapOverride2=142]="wordWrapOverride2",e[e.wrappingIndent=143]="wrappingIndent",e[e.wrappingStrategy=144]="wrappingStrategy",e[e.showDeprecated=145]="showDeprecated",e[e.inlayHints=146]="inlayHints",e[e.effectiveCursorStyle=147]="effectiveCursorStyle",e[e.editorClassName=148]="editorClassName",e[e.pixelRatio=149]="pixelRatio",e[e.tabFocusMode=150]="tabFocusMode",e[e.layoutInfo=151]="layoutInfo",e[e.wrappingInfo=152]="wrappingInfo",e[e.defaultColorDecorators=153]="defaultColorDecorators",e[e.colorDecoratorsActivatedOn=154]="colorDecoratorsActivatedOn",e[e.inlineCompletionsAccessibilityVerbose=155]="inlineCompletionsAccessibilityVerbose",e[e.effectiveExperimentalEditContextEnabled=156]="effectiveExperimentalEditContextEnabled"})(EditorOption||(EditorOption={}));var EndOfLinePreference;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(EndOfLinePreference||(EndOfLinePreference={}));var EndOfLineSequence;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(EndOfLineSequence||(EndOfLineSequence={}));var GlyphMarginLane;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=3]="Right"})(GlyphMarginLane||(GlyphMarginLane={}));var HoverVerbosityAction2;(function(e){e[e.Increase=0]="Increase",e[e.Decrease=1]="Decrease"})(HoverVerbosityAction2||(HoverVerbosityAction2={}));var IndentAction;(function(e){e[e.None=0]="None",e[e.Indent=1]="Indent",e[e.IndentOutdent=2]="IndentOutdent",e[e.Outdent=3]="Outdent"})(IndentAction||(IndentAction={}));var InjectedTextCursorStops;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(InjectedTextCursorStops||(InjectedTextCursorStops={}));var InlayHintKind2;(function(e){e[e.Type=1]="Type",e[e.Parameter=2]="Parameter"})(InlayHintKind2||(InlayHintKind2={}));var InlineCompletionTriggerKind2;(function(e){e[e.Automatic=0]="Automatic",e[e.Explicit=1]="Explicit",e[e.WithCursorInSuggestedActionRange=2]="WithCursorInSuggestedActionRange"})(InlineCompletionTriggerKind2||(InlineCompletionTriggerKind2={}));var InlineEditTriggerKind2;(function(e){e[e.Invoke=0]="Invoke",e[e.Automatic=1]="Automatic"})(InlineEditTriggerKind2||(InlineEditTriggerKind2={}));var KeyCode2;(function(e){e[e.DependsOnKbLayout=-1]="DependsOnKbLayout",e[e.Unknown=0]="Unknown",e[e.Backspace=1]="Backspace",e[e.Tab=2]="Tab",e[e.Enter=3]="Enter",e[e.Shift=4]="Shift",e[e.Ctrl=5]="Ctrl",e[e.Alt=6]="Alt",e[e.PauseBreak=7]="PauseBreak",e[e.CapsLock=8]="CapsLock",e[e.Escape=9]="Escape",e[e.Space=10]="Space",e[e.PageUp=11]="PageUp",e[e.PageDown=12]="PageDown",e[e.End=13]="End",e[e.Home=14]="Home",e[e.LeftArrow=15]="LeftArrow",e[e.UpArrow=16]="UpArrow",e[e.RightArrow=17]="RightArrow",e[e.DownArrow=18]="DownArrow",e[e.Insert=19]="Insert",e[e.Delete=20]="Delete",e[e.Digit0=21]="Digit0",e[e.Digit1=22]="Digit1",e[e.Digit2=23]="Digit2",e[e.Digit3=24]="Digit3",e[e.Digit4=25]="Digit4",e[e.Digit5=26]="Digit5",e[e.Digit6=27]="Digit6",e[e.Digit7=28]="Digit7",e[e.Digit8=29]="Digit8",e[e.Digit9=30]="Digit9",e[e.KeyA=31]="KeyA",e[e.KeyB=32]="KeyB",e[e.KeyC=33]="KeyC",e[e.KeyD=34]="KeyD",e[e.KeyE=35]="KeyE",e[e.KeyF=36]="KeyF",e[e.KeyG=37]="KeyG",e[e.KeyH=38]="KeyH",e[e.KeyI=39]="KeyI",e[e.KeyJ=40]="KeyJ",e[e.KeyK=41]="KeyK",e[e.KeyL=42]="KeyL",e[e.KeyM=43]="KeyM",e[e.KeyN=44]="KeyN",e[e.KeyO=45]="KeyO",e[e.KeyP=46]="KeyP",e[e.KeyQ=47]="KeyQ",e[e.KeyR=48]="KeyR",e[e.KeyS=49]="KeyS",e[e.KeyT=50]="KeyT",e[e.KeyU=51]="KeyU",e[e.KeyV=52]="KeyV",e[e.KeyW=53]="KeyW",e[e.KeyX=54]="KeyX",e[e.KeyY=55]="KeyY",e[e.KeyZ=56]="KeyZ",e[e.Meta=57]="Meta",e[e.ContextMenu=58]="ContextMenu",e[e.F1=59]="F1",e[e.F2=60]="F2",e[e.F3=61]="F3",e[e.F4=62]="F4",e[e.F5=63]="F5",e[e.F6=64]="F6",e[e.F7=65]="F7",e[e.F8=66]="F8",e[e.F9=67]="F9",e[e.F10=68]="F10",e[e.F11=69]="F11",e[e.F12=70]="F12",e[e.F13=71]="F13",e[e.F14=72]="F14",e[e.F15=73]="F15",e[e.F16=74]="F16",e[e.F17=75]="F17",e[e.F18=76]="F18",e[e.F19=77]="F19",e[e.F20=78]="F20",e[e.F21=79]="F21",e[e.F22=80]="F22",e[e.F23=81]="F23",e[e.F24=82]="F24",e[e.NumLock=83]="NumLock",e[e.ScrollLock=84]="ScrollLock",e[e.Semicolon=85]="Semicolon",e[e.Equal=86]="Equal",e[e.Comma=87]="Comma",e[e.Minus=88]="Minus",e[e.Period=89]="Period",e[e.Slash=90]="Slash",e[e.Backquote=91]="Backquote",e[e.BracketLeft=92]="BracketLeft",e[e.Backslash=93]="Backslash",e[e.BracketRight=94]="BracketRight",e[e.Quote=95]="Quote",e[e.OEM_8=96]="OEM_8",e[e.IntlBackslash=97]="IntlBackslash",e[e.Numpad0=98]="Numpad0",e[e.Numpad1=99]="Numpad1",e[e.Numpad2=100]="Numpad2",e[e.Numpad3=101]="Numpad3",e[e.Numpad4=102]="Numpad4",e[e.Numpad5=103]="Numpad5",e[e.Numpad6=104]="Numpad6",e[e.Numpad7=105]="Numpad7",e[e.Numpad8=106]="Numpad8",e[e.Numpad9=107]="Numpad9",e[e.NumpadMultiply=108]="NumpadMultiply",e[e.NumpadAdd=109]="NumpadAdd",e[e.NUMPAD_SEPARATOR=110]="NUMPAD_SEPARATOR",e[e.NumpadSubtract=111]="NumpadSubtract",e[e.NumpadDecimal=112]="NumpadDecimal",e[e.NumpadDivide=113]="NumpadDivide",e[e.KEY_IN_COMPOSITION=114]="KEY_IN_COMPOSITION",e[e.ABNT_C1=115]="ABNT_C1",e[e.ABNT_C2=116]="ABNT_C2",e[e.AudioVolumeMute=117]="AudioVolumeMute",e[e.AudioVolumeUp=118]="AudioVolumeUp",e[e.AudioVolumeDown=119]="AudioVolumeDown",e[e.BrowserSearch=120]="BrowserSearch",e[e.BrowserHome=121]="BrowserHome",e[e.BrowserBack=122]="BrowserBack",e[e.BrowserForward=123]="BrowserForward",e[e.MediaTrackNext=124]="MediaTrackNext",e[e.MediaTrackPrevious=125]="MediaTrackPrevious",e[e.MediaStop=126]="MediaStop",e[e.MediaPlayPause=127]="MediaPlayPause",e[e.LaunchMediaPlayer=128]="LaunchMediaPlayer",e[e.LaunchMail=129]="LaunchMail",e[e.LaunchApp2=130]="LaunchApp2",e[e.Clear=131]="Clear",e[e.MAX_VALUE=132]="MAX_VALUE"})(KeyCode2||(KeyCode2={}));var MarkerSeverity;(function(e){e[e.Hint=1]="Hint",e[e.Info=2]="Info",e[e.Warning=4]="Warning",e[e.Error=8]="Error"})(MarkerSeverity||(MarkerSeverity={}));var MarkerTag;(function(e){e[e.Unnecessary=1]="Unnecessary",e[e.Deprecated=2]="Deprecated"})(MarkerTag||(MarkerTag={}));var MinimapPosition;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(MinimapPosition||(MinimapPosition={}));var MinimapSectionHeaderStyle;(function(e){e[e.Normal=1]="Normal",e[e.Underlined=2]="Underlined"})(MinimapSectionHeaderStyle||(MinimapSectionHeaderStyle={}));var MouseTargetType;(function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.TEXTAREA=1]="TEXTAREA",e[e.GUTTER_GLYPH_MARGIN=2]="GUTTER_GLYPH_MARGIN",e[e.GUTTER_LINE_NUMBERS=3]="GUTTER_LINE_NUMBERS",e[e.GUTTER_LINE_DECORATIONS=4]="GUTTER_LINE_DECORATIONS",e[e.GUTTER_VIEW_ZONE=5]="GUTTER_VIEW_ZONE",e[e.CONTENT_TEXT=6]="CONTENT_TEXT",e[e.CONTENT_EMPTY=7]="CONTENT_EMPTY",e[e.CONTENT_VIEW_ZONE=8]="CONTENT_VIEW_ZONE",e[e.CONTENT_WIDGET=9]="CONTENT_WIDGET",e[e.OVERVIEW_RULER=10]="OVERVIEW_RULER",e[e.SCROLLBAR=11]="SCROLLBAR",e[e.OVERLAY_WIDGET=12]="OVERLAY_WIDGET",e[e.OUTSIDE_EDITOR=13]="OUTSIDE_EDITOR"})(MouseTargetType||(MouseTargetType={}));var NewSymbolNameTag2;(function(e){e[e.AIGenerated=1]="AIGenerated"})(NewSymbolNameTag2||(NewSymbolNameTag2={}));var NewSymbolNameTriggerKind2;(function(e){e[e.Invoke=0]="Invoke",e[e.Automatic=1]="Automatic"})(NewSymbolNameTriggerKind2||(NewSymbolNameTriggerKind2={}));var OverlayWidgetPositionPreference;(function(e){e[e.TOP_RIGHT_CORNER=0]="TOP_RIGHT_CORNER",e[e.BOTTOM_RIGHT_CORNER=1]="BOTTOM_RIGHT_CORNER",e[e.TOP_CENTER=2]="TOP_CENTER"})(OverlayWidgetPositionPreference||(OverlayWidgetPositionPreference={}));var OverviewRulerLane;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(OverviewRulerLane||(OverviewRulerLane={}));var PartialAcceptTriggerKind2;(function(e){e[e.Word=0]="Word",e[e.Line=1]="Line",e[e.Suggest=2]="Suggest"})(PartialAcceptTriggerKind2||(PartialAcceptTriggerKind2={}));var PositionAffinity;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"})(PositionAffinity||(PositionAffinity={}));var RenderLineNumbersType;(function(e){e[e.Off=0]="Off",e[e.On=1]="On",e[e.Relative=2]="Relative",e[e.Interval=3]="Interval",e[e.Custom=4]="Custom"})(RenderLineNumbersType||(RenderLineNumbersType={}));var RenderMinimap;(function(e){e[e.None=0]="None",e[e.Text=1]="Text",e[e.Blocks=2]="Blocks"})(RenderMinimap||(RenderMinimap={}));var ScrollType;(function(e){e[e.Smooth=0]="Smooth",e[e.Immediate=1]="Immediate"})(ScrollType||(ScrollType={}));var ScrollbarVisibility;(function(e){e[e.Auto=1]="Auto",e[e.Hidden=2]="Hidden",e[e.Visible=3]="Visible"})(ScrollbarVisibility||(ScrollbarVisibility={}));var SelectionDirection2;(function(e){e[e.LTR=0]="LTR",e[e.RTL=1]="RTL"})(SelectionDirection2||(SelectionDirection2={}));var ShowLightbulbIconMode;(function(e){e.Off="off",e.OnCode="onCode",e.On="on"})(ShowLightbulbIconMode||(ShowLightbulbIconMode={}));var SignatureHelpTriggerKind2;(function(e){e[e.Invoke=1]="Invoke",e[e.TriggerCharacter=2]="TriggerCharacter",e[e.ContentChange=3]="ContentChange"})(SignatureHelpTriggerKind2||(SignatureHelpTriggerKind2={}));var SymbolKind2;(function(e){e[e.File=0]="File",e[e.Module=1]="Module",e[e.Namespace=2]="Namespace",e[e.Package=3]="Package",e[e.Class=4]="Class",e[e.Method=5]="Method",e[e.Property=6]="Property",e[e.Field=7]="Field",e[e.Constructor=8]="Constructor",e[e.Enum=9]="Enum",e[e.Interface=10]="Interface",e[e.Function=11]="Function",e[e.Variable=12]="Variable",e[e.Constant=13]="Constant",e[e.String=14]="String",e[e.Number=15]="Number",e[e.Boolean=16]="Boolean",e[e.Array=17]="Array",e[e.Object=18]="Object",e[e.Key=19]="Key",e[e.Null=20]="Null",e[e.EnumMember=21]="EnumMember",e[e.Struct=22]="Struct",e[e.Event=23]="Event",e[e.Operator=24]="Operator",e[e.TypeParameter=25]="TypeParameter"})(SymbolKind2||(SymbolKind2={}));var SymbolTag2;(function(e){e[e.Deprecated=1]="Deprecated"})(SymbolTag2||(SymbolTag2={}));var TextEditorCursorBlinkingStyle;(function(e){e[e.Hidden=0]="Hidden",e[e.Blink=1]="Blink",e[e.Smooth=2]="Smooth",e[e.Phase=3]="Phase",e[e.Expand=4]="Expand",e[e.Solid=5]="Solid"})(TextEditorCursorBlinkingStyle||(TextEditorCursorBlinkingStyle={}));var TextEditorCursorStyle;(function(e){e[e.Line=1]="Line",e[e.Block=2]="Block",e[e.Underline=3]="Underline",e[e.LineThin=4]="LineThin",e[e.BlockOutline=5]="BlockOutline",e[e.UnderlineThin=6]="UnderlineThin"})(TextEditorCursorStyle||(TextEditorCursorStyle={}));var TrackedRangeStickiness;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(TrackedRangeStickiness||(TrackedRangeStickiness={}));var WrappingIndent;(function(e){e[e.None=0]="None",e[e.Same=1]="Same",e[e.Indent=2]="Indent",e[e.DeepIndent=3]="DeepIndent"})(WrappingIndent||(WrappingIndent={}));var $Wzb=class{static{this.CtrlCmd=2048}static{this.Shift=1024}static{this.Alt=512}static{this.WinCtrl=256}static chord(e,t){return $If(e,t)}};function $Xzb(){return{editor:void 0,languages:void 0,CancellationTokenSource:$Dd,Emitter:$0e,KeyCode:KeyCode2,KeyMod:$Wzb,Position:$lV,Range:$mV,Selection:$2V,SelectionDirection:SelectionDirection2,MarkerSeverity,MarkerTag,Uri:URI,Token:$RW}}var minute=60,hour=minute*60,day=hour*24,week=day*7,month=day*30,year=day*365,WordCharacterClass;(function(e){e[e.Regular=0]="Regular",e[e.Whitespace=1]="Whitespace",e[e.WordSeparator=2]="WordSeparator"})(WordCharacterClass||(WordCharacterClass={}));var wordClassifierCache=new $Ic(10),OverviewRulerLane2;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=4]="Right",e[e.Full=7]="Full"})(OverviewRulerLane2||(OverviewRulerLane2={}));var GlyphMarginLane2;(function(e){e[e.Left=1]="Left",e[e.Center=2]="Center",e[e.Right=3]="Right"})(GlyphMarginLane2||(GlyphMarginLane2={}));var MinimapPosition2;(function(e){e[e.Inline=1]="Inline",e[e.Gutter=2]="Gutter"})(MinimapPosition2||(MinimapPosition2={}));var MinimapSectionHeaderStyle2;(function(e){e[e.Normal=1]="Normal",e[e.Underlined=2]="Underlined"})(MinimapSectionHeaderStyle2||(MinimapSectionHeaderStyle2={}));var InjectedTextCursorStops2;(function(e){e[e.Both=0]="Both",e[e.Right=1]="Right",e[e.Left=2]="Left",e[e.None=3]="None"})(InjectedTextCursorStops2||(InjectedTextCursorStops2={}));var EndOfLinePreference2;(function(e){e[e.TextDefined=0]="TextDefined",e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(EndOfLinePreference2||(EndOfLinePreference2={}));var DefaultEndOfLine2;(function(e){e[e.LF=1]="LF",e[e.CRLF=2]="CRLF"})(DefaultEndOfLine2||(DefaultEndOfLine2={}));var EndOfLineSequence2;(function(e){e[e.LF=0]="LF",e[e.CRLF=1]="CRLF"})(EndOfLineSequence2||(EndOfLineSequence2={}));var TrackedRangeStickiness2;(function(e){e[e.AlwaysGrowsWhenTypingAtEdges=0]="AlwaysGrowsWhenTypingAtEdges",e[e.NeverGrowsWhenTypingAtEdges=1]="NeverGrowsWhenTypingAtEdges",e[e.GrowsOnlyWhenTypingBefore=2]="GrowsOnlyWhenTypingBefore",e[e.GrowsOnlyWhenTypingAfter=3]="GrowsOnlyWhenTypingAfter"})(TrackedRangeStickiness2||(TrackedRangeStickiness2={}));var PositionAffinity2;(function(e){e[e.Left=0]="Left",e[e.Right=1]="Right",e[e.None=2]="None",e[e.LeftOfInjectedText=3]="LeftOfInjectedText",e[e.RightOfInjectedText=4]="RightOfInjectedText"})(PositionAffinity2||(PositionAffinity2={}));var ModelConstants;(function(e){e[e.FIRST_LINE_DETECTION_LENGTH_LIMIT=1e3]="FIRST_LINE_DETECTION_LENGTH_LIMIT"})(ModelConstants||(ModelConstants={}));function $l1(e){if(!e||e.length===0)return!1;for(let t=0,n=e.length;t<n;t++){const r=e.charCodeAt(t);if(r===10)return!0;if(r===92){if(t++,t>=n)break;const s=e.charCodeAt(t);if(s===110||s===114||s===87)return!0}}return!1}function leftIsWordBounday(e,t,n,r,s){if(r===0)return!0;const i=t.charCodeAt(r-1);if(e.get(i)!==0||i===13||i===10)return!0;if(s>0){const a=t.charCodeAt(r);if(e.get(a)!==0)return!0}return!1}function rightIsWordBounday(e,t,n,r,s){if(r+s===n)return!0;const i=t.charCodeAt(r+s);if(e.get(i)!==0||i===13||i===10)return!0;if(s>0){const a=t.charCodeAt(r+s-1);if(e.get(a)!==0)return!0}return!1}function $o1(e,t,n,r,s){return leftIsWordBounday(e,t,n,r,s)&&rightIsWordBounday(e,t,n,r,s)}var $p1=class{constructor(e,t){this._wordSeparators=e,this.a=t,this.b=-1,this.c=0}reset(e){this.a.lastIndex=e,this.b=-1,this.c=0}next(e){const t=e.length;let n;do{if(this.b+this.c===t||(n=this.a.exec(e),!n))return null;const r=n.index,s=n[0].length;if(r===this.b&&s===this.c){if(s===0){$Fg(e,t,this.a.lastIndex)>65535?this.a.lastIndex+=2:this.a.lastIndex+=1;continue}return null}if(this.b=r,this.c=s,!this._wordSeparators||$o1(this._wordSeparators,e,t,r,s))return n}while(n);return null}},$yV="`~!@#$%^&*()-=+[{]}\\|;:'\",.<>/?";function createWordRegExp(e=""){let t="(-?\\d*\\.\\d\\w*)|([^";for(const n of $yV)e.indexOf(n)>=0||(t+="\\"+n);return t+="\\s]+)",new RegExp(t,"g")}var $zV=createWordRegExp();function $AV(e){let t=$zV;if(e&&e instanceof RegExp)if(e.global)t=e;else{let n="g";e.ignoreCase&&(n+="i"),e.multiline&&(n+="m"),e.unicode&&(n+="u"),t=new RegExp(e.source,n)}return t.lastIndex=0,t}var _defaultConfig=new $Fd;_defaultConfig.unshift({maxLen:1e3,windowSize:15,timeBudget:150});function $CV(e,t,n,r,s){if(t=$AV(t),s||(s=Iterable.first(_defaultConfig)),n.length>s.maxLen){let o=e-s.maxLen/2;return o<0?o=0:r+=o,n=n.substring(o,e+s.maxLen/2),$CV(e,t,n,r,s)}const i=Date.now(),a=e-1-r;let l=-1,u=null;for(let o=1;!(Date.now()-i>=s.timeBudget);o++){const h=a-s.windowSize*o;t.lastIndex=Math.max(0,h);const c=_findRegexMatchEnclosingPosition(t,n,a,l);if(!c&&u||(u=c,h<=0))break;l=h}if(u){const o={word:u[0],startColumn:r+1+u.index,endColumn:r+1+u.index+u[0].length};return t.lastIndex=0,o}return null}function _findRegexMatchEnclosingPosition(e,t,n,r){let s;for(;s=e.exec(t);){const i=s.index||0;if(i<=n&&e.lastIndex>=n)return s;if(r>0&&i>r)return null}return null}var $Rzb=class{static computeUnicodeHighlights(e,t,n){const r=n?n.startLineNumber:1,s=n?n.endLineNumber:e.getLineCount(),i=new CodePointHighlighter(t),a=i.getCandidateCodePoints();let l;a==="allNonBasicAscii"?l=new RegExp("[^\\t\\n\\r\\x20-\\x7E]","g"):l=new RegExp(`${buildRegExpCharClassExpr(Array.from(a))}`,"g");const u=new $p1(null,l),o=[];let h=!1,c,f=0,g=0,m=0;e:for(let N=r,p=s;N<=p;N++){const d=e.getLineContent(N),A=d.length;u.reset(0);do if(c=u.next(d),c){let v=c.index,L=c.index+c[0].length;if(v>0){const F=d.charCodeAt(v-1);$Cg(F)&&v--}if(L+1<A){const F=d.charCodeAt(L-1);$Cg(F)&&L++}const b=d.substring(v,L);let w=$CV(v+1,$zV,d,0);w&&w.endColumn<=v+1&&(w=null);const R=i.shouldHighlightNonBasicASCII(b,w?w.word:null);if(R!==0){if(R===3?f++:R===2?g++:R===1?m++:$Rc(R),o.length>=1e3){h=!0;break e}o.push(new $mV(N,v+1,N,L+1))}}while(c)}return{ranges:o,hasMore:h,ambiguousCharacterCount:f,invisibleCharacterCount:g,nonBasicAsciiCharacterCount:m}}static computeUnicodeHighlightReason(e,t){const n=new CodePointHighlighter(t);switch(n.shouldHighlightNonBasicASCII(e,null)){case 0:return null;case 2:return{kind:1};case 3:{const s=e.codePointAt(0),i=n.ambiguousCharacters.getPrimaryConfusable(s),a=$9g.getLocales().filter(l=>!$9g.getInstance(new Set([...t.allowedLocales,l])).isAmbiguous(s));return{kind:0,confusableWith:String.fromCodePoint(i),notAmbiguousInLocales:a}}case 1:return{kind:2}}}};function buildRegExpCharClassExpr(e,t){return`[${$$f(e.map(r=>String.fromCodePoint(r)).join(""))}]`}var UnicodeHighlighterReasonKind;(function(e){e[e.Ambiguous=0]="Ambiguous",e[e.Invisible=1]="Invisible",e[e.NonBasicAscii=2]="NonBasicAscii"})(UnicodeHighlighterReasonKind||(UnicodeHighlighterReasonKind={}));var CodePointHighlighter=class{constructor(e){this.b=e,this.a=new Set(e.allowedCodePoints),this.ambiguousCharacters=$9g.getInstance(new Set(e.allowedLocales))}getCandidateCodePoints(){if(this.b.nonBasicASCII)return"allNonBasicAscii";const e=new Set;if(this.b.invisibleCharacters)for(const t of $0g.codePoints)isAllowedInvisibleCharacter(String.fromCodePoint(t))||e.add(t);if(this.b.ambiguousCharacters)for(const t of this.ambiguousCharacters.getConfusableCodePoints())e.add(t);for(const t of this.a)e.delete(t);return e}shouldHighlightNonBasicASCII(e,t){const n=e.codePointAt(0);if(this.a.has(n))return 0;if(this.b.nonBasicASCII)return 1;let r=!1,s=!1;if(t)for(const i of t){const a=i.codePointAt(0),l=$Ng(i);r=r||l,!l&&!this.ambiguousCharacters.isAmbiguous(a)&&!$0g.isInvisibleCharacter(a)&&(s=!0)}return!r&&s?0:this.b.invisibleCharacters&&!isAllowedInvisibleCharacter(e)&&$0g.isInvisibleCharacter(n)?2:this.b.ambiguousCharacters&&this.ambiguousCharacters.isAmbiguous(n)?3:0}};function isAllowedInvisibleCharacter(e){return e===" "||e===`
`||e==="	"}var SimpleHighlightReason;(function(e){e[e.None=0]="None",e[e.NonBasicASCII=1]="NonBasicASCII",e[e.Invisible=2]="Invisible",e[e.Ambiguous=3]="Ambiguous"})(SimpleHighlightReason||(SimpleHighlightReason={}));var $T5=class{constructor(e,t,n){this.changes=e,this.moves=t,this.hitTimeout=n}},$U5=class _e{constructor(t,n){this.lineRangeMapping=t,this.changes=n}flip(){return new _e(this.lineRangeMapping.flip(),this.changes.map(t=>t.flip()))}},$lW=class H{static fromTo(t,n){return new H(t,n)}static addRange(t,n){let r=0;for(;r<n.length&&n[r].endExclusive<t.start;)r++;let s=r;for(;s<n.length&&n[s].start<=t.endExclusive;)s++;if(r===s)n.splice(r,0,t);else{const i=Math.min(t.start,n[r].start),a=Math.max(t.endExclusive,n[s-1].endExclusive);n.splice(r,s-r,new H(i,a))}}static tryCreate(t,n){if(!(t>n))return new H(t,n)}static ofLength(t){return new H(0,t)}static ofStartAndLength(t,n){return new H(t,t+n)}static emptyAt(t){return new H(t,t)}constructor(t,n){if(this.start=t,this.endExclusive=n,t>n)throw new $vb(`Invalid range: ${this.toString()}`)}get isEmpty(){return this.start===this.endExclusive}delta(t){return new H(this.start+t,this.endExclusive+t)}deltaStart(t){return new H(this.start+t,this.endExclusive)}deltaEnd(t){return new H(this.start,this.endExclusive+t)}get length(){return this.endExclusive-this.start}toString(){return`[${this.start}, ${this.endExclusive})`}equals(t){return this.start===t.start&&this.endExclusive===t.endExclusive}containsRange(t){return this.start<=t.start&&t.endExclusive<=this.endExclusive}contains(t){return this.start<=t&&t<this.endExclusive}join(t){return new H(Math.min(this.start,t.start),Math.max(this.endExclusive,t.endExclusive))}intersect(t){const n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);if(n<=r)return new H(n,r)}intersectionLength(t){const n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);return Math.max(0,r-n)}intersects(t){const n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);return n<r}intersectsOrTouches(t){const n=Math.max(this.start,t.start),r=Math.min(this.endExclusive,t.endExclusive);return n<=r}isBefore(t){return this.endExclusive<=t.start}isAfter(t){return this.start>=t.endExclusive}slice(t){return t.slice(this.start,this.endExclusive)}substring(t){return t.substring(this.start,this.endExclusive)}clip(t){if(this.isEmpty)throw new $vb(`Invalid clipping range: ${this.toString()}`);return Math.max(this.start,Math.min(this.endExclusive-1,t))}clipCyclic(t){if(this.isEmpty)throw new $vb(`Invalid clipping range: ${this.toString()}`);return t<this.start?this.endExclusive-(this.start-t)%this.length:t>=this.endExclusive?this.start+(t-this.start)%this.length:t}map(t){const n=[];for(let r=this.start;r<this.endExclusive;r++)n.push(t(r));return n}forEach(t){for(let n=this.start;n<this.endExclusive;n++)t(n)}},$vW=class U{static fromRange(t){return new U(t.startLineNumber,t.endLineNumber)}static fromRangeInclusive(t){return new U(t.startLineNumber,t.endLineNumber+1)}static subtract(t,n){return n?t.startLineNumber<n.startLineNumber&&n.endLineNumberExclusive<t.endLineNumberExclusive?[new U(t.startLineNumber,n.startLineNumber),new U(n.endLineNumberExclusive,t.endLineNumberExclusive)]:n.startLineNumber<=t.startLineNumber&&t.endLineNumberExclusive<=n.endLineNumberExclusive?[]:n.endLineNumberExclusive<t.endLineNumberExclusive?[new U(Math.max(n.endLineNumberExclusive,t.startLineNumber),t.endLineNumberExclusive)]:[new U(t.startLineNumber,Math.min(n.startLineNumber,t.endLineNumberExclusive))]:[t]}static joinMany(t){if(t.length===0)return[];let n=new $wW(t[0].slice());for(let r=1;r<t.length;r++)n=n.getUnion(new $wW(t[r].slice()));return n.ranges}static join(t){if(t.length===0)throw new $vb("lineRanges cannot be empty");let n=t[0].startLineNumber,r=t[0].endLineNumberExclusive;for(let s=1;s<t.length;s++)n=Math.min(n,t[s].startLineNumber),r=Math.max(r,t[s].endLineNumberExclusive);return new U(n,r)}static ofLength(t,n){return new U(t,t+n)}static deserialize(t){return new U(t[0],t[1])}constructor(t,n){if(t>n)throw new $vb(`startLineNumber ${t} cannot be after endLineNumberExclusive ${n}`);this.startLineNumber=t,this.endLineNumberExclusive=n}contains(t){return this.startLineNumber<=t&&t<this.endLineNumberExclusive}get isEmpty(){return this.startLineNumber===this.endLineNumberExclusive}delta(t){return new U(this.startLineNumber+t,this.endLineNumberExclusive+t)}deltaLength(t){return new U(this.startLineNumber,this.endLineNumberExclusive+t)}get length(){return this.endLineNumberExclusive-this.startLineNumber}join(t){return new U(Math.min(this.startLineNumber,t.startLineNumber),Math.max(this.endLineNumberExclusive,t.endLineNumberExclusive))}toString(){return`[${this.startLineNumber},${this.endLineNumberExclusive})`}intersect(t){const n=Math.max(this.startLineNumber,t.startLineNumber),r=Math.min(this.endLineNumberExclusive,t.endLineNumberExclusive);if(n<=r)return new U(n,r)}intersectsStrict(t){return this.startLineNumber<t.endLineNumberExclusive&&t.startLineNumber<this.endLineNumberExclusive}overlapOrTouch(t){return this.startLineNumber<=t.endLineNumberExclusive&&t.startLineNumber<=this.endLineNumberExclusive}equals(t){return this.startLineNumber===t.startLineNumber&&this.endLineNumberExclusive===t.endLineNumberExclusive}toInclusiveRange(){return this.isEmpty?null:new $mV(this.startLineNumber,1,this.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER)}toExclusiveRange(){return new $mV(this.startLineNumber,1,this.endLineNumberExclusive,1)}mapToLineArray(t){const n=[];for(let r=this.startLineNumber;r<this.endLineNumberExclusive;r++)n.push(t(r));return n}forEach(t){for(let n=this.startLineNumber;n<this.endLineNumberExclusive;n++)t(n)}serialize(){return[this.startLineNumber,this.endLineNumberExclusive]}includes(t){return this.startLineNumber<=t&&t<this.endLineNumberExclusive}toOffsetRange(){return new $lW(this.startLineNumber-1,this.endLineNumberExclusive-1)}distanceToRange(t){return this.endLineNumberExclusive<=t.startLineNumber?t.startLineNumber-this.endLineNumberExclusive:t.endLineNumberExclusive<=this.startLineNumber?this.startLineNumber-t.endLineNumberExclusive:0}distanceToLine(t){return this.contains(t)?0:t<this.startLineNumber?this.startLineNumber-t:t-this.endLineNumberExclusive}addMargin(t,n){return new U(this.startLineNumber-t,this.endLineNumberExclusive+n)}},$wW=class Y{constructor(t=[]){this.c=t}get ranges(){return this.c}addRange(t){if(t.length===0)return;const n=$6(this.c,s=>s.endLineNumberExclusive>=t.startLineNumber),r=$4(this.c,s=>s.startLineNumber<=t.endLineNumberExclusive)+1;if(n===r)this.c.splice(n,0,t);else if(n===r-1){const s=this.c[n];this.c[n]=s.join(t)}else{const s=this.c[n].join(this.c[r-1]).join(t);this.c.splice(n,r-n,s)}}contains(t){const n=$3(this.c,r=>r.startLineNumber<=t);return!!n&&n.endLineNumberExclusive>t}intersects(t){const n=$3(this.c,r=>r.startLineNumber<t.endLineNumberExclusive);return!!n&&n.endLineNumberExclusive>t.startLineNumber}getUnion(t){if(this.c.length===0)return t;if(t.c.length===0)return this;const n=[];let r=0,s=0,i=null;for(;r<this.c.length||s<t.c.length;){let a=null;if(r<this.c.length&&s<t.c.length){const l=this.c[r],u=t.c[s];l.startLineNumber<u.startLineNumber?(a=l,r++):(a=u,s++)}else r<this.c.length?(a=this.c[r],r++):(a=t.c[s],s++);i===null?i=a:i.endLineNumberExclusive>=a.startLineNumber?i=new $vW(i.startLineNumber,Math.max(i.endLineNumberExclusive,a.endLineNumberExclusive)):(n.push(i),i=a)}return i!==null&&n.push(i),new Y(n)}subtractFrom(t){const n=$6(this.c,a=>a.endLineNumberExclusive>=t.startLineNumber),r=$4(this.c,a=>a.startLineNumber<=t.endLineNumberExclusive)+1;if(n===r)return new Y([t]);const s=[];let i=t.startLineNumber;for(let a=n;a<r;a++){const l=this.c[a];l.startLineNumber>i&&s.push(new $vW(i,l.startLineNumber)),i=l.endLineNumberExclusive}return i<t.endLineNumberExclusive&&s.push(new $vW(i,t.endLineNumberExclusive)),new Y(s)}toString(){return this.c.map(t=>t.toString()).join(", ")}getIntersection(t){const n=[];let r=0,s=0;for(;r<this.c.length&&s<t.c.length;){const i=this.c[r],a=t.c[s],l=i.intersect(a);l&&!l.isEmpty&&n.push(l),i.endLineNumberExclusive<a.endLineNumberExclusive?r++:s++}return new Y(n)}getWithDelta(t){return new Y(this.c.map(n=>n.delta(t)))}},$iX=class z{static{this.zero=new z(0,0)}static lengthDiffNonNegative(t,n){return n.isLessThan(t)?z.zero:t.lineCount===n.lineCount?new z(0,n.columnCount-t.columnCount):new z(n.lineCount-t.lineCount,n.columnCount)}static betweenPositions(t,n){return t.lineNumber===n.lineNumber?new z(0,n.column-t.column):new z(n.lineNumber-t.lineNumber,n.column-1)}static fromPosition(t){return new z(t.lineNumber-1,t.column-1)}static ofRange(t){return z.betweenPositions(t.getStartPosition(),t.getEndPosition())}static ofText(t){let n=0,r=0;for(const s of t)s===`
`?(n++,r=0):r++;return new z(n,r)}constructor(t,n){this.lineCount=t,this.columnCount=n}isZero(){return this.lineCount===0&&this.columnCount===0}isLessThan(t){return this.lineCount!==t.lineCount?this.lineCount<t.lineCount:this.columnCount<t.columnCount}isGreaterThan(t){return this.lineCount!==t.lineCount?this.lineCount>t.lineCount:this.columnCount>t.columnCount}isGreaterThanOrEqualTo(t){return this.lineCount!==t.lineCount?this.lineCount>t.lineCount:this.columnCount>=t.columnCount}equals(t){return this.lineCount===t.lineCount&&this.columnCount===t.columnCount}compare(t){return this.lineCount!==t.lineCount?this.lineCount-t.lineCount:this.columnCount-t.columnCount}add(t){return t.lineCount===0?new z(this.lineCount,this.columnCount+t.columnCount):new z(this.lineCount+t.lineCount,t.columnCount)}createRange(t){return this.lineCount===0?new $mV(t.lineNumber,t.column,t.lineNumber,t.column+this.columnCount):new $mV(t.lineNumber,t.column,t.lineNumber+this.lineCount,this.columnCount+1)}toRange(){return new $mV(1,1,this.lineCount+1,this.columnCount+1)}toLineRange(){return $vW.ofLength(1,this.lineCount+1)}addToPosition(t){return this.lineCount===0?new $lV(t.lineNumber,t.column+this.columnCount):new $lV(t.lineNumber+this.lineCount,this.columnCount+1)}addToRange(t){return $mV.fromPositions(this.addToPosition(t.getStartPosition()),this.addToPosition(t.getEndPosition()))}toString(){return`${this.lineCount},${this.columnCount}`}},$G5=class{constructor(e){this.text=e,this.a=[],this.b=[],this.a.push(0);for(let t=0;t<e.length;t++)e.charAt(t)===`
`&&(this.a.push(t+1),t>0&&e.charAt(t-1)==="\r"?this.b.push(t-1):this.b.push(t));this.b.push(e.length)}getOffset(e){const t=this.c(e);return this.a[t.lineNumber-1]+t.column-1}c(e){if(e.lineNumber<1)return new $lV(1,1);const t=this.textLength.lineCount+1;if(e.lineNumber>t){const r=this.getLineLength(t);return new $lV(t,r+1)}if(e.column<1)return new $lV(e.lineNumber,1);const n=this.getLineLength(e.lineNumber);return e.column-1>n?new $lV(e.lineNumber,n+1):e}getOffsetRange(e){return new $lW(this.getOffset(e.getStartPosition()),this.getOffset(e.getEndPosition()))}getPosition(e){const t=$4(this.a,s=>s<=e),n=t+1,r=e-this.a[t]+1;return new $lV(n,r)}getRange(e){return $mV.fromPositions(this.getPosition(e.start),this.getPosition(e.endExclusive))}getTextLength(e){return $iX.ofRange(this.getRange(e))}get textLength(){const e=this.a.length-1;return new $iX(e,this.text.length-this.a[e])}getLineLength(e){return this.b[e-1]-this.a[e-1]}},$H5=class Q{static fromOffsetEdit(t,n){const r=t.edits.map(s=>new $I5(n.getTransformer().getRange(s.replaceRange),s.newText));return new Q(r)}static single(t,n){return new Q([new $I5(t,n)])}static insert(t,n){return new Q([new $I5($mV.fromPositions(t,t),n)])}constructor(t){this.edits=t,$Uc(()=>$Vc(t,(n,r)=>n.range.getEndPosition().isBeforeOrEqual(r.range.getStartPosition())))}normalize(){const t=[];for(const n of this.edits)if(t.length>0&&t[t.length-1].range.getEndPosition().equals(n.range.getStartPosition())){const r=t[t.length-1];t[t.length-1]=new $I5(r.range.plusRange(n.range),r.text+n.text)}else n.isEmpty||t.push(n);return new Q(t)}mapPosition(t){let n=0,r=0,s=0;for(const i of this.edits){const a=i.range.getStartPosition();if(t.isBeforeOrEqual(a))break;const l=i.range.getEndPosition(),u=$iX.ofText(i.text);if(t.isBefore(l)){const o=new $lV(a.lineNumber+n,a.column+(a.lineNumber+n===r?s:0)),h=u.addToPosition(o);return rangeFromPositions(o,h)}a.lineNumber+n!==r&&(s=0),n+=u.lineCount-(i.range.endLineNumber-i.range.startLineNumber),u.lineCount===0?l.lineNumber!==a.lineNumber?s+=u.columnCount-(l.column-1):s+=u.columnCount-(l.column-a.column):s=u.columnCount,r=l.lineNumber+n}return new $lV(t.lineNumber+n,t.column+(t.lineNumber+n===r?s:0))}mapRange(t){function n(a){return a instanceof $lV?a:a.getStartPosition()}function r(a){return a instanceof $lV?a:a.getEndPosition()}const s=n(this.mapPosition(t.getStartPosition())),i=r(this.mapPosition(t.getEndPosition()));return rangeFromPositions(s,i)}inverseMapPosition(t,n){return this.inverse(n).mapPosition(t)}inverseMapRange(t,n){return this.inverse(n).mapRange(t)}apply(t){let n="",r=new $lV(1,1);for(const i of this.edits){const a=i.range,l=a.getStartPosition(),u=a.getEndPosition(),o=rangeFromPositions(r,l);o.isEmpty()||(n+=t.getValueOfRange(o)),n+=i.text,r=u}const s=rangeFromPositions(r,t.endPositionExclusive);return s.isEmpty()||(n+=t.getValueOfRange(s)),n}applyToString(t){const n=new $M5(t);return this.apply(n)}inverse(t){const n=this.getNewRanges();return new Q(this.edits.map((r,s)=>new $I5(n[s],t.getValueOfRange(r.range))))}getNewRanges(){const t=[];let n=0,r=0,s=0;for(const i of this.edits){const a=$iX.ofText(i.text),l=$lV.lift({lineNumber:i.range.startLineNumber+r,column:i.range.startColumn+(i.range.startLineNumber===n?s:0)}),u=a.createRange(l);t.push(u),r=u.endLineNumber-i.range.endLineNumber,s=u.endColumn-i.range.endColumn,n=i.range.endLineNumber}return t}toSingle(t){if(this.edits.length===0)throw new $vb;if(this.edits.length===1)return this.edits[0];const n=this.edits[0].range.getStartPosition(),r=this.edits[this.edits.length-1].range.getEndPosition();let s="";for(let i=0;i<this.edits.length;i++){const a=this.edits[i];if(s+=a.text,i<this.edits.length-1){const l=this.edits[i+1],u=$mV.fromPositions(a.range.getEndPosition(),l.range.getStartPosition()),o=t.getValueOfRange(u);s+=o}}return new $I5($mV.fromPositions(n,r),s)}equals(t){return $yb(this.edits,t.edits,(n,r)=>n.equals(r))}},$I5=class ae{static joinEdits(t,n){if(t.length===0)throw new $vb;if(t.length===1)return t[0];const r=t[0].range.getStartPosition(),s=t[t.length-1].range.getEndPosition();let i="";for(let a=0;a<t.length;a++){const l=t[a];if(i+=l.text,a<t.length-1){const u=t[a+1],o=$mV.fromPositions(l.range.getEndPosition(),u.range.getStartPosition()),h=n.getValueOfRange(o);i+=h}}return new ae($mV.fromPositions(r,s),i)}constructor(t,n){this.range=t,this.text=n}get isEmpty(){return this.range.isEmpty()&&this.text.length===0}static equals(t,n){return t.range.equalsRange(n.range)&&t.text===n.text}toSingleEditOperation(){return{range:this.range,text:this.text}}toEdit(){return new $H5([this])}equals(t){return ae.equals(this,t)}extendToCoverRange(t,n){if(this.range.containsRange(t))return this;const r=this.range.plusRange(t),s=n.getValueOfRange($mV.fromPositions(r.getStartPosition(),this.range.getStartPosition())),i=n.getValueOfRange($mV.fromPositions(this.range.getEndPosition(),r.getEndPosition())),a=s+this.text+i;return new ae(r,a)}extendToFullLine(t){const n=new $mV(this.range.startLineNumber,1,this.range.endLineNumber,t.getTransformer().getLineLength(this.range.endLineNumber)+1);return this.extendToCoverRange(n,t)}removeCommonPrefix(t){const n=t.getValueOfRange(this.range).replaceAll(`\r
`,`
`),r=this.text.replaceAll(`\r
`,`
`),s=$Ag(n,r),i=$iX.ofText(n.substring(0,s)).addToPosition(this.range.getStartPosition()),a=r.substring(s),l=$mV.fromPositions(i,this.range.getEndPosition());return new ae(l,a)}isEffectiveDeletion(t){let n=this.text.replaceAll(`\r
`,`
`),r=t.getValueOfRange(this.range).replaceAll(`\r
`,`
`);const s=$Ag(n,r);n=n.substring(s),r=r.substring(s);const i=$Bg(n,r);return n=n.substring(0,n.length-i),r=r.substring(0,r.length-i),n===""}};function rangeFromPositions(e,t){if(e.lineNumber===t.lineNumber&&e.column===Number.MAX_SAFE_INTEGER)return $mV.fromPositions(t,t);if(!e.isBeforeOrEqual(t))throw new $vb("start must be before end");return new $mV(e.lineNumber,e.column,t.lineNumber,t.column)}var $J5=class{constructor(){this.c=void 0}get endPositionExclusive(){return this.length.addToPosition(new $lV(1,1))}get lineRange(){return this.length.toLineRange()}getValue(){return this.getValueOfRange(this.length.toRange())}getLineLength(e){return this.getValueOfRange(new $mV(e,1,e,Number.MAX_SAFE_INTEGER)).length}getTransformer(){return this.c||(this.c=new $G5(this.getValue())),this.c}getLineAt(e){return this.getValueOfRange(new $mV(e,1,e,Number.MAX_SAFE_INTEGER))}getLines(){const e=this.getValue();return $kg(e)}},$K5=class extends $J5{constructor(e,t){$Sc(t>=1),super(),this.d=e,this.f=t}getValueOfRange(e){if(e.startLineNumber===e.endLineNumber)return this.d(e.startLineNumber).substring(e.startColumn-1,e.endColumn-1);let t=this.d(e.startLineNumber).substring(e.startColumn-1);for(let n=e.startLineNumber+1;n<e.endLineNumber;n++)t+=`
`+this.d(n);return t+=`
`+this.d(e.endLineNumber).substring(0,e.endColumn-1),t}getLineLength(e){return this.d(e).length}get length(){const e=this.d(this.f);return new $iX(this.f-1,e.length)}},$L5=class extends $K5{constructor(e){super(t=>e[t-1],e.length)}},$M5=class extends $J5{constructor(e){super(),this.value=e,this.d=new $G5(this.value)}getValueOfRange(e){return this.d.getOffsetRange(e).substring(this.value)}get length(){return this.d.textLength}},$N5=class K{static inverse(t,n,r){const s=[];let i=1,a=1;for(const u of t){const o=new K(new $vW(i,u.original.startLineNumber),new $vW(a,u.modified.startLineNumber));o.modified.isEmpty||s.push(o),i=u.original.endLineNumberExclusive,a=u.modified.endLineNumberExclusive}const l=new K(new $vW(i,n+1),new $vW(a,r+1));return l.modified.isEmpty||s.push(l),s}static clip(t,n,r){const s=[];for(const i of t){const a=i.original.intersect(n),l=i.modified.intersect(r);a&&!a.isEmpty&&l&&!l.isEmpty&&s.push(new K(a,l))}return s}constructor(t,n){this.original=t,this.modified=n}toString(){return`{${this.original.toString()}->${this.modified.toString()}}`}flip(){return new K(this.modified,this.original)}join(t){return new K(this.original.join(t.original),this.modified.join(t.modified))}get changedLineCount(){return Math.max(this.original.length,this.modified.length)}toRangeMapping(){const t=this.original.toInclusiveRange(),n=this.modified.toInclusiveRange();if(t&&n)return new $P5(t,n);if(this.original.startLineNumber===1||this.modified.startLineNumber===1){if(!(this.modified.startLineNumber===1&&this.original.startLineNumber===1))throw new $vb("not a valid diff");return new $P5(new $mV(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new $mV(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1))}else return new $P5(new $mV(this.original.startLineNumber-1,Number.MAX_SAFE_INTEGER,this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),new $mV(this.modified.startLineNumber-1,Number.MAX_SAFE_INTEGER,this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER))}toRangeMapping2(t,n){if(isValidLineNumber(this.original.endLineNumberExclusive,t)&&isValidLineNumber(this.modified.endLineNumberExclusive,n))return new $P5(new $mV(this.original.startLineNumber,1,this.original.endLineNumberExclusive,1),new $mV(this.modified.startLineNumber,1,this.modified.endLineNumberExclusive,1));if(!this.original.isEmpty&&!this.modified.isEmpty)return new $P5($mV.fromPositions(new $lV(this.original.startLineNumber,1),normalizePosition(new $lV(this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),t)),$mV.fromPositions(new $lV(this.modified.startLineNumber,1),normalizePosition(new $lV(this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),n)));if(this.original.startLineNumber>1&&this.modified.startLineNumber>1)return new $P5($mV.fromPositions(normalizePosition(new $lV(this.original.startLineNumber-1,Number.MAX_SAFE_INTEGER),t),normalizePosition(new $lV(this.original.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),t)),$mV.fromPositions(normalizePosition(new $lV(this.modified.startLineNumber-1,Number.MAX_SAFE_INTEGER),n),normalizePosition(new $lV(this.modified.endLineNumberExclusive-1,Number.MAX_SAFE_INTEGER),n)));throw new $vb}};function normalizePosition(e,t){if(e.lineNumber<1)return new $lV(1,1);if(e.lineNumber>t.length)return new $lV(t.length,t[t.length-1].length+1);const n=t[e.lineNumber-1];return e.column>n.length+1?new $lV(e.lineNumber,n.length+1):e}function isValidLineNumber(e,t){return e>=1&&e<=t.length}var $O5=class me extends $N5{static fromRangeMappings(t){const n=$vW.join(t.map(s=>$vW.fromRangeInclusive(s.originalRange))),r=$vW.join(t.map(s=>$vW.fromRangeInclusive(s.modifiedRange)));return new me(n,r,t)}constructor(t,n,r){super(t,n),this.innerChanges=r}flip(){return new me(this.modified,this.original,this.innerChanges?.map(t=>t.flip()))}withInnerChangesFromLineRanges(){return new me(this.original,this.modified,[this.toRangeMapping()])}},$P5=class ee{static fromEdit(t){const n=t.getNewRanges();return t.edits.map((s,i)=>new ee(s.range,n[i]))}static fromEditJoin(t){const n=t.getNewRanges(),r=t.edits.map((s,i)=>new ee(s.range,n[i]));return ee.join(r)}static join(t){if(t.length===0)throw new $vb("Cannot join an empty list of range mappings");let n=t[0];for(let r=1;r<t.length;r++)n=n.join(t[r]);return n}static assertSorted(t){for(let n=1;n<t.length;n++){const r=t[n-1],s=t[n];if(!(r.originalRange.getEndPosition().isBeforeOrEqual(s.originalRange.getStartPosition())&&r.modifiedRange.getEndPosition().isBeforeOrEqual(s.modifiedRange.getStartPosition())))throw new $vb("Range mappings must be sorted")}}constructor(t,n){this.originalRange=t,this.modifiedRange=n}toString(){return`{${this.originalRange.toString()}->${this.modifiedRange.toString()}}`}flip(){return new ee(this.modifiedRange,this.originalRange)}toTextEdit(t){const n=t.getValueOfRange(this.modifiedRange);return new $I5(this.originalRange,n)}join(t){return new ee(this.originalRange.plusRange(t.originalRange),this.modifiedRange.plusRange(t.modifiedRange))}};function $Q5(e,t,n,r=!1){const s=[];for(const i of $Eb(e.map(a=>$R5(a,t,n)),(a,l)=>a.original.overlapOrTouch(l.original)||a.modified.overlapOrTouch(l.modified))){const a=i[0],l=i[i.length-1];s.push(new $O5(a.original.join(l.original),a.modified.join(l.modified),i.map(u=>u.innerChanges[0])))}return $Uc(()=>!r&&s.length>0&&(s[0].modified.startLineNumber!==s[0].original.startLineNumber||n.length.lineCount-s[s.length-1].modified.endLineNumberExclusive!==t.length.lineCount-s[s.length-1].original.endLineNumberExclusive)?!1:$Vc(s,(i,a)=>a.original.startLineNumber-i.original.endLineNumberExclusive===a.modified.startLineNumber-i.modified.endLineNumberExclusive&&i.original.endLineNumberExclusive<a.original.startLineNumber&&i.modified.endLineNumberExclusive<a.modified.startLineNumber)),s}function $R5(e,t,n){let r=0,s=0;e.modifiedRange.endColumn===1&&e.originalRange.endColumn===1&&e.originalRange.startLineNumber+r<=e.originalRange.endLineNumber&&e.modifiedRange.startLineNumber+r<=e.modifiedRange.endLineNumber&&(s=-1),e.modifiedRange.startColumn-1>=n.getLineLength(e.modifiedRange.startLineNumber)&&e.originalRange.startColumn-1>=t.getLineLength(e.originalRange.startLineNumber)&&e.originalRange.startLineNumber<=e.originalRange.endLineNumber+s&&e.modifiedRange.startLineNumber<=e.modifiedRange.endLineNumber+s&&(r=1);const i=new $vW(e.originalRange.startLineNumber+r,e.originalRange.endLineNumber+1+s),a=new $vW(e.modifiedRange.startLineNumber+r,e.modifiedRange.endLineNumber+1+s);return new $O5(i,a,[e])}var MINIMUM_MATCHING_CHARACTER_LENGTH=3,$V5=class{computeDiff(e,t,n){const s=new $W5(e,t,{maxComputationTime:n.maxComputationTimeMs,shouldIgnoreTrimWhitespace:n.ignoreTrimWhitespace,shouldComputeCharChanges:!0,shouldMakePrettyDiff:!0,shouldPostProcessCharChanges:!0}).computeDiff(),i=[];let a=null;for(const l of s.changes){let u;l.originalEndLineNumber===0?u=new $vW(l.originalStartLineNumber+1,l.originalStartLineNumber+1):u=new $vW(l.originalStartLineNumber,l.originalEndLineNumber+1);let o;l.modifiedEndLineNumber===0?o=new $vW(l.modifiedStartLineNumber+1,l.modifiedStartLineNumber+1):o=new $vW(l.modifiedStartLineNumber,l.modifiedEndLineNumber+1);let h=new $O5(u,o,l.charChanges?.map(c=>new $P5(new $mV(c.originalStartLineNumber,c.originalStartColumn,c.originalEndLineNumber,c.originalEndColumn),new $mV(c.modifiedStartLineNumber,c.modifiedStartColumn,c.modifiedEndLineNumber,c.modifiedEndColumn))));a&&(a.modified.endLineNumberExclusive===h.modified.startLineNumber||a.original.endLineNumberExclusive===h.original.startLineNumber)&&(h=new $O5(a.original.join(h.original),a.modified.join(h.modified),a.innerChanges&&h.innerChanges?a.innerChanges.concat(h.innerChanges):void 0),i.pop()),i.push(h),a=h}return $Uc(()=>$Vc(i,(l,u)=>u.original.startLineNumber-l.original.endLineNumberExclusive===u.modified.startLineNumber-l.modified.endLineNumberExclusive&&l.original.endLineNumberExclusive<u.original.startLineNumber&&l.modified.endLineNumberExclusive<u.modified.startLineNumber)),new $T5(i,[],s.quitEarly)}};function computeDiff(e,t,n,r){return new $jV(e,t,n).ComputeDiff(r)}var LineSequence=class{constructor(e){const t=[],n=[];for(let r=0,s=e.length;r<s;r++)t[r]=getFirstNonBlankColumn(e[r],1),n[r]=getLastNonBlankColumn(e[r],1);this.lines=e,this.a=t,this.b=n}getElements(){const e=[];for(let t=0,n=this.lines.length;t<n;t++)e[t]=this.lines[t].substring(this.a[t]-1,this.b[t]-1);return e}getStrictElement(e){return this.lines[e]}getStartLineNumber(e){return e+1}getEndLineNumber(e){return e+1}createCharSequence(e,t,n){const r=[],s=[],i=[];let a=0;for(let l=t;l<=n;l++){const u=this.lines[l],o=e?this.a[l]:1,h=e?this.b[l]:u.length+1;for(let c=o;c<h;c++)r[a]=u.charCodeAt(c-1),s[a]=l+1,i[a]=c,a++;!e&&l<n&&(r[a]=10,s[a]=l+1,i[a]=u.length+1,a++)}return new CharSequence(r,s,i)}},CharSequence=class{constructor(e,t,n){this.a=e,this.b=t,this.d=n}toString(){return"["+this.a.map((e,t)=>(e===10?"\\n":String.fromCharCode(e))+`-(${this.b[t]},${this.d[t]})`).join(", ")+"]"}e(e,t){if(e<0||e>=t.length)throw new Error("Illegal index")}getElements(){return this.a}getStartLineNumber(e){return e>0&&e===this.b.length?this.getEndLineNumber(e-1):(this.e(e,this.b),this.b[e])}getEndLineNumber(e){return e===-1?this.getStartLineNumber(e+1):(this.e(e,this.b),this.a[e]===10?this.b[e]+1:this.b[e])}getStartColumn(e){return e>0&&e===this.d.length?this.getEndColumn(e-1):(this.e(e,this.d),this.d[e])}getEndColumn(e){return e===-1?this.getStartColumn(e+1):(this.e(e,this.d),this.a[e]===10?1:this.d[e]+1)}},CharChange=class Ve{constructor(t,n,r,s,i,a,l,u){this.originalStartLineNumber=t,this.originalStartColumn=n,this.originalEndLineNumber=r,this.originalEndColumn=s,this.modifiedStartLineNumber=i,this.modifiedStartColumn=a,this.modifiedEndLineNumber=l,this.modifiedEndColumn=u}static createFromDiffChange(t,n,r){const s=n.getStartLineNumber(t.originalStart),i=n.getStartColumn(t.originalStart),a=n.getEndLineNumber(t.originalStart+t.originalLength-1),l=n.getEndColumn(t.originalStart+t.originalLength-1),u=r.getStartLineNumber(t.modifiedStart),o=r.getStartColumn(t.modifiedStart),h=r.getEndLineNumber(t.modifiedStart+t.modifiedLength-1),c=r.getEndColumn(t.modifiedStart+t.modifiedLength-1);return new Ve(s,i,a,l,u,o,h,c)}};function postProcessCharChanges(e){if(e.length<=1)return e;const t=[e[0]];let n=t[0];for(let r=1,s=e.length;r<s;r++){const i=e[r],a=i.originalStart-(n.originalStart+n.originalLength),l=i.modifiedStart-(n.modifiedStart+n.modifiedLength);Math.min(a,l)<MINIMUM_MATCHING_CHARACTER_LENGTH?(n.originalLength=i.originalStart+i.originalLength-n.originalStart,n.modifiedLength=i.modifiedStart+i.modifiedLength-n.modifiedStart):(t.push(i),n=i)}return t}var LineChange=class Se{constructor(t,n,r,s,i){this.originalStartLineNumber=t,this.originalEndLineNumber=n,this.modifiedStartLineNumber=r,this.modifiedEndLineNumber=s,this.charChanges=i}static createFromDiffResult(t,n,r,s,i,a,l){let u,o,h,c,f;if(n.originalLength===0?(u=r.getStartLineNumber(n.originalStart)-1,o=0):(u=r.getStartLineNumber(n.originalStart),o=r.getEndLineNumber(n.originalStart+n.originalLength-1)),n.modifiedLength===0?(h=s.getStartLineNumber(n.modifiedStart)-1,c=0):(h=s.getStartLineNumber(n.modifiedStart),c=s.getEndLineNumber(n.modifiedStart+n.modifiedLength-1)),a&&n.originalLength>0&&n.originalLength<20&&n.modifiedLength>0&&n.modifiedLength<20&&i()){const g=r.createCharSequence(t,n.originalStart,n.originalStart+n.originalLength-1),m=s.createCharSequence(t,n.modifiedStart,n.modifiedStart+n.modifiedLength-1);if(g.getElements().length>0&&m.getElements().length>0){let N=computeDiff(g,m,i,!0).changes;l&&(N=postProcessCharChanges(N)),f=[];for(let p=0,d=N.length;p<d;p++)f.push(CharChange.createFromDiffChange(N[p],g,m))}}return new Se(u,o,h,c,f)}},$W5=class{constructor(e,t,n){this.a=n.shouldComputeCharChanges,this.b=n.shouldPostProcessCharChanges,this.d=n.shouldIgnoreTrimWhitespace,this.e=n.shouldMakePrettyDiff,this.f=e,this.g=t,this.h=new LineSequence(e),this.j=new LineSequence(t),this.k=createContinueProcessingPredicate(n.maxComputationTime),this.l=createContinueProcessingPredicate(n.maxComputationTime===0?0:Math.min(n.maxComputationTime,5e3))}computeDiff(){if(this.h.lines.length===1&&this.h.lines[0].length===0)return this.j.lines.length===1&&this.j.lines[0].length===0?{quitEarly:!1,changes:[]}:{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:1,modifiedStartLineNumber:1,modifiedEndLineNumber:this.j.lines.length,charChanges:void 0}]};if(this.j.lines.length===1&&this.j.lines[0].length===0)return{quitEarly:!1,changes:[{originalStartLineNumber:1,originalEndLineNumber:this.h.lines.length,modifiedStartLineNumber:1,modifiedEndLineNumber:1,charChanges:void 0}]};const e=computeDiff(this.h,this.j,this.k,this.e),t=e.changes,n=e.quitEarly;if(this.d){const a=[];for(let l=0,u=t.length;l<u;l++)a.push(LineChange.createFromDiffResult(this.d,t[l],this.h,this.j,this.l,this.a,this.b));return{quitEarly:n,changes:a}}const r=[];let s=0,i=0;for(let a=-1,l=t.length;a<l;a++){const u=a+1<l?t[a+1]:null,o=u?u.originalStart:this.f.length,h=u?u.modifiedStart:this.g.length;for(;s<o&&i<h;){const c=this.f[s],f=this.g[i];if(c!==f){{let g=getFirstNonBlankColumn(c,1),m=getFirstNonBlankColumn(f,1);for(;g>1&&m>1;){const N=c.charCodeAt(g-2),p=f.charCodeAt(m-2);if(N!==p)break;g--,m--}(g>1||m>1)&&this.m(r,s+1,1,g,i+1,1,m)}{let g=getLastNonBlankColumn(c,1),m=getLastNonBlankColumn(f,1);const N=c.length+1,p=f.length+1;for(;g<N&&m<p;){const d=c.charCodeAt(g-1),A=c.charCodeAt(m-1);if(d!==A)break;g++,m++}(g<N||m<p)&&this.m(r,s+1,g,N,i+1,m,p)}}s++,i++}u&&(r.push(LineChange.createFromDiffResult(this.d,u,this.h,this.j,this.l,this.a,this.b)),s+=u.originalLength,i+=u.modifiedLength)}return{quitEarly:n,changes:r}}m(e,t,n,r,s,i,a){if(this.n(e,t,n,r,s,i,a))return;let l;this.a&&(l=[new CharChange(t,n,t,r,s,i,s,a)]),e.push(new LineChange(t,t,s,s,l))}n(e,t,n,r,s,i,a){const l=e.length;if(l===0)return!1;const u=e[l-1];return u.originalEndLineNumber===0||u.modifiedEndLineNumber===0?!1:u.originalEndLineNumber===t&&u.modifiedEndLineNumber===s?(this.a&&u.charChanges&&u.charChanges.push(new CharChange(t,n,t,r,s,i,s,a)),!0):u.originalEndLineNumber+1===t&&u.modifiedEndLineNumber+1===s?(u.originalEndLineNumber=t,u.modifiedEndLineNumber=s,this.a&&u.charChanges&&u.charChanges.push(new CharChange(t,n,t,r,s,i,s,a)),!0):!1}};function getFirstNonBlankColumn(e,t){const n=$mg(e);return n===-1?t:n+1}function getLastNonBlankColumn(e,t){const n=$og(e);return n===-1?t:n+2}function createContinueProcessingPredicate(e){if(e===0)return()=>!0;const t=Date.now();return()=>Date.now()-t<e}var $Yzb=class Le{static trivial(t,n){return new Le([new $Zzb($lW.ofLength(t.length),$lW.ofLength(n.length))],!1)}static trivialTimedOut(t,n){return new Le([new $Zzb($lW.ofLength(t.length),$lW.ofLength(n.length))],!0)}constructor(t,n){this.diffs=t,this.hitTimeout=n}},$Zzb=class G{static invert(t,n){const r=[];return $Fb(t,(s,i)=>{r.push(G.fromOffsetPairs(s?s.getEndExclusives():$1zb.zero,i?i.getStarts():new $1zb(n,(s?s.seq2Range.endExclusive-s.seq1Range.endExclusive:0)+n)))}),r}static fromOffsetPairs(t,n){return new G(new $lW(t.offset1,n.offset1),new $lW(t.offset2,n.offset2))}static assertSorted(t){let n;for(const r of t){if(n&&!(n.seq1Range.endExclusive<=r.seq1Range.start&&n.seq2Range.endExclusive<=r.seq2Range.start))throw new $vb("Sequence diffs must be sorted");n=r}}constructor(t,n){this.seq1Range=t,this.seq2Range=n}swap(){return new G(this.seq2Range,this.seq1Range)}toString(){return`${this.seq1Range} <-> ${this.seq2Range}`}join(t){return new G(this.seq1Range.join(t.seq1Range),this.seq2Range.join(t.seq2Range))}delta(t){return t===0?this:new G(this.seq1Range.delta(t),this.seq2Range.delta(t))}deltaStart(t){return t===0?this:new G(this.seq1Range.deltaStart(t),this.seq2Range.deltaStart(t))}deltaEnd(t){return t===0?this:new G(this.seq1Range.deltaEnd(t),this.seq2Range.deltaEnd(t))}intersectsOrTouches(t){return this.seq1Range.intersectsOrTouches(t.seq1Range)||this.seq2Range.intersectsOrTouches(t.seq2Range)}intersect(t){const n=this.seq1Range.intersect(t.seq1Range),r=this.seq2Range.intersect(t.seq2Range);if(!(!n||!r))return new G(n,r)}getStarts(){return new $1zb(this.seq1Range.start,this.seq2Range.start)}getEndExclusives(){return new $1zb(this.seq1Range.endExclusive,this.seq2Range.endExclusive)}},$1zb=class de{static{this.zero=new de(0,0)}static{this.max=new de(Number.MAX_SAFE_INTEGER,Number.MAX_SAFE_INTEGER)}constructor(t,n){this.offset1=t,this.offset2=n}toString(){return`${this.offset1} <-> ${this.offset2}`}delta(t){return t===0?this:new de(this.offset1+t,this.offset2+t)}equals(t){return this.offset1===t.offset1&&this.offset2===t.offset2}},$2zb=class Be{static{this.instance=new Be}isValid(){return!0}},$3zb=class{constructor(e){if(this.e=e,this.c=Date.now(),this.d=!0,e<=0)throw new $vb("timeout must be positive")}isValid(){return!(Date.now()-this.c<this.e)&&this.d&&(this.d=!1),this.d}disable(){this.e=Number.MAX_SAFE_INTEGER,this.isValid=()=>!0,this.d=!0}},$4zb=class{constructor(e,t){this.width=e,this.height=t,this.a=[],this.a=new Array(e*t)}get(e,t){return this.a[e+t*this.width]}set(e,t,n){this.a[e+t*this.width]=n}};function $5zb(e){return e===32||e===9}var $6zb=class Ne{static{this.a=new Map}static b(t){let n=this.a.get(t);return n===void 0&&(n=this.a.size,this.a.set(t,n)),n}constructor(t,n,r){this.range=t,this.lines=n,this.source=r,this.d=[];let s=0;for(let i=t.startLineNumber-1;i<t.endLineNumberExclusive-1;i++){const a=n[i];for(let u=0;u<a.length;u++){s++;const o=a[u],h=Ne.b(o);this.d[h]=(this.d[h]||0)+1}s++;const l=Ne.b(`
`);this.d[l]=(this.d[l]||0)+1}this.c=s}computeSimilarity(t){let n=0;const r=Math.max(this.d.length,t.d.length);for(let s=0;s<r;s++)n+=Math.abs((this.d[s]??0)-(t.d[s]??0));return 1-n/(this.c+t.c)}},$7zb=class{compute(e,t,n=$2zb.instance,r){if(e.length===0||t.length===0)return $Yzb.trivial(e,t);const s=new $4zb(e.length,t.length),i=new $4zb(e.length,t.length),a=new $4zb(e.length,t.length);for(let g=0;g<e.length;g++)for(let m=0;m<t.length;m++){if(!n.isValid())return $Yzb.trivialTimedOut(e,t);const N=g===0?0:s.get(g-1,m),p=m===0?0:s.get(g,m-1);let d;e.getElement(g)===t.getElement(m)?(g===0||m===0?d=0:d=s.get(g-1,m-1),g>0&&m>0&&i.get(g-1,m-1)===3&&(d+=a.get(g-1,m-1)),d+=r?r(g,m):1):d=-1;const A=Math.max(N,p,d);if(A===d){const v=g>0&&m>0?a.get(g-1,m-1):0;a.set(g,m,v+1),i.set(g,m,3)}else A===N?(a.set(g,m,0),i.set(g,m,1)):A===p&&(a.set(g,m,0),i.set(g,m,2));s.set(g,m,A)}const l=[];let u=e.length,o=t.length;function h(g,m){(g+1!==u||m+1!==o)&&l.push(new $Zzb(new $lW(g+1,u),new $lW(m+1,o))),u=g,o=m}let c=e.length-1,f=t.length-1;for(;c>=0&&f>=0;)i.get(c,f)===3?(h(c,f),c--,f--):i.get(c,f)===1?c--:f--;return h(-1,-1),l.reverse(),new $Yzb(l,!1)}},$8zb=class{compute(e,t,n=$2zb.instance){if(e.length===0||t.length===0)return $Yzb.trivial(e,t);const r=e,s=t;function i(m,N){for(;m<r.length&&N<s.length&&r.getElement(m)===s.getElement(N);)m++,N++;return m}let a=0;const l=new FastInt32Array;l.set(0,i(0,0));const u=new FastArrayNegativeIndices;u.set(0,l.get(0)===0?null:new SnakePath(null,0,0,l.get(0)));let o=0;e:for(;;){if(a++,!n.isValid())return $Yzb.trivialTimedOut(r,s);const m=-Math.min(a,s.length+a%2),N=Math.min(a,r.length+a%2);for(o=m;o<=N;o+=2){let p=0;const d=o===N?-1:l.get(o+1),A=o===m?-1:l.get(o-1)+1;p++;const v=Math.min(Math.max(d,A),r.length),L=v-o;if(p++,v>r.length||L>s.length)continue;const b=i(v,L);l.set(o,b);const w=v===d?u.get(o+1):u.get(o-1);if(u.set(o,b!==v?new SnakePath(w,v,L,b-v):w),l.get(o)===r.length&&l.get(o)-o===s.length)break e}}let h=u.get(o);const c=[];let f=r.length,g=s.length;for(;;){const m=h?h.x+h.length:0,N=h?h.y+h.length:0;if((m!==f||N!==g)&&c.push(new $Zzb(new $lW(m,f),new $lW(N,g))),!h)break;f=h.x,g=h.y,h=h.prev}return c.reverse(),new $Yzb(c,!1)}},SnakePath=class{constructor(e,t,n,r){this.prev=e,this.x=t,this.y=n,this.length=r}},FastInt32Array=class{constructor(){this.a=new Int32Array(10),this.b=new Int32Array(10)}get(e){return e<0?(e=-e-1,this.b[e]):this.a[e]}set(e,t){if(e<0){if(e=-e-1,e>=this.b.length){const n=this.b;this.b=new Int32Array(n.length*2),this.b.set(n)}this.b[e]=t}else{if(e>=this.a.length){const n=this.a;this.a=new Int32Array(n.length*2),this.a.set(n)}this.a[e]=t}}},FastArrayNegativeIndices=class{constructor(){this.a=[],this.b=[]}get(e){return e<0?(e=-e-1,this.b[e]):this.a[e]}set(e,t){e<0?(e=-e-1,this.b[e]=t):this.a[e]=t}},$9zb=class{constructor(e,t,n){this.lines=e,this.g=t,this.considerWhitespaceChanges=n,this.b=[],this.c=[],this.d=[],this.f=[],this.c.push(0);for(let r=this.g.startLineNumber;r<=this.g.endLineNumber;r++){let s=e[r-1],i=0;r===this.g.startLineNumber&&this.g.startColumn>1&&(i=this.g.startColumn-1,s=s.substring(i)),this.d.push(i);let a=0;if(!n){const u=s.trimStart();a=s.length-u.length,s=u.trimEnd()}this.f.push(a);const l=r===this.g.endLineNumber?Math.min(this.g.endColumn-1-i-a,s.length):s.length;for(let u=0;u<l;u++)this.b.push(s.charCodeAt(u));r<this.g.endLineNumber&&(this.b.push(10),this.c.push(this.b.length))}}toString(){return`Slice: "${this.text}"`}get text(){return this.getText(new $lW(0,this.length))}getText(e){return this.b.slice(e.start,e.endExclusive).map(t=>String.fromCharCode(t)).join("")}getElement(e){return this.b[e]}get length(){return this.b.length}getBoundaryScore(e){const t=getCategory(e>0?this.b[e-1]:-1),n=getCategory(e<this.b.length?this.b[e]:-1);if(t===7&&n===8)return 0;if(t===8)return 150;let r=0;return t!==n&&(r+=10,t===0&&n===1&&(r+=1)),r+=getCategoryBoundaryScore(t),r+=getCategoryBoundaryScore(n),r}translateOffset(e,t="right"){const n=$4(this.c,s=>s<=e),r=e-this.c[n];return new $lV(this.g.startLineNumber+n,1+this.d[n]+r+(r===0&&t==="left"?0:this.f[n]))}translateRange(e){const t=this.translateOffset(e.start,"right"),n=this.translateOffset(e.endExclusive,"left");return n.isBefore(t)?$mV.fromPositions(n,n):$mV.fromPositions(t,n)}findWordContaining(e){if(e<0||e>=this.b.length||!isWordChar(this.b[e]))return;let t=e;for(;t>0&&isWordChar(this.b[t-1]);)t--;let n=e;for(;n<this.b.length&&isWordChar(this.b[n]);)n++;return new $lW(t,n)}findSubWordContaining(e){if(e<0||e>=this.b.length||!isWordChar(this.b[e]))return;let t=e;for(;t>0&&isWordChar(this.b[t-1])&&!isUpperCase(this.b[t]);)t--;let n=e;for(;n<this.b.length&&isWordChar(this.b[n])&&!isUpperCase(this.b[n]);)n++;return new $lW(t,n)}countLinesIn(e){return this.translateOffset(e.endExclusive).lineNumber-this.translateOffset(e.start).lineNumber}isStronglyEqual(e,t){return this.b[e]===this.b[t]}extendToFullLines(e){const t=$3(this.c,r=>r<=e.start)??0,n=$5(this.c,r=>e.endExclusive<=r)??this.b.length;return new $lW(t,n)}};function isWordChar(e){return e>=97&&e<=122||e>=65&&e<=90||e>=48&&e<=57}function isUpperCase(e){return e>=65&&e<=90}var CharBoundaryCategory;(function(e){e[e.WordLower=0]="WordLower",e[e.WordUpper=1]="WordUpper",e[e.WordNumber=2]="WordNumber",e[e.End=3]="End",e[e.Other=4]="Other",e[e.Separator=5]="Separator",e[e.Space=6]="Space",e[e.LineBreakCR=7]="LineBreakCR",e[e.LineBreakLF=8]="LineBreakLF"})(CharBoundaryCategory||(CharBoundaryCategory={}));var score={0:0,1:0,2:0,3:10,4:2,5:30,6:3,7:10,8:10};function getCategoryBoundaryScore(e){return score[e]}function getCategory(e){return e===10?8:e===13?7:$5zb(e)?6:e>=97&&e<=122?0:e>=65&&e<=90?1:e>=48&&e<=57?2:e===-1?3:e===44||e===59?5:4}function $0zb(e,t,n,r,s,i){let{moves:a,excludedChanges:l}=computeMovesFromSimpleDeletionsToSimpleInsertions(e,t,n,i);if(!i.isValid())return[];const u=e.filter(h=>!l.has(h)),o=computeUnchangedMoves(u,r,s,t,n,i);return $2b(a,o),a=joinCloseConsecutiveMoves(a),a=a.filter(h=>{const c=h.original.toOffsetRange().slice(t).map(g=>g.trim());return c.join(`
`).length>=15&&countWhere(c,g=>g.length>=2)>=2}),a=removeMovesInSameDiff(e,a),a}function countWhere(e,t){let n=0;for(const r of e)t(r)&&n++;return n}function computeMovesFromSimpleDeletionsToSimpleInsertions(e,t,n,r){const s=[],i=e.filter(u=>u.modified.isEmpty&&u.original.length>=3).map(u=>new $6zb(u.original,t,u)),a=new Set(e.filter(u=>u.original.isEmpty&&u.modified.length>=3).map(u=>new $6zb(u.modified,n,u))),l=new Set;for(const u of i){let o=-1,h;for(const c of a){const f=u.computeSimilarity(c);f>o&&(o=f,h=c)}if(o>.9&&h&&(a.delete(h),s.push(new $N5(u.range,h.range)),l.add(u.source),l.add(h.source)),!r.isValid())return{moves:s,excludedChanges:l}}return{moves:s,excludedChanges:l}}function computeUnchangedMoves(e,t,n,r,s,i){const a=[],l=new $Mc;for(const f of e)for(let g=f.original.startLineNumber;g<f.original.endLineNumberExclusive-2;g++){const m=`${t[g-1]}:${t[g+1-1]}:${t[g+2-1]}`;l.add(m,{range:new $vW(g,g+3)})}const u=[];e.sort($9b(f=>f.modified.startLineNumber,$$b));for(const f of e){let g=[];for(let m=f.modified.startLineNumber;m<f.modified.endLineNumberExclusive-2;m++){const N=`${n[m-1]}:${n[m+1-1]}:${n[m+2-1]}`,p=new $vW(m,m+3),d=[];l.forEach(N,({range:A})=>{for(const L of g)if(L.originalLineRange.endLineNumberExclusive+1===A.endLineNumberExclusive&&L.modifiedLineRange.endLineNumberExclusive+1===p.endLineNumberExclusive){L.originalLineRange=new $vW(L.originalLineRange.startLineNumber,A.endLineNumberExclusive),L.modifiedLineRange=new $vW(L.modifiedLineRange.startLineNumber,p.endLineNumberExclusive),d.push(L);return}const v={modifiedLineRange:p,originalLineRange:A};u.push(v),d.push(v)}),g=d}if(!i.isValid())return[]}u.sort($ac($9b(f=>f.modifiedLineRange.length,$$b)));const o=new $wW,h=new $wW;for(const f of u){const g=f.modifiedLineRange.startLineNumber-f.originalLineRange.startLineNumber,m=o.subtractFrom(f.modifiedLineRange),N=h.subtractFrom(f.originalLineRange).getWithDelta(g),p=m.getIntersection(N);for(const d of p.ranges){if(d.length<3)continue;const A=d,v=d.delta(-g);a.push(new $N5(v,A)),o.addRange(A),h.addRange(v)}}a.sort($9b(f=>f.original.startLineNumber,$$b));const c=new $8(e);for(let f=0;f<a.length;f++){const g=a[f],m=c.findLastMonotonous(w=>w.original.startLineNumber<=g.original.startLineNumber),N=$3(e,w=>w.modified.startLineNumber<=g.modified.startLineNumber),p=Math.max(g.original.startLineNumber-m.original.startLineNumber,g.modified.startLineNumber-N.modified.startLineNumber),d=c.findLastMonotonous(w=>w.original.startLineNumber<g.original.endLineNumberExclusive),A=$3(e,w=>w.modified.startLineNumber<g.modified.endLineNumberExclusive),v=Math.max(d.original.endLineNumberExclusive-g.original.endLineNumberExclusive,A.modified.endLineNumberExclusive-g.modified.endLineNumberExclusive);let L;for(L=0;L<p;L++){const w=g.original.startLineNumber-L-1,R=g.modified.startLineNumber-L-1;if(w>r.length||R>s.length||o.contains(R)||h.contains(w)||!areLinesSimilar(r[w-1],s[R-1],i))break}L>0&&(h.addRange(new $vW(g.original.startLineNumber-L,g.original.startLineNumber)),o.addRange(new $vW(g.modified.startLineNumber-L,g.modified.startLineNumber)));let b;for(b=0;b<v;b++){const w=g.original.endLineNumberExclusive+b,R=g.modified.endLineNumberExclusive+b;if(w>r.length||R>s.length||o.contains(R)||h.contains(w)||!areLinesSimilar(r[w-1],s[R-1],i))break}b>0&&(h.addRange(new $vW(g.original.endLineNumberExclusive,g.original.endLineNumberExclusive+b)),o.addRange(new $vW(g.modified.endLineNumberExclusive,g.modified.endLineNumberExclusive+b))),(L>0||b>0)&&(a[f]=new $N5(new $vW(g.original.startLineNumber-L,g.original.endLineNumberExclusive+b),new $vW(g.modified.startLineNumber-L,g.modified.endLineNumberExclusive+b)))}return a}function areLinesSimilar(e,t,n){if(e.trim()===t.trim())return!0;if(e.length>300&&t.length>300)return!1;const s=new $8zb().compute(new $9zb([e],new $mV(1,1,1,e.length),!1),new $9zb([t],new $mV(1,1,1,t.length),!1),n);let i=0;const a=$Zzb.invert(s.diffs,e.length);for(const h of a)h.seq1Range.forEach(c=>{$5zb(e.charCodeAt(c))||i++});function l(h){let c=0;for(let f=0;f<e.length;f++)$5zb(h.charCodeAt(f))||c++;return c}const u=l(e.length>t.length?e:t);return i/u>.6&&u>10}function joinCloseConsecutiveMoves(e){if(e.length===0)return e;e.sort($9b(n=>n.original.startLineNumber,$$b));const t=[e[0]];for(let n=1;n<e.length;n++){const r=t[t.length-1],s=e[n],i=s.original.startLineNumber-r.original.endLineNumberExclusive,a=s.modified.startLineNumber-r.modified.endLineNumberExclusive;if(i>=0&&a>=0&&i+a<=2){t[t.length-1]=r.join(s);continue}t.push(s)}return t}function removeMovesInSameDiff(e,t){const n=new $8(e);return t=t.filter(r=>{const s=n.findLastMonotonous(l=>l.original.startLineNumber<r.original.endLineNumberExclusive)||new $N5(new $vW(1,1),new $vW(1,1)),i=$3(e,l=>l.modified.startLineNumber<r.modified.endLineNumberExclusive);return s!==i}),t}function $_zb(e,t,n){let r=n;return r=joinSequenceDiffsByShifting(e,t,r),r=joinSequenceDiffsByShifting(e,t,r),r=shiftSequenceDiffs(e,t,r),r}function joinSequenceDiffsByShifting(e,t,n){if(n.length===0)return n;const r=[];r.push(n[0]);for(let i=1;i<n.length;i++){const a=r[r.length-1];let l=n[i];if(l.seq1Range.isEmpty||l.seq2Range.isEmpty){const u=l.seq1Range.start-a.seq1Range.endExclusive;let o;for(o=1;o<=u&&!(e.getElement(l.seq1Range.start-o)!==e.getElement(l.seq1Range.endExclusive-o)||t.getElement(l.seq2Range.start-o)!==t.getElement(l.seq2Range.endExclusive-o));o++);if(o--,o===u){r[r.length-1]=new $Zzb(new $lW(a.seq1Range.start,l.seq1Range.endExclusive-u),new $lW(a.seq2Range.start,l.seq2Range.endExclusive-u));continue}l=l.delta(-o)}r.push(l)}const s=[];for(let i=0;i<r.length-1;i++){const a=r[i+1];let l=r[i];if(l.seq1Range.isEmpty||l.seq2Range.isEmpty){const u=a.seq1Range.start-l.seq1Range.endExclusive;let o;for(o=0;o<u&&!(!e.isStronglyEqual(l.seq1Range.start+o,l.seq1Range.endExclusive+o)||!t.isStronglyEqual(l.seq2Range.start+o,l.seq2Range.endExclusive+o));o++);if(o===u){r[i+1]=new $Zzb(new $lW(l.seq1Range.start+u,a.seq1Range.endExclusive),new $lW(l.seq2Range.start+u,a.seq2Range.endExclusive));continue}o>0&&(l=l.delta(o))}s.push(l)}return r.length>0&&s.push(r[r.length-1]),s}function shiftSequenceDiffs(e,t,n){if(!e.getBoundaryScore||!t.getBoundaryScore)return n;for(let r=0;r<n.length;r++){const s=r>0?n[r-1]:void 0,i=n[r],a=r+1<n.length?n[r+1]:void 0,l=new $lW(s?s.seq1Range.endExclusive+1:0,a?a.seq1Range.start-1:e.length),u=new $lW(s?s.seq2Range.endExclusive+1:0,a?a.seq2Range.start-1:t.length);i.seq1Range.isEmpty?n[r]=shiftDiffToBetterPosition(i,e,t,l,u):i.seq2Range.isEmpty&&(n[r]=shiftDiffToBetterPosition(i.swap(),t,e,u,l).swap())}return n}function shiftDiffToBetterPosition(e,t,n,r,s){let a=1;for(;e.seq1Range.start-a>=r.start&&e.seq2Range.start-a>=s.start&&n.isStronglyEqual(e.seq2Range.start-a,e.seq2Range.endExclusive-a)&&a<100;)a++;a--;let l=0;for(;e.seq1Range.start+l<r.endExclusive&&e.seq2Range.endExclusive+l<s.endExclusive&&n.isStronglyEqual(e.seq2Range.start+l,e.seq2Range.endExclusive+l)&&l<100;)l++;if(a===0&&l===0)return e;let u=0,o=-1;for(let h=-a;h<=l;h++){const c=e.seq2Range.start+h,f=e.seq2Range.endExclusive+h,g=e.seq1Range.start+h,m=t.getBoundaryScore(g)+n.getBoundaryScore(c)+n.getBoundaryScore(f);m>o&&(o=m,u=h)}return e.delta(u)}function $aAb(e,t,n){const r=[];for(const s of n){const i=r[r.length-1];if(!i){r.push(s);continue}s.seq1Range.start-i.seq1Range.endExclusive<=2||s.seq2Range.start-i.seq2Range.endExclusive<=2?r[r.length-1]=new $Zzb(i.seq1Range.join(s.seq1Range),i.seq2Range.join(s.seq2Range)):r.push(s)}return r}function $bAb(e,t,n,r,s=!1){const i=$Zzb.invert(n,e.length),a=[];let l=new $1zb(0,0);function u(h,c){if(h.offset1<l.offset1||h.offset2<l.offset2)return;const f=r(e,h.offset1),g=r(t,h.offset2);if(!f||!g)return;let m=new $Zzb(f,g);const N=m.intersect(c);let p=N.seq1Range.length,d=N.seq2Range.length;for(;i.length>0;){const A=i[0];if(!(A.seq1Range.intersects(m.seq1Range)||A.seq2Range.intersects(m.seq2Range)))break;const L=r(e,A.seq1Range.start),b=r(t,A.seq2Range.start),w=new $Zzb(L,b),R=w.intersect(A);if(p+=R.seq1Range.length,d+=R.seq2Range.length,m=m.join(w),m.seq1Range.endExclusive>=A.seq1Range.endExclusive)i.shift();else break}(s&&p+d<m.seq1Range.length+m.seq2Range.length||p+d<(m.seq1Range.length+m.seq2Range.length)*2/3)&&a.push(m),l=m.getEndExclusives()}for(;i.length>0;){const h=i.shift();h.seq1Range.isEmpty||(u(h.getStarts(),h),u(h.getEndExclusives().delta(-1),h))}return mergeSequenceDiffs(n,a)}function mergeSequenceDiffs(e,t){const n=[];for(;e.length>0||t.length>0;){const r=e[0],s=t[0];let i;r&&(!s||r.seq1Range.start<s.seq1Range.start)?i=e.shift():i=t.shift(),n.length>0&&n[n.length-1].seq1Range.endExclusive>=i.seq1Range.start?n[n.length-1]=n[n.length-1].join(i):n.push(i)}return n}function $cAb(e,t,n){let r=n;if(r.length===0)return r;let s=0,i;do{i=!1;const l=[r[0]];for(let u=1;u<r.length;u++){let o=function(g,m){const N=new $lW(c.seq1Range.endExclusive,h.seq1Range.start);return e.getText(N).replace(/\s/g,"").length<=4&&(g.seq1Range.length+g.seq2Range.length>5||m.seq1Range.length+m.seq2Range.length>5)};var a=o;const h=r[u],c=l[l.length-1];o(c,h)?(i=!0,l[l.length-1]=l[l.length-1].join(h)):l.push(h)}r=l}while(s++<10&&i);return r}function $dAb(e,t,n){let r=n;if(r.length===0)return r;let s=0,i;do{i=!1;const u=[r[0]];for(let o=1;o<r.length;o++){let h=function(m,N){const p=new $lW(f.seq1Range.endExclusive,c.seq1Range.start);if(e.countLinesIn(p)>5||p.length>500)return!1;const A=e.getText(p).trim();if(A.length>20||A.split(/\r\n|\r|\n/).length>1)return!1;const v=e.countLinesIn(m.seq1Range),L=m.seq1Range.length,b=t.countLinesIn(m.seq2Range),w=m.seq2Range.length,R=e.countLinesIn(N.seq1Range),F=N.seq1Range.length,$=t.countLinesIn(N.seq2Range),B=N.seq2Range.length,T=2*40+50;function y(O){return Math.min(O,T)}return Math.pow(Math.pow(y(v*40+L),1.5)+Math.pow(y(b*40+w),1.5),1.5)+Math.pow(Math.pow(y(R*40+F),1.5)+Math.pow(y($*40+B),1.5),1.5)>(T**1.5)**1.5*1.3};var a=h;const c=r[o],f=u[u.length-1];h(f,c)?(i=!0,u[u.length-1]=u[u.length-1].join(c)):u.push(c)}r=u}while(s++<10&&i);const l=[];return $Gb(r,(u,o,h)=>{let c=o;function f(A){return A.length>0&&A.trim().length<=3&&o.seq1Range.length+o.seq2Range.length>100}const g=e.extendToFullLines(o.seq1Range),m=e.getText(new $lW(g.start,o.seq1Range.start));f(m)&&(c=c.deltaStart(-m.length));const N=e.getText(new $lW(o.seq1Range.endExclusive,g.endExclusive));f(N)&&(c=c.deltaEnd(N.length));const p=$Zzb.fromOffsetPairs(u?u.getEndExclusives():$1zb.zero,h?h.getStarts():$1zb.max),d=c.intersect(p);l.length>0&&d.getStarts().equals(l[l.length-1].getEndExclusives())?l[l.length-1]=l[l.length-1].join(d):l.push(d)}),l}var $$zb=class{constructor(e,t){this.a=e,this.b=t}getElement(e){return this.a[e]}get length(){return this.a.length}getBoundaryScore(e){const t=e===0?0:getIndentation(this.b[e-1]),n=e===this.b.length?0:getIndentation(this.b[e]);return 1e3-(t+n)}getText(e){return this.b.slice(e.start,e.endExclusive).join(`
`)}isStronglyEqual(e,t){return this.b[e]===this.b[t]}};function getIndentation(e){let t=0;for(;t<e.length&&(e.charCodeAt(t)===32||e.charCodeAt(t)===9);)t++;return t}var $eAb=class{constructor(){this.e=new $7zb,this.f=new $8zb}computeDiff(e,t,n){if(e.length<=1&&$yb(e,t,(L,b)=>L===b))return new $T5([],[],!1);if(e.length===1&&e[0].length===0||t.length===1&&t[0].length===0)return new $T5([new $O5(new $vW(1,e.length+1),new $vW(1,t.length+1),[new $P5(new $mV(1,1,e.length,e[e.length-1].length+1),new $mV(1,1,t.length,t[t.length-1].length+1))])],[],!1);const r=n.maxComputationTimeMs===0?$2zb.instance:new $3zb(n.maxComputationTimeMs),s=!n.ignoreTrimWhitespace,i=new Map;function a(L){let b=i.get(L);return b===void 0&&(b=i.size,i.set(L,b)),b}const l=e.map(L=>a(L.trim())),u=t.map(L=>a(L.trim())),o=new $$zb(l,e),h=new $$zb(u,t),c=o.length+h.length<1700?this.e.compute(o,h,r,(L,b)=>e[L]===t[b]?t[b].length===0?.1:1+Math.log(1+t[b].length):.99):this.f.compute(o,h,r);let f=c.diffs,g=c.hitTimeout;f=$_zb(o,h,f),f=$cAb(o,h,f);const m=[],N=L=>{if(s)for(let b=0;b<L;b++){const w=p+b,R=d+b;if(e[w]!==t[R]){const F=this.h(e,t,new $Zzb(new $lW(w,w+1),new $lW(R,R+1)),r,s,n);for(const $ of F.mappings)m.push($);F.hitTimeout&&(g=!0)}}};let p=0,d=0;for(const L of f){$Uc(()=>L.seq1Range.start-p===L.seq2Range.start-d);const b=L.seq1Range.start-p;N(b),p=L.seq1Range.endExclusive,d=L.seq2Range.endExclusive;const w=this.h(e,t,L,r,s,n);w.hitTimeout&&(g=!0);for(const R of w.mappings)m.push(R)}N(e.length-p);const A=$Q5(m,new $L5(e),new $L5(t));let v=[];return n.computeMoves&&(v=this.g(A,e,t,l,u,r,s,n)),$Uc(()=>{function L(w,R){if(w.lineNumber<1||w.lineNumber>R.length)return!1;const F=R[w.lineNumber-1];return!(w.column<1||w.column>F.length+1)}function b(w,R){return!(w.startLineNumber<1||w.startLineNumber>R.length+1||w.endLineNumberExclusive<1||w.endLineNumberExclusive>R.length+1)}for(const w of A){if(!w.innerChanges)return!1;for(const R of w.innerChanges)if(!(L(R.modifiedRange.getStartPosition(),t)&&L(R.modifiedRange.getEndPosition(),t)&&L(R.originalRange.getStartPosition(),e)&&L(R.originalRange.getEndPosition(),e)))return!1;if(!b(w.modified,t)||!b(w.original,e))return!1}return!0}),new $T5(A,v,g)}g(e,t,n,r,s,i,a,l){return $0zb(e,t,n,r,s,i).map(h=>{const c=this.h(t,n,new $Zzb(h.original.toOffsetRange(),h.modified.toOffsetRange()),i,a,l),f=$Q5(c.mappings,new $L5(t),new $L5(n),!0);return new $U5(h,f)})}h(e,t,n,r,s,i){const l=toLineRangeMapping(n).toRangeMapping2(e,t),u=new $9zb(e,l.originalRange,s),o=new $9zb(t,l.modifiedRange,s),h=u.length+o.length<500?this.e.compute(u,o,r):this.f.compute(u,o,r),c=!1;let f=h.diffs;c&&$Zzb.assertSorted(f),f=$_zb(u,o,f),c&&$Zzb.assertSorted(f),f=$bAb(u,o,f,(m,N)=>m.findWordContaining(N)),c&&$Zzb.assertSorted(f),i.extendToSubwords&&(f=$bAb(u,o,f,(m,N)=>m.findSubWordContaining(N),!0),c&&$Zzb.assertSorted(f)),f=$aAb(u,o,f),c&&$Zzb.assertSorted(f),f=$dAb(u,o,f),c&&$Zzb.assertSorted(f);const g=f.map(m=>new $P5(u.translateRange(m.seq1Range),o.translateRange(m.seq2Range)));return c&&$P5.assertSorted(g),{mappings:g,hitTimeout:h.hitTimeout}}};function toLineRangeMapping(e){return new $N5(new $vW(e.seq1Range.start+1,e.seq1Range.endExclusive+1),new $vW(e.seq2Range.start+1,e.seq2Range.endExclusive+1))}var $fAb={getLegacy:()=>new $V5,getDefault:()=>new $eAb};function roundFloat(e,t){const n=Math.pow(10,t);return Math.round(e*n)/n}var $po=class{constructor(e,t,n,r=1){this._rgbaBrand=void 0,this.r=Math.min(255,Math.max(0,e))|0,this.g=Math.min(255,Math.max(0,t))|0,this.b=Math.min(255,Math.max(0,n))|0,this.a=roundFloat(Math.max(Math.min(1,r),0),3)}static equals(e,t){return e.r===t.r&&e.g===t.g&&e.b===t.b&&e.a===t.a}},$qo=class le{constructor(t,n,r,s){this._hslaBrand=void 0,this.h=Math.max(Math.min(360,t),0)|0,this.s=roundFloat(Math.max(Math.min(1,n),0),3),this.l=roundFloat(Math.max(Math.min(1,r),0),3),this.a=roundFloat(Math.max(Math.min(1,s),0),3)}static equals(t,n){return t.h===n.h&&t.s===n.s&&t.l===n.l&&t.a===n.a}static fromRGBA(t){const n=t.r/255,r=t.g/255,s=t.b/255,i=t.a,a=Math.max(n,r,s),l=Math.min(n,r,s);let u=0,o=0;const h=(l+a)/2,c=a-l;if(c>0){switch(o=Math.min(h<=.5?c/(2*h):c/(2-2*h),1),a){case n:u=(r-s)/c+(r<s?6:0);break;case r:u=(s-n)/c+2;break;case s:u=(n-r)/c+4;break}u*=60,u=Math.round(u)}return new le(u,o,h,i)}static i(t,n,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+(n-t)*6*r:r<1/2?n:r<2/3?t+(n-t)*(2/3-r)*6:t}static toRGBA(t){const n=t.h/360,{s:r,l:s,a:i}=t;let a,l,u;if(r===0)a=l=u=s;else{const o=s<.5?s*(1+r):s+r-s*r,h=2*s-o;a=le.i(h,o,n+1/3),l=le.i(h,o,n),u=le.i(h,o,n-1/3)}return new $po(Math.round(a*255),Math.round(l*255),Math.round(u*255),i)}},$ro=class qe{constructor(t,n,r,s){this._hsvaBrand=void 0,this.h=Math.max(Math.min(360,t),0)|0,this.s=roundFloat(Math.max(Math.min(1,n),0),3),this.v=roundFloat(Math.max(Math.min(1,r),0),3),this.a=roundFloat(Math.max(Math.min(1,s),0),3)}static equals(t,n){return t.h===n.h&&t.s===n.s&&t.v===n.v&&t.a===n.a}static fromRGBA(t){const n=t.r/255,r=t.g/255,s=t.b/255,i=Math.max(n,r,s),a=Math.min(n,r,s),l=i-a,u=i===0?0:l/i;let o;return l===0?o=0:i===n?o=((r-s)/l%6+6)%6:i===r?o=(s-n)/l+2:o=(n-r)/l+4,new qe(Math.round(o*60),u,i,t.a)}static toRGBA(t){const{h:n,s:r,v:s,a:i}=t,a=s*r,l=a*(1-Math.abs(n/60%2-1)),u=s-a;let[o,h,c]=[0,0,0];return n<60?(o=a,h=l):n<120?(o=l,h=a):n<180?(h=a,c=l):n<240?(h=l,c=a):n<300?(o=l,c=a):n<=360&&(o=a,c=l),o=Math.round((o+u)*255),h=Math.round((h+u)*255),c=Math.round((c+u)*255),new $po(o,h,c,i)}},$so=class V{static fromHex(t){return V.Format.CSS.parseHex(t)||V.red}static equals(t,n){return!t&&!n?!0:!t||!n?!1:t.equals(n)}get hsla(){return this.i?this.i:$qo.fromRGBA(this.rgba)}get hsva(){return this.j?this.j:$ro.fromRGBA(this.rgba)}constructor(t){if(t)if(t instanceof $po)this.rgba=t;else if(t instanceof $qo)this.i=t,this.rgba=$qo.toRGBA(t);else if(t instanceof $ro)this.j=t,this.rgba=$ro.toRGBA(t);else throw new Error("Invalid color ctor argument");else throw new Error("Color needs a value")}equals(t){return!!t&&$po.equals(this.rgba,t.rgba)&&$qo.equals(this.hsla,t.hsla)&&$ro.equals(this.hsva,t.hsva)}getRelativeLuminance(){const t=V.k(this.rgba.r),n=V.k(this.rgba.g),r=V.k(this.rgba.b),s=.2126*t+.7152*n+.0722*r;return roundFloat(s,4)}reduceRelativeLuminace(t,n){let{r,g:s,b:i}=t.rgba,a=this.getContrastRatio(t);for(;a<n&&(r>0||s>0||i>0);)r-=Math.max(0,Math.ceil(r*.1)),s-=Math.max(0,Math.ceil(s*.1)),i-=Math.max(0,Math.ceil(i*.1)),a=this.getContrastRatio(new V(new $po(r,s,i)));return new V(new $po(r,s,i))}increaseRelativeLuminace(t,n){let{r,g:s,b:i}=t.rgba,a=this.getContrastRatio(t);for(;a<n&&(r<255||s<255||i<255);)r=Math.min(255,r+Math.ceil((255-r)*.1)),s=Math.min(255,s+Math.ceil((255-s)*.1)),i=Math.min(255,i+Math.ceil((255-i)*.1)),a=this.getContrastRatio(new V(new $po(r,s,i)));return new V(new $po(r,s,i))}static k(t){const n=t/255;return n<=.03928?n/12.92:Math.pow((n+.055)/1.055,2.4)}getContrastRatio(t){const n=this.getRelativeLuminance(),r=t.getRelativeLuminance();return n>r?(n+.05)/(r+.05):(r+.05)/(n+.05)}isDarker(){return(this.rgba.r*299+this.rgba.g*587+this.rgba.b*114)/1e3<128}isLighter(){return(this.rgba.r*299+this.rgba.g*587+this.rgba.b*114)/1e3>=128}isLighterThan(t){const n=this.getRelativeLuminance(),r=t.getRelativeLuminance();return n>r}isDarkerThan(t){const n=this.getRelativeLuminance(),r=t.getRelativeLuminance();return n<r}ensureConstrast(t,n){const r=this.getRelativeLuminance(),s=t.getRelativeLuminance();if(this.getContrastRatio(t)<n){if(s<r){const u=this.reduceRelativeLuminace(t,n),o=this.getContrastRatio(u);if(o<n){const h=this.increaseRelativeLuminace(t,n),c=this.getContrastRatio(h);return o>c?u:h}return u}const a=this.increaseRelativeLuminace(t,n),l=this.getContrastRatio(a);if(l<n){const u=this.reduceRelativeLuminace(t,n),o=this.getContrastRatio(u);return l>o?a:u}return a}return t}lighten(t){return new V(new $qo(this.hsla.h,this.hsla.s,this.hsla.l+this.hsla.l*t,this.hsla.a))}darken(t){return new V(new $qo(this.hsla.h,this.hsla.s,this.hsla.l-this.hsla.l*t,this.hsla.a))}transparent(t){const{r:n,g:r,b:s,a:i}=this.rgba;return new V(new $po(n,r,s,i*t))}isTransparent(){return this.rgba.a===0}isOpaque(){return this.rgba.a===1}opposite(){return new V(new $po(255-this.rgba.r,255-this.rgba.g,255-this.rgba.b,this.rgba.a))}blend(t){const n=t.rgba,r=this.rgba.a,s=n.a,i=r+s*(1-r);if(i<1e-6)return V.transparent;const a=this.rgba.r*r/i+n.r*s*(1-r)/i,l=this.rgba.g*r/i+n.g*s*(1-r)/i,u=this.rgba.b*r/i+n.b*s*(1-r)/i;return new V(new $po(a,l,u,i))}makeOpaque(t){if(this.isOpaque()||t.rgba.a!==1)return this;const{r:n,g:r,b:s,a:i}=this.rgba;return new V(new $po(t.rgba.r-i*(t.rgba.r-n),t.rgba.g-i*(t.rgba.g-r),t.rgba.b-i*(t.rgba.b-s),1))}flatten(...t){const n=t.reduceRight((r,s)=>V.o(s,r));return V.o(this,n)}static o(t,n){const r=1-t.rgba.a;return new V(new $po(r*n.rgba.r+t.rgba.a*t.rgba.r,r*n.rgba.g+t.rgba.a*t.rgba.g,r*n.rgba.b+t.rgba.a*t.rgba.b))}toString(){return this.u||(this.u=V.Format.CSS.format(this)),this.u}toNumber32Bit(){return this.w||(this.w=(this.rgba.r<<24|this.rgba.g<<16|this.rgba.b<<8|this.rgba.a*255<<0)>>>0),this.w}static getLighterColor(t,n,r){if(t.isLighterThan(n))return t;r=r||.5;const s=t.getRelativeLuminance(),i=n.getRelativeLuminance();return r=r*(i-s)/i,t.lighten(r)}static getDarkerColor(t,n,r){if(t.isDarkerThan(n))return t;r=r||.5;const s=t.getRelativeLuminance(),i=n.getRelativeLuminance();return r=r*(s-i)/s,t.darken(r)}static{this.white=new V(new $po(255,255,255,1))}static{this.black=new V(new $po(0,0,0,1))}static{this.red=new V(new $po(255,0,0,1))}static{this.blue=new V(new $po(0,0,255,1))}static{this.green=new V(new $po(0,255,0,1))}static{this.cyan=new V(new $po(0,255,255,1))}static{this.lightgrey=new V(new $po(211,211,211,1))}static{this.transparent=new V(new $po(0,0,0,0))}};(function(e){let t;(function(n){let r;(function(s){function i(d){return d.rgba.a===1?`rgb(${d.rgba.r}, ${d.rgba.g}, ${d.rgba.b})`:e.Format.CSS.formatRGBA(d)}s.formatRGB=i;function a(d){return`rgba(${d.rgba.r}, ${d.rgba.g}, ${d.rgba.b}, ${+d.rgba.a.toFixed(2)})`}s.formatRGBA=a;function l(d){return d.hsla.a===1?`hsl(${d.hsla.h}, ${(d.hsla.s*100).toFixed(2)}%, ${(d.hsla.l*100).toFixed(2)}%)`:e.Format.CSS.formatHSLA(d)}s.formatHSL=l;function u(d){return`hsla(${d.hsla.h}, ${(d.hsla.s*100).toFixed(2)}%, ${(d.hsla.l*100).toFixed(2)}%, ${d.hsla.a.toFixed(2)})`}s.formatHSLA=u;function o(d){const A=d.toString(16);return A.length!==2?"0"+A:A}function h(d){return`#${o(d.rgba.r)}${o(d.rgba.g)}${o(d.rgba.b)}`}s.formatHex=h;function c(d,A=!1){return A&&d.rgba.a===1?e.Format.CSS.formatHex(d):`#${o(d.rgba.r)}${o(d.rgba.g)}${o(d.rgba.b)}${o(Math.round(d.rgba.a*255))}`}s.formatHexA=c;function f(d){return d.isOpaque()?e.Format.CSS.formatHex(d):e.Format.CSS.formatRGBA(d)}s.format=f;function g(d){if(d==="transparent")return e.transparent;if(d.startsWith("#"))return N(d);if(d.startsWith("rgba(")){const A=d.match(/rgba\((?<r>(?:\+|-)?\d+), *(?<g>(?:\+|-)?\d+), *(?<b>(?:\+|-)?\d+), *(?<a>(?:\+|-)?\d+(\.\d+)?)\)/);if(!A)throw new Error("Invalid color format "+d);const v=parseInt(A.groups?.r??"0"),L=parseInt(A.groups?.g??"0"),b=parseInt(A.groups?.b??"0"),w=parseFloat(A.groups?.a??"0");return new e(new $po(v,L,b,w))}if(d.startsWith("rgb(")){const A=d.match(/rgb\((?<r>(?:\+|-)?\d+), *(?<g>(?:\+|-)?\d+), *(?<b>(?:\+|-)?\d+)\)/);if(!A)throw new Error("Invalid color format "+d);const v=parseInt(A.groups?.r??"0"),L=parseInt(A.groups?.g??"0"),b=parseInt(A.groups?.b??"0");return new e(new $po(v,L,b))}return m(d)}s.parse=g;function m(d){switch(d){case"aliceblue":return new e(new $po(240,248,255,1));case"antiquewhite":return new e(new $po(250,235,215,1));case"aqua":return new e(new $po(0,255,255,1));case"aquamarine":return new e(new $po(127,255,212,1));case"azure":return new e(new $po(240,255,255,1));case"beige":return new e(new $po(245,245,220,1));case"bisque":return new e(new $po(255,228,196,1));case"black":return new e(new $po(0,0,0,1));case"blanchedalmond":return new e(new $po(255,235,205,1));case"blue":return new e(new $po(0,0,255,1));case"blueviolet":return new e(new $po(138,43,226,1));case"brown":return new e(new $po(165,42,42,1));case"burlywood":return new e(new $po(222,184,135,1));case"cadetblue":return new e(new $po(95,158,160,1));case"chartreuse":return new e(new $po(127,255,0,1));case"chocolate":return new e(new $po(210,105,30,1));case"coral":return new e(new $po(255,127,80,1));case"cornflowerblue":return new e(new $po(100,149,237,1));case"cornsilk":return new e(new $po(255,248,220,1));case"crimson":return new e(new $po(220,20,60,1));case"cyan":return new e(new $po(0,255,255,1));case"darkblue":return new e(new $po(0,0,139,1));case"darkcyan":return new e(new $po(0,139,139,1));case"darkgoldenrod":return new e(new $po(184,134,11,1));case"darkgray":return new e(new $po(169,169,169,1));case"darkgreen":return new e(new $po(0,100,0,1));case"darkgrey":return new e(new $po(169,169,169,1));case"darkkhaki":return new e(new $po(189,183,107,1));case"darkmagenta":return new e(new $po(139,0,139,1));case"darkolivegreen":return new e(new $po(85,107,47,1));case"darkorange":return new e(new $po(255,140,0,1));case"darkorchid":return new e(new $po(153,50,204,1));case"darkred":return new e(new $po(139,0,0,1));case"darksalmon":return new e(new $po(233,150,122,1));case"darkseagreen":return new e(new $po(143,188,143,1));case"darkslateblue":return new e(new $po(72,61,139,1));case"darkslategray":return new e(new $po(47,79,79,1));case"darkslategrey":return new e(new $po(47,79,79,1));case"darkturquoise":return new e(new $po(0,206,209,1));case"darkviolet":return new e(new $po(148,0,211,1));case"deeppink":return new e(new $po(255,20,147,1));case"deepskyblue":return new e(new $po(0,191,255,1));case"dimgray":return new e(new $po(105,105,105,1));case"dimgrey":return new e(new $po(105,105,105,1));case"dodgerblue":return new e(new $po(30,144,255,1));case"firebrick":return new e(new $po(178,34,34,1));case"floralwhite":return new e(new $po(255,250,240,1));case"forestgreen":return new e(new $po(34,139,34,1));case"fuchsia":return new e(new $po(255,0,255,1));case"gainsboro":return new e(new $po(220,220,220,1));case"ghostwhite":return new e(new $po(248,248,255,1));case"gold":return new e(new $po(255,215,0,1));case"goldenrod":return new e(new $po(218,165,32,1));case"gray":return new e(new $po(128,128,128,1));case"green":return new e(new $po(0,128,0,1));case"greenyellow":return new e(new $po(173,255,47,1));case"grey":return new e(new $po(128,128,128,1));case"honeydew":return new e(new $po(240,255,240,1));case"hotpink":return new e(new $po(255,105,180,1));case"indianred":return new e(new $po(205,92,92,1));case"indigo":return new e(new $po(75,0,130,1));case"ivory":return new e(new $po(255,255,240,1));case"khaki":return new e(new $po(240,230,140,1));case"lavender":return new e(new $po(230,230,250,1));case"lavenderblush":return new e(new $po(255,240,245,1));case"lawngreen":return new e(new $po(124,252,0,1));case"lemonchiffon":return new e(new $po(255,250,205,1));case"lightblue":return new e(new $po(173,216,230,1));case"lightcoral":return new e(new $po(240,128,128,1));case"lightcyan":return new e(new $po(224,255,255,1));case"lightgoldenrodyellow":return new e(new $po(250,250,210,1));case"lightgray":return new e(new $po(211,211,211,1));case"lightgreen":return new e(new $po(144,238,144,1));case"lightgrey":return new e(new $po(211,211,211,1));case"lightpink":return new e(new $po(255,182,193,1));case"lightsalmon":return new e(new $po(255,160,122,1));case"lightseagreen":return new e(new $po(32,178,170,1));case"lightskyblue":return new e(new $po(135,206,250,1));case"lightslategray":return new e(new $po(119,136,153,1));case"lightslategrey":return new e(new $po(119,136,153,1));case"lightsteelblue":return new e(new $po(176,196,222,1));case"lightyellow":return new e(new $po(255,255,224,1));case"lime":return new e(new $po(0,255,0,1));case"limegreen":return new e(new $po(50,205,50,1));case"linen":return new e(new $po(250,240,230,1));case"magenta":return new e(new $po(255,0,255,1));case"maroon":return new e(new $po(128,0,0,1));case"mediumaquamarine":return new e(new $po(102,205,170,1));case"mediumblue":return new e(new $po(0,0,205,1));case"mediumorchid":return new e(new $po(186,85,211,1));case"mediumpurple":return new e(new $po(147,112,219,1));case"mediumseagreen":return new e(new $po(60,179,113,1));case"mediumslateblue":return new e(new $po(123,104,238,1));case"mediumspringgreen":return new e(new $po(0,250,154,1));case"mediumturquoise":return new e(new $po(72,209,204,1));case"mediumvioletred":return new e(new $po(199,21,133,1));case"midnightblue":return new e(new $po(25,25,112,1));case"mintcream":return new e(new $po(245,255,250,1));case"mistyrose":return new e(new $po(255,228,225,1));case"moccasin":return new e(new $po(255,228,181,1));case"navajowhite":return new e(new $po(255,222,173,1));case"navy":return new e(new $po(0,0,128,1));case"oldlace":return new e(new $po(253,245,230,1));case"olive":return new e(new $po(128,128,0,1));case"olivedrab":return new e(new $po(107,142,35,1));case"orange":return new e(new $po(255,165,0,1));case"orangered":return new e(new $po(255,69,0,1));case"orchid":return new e(new $po(218,112,214,1));case"palegoldenrod":return new e(new $po(238,232,170,1));case"palegreen":return new e(new $po(152,251,152,1));case"paleturquoise":return new e(new $po(175,238,238,1));case"palevioletred":return new e(new $po(219,112,147,1));case"papayawhip":return new e(new $po(255,239,213,1));case"peachpuff":return new e(new $po(255,218,185,1));case"peru":return new e(new $po(205,133,63,1));case"pink":return new e(new $po(255,192,203,1));case"plum":return new e(new $po(221,160,221,1));case"powderblue":return new e(new $po(176,224,230,1));case"purple":return new e(new $po(128,0,128,1));case"rebeccapurple":return new e(new $po(102,51,153,1));case"red":return new e(new $po(255,0,0,1));case"rosybrown":return new e(new $po(188,143,143,1));case"royalblue":return new e(new $po(65,105,225,1));case"saddlebrown":return new e(new $po(139,69,19,1));case"salmon":return new e(new $po(250,128,114,1));case"sandybrown":return new e(new $po(244,164,96,1));case"seagreen":return new e(new $po(46,139,87,1));case"seashell":return new e(new $po(255,245,238,1));case"sienna":return new e(new $po(160,82,45,1));case"silver":return new e(new $po(192,192,192,1));case"skyblue":return new e(new $po(135,206,235,1));case"slateblue":return new e(new $po(106,90,205,1));case"slategray":return new e(new $po(112,128,144,1));case"slategrey":return new e(new $po(112,128,144,1));case"snow":return new e(new $po(255,250,250,1));case"springgreen":return new e(new $po(0,255,127,1));case"steelblue":return new e(new $po(70,130,180,1));case"tan":return new e(new $po(210,180,140,1));case"teal":return new e(new $po(0,128,128,1));case"thistle":return new e(new $po(216,191,216,1));case"tomato":return new e(new $po(255,99,71,1));case"turquoise":return new e(new $po(64,224,208,1));case"violet":return new e(new $po(238,130,238,1));case"wheat":return new e(new $po(245,222,179,1));case"white":return new e(new $po(255,255,255,1));case"whitesmoke":return new e(new $po(245,245,245,1));case"yellow":return new e(new $po(255,255,0,1));case"yellowgreen":return new e(new $po(154,205,50,1));default:return null}}function N(d){const A=d.length;if(A===0||d.charCodeAt(0)!==35)return null;if(A===7){const v=16*p(d.charCodeAt(1))+p(d.charCodeAt(2)),L=16*p(d.charCodeAt(3))+p(d.charCodeAt(4)),b=16*p(d.charCodeAt(5))+p(d.charCodeAt(6));return new e(new $po(v,L,b,1))}if(A===9){const v=16*p(d.charCodeAt(1))+p(d.charCodeAt(2)),L=16*p(d.charCodeAt(3))+p(d.charCodeAt(4)),b=16*p(d.charCodeAt(5))+p(d.charCodeAt(6)),w=16*p(d.charCodeAt(7))+p(d.charCodeAt(8));return new e(new $po(v,L,b,w/255))}if(A===4){const v=p(d.charCodeAt(1)),L=p(d.charCodeAt(2)),b=p(d.charCodeAt(3));return new e(new $po(16*v+v,16*L+L,16*b+b))}if(A===5){const v=p(d.charCodeAt(1)),L=p(d.charCodeAt(2)),b=p(d.charCodeAt(3)),w=p(d.charCodeAt(4));return new e(new $po(16*v+v,16*L+L,16*b+b,(16*w+w)/255))}return null}s.parseHex=N;function p(d){switch(d){case 48:return 0;case 49:return 1;case 50:return 2;case 51:return 3;case 52:return 4;case 53:return 5;case 54:return 6;case 55:return 7;case 56:return 8;case 57:return 9;case 97:return 10;case 65:return 10;case 98:return 11;case 66:return 11;case 99:return 12;case 67:return 12;case 100:return 13;case 68:return 13;case 101:return 14;case 69:return 14;case 102:return 15;case 70:return 15}return 0}})(r=n.CSS||(n.CSS={}))})(t=e.Format||(e.Format={}))})($so||($so={}));function _parseCaptureGroups(e){const t=[];for(const n of e){const r=Number(n);(r||r===0&&n.replace(/\s/g,"")!=="")&&t.push(r)}return t}function _toIColor(e,t,n,r){return{red:e/255,blue:n/255,green:t/255,alpha:r}}function _findRange(e,t){const n=t.index,r=t[0].length;if(n===void 0)return;const s=e.positionAt(n);return{startLineNumber:s.lineNumber,startColumn:s.column,endLineNumber:s.lineNumber,endColumn:s.column+r}}function _findHexColorInformation(e,t){if(!e)return;const n=$so.Format.CSS.parseHex(t);if(n)return{range:e,color:_toIColor(n.rgba.r,n.rgba.g,n.rgba.b,n.rgba.a)}}function _findRGBColorInformation(e,t,n){if(!e||t.length!==1)return;const s=t[0].values(),i=_parseCaptureGroups(s);return{range:e,color:_toIColor(i[0],i[1],i[2],n?i[3]:1)}}function _findHSLColorInformation(e,t,n){if(!e||t.length!==1)return;const s=t[0].values(),i=_parseCaptureGroups(s),a=new $so(new $qo(i[0],i[1]/100,i[2]/100,n?i[3]:1));return{range:e,color:_toIColor(a.rgba.r,a.rgba.g,a.rgba.b,a.rgba.a)}}function _findMatches(e,t){return typeof e=="string"?[...e.matchAll(t)]:e.findMatches(t)}function computeColors(e){const t=[],r=_findMatches(e,/\b(rgb|rgba|hsl|hsla)(\([0-9\s,.\%]*\))|\s+(#)([A-Fa-f0-9]{6})\b|\s+(#)([A-Fa-f0-9]{8})\b|^(#)([A-Fa-f0-9]{6})\b|^(#)([A-Fa-f0-9]{8})\b/gm);if(r.length>0)for(const s of r){const i=s.filter(o=>o!==void 0),a=i[1],l=i[2];if(!l)continue;let u;if(a==="rgb"){const o=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*\)$/gm;u=_findRGBColorInformation(_findRange(e,s),_findMatches(l,o),!1)}else if(a==="rgba"){const o=/^\(\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9][0-9]|[0-9])\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;u=_findRGBColorInformation(_findRange(e,s),_findMatches(l,o),!0)}else if(a==="hsl"){const o=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*\)$/gm;u=_findHSLColorInformation(_findRange(e,s),_findMatches(l,o),!1)}else if(a==="hsla"){const o=/^\(\s*(36[0]|3[0-5][0-9]|[12][0-9][0-9]|[1-9]?[0-9])\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(100|\d{1,2}[.]\d*|\d{1,2})%\s*,\s*(0[.][0-9]+|[.][0-9]+|[01][.]|[01])\s*\)$/gm;u=_findHSLColorInformation(_findRange(e,s),_findMatches(l,o),!0)}else a==="#"&&(u=_findHexColorInformation(_findRange(e,s),a+l));u&&t.push(u)}return t}function $gAb(e){return!e||typeof e.getValue!="function"||typeof e.positionAt!="function"?[]:computeColors(e)}var trimDashesRegex=/^-+|-+$/g,CHUNK_SIZE=100,MAX_SECTION_LINES=5;function $hAb(e,t){let n=[];if(t.findRegionSectionHeaders&&t.foldingRules?.markers){const r=collectRegionHeaders(e,t);n=n.concat(r)}if(t.findMarkSectionHeaders){const r=$iAb(e,t);n=n.concat(r)}return n}function collectRegionHeaders(e,t){const n=[],r=e.getLineCount();for(let s=1;s<=r;s++){const i=e.getLineContent(s),a=i.match(t.foldingRules.markers.start);if(a){const l={startLineNumber:s,startColumn:a[0].length+1,endLineNumber:s,endColumn:i.length+1};if(l.endColumn>l.startColumn){const u={range:l,...getHeaderText(i.substring(a[0].length)),shouldBeInComments:!1};(u.text||u.hasSeparatorLine)&&n.push(u)}}}return n}function $iAb(e,t){const n=[],r=e.getLineCount(),s=$l1(t.markSectionHeaderRegex),i=new RegExp(t.markSectionHeaderRegex,`gdm${s?"s":""}`);for(let a=1;a<=r;a+=CHUNK_SIZE-MAX_SECTION_LINES){const l=Math.min(a+CHUNK_SIZE-1,r),u=[];for(let c=a;c<=l;c++)u.push(e.getLineContent(c));const o=u.join(`
`);i.lastIndex=0;let h;for(;(h=i.exec(o))!==null;){const c=o.substring(0,h.index),f=(c.match(/\n/g)||[]).length,g=a+f,m=h[0].split(`
`),N=m.length,p=g+N-1,d=c.lastIndexOf(`
`)+1,A=h.index-d+1,v=m[m.length-1],L=N===1?A+h[0].length:v.length+1,b={startLineNumber:g,startColumn:A,endLineNumber:p,endColumn:L},w=(h.groups??{}).label??"",R=((h.groups??{}).separator??"")!=="",F={range:b,text:w,hasSeparatorLine:R,shouldBeInComments:!0};(F.text||F.hasSeparatorLine)&&(n.length===0||n[n.length-1].range.endLineNumber<F.range.startLineNumber)&&n.push(F),i.lastIndex=h.index+h[0].length}}return n}function getHeaderText(e){e=e.trim();const t=e.startsWith("-");return e=e.replace(trimDashesRegex,""),{text:e,hasSeparatorLine:t}}function $$g(e){return e===47||e===92}function $_g(e){return e.replace(/[\\/]/g,$lc.sep)}function $ah(e){return e.indexOf("/")===-1&&(e=$_g(e)),/^[a-zA-Z]:(\/|$)/.test(e)&&(e="/"+e),e}function $bh(e,t=$lc.sep){if(!e)return"";const n=e.length,r=e.charCodeAt(0);if($$g(r)){if($$g(e.charCodeAt(1))&&!$$g(e.charCodeAt(2))){let i=3;const a=i;for(;i<n&&!$$g(e.charCodeAt(i));i++);if(a!==i&&!$$g(e.charCodeAt(i+1))){for(i+=1;i<n;i++)if($$g(e.charCodeAt(i)))return e.slice(0,i+1).replace(/[\\/]/g,t)}}return t}else if($gh(r)&&e.charCodeAt(1)===58)return $$g(e.charCodeAt(2))?e.slice(0,2)+t:e.slice(0,2);let s=e.indexOf("://");if(s!==-1){for(s+=3;s<n;s++)if($$g(e.charCodeAt(s)))return e.slice(0,s+1)}return""}function $fh(e,t,n,r=sep){if(e===t)return!0;if(!e||!t||t.length>e.length)return!1;if(n){if(!$zg(e,t))return!1;if(t.length===e.length)return!0;let i=t.length;return t.charAt(t.length-1)===r&&i--,e.charAt(i)===r}return t.charAt(t.length-1)!==r&&(t+=r),e.indexOf(t)===0}function $gh(e){return e>=65&&e<=90||e>=97&&e<=122}var Schemas;(function(e){e.inMemory="inmemory",e.vscode="vscode",e.internal="private",e.walkThrough="walkThrough",e.walkThroughSnippet="walkThroughSnippet",e.http="http",e.https="https",e.file="file",e.mailto="mailto",e.untitled="untitled",e.data="data",e.command="command",e.vscodeRemote="vscode-remote",e.vscodeRemoteResource="vscode-remote-resource",e.vscodeManagedRemoteResource="vscode-managed-remote-resource",e.vscodeUserData="vscode-userdata",e.vscodeCustomEditor="vscode-custom-editor",e.vscodeNotebookCell="vscode-notebook-cell",e.vscodeNotebookCellMetadata="vscode-notebook-cell-metadata",e.vscodeNotebookCellMetadataDiff="vscode-notebook-cell-metadata-diff",e.vscodeNotebookCellOutput="vscode-notebook-cell-output",e.vscodeNotebookCellOutputDiff="vscode-notebook-cell-output-diff",e.vscodeNotebookMetadata="vscode-notebook-metadata",e.vscodeInteractiveInput="vscode-interactive-input",e.vscodeSettings="vscode-settings",e.vscodeWorkspaceTrust="vscode-workspace-trust",e.vscodeTerminal="vscode-terminal",e.vscodeChatCodeBlock="vscode-chat-code-block",e.vscodeChatCodeCompareBlock="vscode-chat-code-compare-block",e.vscodeChatSesssion="vscode-chat-editor",e.webviewPanel="webview-panel",e.vscodeWebview="vscode-webview",e.extension="extension",e.vscodeFileResource="vscode-file",e.tmp="tmp",e.vsls="vsls",e.vscodeSourceControl="vscode-scm",e.commentsInput="comment",e.codeSetting="code-setting",e.outputChannel="output",e.accessibleView="accessible-view"})(Schemas||(Schemas={}));var $sh="tkn",RemoteAuthoritiesImpl=class{constructor(){this.a=Object.create(null),this.b=Object.create(null),this.c=Object.create(null),this.d="http",this.e=null,this.f="/"}setPreferredWebSchema(e){this.d=e}setDelegate(e){this.e=e}setServerRootPath(e,t){this.f=$lc.join(t??"/",$uh(e))}getServerRootPath(){return this.f}get g(){return $lc.join(this.f,Schemas.vscodeRemoteResource)}set(e,t,n){this.a[e]=t,this.b[e]=n}setConnectionToken(e,t){this.c[e]=t}getPreferredWebSchema(){return this.d}rewrite(e){if(this.e)try{return this.e(e)}catch(a){return $gb(a),e}const t=e.authority;let n=this.a[t];n&&n.indexOf(":")!==-1&&n.indexOf("[")===-1&&(n=`[${n}]`);const r=this.b[t],s=this.c[t];let i=`path=${encodeURIComponent(e.path)}`;return typeof s=="string"&&(i+=`&${$sh}=${encodeURIComponent(s)}`),URI.from({scheme:$r?this.d:Schemas.vscodeRemoteResource,authority:`${n}:${r}`,path:this.g,query:i})}},$th=new RemoteAuthoritiesImpl;function $uh(e){return`${e.quality??"oss"}-${e.commit??"dev"}`}var $zh="vscode-app",FileAccessImpl=class be{static{this.a=$zh}asBrowserUri(t){const n=this.b(t);return this.uriToBrowserUri(n)}uriToBrowserUri(t){return t.scheme===Schemas.vscodeRemote?$th.rewrite(t):t.scheme===Schemas.file&&($p||$t===`${Schemas.vscodeFileResource}://${be.a}`)?t.with({scheme:Schemas.vscodeFileResource,authority:t.authority||be.a,query:null,fragment:null}):t}asFileUri(t){const n=this.b(t);return this.uriToFileUri(n)}uriToFileUri(t){return t.scheme===Schemas.vscodeFileResource?t.with({scheme:Schemas.file,authority:t.authority!==be.a?t.authority:null,query:null,fragment:null}):t}b(t){if(URI.isUri(t))return t;if(globalThis._VSCODE_FILE_ROOT){const n=globalThis._VSCODE_FILE_ROOT;if(/^\w[\w\d+.-]*:\/\//.test(n))return URI.joinPath(URI.parse(n,!0),t);const r=$oc(n,t);return URI.file(r)}throw new Error("Cannot determine URI for module id!")}},$Ah=new FileAccessImpl,$Bh=Object.freeze({"Cache-Control":"no-cache, no-store"}),$Ch=Object.freeze({"Document-Policy":"include-js-call-stacks-in-crash-reports"}),COI;(function(e){const t=new Map([["1",{"Cross-Origin-Opener-Policy":"same-origin"}],["2",{"Cross-Origin-Embedder-Policy":"require-corp"}],["3",{"Cross-Origin-Opener-Policy":"same-origin","Cross-Origin-Embedder-Policy":"require-corp"}]]);e.CoopAndCoep=Object.freeze(t.get("3"));const n="vscode-coi";function r(i){let a;typeof i=="string"?a=new URL(i).searchParams:i instanceof URL?a=i.searchParams:URI.isUri(i)&&(a=new URL(i.toString(!0)).searchParams);const l=a?.get(n);if(l)return t.get(l)}e.getHeadersFromQuery=r;function s(i,a,l){if(!globalThis.crossOriginIsolated)return;const u=a&&l?"3":l?"2":"1";i instanceof URLSearchParams?i.set(n,u):i[n]=u}e.addSearchParam=s})(COI||(COI={}));function $Dh(e){return $Bc(e,!0)}var $Eh=class{constructor(e){this.a=e}compare(e,t,n=!1){return e===t?0:$rg(this.getComparisonKey(e,n),this.getComparisonKey(t,n))}isEqual(e,t,n=!1){return e===t?!0:!e||!t?!1:this.getComparisonKey(e,n)===this.getComparisonKey(t,n)}getComparisonKey(e,t=!1){return e.with({path:this.a(e)?e.path.toLowerCase():void 0,fragment:t?null:void 0}).toString()}ignorePathCasing(e){return this.a(e)}isEqualOrParent(e,t,n=!1){if(e.scheme===t.scheme){if(e.scheme===Schemas.file)return $fh($Dh(e),$Dh(t),this.a(e))&&e.query===t.query&&(n||e.fragment===t.fragment);if($Uh(e.authority,t.authority))return $fh(e.path,t.path,this.a(e),"/")&&e.query===t.query&&(n||e.fragment===t.fragment)}return!1}joinPath(e,...t){return URI.joinPath(e,...t)}basenameOrAuthority(e){return $Mh(e)||e.authority}basename(e){return $lc.basename(e.path)}extname(e){return $lc.extname(e.path)}dirname(e){if(e.path.length===0)return e;let t;return e.scheme===Schemas.file?t=URI.file($rc($Dh(e))).path:(t=$lc.dirname(e.path),e.authority&&t.length&&t.charCodeAt(0)!==47&&(console.error(`dirname("${e.toString})) resulted in a relative path`),t="/")),e.with({path:t})}normalizePath(e){if(!e.path.length)return e;let t;return e.scheme===Schemas.file?t=URI.file($mc($Dh(e))).path:t=$lc.normalize(e.path),e.with({path:t})}relativePath(e,t){if(e.scheme!==t.scheme||!$Uh(e.authority,t.authority))return;if(e.scheme===Schemas.file){const s=$qc($Dh(e),$Dh(t));return $l?$_g(s):s}let n=e.path||"/";const r=t.path||"/";if(this.a(e)){let s=0;for(const i=Math.min(n.length,r.length);s<i&&!(n.charCodeAt(s)!==r.charCodeAt(s)&&n.charAt(s).toLowerCase()!==r.charAt(s).toLowerCase());s++);n=r.substr(0,s)+n.substr(s)}return $lc.relative(n,r)}resolvePath(e,t){if(e.scheme===Schemas.file){const n=URI.file($pc($Dh(e),t));return e.with({authority:n.authority,path:n.path})}return t=$ah(t),e.with({path:$lc.resolve(e.path,t)})}isAbsolutePath(e){return!!e.path&&e.path[0]==="/"}isEqualAuthority(e,t){return e===t||e!==void 0&&t!==void 0&&$yg(e,t)}hasTrailingPathSeparator(e,t=sep){if(e.scheme===Schemas.file){const n=$Dh(e);return n.length>$bh(n).length&&n[n.length-1]===t}else{const n=e.path;return n.length>1&&n.charCodeAt(n.length-1)===47&&!/^[a-zA-Z]:(\/$|\\$)/.test(e.fsPath)}}removeTrailingPathSeparator(e,t=sep){return $Vh(e,t)?e.with({path:e.path.substr(0,e.path.length-1)}):e}addTrailingPathSeparator(e,t=sep){let n=!1;if(e.scheme===Schemas.file){const r=$Dh(e);n=r!==void 0&&r.length===$bh(r).length&&r[r.length-1]===t}else{t="/";const r=e.path;n=r.length===1&&r.charCodeAt(r.length-1)===47}return!n&&!$Vh(e,t)?e.with({path:e.path+"/"}):e}},$Fh=new $Eh(()=>!1),$Gh=new $Eh(e=>e.scheme===Schemas.file?!$n:!0),$Hh=new $Eh(e=>!0),$Ih=$Fh.isEqual.bind($Fh),$Jh=$Fh.isEqualOrParent.bind($Fh),$Kh=$Fh.getComparisonKey.bind($Fh),$Lh=$Fh.basenameOrAuthority.bind($Fh),$Mh=$Fh.basename.bind($Fh),$Nh=$Fh.extname.bind($Fh),$Oh=$Fh.dirname.bind($Fh),$Ph=$Fh.joinPath.bind($Fh),$Qh=$Fh.normalizePath.bind($Fh),$Rh=$Fh.relativePath.bind($Fh),$Sh=$Fh.resolvePath.bind($Fh),$Th=$Fh.isAbsolutePath.bind($Fh),$Uh=$Fh.isEqualAuthority.bind($Fh),$Vh=$Fh.hasTrailingPathSeparator.bind($Fh),$Wh=$Fh.removeTrailingPathSeparator.bind($Fh),$Xh=$Fh.addTrailingPathSeparator.bind($Fh),DataUri;(function(e){e.META_DATA_LABEL="label",e.META_DATA_DESCRIPTION="description",e.META_DATA_SIZE="size",e.META_DATA_MIME="mime";function t(n){const r=new Map;n.path.substring(n.path.indexOf(";")+1,n.path.lastIndexOf(";")).split(";").forEach(a=>{const[l,u]=a.split(":");l&&u&&r.set(l,u)});const i=n.path.substring(0,n.path.indexOf(";"));return i&&r.set(e.META_DATA_MIME,i),r}e.parseMetaData=t})(DataUri||(DataUri={}));var $5e=Symbol("MicrotaskDelay"),$ti,$ui;(function(){typeof globalThis.requestIdleCallback!="function"||typeof globalThis.cancelIdleCallback!="function"?$ui=(e,t,n)=>{$E(()=>{if(r)return;const s=Date.now()+15;t(Object.freeze({didTimeout:!0,timeRemaining(){return Math.max(0,s-Date.now())}}))});let r=!1;return{dispose(){r||(r=!0)}}}:$ui=(e,t,n)=>{const r=e.requestIdleCallback(t,typeof n=="number"?{timeout:n}:void 0);let s=!1;return{dispose(){s||(s=!0,e.cancelIdleCallback(r))}}},$ti=(e,t)=>$ui(globalThis,e,t)})();var DeferredOutcome;(function(e){e[e.Resolved=0]="Resolved",e[e.Rejected=1]="Rejected"})(DeferredOutcome||(DeferredOutcome={}));var Promises;(function(e){async function t(r){let s;const i=await Promise.all(r.map(a=>a.then(l=>l,l=>{s||(s=l)})));if(typeof s<"u")throw s;return i}e.settled=t;function n(r){return new Promise(async(s,i)=>{try{await r(s,i)}catch(a){i(a)}})}e.withAsyncBody=n})(Promises||(Promises={}));var AsyncIterableSourceState;(function(e){e[e.Initial=0]="Initial",e[e.DoneOK=1]="DoneOK",e[e.DoneError=2]="DoneError"})(AsyncIterableSourceState||(AsyncIterableSourceState={}));var $Di=class W{static fromArray(t){return new W(n=>{n.emitMany(t)})}static fromPromise(t){return new W(async n=>{n.emitMany(await t)})}static fromPromisesResolveOrder(t){return new W(async n=>{await Promise.all(t.map(async r=>n.emitOne(await r)))})}static merge(t){return new W(async n=>{await Promise.all(t.map(async r=>{for await(const s of r)n.emitOne(s)}))})}static{this.EMPTY=W.fromArray([])}constructor(t,n){this.a=0,this.b=[],this.d=null,this.f=n,this.g=new $0e,queueMicrotask(async()=>{const r={emitOne:s=>this.h(s),emitMany:s=>this.j(s),reject:s=>this.l(s)};try{await Promise.resolve(t(r)),this.k()}catch(s){this.l(s)}finally{r.emitOne=void 0,r.emitMany=void 0,r.reject=void 0}})}[Symbol.asyncIterator](){let t=0;return{next:async()=>{do{if(this.a===2)throw this.d;if(t<this.b.length)return{done:!1,value:this.b[t++]};if(this.a===1)return{done:!0,value:void 0};await Event.toPromise(this.g.event)}while(!0)},return:async()=>(this.f?.(),{done:!0,value:void 0})}}static map(t,n){return new W(async r=>{for await(const s of t)r.emitOne(n(s))})}map(t){return W.map(this,t)}static filter(t,n){return new W(async r=>{for await(const s of t)n(s)&&r.emitOne(s)})}filter(t){return W.filter(this,t)}static coalesce(t){return W.filter(t,n=>!!n)}coalesce(){return W.coalesce(this)}static async toPromise(t){const n=[];for await(const r of t)n.push(r);return n}toPromise(){return W.toPromise(this)}h(t){this.a===0&&(this.b.push(t),this.g.fire())}j(t){this.a===0&&(this.b=this.b.concat(t),this.g.fire())}k(){this.a===0&&(this.a=1,this.g.fire())}l(t){this.a===0&&(this.a=2,this.d=t,this.g.fire())}},$X5=class{constructor(e){this.a=e,this.b=new Uint32Array(e.length),this.c=new Int32Array(1),this.c[0]=-1}getCount(){return this.a.length}insertValues(e,t){e=$5f(e);const n=this.a,r=this.b,s=t.length;return s===0?!1:(this.a=new Uint32Array(n.length+s),this.a.set(n.subarray(0,e),0),this.a.set(n.subarray(e),e+s),this.a.set(t,e),e-1<this.c[0]&&(this.c[0]=e-1),this.b=new Uint32Array(this.a.length),this.c[0]>=0&&this.b.set(r.subarray(0,this.c[0]+1)),!0)}setValue(e,t){return e=$5f(e),t=$5f(t),this.a[e]===t?!1:(this.a[e]=t,e-1<this.c[0]&&(this.c[0]=e-1),!0)}removeValues(e,t){e=$5f(e),t=$5f(t);const n=this.a,r=this.b;if(e>=n.length)return!1;const s=n.length-e;return t>=s&&(t=s),t===0?!1:(this.a=new Uint32Array(n.length-t),this.a.set(n.subarray(0,e),0),this.a.set(n.subarray(e+t),e),this.b=new Uint32Array(this.a.length),e-1<this.c[0]&&(this.c[0]=e-1),this.c[0]>=0&&this.b.set(r.subarray(0,this.c[0]+1)),!0)}getTotalSum(){return this.a.length===0?0:this.d(this.a.length-1)}getPrefixSum(e){return e<0?0:(e=$5f(e),this.d(e))}d(e){if(e<=this.c[0])return this.b[e];let t=this.c[0]+1;t===0&&(this.b[0]=this.a[0],t++),e>=this.a.length&&(e=this.a.length-1);for(let n=t;n<=e;n++)this.b[n]=this.b[n-1]+this.a[n];return this.c[0]=Math.max(this.c[0],e),this.b[e]}getIndexOf(e){e=Math.floor(e),this.getTotalSum();let t=0,n=this.a.length-1,r=0,s=0,i=0;for(;t<=n;)if(r=t+(n-t)/2|0,s=this.b[r],i=s-this.a[r],e<i)n=r-1;else if(e>=s)t=r+1;else break;return new $Z5(r,e-i)}},$Z5=class{constructor(e,t){this.index=e,this.remainder=t,this._prefixSumIndexOfResultBrand=void 0,this.index=e,this.remainder=t}},$15=class{constructor(e,t,n,r){this.a=e,this.b=t,this.c=n,this.d=r,this.f=null,this.g=null}dispose(){this.b.length=0}get version(){return this.d}getText(){return this.g===null&&(this.g=this.b.join(this.c)),this.g}onEvents(e){e.eol&&e.eol!==this.c&&(this.c=e.eol,this.f=null);const t=e.changes;for(const n of t)this.k(n.range),this.l(new $lV(n.range.startLineNumber,n.range.startColumn),n.text);this.d=e.versionId,this.g=null}h(){if(!this.f){const e=this.c.length,t=this.b.length,n=new Uint32Array(t);for(let r=0;r<t;r++)n[r]=this.b[r].length+e;this.f=new $X5(n)}}j(e,t){this.b[e]=t,this.f&&this.f.setValue(e,this.b[e].length+this.c.length)}k(e){if(e.startLineNumber===e.endLineNumber){if(e.startColumn===e.endColumn)return;this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.startLineNumber-1].substring(e.endColumn-1));return}this.j(e.startLineNumber-1,this.b[e.startLineNumber-1].substring(0,e.startColumn-1)+this.b[e.endLineNumber-1].substring(e.endColumn-1)),this.b.splice(e.startLineNumber,e.endLineNumber-e.startLineNumber),this.f&&this.f.removeValues(e.startLineNumber,e.endLineNumber-e.startLineNumber)}l(e,t){if(t.length===0)return;const n=$kg(t);if(n.length===1){this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+n[0]+this.b[e.lineNumber-1].substring(e.column-1));return}n[n.length-1]+=this.b[e.lineNumber-1].substring(e.column-1),this.j(e.lineNumber-1,this.b[e.lineNumber-1].substring(0,e.column-1)+n[0]);const r=new Uint32Array(n.length-1);for(let s=1;s<n.length;s++)this.b.splice(e.lineNumber+s-1,0,n[s]),r[s-1]=n[s].length+this.c.length;this.f&&this.f.insertValues(e.lineNumber,r)}},$jAb=60*1e3,$kAb="workerTextModelSync",$mAb=class{constructor(){this.a=Object.create(null)}bindToServer(e){e.setChannel($kAb,this)}getModel(e){return this.a[e]}getModels(){const e=[];return Object.keys(this.a).forEach(t=>e.push(this.a[t])),e}$acceptNewModel(e){this.a[e.url]=new $nAb(URI.parse(e.url),e.lines,e.EOL,e.versionId)}$acceptModelChanged(e,t){if(!this.a[e])return;this.a[e].onEvents(t)}$acceptRemovedModel(e){this.a[e]&&delete this.a[e]}},$nAb=class extends $15{get uri(){return this.a}get eol(){return this.c}getValue(){return this.getText()}findMatches(e){const t=[];for(let n=0;n<this.b.length;n++){const r=this.b[n],s=this.offsetAt(new $lV(n+1,1)),i=r.matchAll(e);for(const a of i)(a.index||a.index===0)&&(a.index=a.index+s),t.push(a)}return t}getLinesContent(){return this.b.slice(0)}getLineCount(){return this.b.length}getLineContent(e){return this.b[e-1]}getWordAtPosition(e,t){const n=$CV(e.column,$AV(t),this.b[e.lineNumber-1],0);return n?new $mV(e.lineNumber,n.startColumn,e.lineNumber,n.endColumn):null}getWordUntilPosition(e,t){const n=this.getWordAtPosition(e,t);return n?{word:this.b[e.lineNumber-1].substring(n.startColumn-1,e.column-1),startColumn:n.startColumn,endColumn:e.column}:{word:"",startColumn:e.column,endColumn:e.column}}words(e){const t=this.b,n=this.m.bind(this);let r=0,s="",i=0,a=[];return{*[Symbol.iterator](){for(;;)if(i<a.length){const l=s.substring(a[i].start,a[i].end);i+=1,yield l}else if(r<t.length)s=t[r],a=n(s,e),i=0,r+=1;else break}}}getLineWords(e,t){const n=this.b[e-1],r=this.m(n,t),s=[];for(const i of r)s.push({word:n.substring(i.start,i.end),startColumn:i.start+1,endColumn:i.end+1});return s}m(e,t){const n=[];let r;for(t.lastIndex=0;(r=t.exec(e))&&r[0].length!==0;)n.push({start:r.index,end:r.index+r[0].length});return n}getValueInRange(e){if(e=this.n(e),e.startLineNumber===e.endLineNumber)return this.b[e.startLineNumber-1].substring(e.startColumn-1,e.endColumn-1);const t=this.c,n=e.startLineNumber-1,r=e.endLineNumber-1,s=[];s.push(this.b[n].substring(e.startColumn-1));for(let i=n+1;i<r;i++)s.push(this.b[i]);return s.push(this.b[r].substring(0,e.endColumn-1)),s.join(t)}offsetAt(e){return e=this.o(e),this.h(),this.f.getPrefixSum(e.lineNumber-2)+(e.column-1)}positionAt(e){e=Math.floor(e),e=Math.max(0,e),this.h();const t=this.f.getIndexOf(e),n=this.b[t.index].length;return{lineNumber:1+t.index,column:1+Math.min(t.remainder,n)}}n(e){const t=this.o({lineNumber:e.startLineNumber,column:e.startColumn}),n=this.o({lineNumber:e.endLineNumber,column:e.endColumn});return t.lineNumber!==e.startLineNumber||t.column!==e.startColumn||n.lineNumber!==e.endLineNumber||n.column!==e.endColumn?{startLineNumber:t.lineNumber,startColumn:t.column,endLineNumber:n.lineNumber,endColumn:n.column}:e}o(e){if(!$lV.isIPosition(e))throw new Error("bad position");let{lineNumber:t,column:n}=e,r=!1;if(t<1)t=1,n=1,r=!0;else if(t>this.b.length)t=this.b.length,n=this.b[t-1].length+1,r=!0;else{const s=this.b[t-1].length+1;n<1?(n=1,r=!0):n>s&&(n=s,r=!0)}return r?{lineNumber:t,column:n}:e}},$oAb=class ue{constructor(t=null){this.f=t,this.d=new $mAb}dispose(){}async $ping(){return"pong"}g(t){return this.d.getModel(t)}getModels(){return this.d.getModels()}$acceptNewModel(t){this.d.$acceptNewModel(t)}$acceptModelChanged(t,n){this.d.$acceptModelChanged(t,n)}$acceptRemovedModel(t){this.d.$acceptRemovedModel(t)}async $computeUnicodeHighlights(t,n,r){const s=this.g(t);return s?$Rzb.computeUnicodeHighlights(s,n,r):{ranges:[],hasMore:!1,ambiguousCharacterCount:0,invisibleCharacterCount:0,nonBasicAsciiCharacterCount:0}}async $findSectionHeaders(t,n){const r=this.g(t);return r?$hAb(r,n):[]}async $computeDiff(t,n,r,s){const i=this.g(t),a=this.g(n);return!i||!a?null:ue.h(i,a,r,s)}static h(t,n,r,s){const i=s==="advanced"?$fAb.getDefault():$fAb.getLegacy(),a=t.getLinesContent(),l=n.getLinesContent(),u=i.computeDiff(a,l,r),o=u.changes.length>0?!1:this.j(t,n);function h(c){return c.map(f=>[f.original.startLineNumber,f.original.endLineNumberExclusive,f.modified.startLineNumber,f.modified.endLineNumberExclusive,f.innerChanges?.map(g=>[g.originalRange.startLineNumber,g.originalRange.startColumn,g.originalRange.endLineNumber,g.originalRange.endColumn,g.modifiedRange.startLineNumber,g.modifiedRange.startColumn,g.modifiedRange.endLineNumber,g.modifiedRange.endColumn])])}return{identical:o,quitEarly:u.hitTimeout,changes:h(u.changes),moves:u.moves.map(c=>[c.lineRangeMapping.original.startLineNumber,c.lineRangeMapping.original.endLineNumberExclusive,c.lineRangeMapping.modified.startLineNumber,c.lineRangeMapping.modified.endLineNumberExclusive,h(c.changes)])}}static j(t,n){const r=t.getLineCount(),s=n.getLineCount();if(r!==s)return!1;for(let i=1;i<=r;i++){const a=t.getLineContent(i),l=n.getLineContent(i);if(a!==l)return!1}return!0}async $computeDirtyDiff(t,n,r){const s=this.g(t),i=this.g(n);if(!s||!i)return null;const a=s.getLinesContent(),l=i.getLinesContent();return new $W5(a,l,{shouldComputeCharChanges:!1,shouldPostProcessCharChanges:!1,shouldIgnoreTrimWhitespace:r,shouldMakePrettyDiff:!0,maxComputationTime:1e3}).computeDiff().changes}static{this.k=1e5}async $computeMoreMinimalEdits(t,n,r){const s=this.g(t);if(!s)return n;const i=[];let a;n=n.slice(0).sort((u,o)=>{if(u.range&&o.range)return $mV.compareRangesUsingStarts(u.range,o.range);const h=u.range?0:1,c=o.range?0:1;return h-c});let l=0;for(let u=1;u<n.length;u++)$mV.getEndPosition(n[l].range).equals($mV.getStartPosition(n[u].range))?(n[l].range=$mV.fromPositions($mV.getStartPosition(n[l].range),$mV.getEndPosition(n[u].range)),n[l].text+=n[u].text):(l++,n[l]=n[u]);n.length=l+1;for(let{range:u,text:o,eol:h}of n){if(typeof h=="number"&&(a=h),$mV.isEmpty(u)&&!o)continue;const c=s.getValueInRange(u);if(o=o.replace(/\r\n|\n|\r/g,s.eol),c===o)continue;if(Math.max(o.length,c.length)>ue.k){i.push({range:u,text:o});continue}const f=$iV(c,o,r),g=s.offsetAt($mV.lift(u).getStartPosition());for(const m of f){const N=s.positionAt(g+m.originalStart),p=s.positionAt(g+m.originalStart+m.originalLength),d={text:o.substr(m.modifiedStart,m.modifiedLength),range:{startLineNumber:N.lineNumber,startColumn:N.column,endLineNumber:p.lineNumber,endColumn:p.column}};s.getValueInRange(d.range)!==d.text&&i.push(d)}}return typeof a=="number"&&i.push({eol:a,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}$computeHumanReadableDiff(t,n,r){const s=this.g(t);if(!s)return n;const i=[];let a;n=n.slice(0).sort((o,h)=>{if(o.range&&h.range)return $mV.compareRangesUsingStarts(o.range,h.range);const c=o.range?0:1,f=h.range?0:1;return c-f});for(let{range:o,text:h,eol:c}of n){let f=function(v,L){return new $lV(v.lineNumber+L.lineNumber-1,L.lineNumber===1?v.column+L.column-1:L.column)},g=function(v,L){const b=[];for(let w=L.startLineNumber;w<=L.endLineNumber;w++){const R=v[w-1];w===L.startLineNumber&&w===L.endLineNumber?b.push(R.substring(L.startColumn-1,L.endColumn-1)):w===L.startLineNumber?b.push(R.substring(L.startColumn-1)):w===L.endLineNumber?b.push(R.substring(0,L.endColumn-1)):b.push(R)}return b};var l=f,u=g;if(typeof c=="number"&&(a=c),$mV.isEmpty(o)&&!h)continue;const m=s.getValueInRange(o);if(h=h.replace(/\r\n|\n|\r/g,s.eol),m===h)continue;if(Math.max(h.length,m.length)>ue.k){i.push({range:o,text:h});continue}const N=m.split(/\r\n|\n|\r/),p=h.split(/\r\n|\n|\r/),d=$fAb.getDefault().computeDiff(N,p,r),A=$mV.lift(o).getStartPosition();for(const v of d.changes)if(v.innerChanges)for(const L of v.innerChanges)i.push({range:$mV.fromPositions(f(A,L.originalRange.getStartPosition()),f(A,L.originalRange.getEndPosition())),text:g(p,L.modifiedRange).join(s.eol)});else throw new $vb("The experimental diff algorithm always produces inner changes")}return typeof a=="number"&&i.push({eol:a,text:"",range:{startLineNumber:0,startColumn:0,endLineNumber:0,endColumn:0}}),i}async $computeLinks(t){const n=this.g(t);return n?$Uzb(n):null}async $computeDefaultDocumentColors(t){const n=this.g(t);return n?$gAb(n):null}static{this.l=1e4}async $textualSuggest(t,n,r,s){const i=new $4e,a=new RegExp(r,s),l=new Set;e:for(const u of t){const o=this.g(u);if(o){for(const h of o.words(a))if(!(h===n||!isNaN(Number(h)))&&(l.add(h),l.size>ue.l))break e}}return{words:Array.from(l),duration:i.elapsed()}}async $computeWordRanges(t,n,r,s){const i=this.g(t);if(!i)return Object.create(null);const a=new RegExp(r,s),l=Object.create(null);for(let u=n.startLineNumber;u<n.endLineNumber;u++){const o=i.getLineWords(u,a);for(const h of o){if(!isNaN(Number(h.word)))continue;let c=l[h.word];c||(c=[],l[h.word]=c),c.push({startLineNumber:u,startColumn:h.startColumn,endLineNumber:u,endColumn:h.endColumn})}}return l}async $navigateValueSet(t,n,r,s,i){const a=this.g(t);if(!a)return null;const l=new RegExp(s,i);n.startColumn===n.endColumn&&(n={startLineNumber:n.startLineNumber,startColumn:n.startColumn,endLineNumber:n.endLineNumber,endColumn:n.endColumn+1});const u=a.getValueInRange(n),o=a.getWordAtPosition({lineNumber:n.startLineNumber,column:n.startColumn},l);if(!o)return null;const h=a.getValueInRange(o);return $Vzb.INSTANCE.navigateValueSet(n,u,o,h,r)}$fmr(t,n){if(!this.f||typeof this.f[t]!="function")return Promise.reject(new Error("Missing requestHandler or method: "+t));try{return Promise.resolve(this.f[t].apply(this.f,n))}catch(r){return Promise.reject(r)}}};typeof importScripts=="function"&&(globalThis.monaco=$Xzb()),$Ftb(()=>new $oAb(null));

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/b8f002c02d165600299a109bf21d02d139c52644/core/vs/editor/common/services/editorWebWorkerMain.js.map

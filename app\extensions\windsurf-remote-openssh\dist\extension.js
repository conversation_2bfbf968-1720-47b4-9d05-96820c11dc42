(()=>{var t,e,s={8505:t=>{"use strict";function e(t,e,r){t instanceof RegExp&&(t=s(t,r)),e instanceof RegExp&&(e=s(e,r));var n=i(t,e,r);return n&&{start:n[0],end:n[1],pre:r.slice(0,n[0]),body:r.slice(n[0]+t.length,n[1]),post:r.slice(n[1]+e.length)}}function s(t,e){var s=e.match(t);return s?s[0]:null}function i(t,e,s){var i,r,n,o,a,h=s.indexOf(t),c=s.indexOf(e,h+1),l=h;if(h>=0&&c>0){if(t===e)return[h,c];for(i=[],n=s.length;l>=0&&!a;)l==h?(i.push(l),h=s.indexOf(t,l+1)):1==i.length?a=[i.pop(),c]:((r=i.pop())<n&&(n=r,o=c),c=s.indexOf(e,l+1)),l=h<c&&h>=0?h:c;i.length&&(a=[n,o])}return a}t.exports=e,e.range=i},8928:(t,e,s)=>{var i=s(8505);t.exports=function(t){return t?("{}"===t.substr(0,2)&&(t="\\{\\}"+t.substr(2)),g(function(t){return t.split("\\\\").join(r).split("\\{").join(n).split("\\}").join(o).split("\\,").join(a).split("\\.").join(h)}(t),!0).map(l)):[]};var r="\0SLASH"+Math.random()+"\0",n="\0OPEN"+Math.random()+"\0",o="\0CLOSE"+Math.random()+"\0",a="\0COMMA"+Math.random()+"\0",h="\0PERIOD"+Math.random()+"\0";function c(t){return parseInt(t,10)==t?parseInt(t,10):t.charCodeAt(0)}function l(t){return t.split(r).join("\\").split(n).join("{").split(o).join("}").split(a).join(",").split(h).join(".")}function u(t){if(!t)return[""];var e=[],s=i("{","}",t);if(!s)return t.split(",");var r=s.pre,n=s.body,o=s.post,a=r.split(",");a[a.length-1]+="{"+n+"}";var h=u(o);return o.length&&(a[a.length-1]+=h.shift(),a.push.apply(a,h)),e.push.apply(e,a),e}function d(t){return"{"+t+"}"}function f(t){return/^-?0\d/.test(t)}function p(t,e){return t<=e}function m(t,e){return t>=e}function g(t,e){var s=[],r=i("{","}",t);if(!r)return[t];var n=r.pre,a=r.post.length?g(r.post,!1):[""];if(/\$$/.test(r.pre))for(var h=0;h<a.length;h++){var l=n+"{"+r.body+"}"+a[h];s.push(l)}else{var S,y,w=/^-?\d+\.\.-?\d+(?:\.\.-?\d+)?$/.test(r.body),_=/^[a-zA-Z]\.\.[a-zA-Z](?:\.\.-?\d+)?$/.test(r.body),v=w||_,E=r.body.indexOf(",")>=0;if(!v&&!E)return r.post.match(/,.*\}/)?g(t=r.pre+"{"+r.body+o+r.post):[t];if(v)S=r.body.split(/\.\./);else if(1===(S=u(r.body)).length&&1===(S=g(S[0],!1).map(d)).length)return a.map((function(t){return r.pre+S[0]+t}));if(v){var b=c(S[0]),I=c(S[1]),R=Math.max(S[0].length,S[1].length),O=3==S.length?Math.abs(c(S[2])):1,T=p;I<b&&(O*=-1,T=m);var k=S.some(f);y=[];for(var D=b;T(D,I);D+=O){var A;if(_)"\\"===(A=String.fromCharCode(D))&&(A="");else if(A=String(D),k){var C=R-A.length;if(C>0){var L=new Array(C+1).join("0");A=D<0?"-"+L+A.slice(1):L+A}}y.push(A)}}else{y=[];for(var x=0;x<S.length;x++)y.push.apply(y,g(S[x],!1))}for(x=0;x<y.length;x++)for(h=0;h<a.length;h++)l=n+y[x]+a[h],(!e||v||l)&&s.push(l)}return s}},1182:function(t,e,s){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),r=this&&this.__exportStar||function(t,e){for(var s in t)"default"===s||Object.prototype.hasOwnProperty.call(e,s)||i(e,t,s)},n=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0});const o=n(s(9912));r(s(9912),e),e.default=o.default},8601:(t,e)=>{"use strict";function s(t,e){return t=function(t,e){for(let e of"\\()[]{}.+^$|")t=t.replace(new RegExp("\\"+e,"g"),"\\"+e);return t}(t),t=t.replace(/\*/g,".*").replace(/\?/g,".?"),new RegExp("^(?:"+t+")$").test(e)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t,e){const i=Array.isArray(t)?t:t.split(/,/);let r=!1;for(const t of i){if("!"==t[0]&&s(t.slice(1),e))return!1;s(t,e)&&(r=!0)}return r}},9912:function(t,e,s){"use strict";var i=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.LineType=void 0,e.parse=w,e.stringify=_;const r=i(s(8601)),n=s(5317),o=i(s(857)),a=/\s/,h=/\r|\n/,c=/^(Host|Match)$/i,l=/^(GlobalKnownHostsFile|Host|IPQoS|SendEnv|UserKnownHostsFile|ProxyCommand|Match|CanonicalDomains)$/i,u=/^(?:CertificateFile|IdentityFile|IdentityAgent|User)$/i,d=/^(Include|IdentityFile)$/i;var f;!function(t){t[t.DIRECTIVE=1]="DIRECTIVE",t[t.COMMENT=2]="COMMENT"}(f||(e.LineType=f={}));const p=["IdentityFile","LocalForward","RemoteForward","DynamicForward","CertificateFile"];function m(t,e){return e.hasOwnProperty(t.param)&&e[t.param]===t.value}function g(t){for(const e of t)if(e.type===f.DIRECTIVE&&"config"in e)for(const t of e.config)if(t.before)return t.before;return"  "}function S(t,e){const s=(t,s)=>{switch(t.toLowerCase()){case"all":return!0;case"final":return!!e.inFinalPass||(e.doFinalPass=!0,!1);case"exec":const t=`function main {\n          ${s}\n        }\n        main`;return 0===(0,n.spawnSync)(t,{shell:!0}).status;case"host":return(0,r.default)(s,e.params.HostName);case"originalhost":return(0,r.default)(s,e.params.OriginalHost);case"user":return(0,r.default)(s,e.params.User);case"localuser":return(0,r.default)(s,e.params.LocalUser)}};for(const e in t){const i=t[e];if(!s(e,Array.isArray(i)?i.map((({val:t})=>t)):i))return!1}return!0}class y extends Array{static parse(t){return w(t)}static stringify(t){return _(t)}compute(t){let e;"string"==typeof t&&(t={Host:t});try{e=o.default.userInfo()}catch(t){e={username:process.env.USER||process.env.USERNAME||""}}const s={params:{Host:t.Host,HostName:t.Host,OriginalHost:t.Host,User:e.username,LocalUser:e.username},inFinalPass:!1,doFinalPass:!1},i={},a=(t,e)=>{let r;r=Array.isArray(e)?/ProxyCommand/i.test(t)?e.map((({val:t,separator:e,quoted:s})=>`${e}${s?`"${t.replace(/"/g,'\\"')}"`:t}`)).join("").trim():e.map((({val:t})=>t)):e;const n=Array.isArray(r)?r[0]:r;p.includes(t)?(i[t]||(i[t]=[])).push(...[].concat(r)):null==i[t]&&("HostName"===t?s.params.HostName=n:"User"===t&&(s.params.User=n),i[t]=r)};void 0!==t.User&&a("User",t.User);const h=()=>{for(const t of this)if(t.type===f.DIRECTIVE)if("Host"===t.param&&(0,r.default)(Array.isArray(t.value)?t.value.map((({val:t})=>t)):t.value,s.params.Host)){let e=!1,i=[];a(t.param,t.value);for(const s of t.config)s.type===f.DIRECTIVE&&(a(s.param,s.value),/^CanonicalizeHostName$/i.test(s.param)&&"yes"===s.value&&(e=!0),/^CanonicalDomains$/i.test(s.param)&&Array.isArray(s.value)&&(i=s.value.map((({val:t})=>t))));if(i.length>0&&e&&s.params.Host===s.params.OriginalHost)for(const t of i){const e=`${s.params.OriginalHost}.${t}`,{status:i,stderr:r}=(0,n.spawnSync)("nslookup",[e]);if(0===i&&!/can't find/.test(r.toString())){s.params.Host=e,a("Host",e),h();break}}}else if("Match"===t.param&&"criteria"in t&&S(t.criteria,s))for(const e of t.config)e.type===f.DIRECTIVE&&a(e.param,e.value);else"Host"!==t.param&&"Match"!==t.param&&a(t.param,t.value)};return h(),s.doFinalPass&&(s.inFinalPass=!0,s.params.Host=s.params.HostName,h()),i}find(t){if("function"==typeof t)return super.find(t);if(!t||!("Host"in t)&&!("Match"in t))throw new Error("Can only find by Host or Match");return super.find((e=>m(e,t)))}remove(t){let e;if("function"==typeof t)e=super.findIndex(t);else{if(!t||!("Host"in t)&&!("Match"in t))throw new Error("Can only remove by Host or Match");e=super.findIndex((e=>m(e,t)))}if(e>=0)return this.splice(e,1)}toString(){return _(this)}append(t){const e=g(this),s=this.length>0?this[this.length-1]:null;let i=s&&s.config||this,r=this,n=i.length>0?i[i.length-1]:s;n&&!n.after&&(n.after="\n");let o=i!==r;for(const s in t){const a=t[s],h={type:f.DIRECTIVE,param:s,separator:" ",value:Array.isArray(a)?a.map(((t,e)=>({val:t,separator:0===e?"":" "}))):a,before:o?e:e.replace(/  |\t/,""),after:"\n"};c.test(s)?(o=!0,h.before=e.replace(/  |\t/,""),i=r,n&&"\n"===n.after&&(n.after+="\n"),i.push(h),i=h.config=new y):i.push(h),n=h}return r}prepend(t,e=!1){const s=g(this);let i=this,r=0;if(e){for(;r<this.length&&!("config"in this[r]);)r+=1;if(r>=this.length)return this.append(t)}let n=!1,o=0;for(const e in t){o+=1;const a=t[e],h={type:f.DIRECTIVE,param:e,separator:" ",value:Array.isArray(a)?a.map(((t,e)=>({val:t,separator:0===e?"":" "}))):a,before:"",after:"\n"};c.test(e)?(h.before=s.replace(/  |\t/,""),i.splice(r,0,h),i=h.config=new y,n=!0):(o===Object.keys(t).length&&(h.after+="\n"),n?(h.before=s,i.push(h)):(i.splice(r,0,h),r+=1,d.test(e)&&(h.after+="\n")))}return i}}function w(t){let e=0,s=n(),i=new y,r=i;function n(){return t[e++]}function o(){let t="";for(;a.test(s);)t+=s,s=n();return t}function u(){let t=o();return"="===s&&(t+=s,s=n()),t+o()}function d(){let t="",e=!1,i=!1;for(;s&&!h.test(s);){if(i)t+='"'===s?s:`\\${s}`,i=!1;else if('"'!==s||t&&!e)if("\\"===s)i=!0;else{if("#"===s&&!e)break;t+=s}else e=!e;s=n()}if(e||i)throw new Error(`Unexpected line break at ${t}`);return t.trim()}function p(){const t=[];let e="",i=!1,r=" ",o=!1,a=!1;for(;s&&!h.test(s);){if(a)e+='"'===s?s:`\\${s}`,a=!1;else if('"'===s)o=!o;else if("\\"===s)a=!0;else if(o)e+=s,i=!0;else if(/[ \t=]/.test(s))e&&(t.push({val:e,separator:r,quoted:i}),e="",i=!1,r=s);else{if("#"===s&&t.length>0)break;e+=s}s=n()}if(o||a)throw new Error(`Unexpected line break at ${t.map((({val:t})=>t)).concat(e).join(" ")}`);return e&&t.push({val:e,separator:r,quoted:i}),t.length>1?t:t[0].val}function m(){const t=o(),e="#"===s?function(){const t=f.COMMENT;let e="";for(;s&&!h.test(s);)e+=s,s=n();return{type:t,content:e,before:"",after:""}}():function(){const t=f.DIRECTIVE,e=function(){let t="";for(;s&&/[^ \t=]/.test(s);)t+=s,s=n();return t}(),i=l.test(e),r={type:t,param:e,separator:u(),quoted:!i&&'"'===s,value:i?p():d(),before:"",after:""};if(r.quoted||delete r.quoted,/^Match$/i.test(e)){const t={};"string"==typeof r.value&&(r.value=[{val:r.value,separator:"",quoted:r.quoted}]);let e=0;for(;e<r.value.length;){const{val:s}=r.value[e];switch(s.toLowerCase()){case"all":case"canonical":case"final":t[s]=[],e+=1;break;default:if(e+1>=r.value.length)throw new Error(`Missing value for match criteria ${s}`);t[s]=r.value[e+1].val,e+=2}}r.criteria=t}return r}(),i=function(){let t="";for(;h.test(s);)t+=s,s=n();return t}();return e.before=t,e.after=i,e}for(;s;){let t=m();t.type===f.DIRECTIVE&&c.test(t.param)?(i=r,i.push(t),i=t.config=new y):t.type!==f.DIRECTIVE||t.param?i.push(t):0===i.length?r[r.length-1].after+=t.before:i[i.length-1].after+=t.before}return r}function _(t){let e="";function s(t,e){if(Array.isArray(t)){let e="";for(const{val:i,separator:r,quoted:n}of t)e+=(e?r:"")+s(i,n||a.test(i));return e}return e?`"${t}"`:t}function i(t){const e=t.quoted||u.test(t.param)&&a.test(t.value),i=s(t.value,e);return`${t.param}${t.separator}${i}`}const r=t=>{e+=t.before,t.type===f.COMMENT?e+=t.content:t.type===f.DIRECTIVE&&p.includes(t.param)?(Array.isArray(t.value)?t.value:[t.value]).forEach(((s,r,n)=>{e+=i({...t,value:"string"!=typeof s?s.val:s}),r<n.length-1&&(e+=`\n${t.before}`)})):t.type===f.DIRECTIVE&&(e+=i(t)),e+=t.after,"config"in t&&t.config.forEach(r)};return t.forEach(r),e}y.DIRECTIVE=f.DIRECTIVE,y.COMMENT=f.COMMENT,e.default=y},6744:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.AddressError=void 0;class s extends Error{constructor(t,e){super(t),this.name="AddressError",null!==e&&(this.parseMessage=e)}}e.AddressError=s},1606:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.isCorrect=e.isInSubnet=void 0,e.isInSubnet=function(t){return!(this.subnetMask<t.subnetMask)&&this.mask(t.subnetMask)===t.mask()},e.isCorrect=function(t){return function(){return this.addressMinusSuffix===this.correctForm()&&(this.subnetMask===t&&!this.parsedSubnet||this.parsedSubnet===String(this.subnetMask))}}},1039:function(t,e,s){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),n=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s in t)"default"!==s&&Object.prototype.hasOwnProperty.call(t,s)&&i(e,t,s);return r(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.v6=e.AddressError=e.Address6=e.Address4=void 0;const o=s(7564);Object.defineProperty(e,"Address4",{enumerable:!0,get:function(){return o.Address4}});const a=s(2966);Object.defineProperty(e,"Address6",{enumerable:!0,get:function(){return a.Address6}});const h=s(6744);Object.defineProperty(e,"AddressError",{enumerable:!0,get:function(){return h.AddressError}});const c=n(s(3021));e.v6={helpers:c}},7564:function(t,e,s){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),n=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s in t)"default"!==s&&Object.prototype.hasOwnProperty.call(t,s)&&i(e,t,s);return r(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.Address4=void 0;const o=n(s(1606)),a=n(s(8275)),h=s(6744),c=s(8113),l=s(9471);class u{constructor(t){this.groups=a.GROUPS,this.parsedAddress=[],this.parsedSubnet="",this.subnet="/32",this.subnetMask=32,this.v4=!0,this.isCorrect=o.isCorrect(a.BITS),this.isInSubnet=o.isInSubnet,this.address=t;const e=a.RE_SUBNET_STRING.exec(t);if(e){if(this.parsedSubnet=e[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,this.subnetMask<0||this.subnetMask>a.BITS)throw new h.AddressError("Invalid subnet mask.");t=t.replace(a.RE_SUBNET_STRING,"")}this.addressMinusSuffix=t,this.parsedAddress=this.parse(t)}static isValid(t){try{return new u(t),!0}catch(t){return!1}}parse(t){const e=t.split(".");if(!t.match(a.RE_ADDRESS))throw new h.AddressError("Invalid IPv4 address.");return e}correctForm(){return this.parsedAddress.map((t=>parseInt(t,10))).join(".")}static fromHex(t){const e=t.replace(/:/g,"").padStart(8,"0"),s=[];let i;for(i=0;i<8;i+=2){const t=e.slice(i,i+2);s.push(parseInt(t,16))}return new u(s.join("."))}static fromInteger(t){return u.fromHex(t.toString(16))}static fromArpa(t){const e=t.replace(/(\.in-addr\.arpa)?\.$/,"").split(".").reverse().join(".");return new u(e)}toHex(){return this.parsedAddress.map((t=>(0,l.sprintf)("%02x",parseInt(t,10)))).join(":")}toArray(){return this.parsedAddress.map((t=>parseInt(t,10)))}toGroup6(){const t=[];let e;for(e=0;e<a.GROUPS;e+=2){const s=(0,l.sprintf)("%02x%02x",parseInt(this.parsedAddress[e],10),parseInt(this.parsedAddress[e+1],10));t.push((0,l.sprintf)("%x",parseInt(s,16)))}return t.join(":")}bigInteger(){return new c.BigInteger(this.parsedAddress.map((t=>(0,l.sprintf)("%02x",parseInt(t,10)))).join(""),16)}_startAddress(){return new c.BigInteger(this.mask()+"0".repeat(a.BITS-this.subnetMask),2)}startAddress(){return u.fromBigInteger(this._startAddress())}startAddressExclusive(){const t=new c.BigInteger("1");return u.fromBigInteger(this._startAddress().add(t))}_endAddress(){return new c.BigInteger(this.mask()+"1".repeat(a.BITS-this.subnetMask),2)}endAddress(){return u.fromBigInteger(this._endAddress())}endAddressExclusive(){const t=new c.BigInteger("1");return u.fromBigInteger(this._endAddress().subtract(t))}static fromBigInteger(t){return u.fromInteger(parseInt(t.toString(),10))}mask(t){return void 0===t&&(t=this.subnetMask),this.getBitsBase2(0,t)}getBitsBase2(t,e){return this.binaryZeroPad().slice(t,e)}reverseForm(t){t||(t={});const e=this.correctForm().split(".").reverse().join(".");return t.omitSuffix?e:(0,l.sprintf)("%s.in-addr.arpa.",e)}isMulticast(){return this.isInSubnet(new u("*********/4"))}binaryZeroPad(){return this.bigInteger().toString(2).padStart(a.BITS,"0")}groupForV6(){const t=this.parsedAddress;return this.address.replace(a.RE_ADDRESS,(0,l.sprintf)('<span class="hover-group group-v4 group-6">%s</span>.<span class="hover-group group-v4 group-7">%s</span>',t.slice(0,2).join("."),t.slice(2,4).join(".")))}}e.Address4=u},2966:function(t,e,s){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),n=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s in t)"default"!==s&&Object.prototype.hasOwnProperty.call(t,s)&&i(e,t,s);return r(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.Address6=void 0;const o=n(s(1606)),a=n(s(8275)),h=n(s(1813)),c=n(s(3021)),l=s(7564),u=s(30),d=s(6744),f=s(8113),p=s(9471);function m(t){if(!t)throw new Error("Assertion failed.")}function g(t){return(t=t.replace(/^(0{1,})([1-9]+)$/,'<span class="parse-error">$1</span>$2')).replace(/^(0{1,})(0)$/,'<span class="parse-error">$1</span>$2')}function S(t){return(0,p.sprintf)("%04x",parseInt(t,16))}function y(t){return 255&t}class w{constructor(t,e){this.addressMinusSuffix="",this.parsedSubnet="",this.subnet="/128",this.subnetMask=128,this.v4=!1,this.zone="",this.isInSubnet=o.isInSubnet,this.isCorrect=o.isCorrect(h.BITS),this.groups=void 0===e?h.GROUPS:e,this.address=t;const s=h.RE_SUBNET_STRING.exec(t);if(s){if(this.parsedSubnet=s[0].replace("/",""),this.subnetMask=parseInt(this.parsedSubnet,10),this.subnet=`/${this.subnetMask}`,Number.isNaN(this.subnetMask)||this.subnetMask<0||this.subnetMask>h.BITS)throw new d.AddressError("Invalid subnet mask.");t=t.replace(h.RE_SUBNET_STRING,"")}else if(/\//.test(t))throw new d.AddressError("Invalid subnet mask.");const i=h.RE_ZONE_STRING.exec(t);i&&(this.zone=i[0],t=t.replace(h.RE_ZONE_STRING,"")),this.addressMinusSuffix=t,this.parsedAddress=this.parse(this.addressMinusSuffix)}static isValid(t){try{return new w(t),!0}catch(t){return!1}}static fromBigInteger(t){const e=t.toString(16).padStart(32,"0"),s=[];let i;for(i=0;i<h.GROUPS;i++)s.push(e.slice(4*i,4*(i+1)));return new w(s.join(":"))}static fromURL(t){let e,s,i=null;if(-1!==t.indexOf("[")&&-1!==t.indexOf("]:")){if(s=h.RE_URL_WITH_PORT.exec(t),null===s)return{error:"failed to parse address with port",address:null,port:null};e=s[1],i=s[2]}else if(-1!==t.indexOf("/")){if(t=t.replace(/^[a-z0-9]+:\/\//,""),s=h.RE_URL.exec(t),null===s)return{error:"failed to parse address from URL",address:null,port:null};e=s[1]}else e=t;return i?(i=parseInt(i,10),(i<0||i>65536)&&(i=null)):i=null,{address:new w(e),port:i}}static fromAddress4(t){const e=new l.Address4(t),s=h.BITS-(a.BITS-e.subnetMask);return new w(`::ffff:${e.correctForm()}/${s}`)}static fromArpa(t){let e=t.replace(/(\.ip6\.arpa)?\.$/,"");if(63!==e.length)throw new d.AddressError("Invalid 'ip6.arpa' form.");const s=e.split(".").reverse();for(let t=7;t>0;t--){const e=4*t;s.splice(e,0,":")}return e=s.join(""),new w(e)}microsoftTranscription(){return(0,p.sprintf)("%s.ipv6-literal.net",this.correctForm().replace(/:/g,"-"))}mask(t=this.subnetMask){return this.getBitsBase2(0,t)}possibleSubnets(t=128){const e=h.BITS-this.subnetMask-Math.abs(t-h.BITS);return e<0?"0":function(t){const e=/(\d+)(\d{3})/;for(;e.test(t);)t=t.replace(e,"$1,$2");return t}(new f.BigInteger("2",10).pow(e).toString(10))}_startAddress(){return new f.BigInteger(this.mask()+"0".repeat(h.BITS-this.subnetMask),2)}startAddress(){return w.fromBigInteger(this._startAddress())}startAddressExclusive(){const t=new f.BigInteger("1");return w.fromBigInteger(this._startAddress().add(t))}_endAddress(){return new f.BigInteger(this.mask()+"1".repeat(h.BITS-this.subnetMask),2)}endAddress(){return w.fromBigInteger(this._endAddress())}endAddressExclusive(){const t=new f.BigInteger("1");return w.fromBigInteger(this._endAddress().subtract(t))}getScope(){let t=h.SCOPES[this.getBits(12,16).intValue()];return"Global unicast"===this.getType()&&"Link local"!==t&&(t="Global"),t||"Unknown"}getType(){for(const t of Object.keys(h.TYPES))if(this.isInSubnet(new w(t)))return h.TYPES[t];return"Global unicast"}getBits(t,e){return new f.BigInteger(this.getBitsBase2(t,e),2)}getBitsBase2(t,e){return this.binaryZeroPad().slice(t,e)}getBitsBase16(t,e){const s=e-t;if(s%4!=0)throw new Error("Length of bits to retrieve must be divisible by four");return this.getBits(t,e).toString(16).padStart(s/4,"0")}getBitsPastSubnet(){return this.getBitsBase2(this.subnetMask,h.BITS)}reverseForm(t){t||(t={});const e=Math.floor(this.subnetMask/4),s=this.canonicalForm().replace(/:/g,"").split("").slice(0,e).reverse().join(".");return e>0?t.omitSuffix?s:(0,p.sprintf)("%s.ip6.arpa.",s):t.omitSuffix?"":"ip6.arpa."}correctForm(){let t,e=[],s=0;const i=[];for(t=0;t<this.parsedAddress.length;t++){const e=parseInt(this.parsedAddress[t],16);0===e&&s++,0!==e&&s>0&&(s>1&&i.push([t-s,t-1]),s=0)}s>1&&i.push([this.parsedAddress.length-s,this.parsedAddress.length-1]);const r=i.map((t=>t[1]-t[0]+1));if(i.length>0){const t=r.indexOf(Math.max(...r));e=function(t,e){const s=[],i=[];let r;for(r=0;r<t.length;r++)r<e[0]?s.push(t[r]):r>e[1]&&i.push(t[r]);return s.concat(["compact"]).concat(i)}(this.parsedAddress,i[t])}else e=this.parsedAddress;for(t=0;t<e.length;t++)"compact"!==e[t]&&(e[t]=parseInt(e[t],16).toString(16));let n=e.join(":");return n=n.replace(/^compact$/,"::"),n=n.replace(/^compact|compact$/,":"),n=n.replace(/compact/,""),n}binaryZeroPad(){return this.bigInteger().toString(2).padStart(h.BITS,"0")}parse4in6(t){const e=t.split(":"),s=e.slice(-1)[0].match(a.RE_ADDRESS);if(s){this.parsedAddress4=s[0],this.address4=new l.Address4(this.parsedAddress4);for(let e=0;e<this.address4.groups;e++)if(/^0[0-9]+/.test(this.address4.parsedAddress[e]))throw new d.AddressError("IPv4 addresses can't have leading zeroes.",t.replace(a.RE_ADDRESS,this.address4.parsedAddress.map(g).join(".")));this.v4=!0,e[e.length-1]=this.address4.toGroup6(),t=e.join(":")}return t}parse(t){const e=(t=this.parse4in6(t)).match(h.RE_BAD_CHARACTERS);if(e)throw new d.AddressError((0,p.sprintf)("Bad character%s detected in address: %s",e.length>1?"s":"",e.join("")),t.replace(h.RE_BAD_CHARACTERS,'<span class="parse-error">$1</span>'));const s=t.match(h.RE_BAD_ADDRESS);if(s)throw new d.AddressError((0,p.sprintf)("Address failed regex: %s",s.join("")),t.replace(h.RE_BAD_ADDRESS,'<span class="parse-error">$1</span>'));let i=[];const r=t.split("::");if(2===r.length){let t=r[0].split(":"),e=r[1].split(":");1===t.length&&""===t[0]&&(t=[]),1===e.length&&""===e[0]&&(e=[]);const s=this.groups-(t.length+e.length);if(!s)throw new d.AddressError("Error parsing groups");this.elidedGroups=s,this.elisionBegin=t.length,this.elisionEnd=t.length+this.elidedGroups,i=i.concat(t);for(let t=0;t<s;t++)i.push("0");i=i.concat(e)}else{if(1!==r.length)throw new d.AddressError("Too many :: groups found");i=t.split(":"),this.elidedGroups=0}if(i=i.map((t=>(0,p.sprintf)("%x",parseInt(t,16)))),i.length!==this.groups)throw new d.AddressError("Incorrect number of groups found");return i}canonicalForm(){return this.parsedAddress.map(S).join(":")}decimal(){return this.parsedAddress.map((t=>(0,p.sprintf)("%05d",parseInt(t,16)))).join(":")}bigInteger(){return new f.BigInteger(this.parsedAddress.map(S).join(""),16)}to4(){const t=this.binaryZeroPad().split("");return l.Address4.fromHex(new f.BigInteger(t.slice(96,128).join(""),2).toString(16))}to4in6(){const t=this.to4(),e=new w(this.parsedAddress.slice(0,6).join(":"),6).correctForm();let s="";return/:$/.test(e)||(s=":"),e+s+t.address}inspectTeredo(){const t=this.getBitsBase16(0,32),e=this.getBits(80,96).xor(new f.BigInteger("ffff",16)).toString(),s=l.Address4.fromHex(this.getBitsBase16(32,64)),i=l.Address4.fromHex(this.getBits(96,128).xor(new f.BigInteger("ffffffff",16)).toString(16)),r=this.getBits(64,80),n=this.getBitsBase2(64,80),o=r.testBit(15),a=r.testBit(14),h=r.testBit(8),c=r.testBit(9),u=new f.BigInteger(n.slice(2,6)+n.slice(8,16),2).toString(10);return{prefix:(0,p.sprintf)("%s:%s",t.slice(0,4),t.slice(4,8)),server4:s.address,client4:i.address,flags:n,coneNat:o,microsoft:{reserved:a,universalLocal:c,groupIndividual:h,nonce:u},udpPort:e}}inspect6to4(){const t=this.getBitsBase16(0,16),e=l.Address4.fromHex(this.getBitsBase16(16,48));return{prefix:(0,p.sprintf)("%s",t.slice(0,4)),gateway:e.address}}to6to4(){if(!this.is4())return null;const t=["2002",this.getBitsBase16(96,112),this.getBitsBase16(112,128),"","/16"].join(":");return new w(t)}toByteArray(){const t=this.bigInteger().toByteArray();return 17===t.length&&0===t[0]?t.slice(1):t}toUnsignedByteArray(){return this.toByteArray().map(y)}static fromByteArray(t){return this.fromUnsignedByteArray(t.map(y))}static fromUnsignedByteArray(t){const e=new f.BigInteger("256",10);let s=new f.BigInteger("0",10),i=new f.BigInteger("1",10);for(let r=t.length-1;r>=0;r--)s=s.add(i.multiply(new f.BigInteger(t[r].toString(10),10))),i=i.multiply(e);return w.fromBigInteger(s)}isCanonical(){return this.addressMinusSuffix===this.canonicalForm()}isLinkLocal(){return"1111111010000000000000000000000000000000000000000000000000000000"===this.getBitsBase2(0,64)}isMulticast(){return"Multicast"===this.getType()}is4(){return this.v4}isTeredo(){return this.isInSubnet(new w("2001::/32"))}is6to4(){return this.isInSubnet(new w("2002::/16"))}isLoopback(){return"Loopback"===this.getType()}href(t){return t=void 0===t?"":(0,p.sprintf)(":%s",t),(0,p.sprintf)("http://[%s]%s/",this.correctForm(),t)}link(t){t||(t={}),void 0===t.className&&(t.className=""),void 0===t.prefix&&(t.prefix="/#address="),void 0===t.v4&&(t.v4=!1);let e=this.correctForm;return t.v4&&(e=this.to4in6),t.className?(0,p.sprintf)('<a href="%1$s%2$s" class="%3$s">%2$s</a>',t.prefix,e.call(this),t.className):(0,p.sprintf)('<a href="%1$s%2$s">%2$s</a>',t.prefix,e.call(this))}group(){if(0===this.elidedGroups)return c.simpleGroup(this.address).join(":");m("number"==typeof this.elidedGroups),m("number"==typeof this.elisionBegin);const t=[],[e,s]=this.address.split("::");e.length?t.push(...c.simpleGroup(e)):t.push("");const i=["hover-group"];for(let t=this.elisionBegin;t<this.elisionBegin+this.elidedGroups;t++)i.push((0,p.sprintf)("group-%d",t));return t.push((0,p.sprintf)('<span class="%s"></span>',i.join(" "))),s.length?t.push(...c.simpleGroup(s,this.elisionEnd)):t.push(""),this.is4()&&(m(this.address4 instanceof l.Address4),t.pop(),t.push(this.address4.groupForV6())),t.join(":")}regularExpressionString(t=!1){let e=[];const s=new w(this.correctForm());if(0===s.elidedGroups)e.push((0,u.simpleRegularExpression)(s.parsedAddress));else if(s.elidedGroups===h.GROUPS)e.push((0,u.possibleElisions)(h.GROUPS));else{const t=s.address.split("::");t[0].length&&e.push((0,u.simpleRegularExpression)(t[0].split(":"))),m("number"==typeof s.elidedGroups),e.push((0,u.possibleElisions)(s.elidedGroups,0!==t[0].length,0!==t[1].length)),t[1].length&&e.push((0,u.simpleRegularExpression)(t[1].split(":"))),e=[e.join(":")]}return t||(e=["(?=^|",u.ADDRESS_BOUNDARY,"|[^\\w\\:])(",...e,")(?=[^\\w\\:]|",u.ADDRESS_BOUNDARY,"|$)"]),e.join("")}regularExpression(t=!1){return new RegExp(this.regularExpressionString(t),"i")}}e.Address6=w},8275:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RE_SUBNET_STRING=e.RE_ADDRESS=e.GROUPS=e.BITS=void 0,e.BITS=32,e.GROUPS=4,e.RE_ADDRESS=/^(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.(25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/g,e.RE_SUBNET_STRING=/\/\d{1,2}$/},1813:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.RE_URL_WITH_PORT=e.RE_URL=e.RE_ZONE_STRING=e.RE_SUBNET_STRING=e.RE_BAD_ADDRESS=e.RE_BAD_CHARACTERS=e.TYPES=e.SCOPES=e.GROUPS=e.BITS=void 0,e.BITS=128,e.GROUPS=8,e.SCOPES={0:"Reserved",1:"Interface local",2:"Link local",4:"Admin local",5:"Site local",8:"Organization local",14:"Global",15:"Reserved"},e.TYPES={"ff01::1/128":"Multicast (All nodes on this interface)","ff01::2/128":"Multicast (All routers on this interface)","ff02::1/128":"Multicast (All nodes on this link)","ff02::2/128":"Multicast (All routers on this link)","ff05::2/128":"Multicast (All routers in this site)","ff02::5/128":"Multicast (OSPFv3 AllSPF routers)","ff02::6/128":"Multicast (OSPFv3 AllDR routers)","ff02::9/128":"Multicast (RIP routers)","ff02::a/128":"Multicast (EIGRP routers)","ff02::d/128":"Multicast (PIM routers)","ff02::16/128":"Multicast (MLDv2 reports)","ff01::fb/128":"Multicast (mDNSv6)","ff02::fb/128":"Multicast (mDNSv6)","ff05::fb/128":"Multicast (mDNSv6)","ff02::1:2/128":"Multicast (All DHCP servers and relay agents on this link)","ff05::1:2/128":"Multicast (All DHCP servers and relay agents in this site)","ff02::1:3/128":"Multicast (All DHCP servers on this link)","ff05::1:3/128":"Multicast (All DHCP servers in this site)","::/128":"Unspecified","::1/128":"Loopback","ff00::/8":"Multicast","fe80::/10":"Link-local unicast"},e.RE_BAD_CHARACTERS=/([^0-9a-f:/%])/gi,e.RE_BAD_ADDRESS=/([0-9a-f]{5,}|:{3,}|[^:]:$|^:[^:]|\/$)/gi,e.RE_SUBNET_STRING=/\/\d{1,3}(?=%|$)/,e.RE_ZONE_STRING=/%.*$/,e.RE_URL=new RegExp(/^\[{0,1}([0-9a-f:]+)\]{0,1}/),e.RE_URL_WITH_PORT=new RegExp(/\[([0-9a-f:]+)\]:([0-9]{1,5})/)},3021:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.simpleGroup=e.spanLeadingZeroes=e.spanAll=e.spanAllZeroes=void 0;const i=s(9471);function r(t){return t.replace(/(0+)/g,'<span class="zero">$1</span>')}function n(t){return t.replace(/^(0+)/,'<span class="zero">$1</span>')}e.spanAllZeroes=r,e.spanAll=function(t,e=0){return t.split("").map(((t,s)=>(0,i.sprintf)('<span class="digit value-%s position-%d">%s</span>',t,s+e,r(t)))).join("")},e.spanLeadingZeroes=function(t){return t.split(":").map((t=>n(t))).join(":")},e.simpleGroup=function(t,e=0){return t.split(":").map(((t,s)=>/group-v4/.test(t)?t:(0,i.sprintf)('<span class="hover-group group-%d">%s</span>',s+e,n(t))))}},30:function(t,e,s){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),r=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),n=this&&this.__importStar||function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s in t)"default"!==s&&Object.prototype.hasOwnProperty.call(t,s)&&i(e,t,s);return r(e,t),e};Object.defineProperty(e,"__esModule",{value:!0}),e.possibleElisions=e.simpleRegularExpression=e.ADDRESS_BOUNDARY=e.padGroup=e.groupPossibilities=void 0;const o=n(s(1813)),a=s(9471);function h(t){return(0,a.sprintf)("(%s)",t.join("|"))}function c(t){return t.length<4?(0,a.sprintf)("0{0,%d}%s",4-t.length,t):t}e.groupPossibilities=h,e.padGroup=c,e.ADDRESS_BOUNDARY="[^A-Fa-f0-9:]",e.simpleRegularExpression=function(t){const e=[];t.forEach(((t,s)=>{0===parseInt(t,16)&&e.push(s)}));const s=e.map((e=>t.map(((t,s)=>{if(s===e){const e=0===s||s===o.GROUPS-1?":":"";return h([c(t),e])}return c(t)})).join(":")));return s.push(t.map(c).join(":")),h(s)},e.possibleElisions=function(t,e,s){const i=e?"":":",r=s?"":":",n=[];e||s||n.push("::"),e&&s&&n.push(""),(s&&!e||!s&&e)&&n.push(":"),n.push((0,a.sprintf)("%s(:0{1,4}){1,%d}",i,t-1)),n.push((0,a.sprintf)("(0{1,4}:){1,%d}%s",t-1,r)),n.push((0,a.sprintf)("(0{1,4}:){%d}0{1,4}",t-1));for(let e=1;e<t-1;e++)for(let s=1;s<t-e;s++)n.push((0,a.sprintf)("(0{1,4}:){%d}:(0{1,4}:){%d}0{1,4}",s,t-s-e-1));return h(n)}},8113:function(t,e){(function(){var e;function s(t,e,s){null!=t&&("number"==typeof t?this.fromNumber(t,e,s):null==e&&"string"!=typeof t?this.fromString(t,256):this.fromString(t,e))}function i(){return new s(null)}var r="undefined"!=typeof navigator;r&&"Microsoft Internet Explorer"==navigator.appName?(s.prototype.am=function(t,e,s,i,r,n){for(var o=32767&e,a=e>>15;--n>=0;){var h=32767&this[t],c=this[t++]>>15,l=a*h+c*o;r=((h=o*h+((32767&l)<<15)+s[i]+(1073741823&r))>>>30)+(l>>>15)+a*c+(r>>>30),s[i++]=1073741823&h}return r},e=30):r&&"Netscape"!=navigator.appName?(s.prototype.am=function(t,e,s,i,r,n){for(;--n>=0;){var o=e*this[t++]+s[i]+r;r=Math.floor(o/67108864),s[i++]=67108863&o}return r},e=26):(s.prototype.am=function(t,e,s,i,r,n){for(var o=16383&e,a=e>>14;--n>=0;){var h=16383&this[t],c=this[t++]>>14,l=a*h+c*o;r=((h=o*h+((16383&l)<<14)+s[i]+r)>>28)+(l>>14)+a*c,s[i++]=268435455&h}return r},e=28),s.prototype.DB=e,s.prototype.DM=(1<<e)-1,s.prototype.DV=1<<e,s.prototype.FV=Math.pow(2,52),s.prototype.F1=52-e,s.prototype.F2=2*e-52;var n,o,a=new Array;for(n="0".charCodeAt(0),o=0;o<=9;++o)a[n++]=o;for(n="a".charCodeAt(0),o=10;o<36;++o)a[n++]=o;for(n="A".charCodeAt(0),o=10;o<36;++o)a[n++]=o;function h(t){return"0123456789abcdefghijklmnopqrstuvwxyz".charAt(t)}function c(t,e){var s=a[t.charCodeAt(e)];return null==s?-1:s}function l(t){var e=i();return e.fromInt(t),e}function u(t){var e,s=1;return 0!=(e=t>>>16)&&(t=e,s+=16),0!=(e=t>>8)&&(t=e,s+=8),0!=(e=t>>4)&&(t=e,s+=4),0!=(e=t>>2)&&(t=e,s+=2),0!=(e=t>>1)&&(t=e,s+=1),s}function d(t){this.m=t}function f(t){this.m=t,this.mp=t.invDigit(),this.mpl=32767&this.mp,this.mph=this.mp>>15,this.um=(1<<t.DB-15)-1,this.mt2=2*t.t}function p(t,e){return t&e}function m(t,e){return t|e}function g(t,e){return t^e}function S(t,e){return t&~e}function y(t){if(0==t)return-1;var e=0;return 65535&t||(t>>=16,e+=16),255&t||(t>>=8,e+=8),15&t||(t>>=4,e+=4),3&t||(t>>=2,e+=2),1&t||++e,e}function w(t){for(var e=0;0!=t;)t&=t-1,++e;return e}function _(){}function v(t){return t}function E(t){this.r2=i(),this.q3=i(),s.ONE.dlShiftTo(2*t.t,this.r2),this.mu=this.r2.divide(t),this.m=t}d.prototype.convert=function(t){return t.s<0||t.compareTo(this.m)>=0?t.mod(this.m):t},d.prototype.revert=function(t){return t},d.prototype.reduce=function(t){t.divRemTo(this.m,null,t)},d.prototype.mulTo=function(t,e,s){t.multiplyTo(e,s),this.reduce(s)},d.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},f.prototype.convert=function(t){var e=i();return t.abs().dlShiftTo(this.m.t,e),e.divRemTo(this.m,null,e),t.s<0&&e.compareTo(s.ZERO)>0&&this.m.subTo(e,e),e},f.prototype.revert=function(t){var e=i();return t.copyTo(e),this.reduce(e),e},f.prototype.reduce=function(t){for(;t.t<=this.mt2;)t[t.t++]=0;for(var e=0;e<this.m.t;++e){var s=32767&t[e],i=s*this.mpl+((s*this.mph+(t[e]>>15)*this.mpl&this.um)<<15)&t.DM;for(t[s=e+this.m.t]+=this.m.am(0,i,t,e,0,this.m.t);t[s]>=t.DV;)t[s]-=t.DV,t[++s]++}t.clamp(),t.drShiftTo(this.m.t,t),t.compareTo(this.m)>=0&&t.subTo(this.m,t)},f.prototype.mulTo=function(t,e,s){t.multiplyTo(e,s),this.reduce(s)},f.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)},s.prototype.copyTo=function(t){for(var e=this.t-1;e>=0;--e)t[e]=this[e];t.t=this.t,t.s=this.s},s.prototype.fromInt=function(t){this.t=1,this.s=t<0?-1:0,t>0?this[0]=t:t<-1?this[0]=t+this.DV:this.t=0},s.prototype.fromString=function(t,e){var i;if(16==e)i=4;else if(8==e)i=3;else if(256==e)i=8;else if(2==e)i=1;else if(32==e)i=5;else{if(4!=e)return void this.fromRadix(t,e);i=2}this.t=0,this.s=0;for(var r=t.length,n=!1,o=0;--r>=0;){var a=8==i?255&t[r]:c(t,r);a<0?"-"==t.charAt(r)&&(n=!0):(n=!1,0==o?this[this.t++]=a:o+i>this.DB?(this[this.t-1]|=(a&(1<<this.DB-o)-1)<<o,this[this.t++]=a>>this.DB-o):this[this.t-1]|=a<<o,(o+=i)>=this.DB&&(o-=this.DB))}8==i&&128&t[0]&&(this.s=-1,o>0&&(this[this.t-1]|=(1<<this.DB-o)-1<<o)),this.clamp(),n&&s.ZERO.subTo(this,this)},s.prototype.clamp=function(){for(var t=this.s&this.DM;this.t>0&&this[this.t-1]==t;)--this.t},s.prototype.dlShiftTo=function(t,e){var s;for(s=this.t-1;s>=0;--s)e[s+t]=this[s];for(s=t-1;s>=0;--s)e[s]=0;e.t=this.t+t,e.s=this.s},s.prototype.drShiftTo=function(t,e){for(var s=t;s<this.t;++s)e[s-t]=this[s];e.t=Math.max(this.t-t,0),e.s=this.s},s.prototype.lShiftTo=function(t,e){var s,i=t%this.DB,r=this.DB-i,n=(1<<r)-1,o=Math.floor(t/this.DB),a=this.s<<i&this.DM;for(s=this.t-1;s>=0;--s)e[s+o+1]=this[s]>>r|a,a=(this[s]&n)<<i;for(s=o-1;s>=0;--s)e[s]=0;e[o]=a,e.t=this.t+o+1,e.s=this.s,e.clamp()},s.prototype.rShiftTo=function(t,e){e.s=this.s;var s=Math.floor(t/this.DB);if(s>=this.t)e.t=0;else{var i=t%this.DB,r=this.DB-i,n=(1<<i)-1;e[0]=this[s]>>i;for(var o=s+1;o<this.t;++o)e[o-s-1]|=(this[o]&n)<<r,e[o-s]=this[o]>>i;i>0&&(e[this.t-s-1]|=(this.s&n)<<r),e.t=this.t-s,e.clamp()}},s.prototype.subTo=function(t,e){for(var s=0,i=0,r=Math.min(t.t,this.t);s<r;)i+=this[s]-t[s],e[s++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i-=t.s;s<this.t;)i+=this[s],e[s++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;s<t.t;)i-=t[s],e[s++]=i&this.DM,i>>=this.DB;i-=t.s}e.s=i<0?-1:0,i<-1?e[s++]=this.DV+i:i>0&&(e[s++]=i),e.t=s,e.clamp()},s.prototype.multiplyTo=function(t,e){var i=this.abs(),r=t.abs(),n=i.t;for(e.t=n+r.t;--n>=0;)e[n]=0;for(n=0;n<r.t;++n)e[n+i.t]=i.am(0,r[n],e,n,0,i.t);e.s=0,e.clamp(),this.s!=t.s&&s.ZERO.subTo(e,e)},s.prototype.squareTo=function(t){for(var e=this.abs(),s=t.t=2*e.t;--s>=0;)t[s]=0;for(s=0;s<e.t-1;++s){var i=e.am(s,e[s],t,2*s,0,1);(t[s+e.t]+=e.am(s+1,2*e[s],t,2*s+1,i,e.t-s-1))>=e.DV&&(t[s+e.t]-=e.DV,t[s+e.t+1]=1)}t.t>0&&(t[t.t-1]+=e.am(s,e[s],t,2*s,0,1)),t.s=0,t.clamp()},s.prototype.divRemTo=function(t,e,r){var n=t.abs();if(!(n.t<=0)){var o=this.abs();if(o.t<n.t)return null!=e&&e.fromInt(0),void(null!=r&&this.copyTo(r));null==r&&(r=i());var a=i(),h=this.s,c=t.s,l=this.DB-u(n[n.t-1]);l>0?(n.lShiftTo(l,a),o.lShiftTo(l,r)):(n.copyTo(a),o.copyTo(r));var d=a.t,f=a[d-1];if(0!=f){var p=f*(1<<this.F1)+(d>1?a[d-2]>>this.F2:0),m=this.FV/p,g=(1<<this.F1)/p,S=1<<this.F2,y=r.t,w=y-d,_=null==e?i():e;for(a.dlShiftTo(w,_),r.compareTo(_)>=0&&(r[r.t++]=1,r.subTo(_,r)),s.ONE.dlShiftTo(d,_),_.subTo(a,a);a.t<d;)a[a.t++]=0;for(;--w>=0;){var v=r[--y]==f?this.DM:Math.floor(r[y]*m+(r[y-1]+S)*g);if((r[y]+=a.am(0,v,r,w,0,d))<v)for(a.dlShiftTo(w,_),r.subTo(_,r);r[y]<--v;)r.subTo(_,r)}null!=e&&(r.drShiftTo(d,e),h!=c&&s.ZERO.subTo(e,e)),r.t=d,r.clamp(),l>0&&r.rShiftTo(l,r),h<0&&s.ZERO.subTo(r,r)}}},s.prototype.invDigit=function(){if(this.t<1)return 0;var t=this[0];if(!(1&t))return 0;var e=3&t;return(e=(e=(e=(e=e*(2-(15&t)*e)&15)*(2-(255&t)*e)&255)*(2-((65535&t)*e&65535))&65535)*(2-t*e%this.DV)%this.DV)>0?this.DV-e:-e},s.prototype.isEven=function(){return 0==(this.t>0?1&this[0]:this.s)},s.prototype.exp=function(t,e){if(t>4294967295||t<1)return s.ONE;var r=i(),n=i(),o=e.convert(this),a=u(t)-1;for(o.copyTo(r);--a>=0;)if(e.sqrTo(r,n),(t&1<<a)>0)e.mulTo(n,o,r);else{var h=r;r=n,n=h}return e.revert(r)},s.prototype.toString=function(t){if(this.s<0)return"-"+this.negate().toString(t);var e;if(16==t)e=4;else if(8==t)e=3;else if(2==t)e=1;else if(32==t)e=5;else{if(4!=t)return this.toRadix(t);e=2}var s,i=(1<<e)-1,r=!1,n="",o=this.t,a=this.DB-o*this.DB%e;if(o-- >0)for(a<this.DB&&(s=this[o]>>a)>0&&(r=!0,n=h(s));o>=0;)a<e?(s=(this[o]&(1<<a)-1)<<e-a,s|=this[--o]>>(a+=this.DB-e)):(s=this[o]>>(a-=e)&i,a<=0&&(a+=this.DB,--o)),s>0&&(r=!0),r&&(n+=h(s));return r?n:"0"},s.prototype.negate=function(){var t=i();return s.ZERO.subTo(this,t),t},s.prototype.abs=function(){return this.s<0?this.negate():this},s.prototype.compareTo=function(t){var e=this.s-t.s;if(0!=e)return e;var s=this.t;if(0!=(e=s-t.t))return this.s<0?-e:e;for(;--s>=0;)if(0!=(e=this[s]-t[s]))return e;return 0},s.prototype.bitLength=function(){return this.t<=0?0:this.DB*(this.t-1)+u(this[this.t-1]^this.s&this.DM)},s.prototype.mod=function(t){var e=i();return this.abs().divRemTo(t,null,e),this.s<0&&e.compareTo(s.ZERO)>0&&t.subTo(e,e),e},s.prototype.modPowInt=function(t,e){var s;return s=t<256||e.isEven()?new d(e):new f(e),this.exp(t,s)},s.ZERO=l(0),s.ONE=l(1),_.prototype.convert=v,_.prototype.revert=v,_.prototype.mulTo=function(t,e,s){t.multiplyTo(e,s)},_.prototype.sqrTo=function(t,e){t.squareTo(e)},E.prototype.convert=function(t){if(t.s<0||t.t>2*this.m.t)return t.mod(this.m);if(t.compareTo(this.m)<0)return t;var e=i();return t.copyTo(e),this.reduce(e),e},E.prototype.revert=function(t){return t},E.prototype.reduce=function(t){for(t.drShiftTo(this.m.t-1,this.r2),t.t>this.m.t+1&&(t.t=this.m.t+1,t.clamp()),this.mu.multiplyUpperTo(this.r2,this.m.t+1,this.q3),this.m.multiplyLowerTo(this.q3,this.m.t+1,this.r2);t.compareTo(this.r2)<0;)t.dAddOffset(1,this.m.t+1);for(t.subTo(this.r2,t);t.compareTo(this.m)>=0;)t.subTo(this.m,t)},E.prototype.mulTo=function(t,e,s){t.multiplyTo(e,s),this.reduce(s)},E.prototype.sqrTo=function(t,e){t.squareTo(e),this.reduce(e)};var b,I,R,O=[2,3,5,7,11,13,17,19,23,29,31,37,41,43,47,53,59,61,67,71,73,79,83,89,97,101,103,107,109,113,127,131,137,139,149,151,157,163,167,173,179,181,191,193,197,199,211,223,227,229,233,239,241,251,257,263,269,271,277,281,283,293,307,311,313,317,331,337,347,349,353,359,367,373,379,383,389,397,401,409,419,421,431,433,439,443,449,457,461,463,467,479,487,491,499,503,509,521,523,541,547,557,563,569,571,577,587,593,599,601,607,613,617,619,631,641,643,647,653,659,661,673,677,683,691,701,709,719,727,733,739,743,751,757,761,769,773,787,797,809,811,821,823,827,829,839,853,857,859,863,877,881,883,887,907,911,919,929,937,941,947,953,967,971,977,983,991,997],T=(1<<26)/O[O.length-1];function k(){var t;t=(new Date).getTime(),I[R++]^=255&t,I[R++]^=t>>8&255,I[R++]^=t>>16&255,I[R++]^=t>>24&255,R>=P&&(R-=P)}if(s.prototype.chunkSize=function(t){return Math.floor(Math.LN2*this.DB/Math.log(t))},s.prototype.toRadix=function(t){if(null==t&&(t=10),0==this.signum()||t<2||t>36)return"0";var e=this.chunkSize(t),s=Math.pow(t,e),r=l(s),n=i(),o=i(),a="";for(this.divRemTo(r,n,o);n.signum()>0;)a=(s+o.intValue()).toString(t).substr(1)+a,n.divRemTo(r,n,o);return o.intValue().toString(t)+a},s.prototype.fromRadix=function(t,e){this.fromInt(0),null==e&&(e=10);for(var i=this.chunkSize(e),r=Math.pow(e,i),n=!1,o=0,a=0,h=0;h<t.length;++h){var l=c(t,h);l<0?"-"==t.charAt(h)&&0==this.signum()&&(n=!0):(a=e*a+l,++o>=i&&(this.dMultiply(r),this.dAddOffset(a,0),o=0,a=0))}o>0&&(this.dMultiply(Math.pow(e,o)),this.dAddOffset(a,0)),n&&s.ZERO.subTo(this,this)},s.prototype.fromNumber=function(t,e,i){if("number"==typeof e)if(t<2)this.fromInt(1);else for(this.fromNumber(t,i),this.testBit(t-1)||this.bitwiseTo(s.ONE.shiftLeft(t-1),m,this),this.isEven()&&this.dAddOffset(1,0);!this.isProbablePrime(e);)this.dAddOffset(2,0),this.bitLength()>t&&this.subTo(s.ONE.shiftLeft(t-1),this);else{var r=new Array,n=7&t;r.length=1+(t>>3),e.nextBytes(r),n>0?r[0]&=(1<<n)-1:r[0]=0,this.fromString(r,256)}},s.prototype.bitwiseTo=function(t,e,s){var i,r,n=Math.min(t.t,this.t);for(i=0;i<n;++i)s[i]=e(this[i],t[i]);if(t.t<this.t){for(r=t.s&this.DM,i=n;i<this.t;++i)s[i]=e(this[i],r);s.t=this.t}else{for(r=this.s&this.DM,i=n;i<t.t;++i)s[i]=e(r,t[i]);s.t=t.t}s.s=e(this.s,t.s),s.clamp()},s.prototype.changeBit=function(t,e){var i=s.ONE.shiftLeft(t);return this.bitwiseTo(i,e,i),i},s.prototype.addTo=function(t,e){for(var s=0,i=0,r=Math.min(t.t,this.t);s<r;)i+=this[s]+t[s],e[s++]=i&this.DM,i>>=this.DB;if(t.t<this.t){for(i+=t.s;s<this.t;)i+=this[s],e[s++]=i&this.DM,i>>=this.DB;i+=this.s}else{for(i+=this.s;s<t.t;)i+=t[s],e[s++]=i&this.DM,i>>=this.DB;i+=t.s}e.s=i<0?-1:0,i>0?e[s++]=i:i<-1&&(e[s++]=this.DV+i),e.t=s,e.clamp()},s.prototype.dMultiply=function(t){this[this.t]=this.am(0,t-1,this,0,0,this.t),++this.t,this.clamp()},s.prototype.dAddOffset=function(t,e){if(0!=t){for(;this.t<=e;)this[this.t++]=0;for(this[e]+=t;this[e]>=this.DV;)this[e]-=this.DV,++e>=this.t&&(this[this.t++]=0),++this[e]}},s.prototype.multiplyLowerTo=function(t,e,s){var i,r=Math.min(this.t+t.t,e);for(s.s=0,s.t=r;r>0;)s[--r]=0;for(i=s.t-this.t;r<i;++r)s[r+this.t]=this.am(0,t[r],s,r,0,this.t);for(i=Math.min(t.t,e);r<i;++r)this.am(0,t[r],s,r,0,e-r);s.clamp()},s.prototype.multiplyUpperTo=function(t,e,s){--e;var i=s.t=this.t+t.t-e;for(s.s=0;--i>=0;)s[i]=0;for(i=Math.max(e-this.t,0);i<t.t;++i)s[this.t+i-e]=this.am(e-i,t[i],s,0,0,this.t+i-e);s.clamp(),s.drShiftTo(1,s)},s.prototype.modInt=function(t){if(t<=0)return 0;var e=this.DV%t,s=this.s<0?t-1:0;if(this.t>0)if(0==e)s=this[0]%t;else for(var i=this.t-1;i>=0;--i)s=(e*s+this[i])%t;return s},s.prototype.millerRabin=function(t){var e=this.subtract(s.ONE),r=e.getLowestSetBit();if(r<=0)return!1;var n=e.shiftRight(r);(t=t+1>>1)>O.length&&(t=O.length);for(var o=i(),a=0;a<t;++a){o.fromInt(O[Math.floor(Math.random()*O.length)]);var h=o.modPow(n,this);if(0!=h.compareTo(s.ONE)&&0!=h.compareTo(e)){for(var c=1;c++<r&&0!=h.compareTo(e);)if(0==(h=h.modPowInt(2,this)).compareTo(s.ONE))return!1;if(0!=h.compareTo(e))return!1}}return!0},s.prototype.clone=function(){var t=i();return this.copyTo(t),t},s.prototype.intValue=function(){if(this.s<0){if(1==this.t)return this[0]-this.DV;if(0==this.t)return-1}else{if(1==this.t)return this[0];if(0==this.t)return 0}return(this[1]&(1<<32-this.DB)-1)<<this.DB|this[0]},s.prototype.byteValue=function(){return 0==this.t?this.s:this[0]<<24>>24},s.prototype.shortValue=function(){return 0==this.t?this.s:this[0]<<16>>16},s.prototype.signum=function(){return this.s<0?-1:this.t<=0||1==this.t&&this[0]<=0?0:1},s.prototype.toByteArray=function(){var t=this.t,e=new Array;e[0]=this.s;var s,i=this.DB-t*this.DB%8,r=0;if(t-- >0)for(i<this.DB&&(s=this[t]>>i)!=(this.s&this.DM)>>i&&(e[r++]=s|this.s<<this.DB-i);t>=0;)i<8?(s=(this[t]&(1<<i)-1)<<8-i,s|=this[--t]>>(i+=this.DB-8)):(s=this[t]>>(i-=8)&255,i<=0&&(i+=this.DB,--t)),128&s&&(s|=-256),0==r&&(128&this.s)!=(128&s)&&++r,(r>0||s!=this.s)&&(e[r++]=s);return e},s.prototype.equals=function(t){return 0==this.compareTo(t)},s.prototype.min=function(t){return this.compareTo(t)<0?this:t},s.prototype.max=function(t){return this.compareTo(t)>0?this:t},s.prototype.and=function(t){var e=i();return this.bitwiseTo(t,p,e),e},s.prototype.or=function(t){var e=i();return this.bitwiseTo(t,m,e),e},s.prototype.xor=function(t){var e=i();return this.bitwiseTo(t,g,e),e},s.prototype.andNot=function(t){var e=i();return this.bitwiseTo(t,S,e),e},s.prototype.not=function(){for(var t=i(),e=0;e<this.t;++e)t[e]=this.DM&~this[e];return t.t=this.t,t.s=~this.s,t},s.prototype.shiftLeft=function(t){var e=i();return t<0?this.rShiftTo(-t,e):this.lShiftTo(t,e),e},s.prototype.shiftRight=function(t){var e=i();return t<0?this.lShiftTo(-t,e):this.rShiftTo(t,e),e},s.prototype.getLowestSetBit=function(){for(var t=0;t<this.t;++t)if(0!=this[t])return t*this.DB+y(this[t]);return this.s<0?this.t*this.DB:-1},s.prototype.bitCount=function(){for(var t=0,e=this.s&this.DM,s=0;s<this.t;++s)t+=w(this[s]^e);return t},s.prototype.testBit=function(t){var e=Math.floor(t/this.DB);return e>=this.t?0!=this.s:!!(this[e]&1<<t%this.DB)},s.prototype.setBit=function(t){return this.changeBit(t,m)},s.prototype.clearBit=function(t){return this.changeBit(t,S)},s.prototype.flipBit=function(t){return this.changeBit(t,g)},s.prototype.add=function(t){var e=i();return this.addTo(t,e),e},s.prototype.subtract=function(t){var e=i();return this.subTo(t,e),e},s.prototype.multiply=function(t){var e=i();return this.multiplyTo(t,e),e},s.prototype.divide=function(t){var e=i();return this.divRemTo(t,e,null),e},s.prototype.remainder=function(t){var e=i();return this.divRemTo(t,null,e),e},s.prototype.divideAndRemainder=function(t){var e=i(),s=i();return this.divRemTo(t,e,s),new Array(e,s)},s.prototype.modPow=function(t,e){var s,r,n=t.bitLength(),o=l(1);if(n<=0)return o;s=n<18?1:n<48?3:n<144?4:n<768?5:6,r=n<8?new d(e):e.isEven()?new E(e):new f(e);var a=new Array,h=3,c=s-1,p=(1<<s)-1;if(a[1]=r.convert(this),s>1){var m=i();for(r.sqrTo(a[1],m);h<=p;)a[h]=i(),r.mulTo(m,a[h-2],a[h]),h+=2}var g,S,y=t.t-1,w=!0,_=i();for(n=u(t[y])-1;y>=0;){for(n>=c?g=t[y]>>n-c&p:(g=(t[y]&(1<<n+1)-1)<<c-n,y>0&&(g|=t[y-1]>>this.DB+n-c)),h=s;!(1&g);)g>>=1,--h;if((n-=h)<0&&(n+=this.DB,--y),w)a[g].copyTo(o),w=!1;else{for(;h>1;)r.sqrTo(o,_),r.sqrTo(_,o),h-=2;h>0?r.sqrTo(o,_):(S=o,o=_,_=S),r.mulTo(_,a[g],o)}for(;y>=0&&!(t[y]&1<<n);)r.sqrTo(o,_),S=o,o=_,_=S,--n<0&&(n=this.DB-1,--y)}return r.revert(o)},s.prototype.modInverse=function(t){var e=t.isEven();if(this.isEven()&&e||0==t.signum())return s.ZERO;for(var i=t.clone(),r=this.clone(),n=l(1),o=l(0),a=l(0),h=l(1);0!=i.signum();){for(;i.isEven();)i.rShiftTo(1,i),e?(n.isEven()&&o.isEven()||(n.addTo(this,n),o.subTo(t,o)),n.rShiftTo(1,n)):o.isEven()||o.subTo(t,o),o.rShiftTo(1,o);for(;r.isEven();)r.rShiftTo(1,r),e?(a.isEven()&&h.isEven()||(a.addTo(this,a),h.subTo(t,h)),a.rShiftTo(1,a)):h.isEven()||h.subTo(t,h),h.rShiftTo(1,h);i.compareTo(r)>=0?(i.subTo(r,i),e&&n.subTo(a,n),o.subTo(h,o)):(r.subTo(i,r),e&&a.subTo(n,a),h.subTo(o,h))}return 0!=r.compareTo(s.ONE)?s.ZERO:h.compareTo(t)>=0?h.subtract(t):h.signum()<0?(h.addTo(t,h),h.signum()<0?h.add(t):h):h},s.prototype.pow=function(t){return this.exp(t,new _)},s.prototype.gcd=function(t){var e=this.s<0?this.negate():this.clone(),s=t.s<0?t.negate():t.clone();if(e.compareTo(s)<0){var i=e;e=s,s=i}var r=e.getLowestSetBit(),n=s.getLowestSetBit();if(n<0)return e;for(r<n&&(n=r),n>0&&(e.rShiftTo(n,e),s.rShiftTo(n,s));e.signum()>0;)(r=e.getLowestSetBit())>0&&e.rShiftTo(r,e),(r=s.getLowestSetBit())>0&&s.rShiftTo(r,s),e.compareTo(s)>=0?(e.subTo(s,e),e.rShiftTo(1,e)):(s.subTo(e,s),s.rShiftTo(1,s));return n>0&&s.lShiftTo(n,s),s},s.prototype.isProbablePrime=function(t){var e,s=this.abs();if(1==s.t&&s[0]<=O[O.length-1]){for(e=0;e<O.length;++e)if(s[0]==O[e])return!0;return!1}if(s.isEven())return!1;for(e=1;e<O.length;){for(var i=O[e],r=e+1;r<O.length&&i<T;)i*=O[r++];for(i=s.modInt(i);e<r;)if(i%O[e++]==0)return!1}return s.millerRabin(t)},s.prototype.square=function(){var t=i();return this.squareTo(t),t},s.prototype.Barrett=E,null==I){var D;if(I=new Array,R=0,"undefined"!=typeof window&&window.crypto)if(window.crypto.getRandomValues){var A=new Uint8Array(32);for(window.crypto.getRandomValues(A),D=0;D<32;++D)I[R++]=A[D]}else if("Netscape"==navigator.appName&&navigator.appVersion<"5"){var C=window.crypto.random(32);for(D=0;D<C.length;++D)I[R++]=255&C.charCodeAt(D)}for(;R<P;)D=Math.floor(65536*Math.random()),I[R++]=D>>>8,I[R++]=255&D;R=0,k()}function L(){if(null==b){for(k(),(b=new B).init(I),R=0;R<I.length;++R)I[R]=0;R=0}return b.next()}function x(){}function B(){this.i=0,this.j=0,this.S=new Array}x.prototype.nextBytes=function(t){var e;for(e=0;e<t.length;++e)t[e]=L()},B.prototype.init=function(t){var e,s,i;for(e=0;e<256;++e)this.S[e]=e;for(s=0,e=0;e<256;++e)s=s+this.S[e]+t[e%t.length]&255,i=this.S[e],this.S[e]=this.S[s],this.S[s]=i;this.i=0,this.j=0},B.prototype.next=function(){var t;return this.i=this.i+1&255,this.j=this.j+this.S[this.i]&255,t=this.S[this.i],this.S[this.i]=this.S[this.j],this.S[this.j]=t,this.S[t+this.S[this.i]&255]};var P=256;t.exports={default:s,BigInteger:s,SecureRandom:x}}).call(this)},5092:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});const i=s(6742);class r{constructor(t){if(this.length=0,this._encoding="utf8",this._writeOffset=0,this._readOffset=0,r.isSmartBufferOptions(t))if(t.encoding&&(i.checkEncoding(t.encoding),this._encoding=t.encoding),t.size){if(!(i.isFiniteInteger(t.size)&&t.size>0))throw new Error(i.ERRORS.INVALID_SMARTBUFFER_SIZE);this._buff=Buffer.allocUnsafe(t.size)}else if(t.buff){if(!Buffer.isBuffer(t.buff))throw new Error(i.ERRORS.INVALID_SMARTBUFFER_BUFFER);this._buff=t.buff,this.length=t.buff.length}else this._buff=Buffer.allocUnsafe(4096);else{if(void 0!==t)throw new Error(i.ERRORS.INVALID_SMARTBUFFER_OBJECT);this._buff=Buffer.allocUnsafe(4096)}}static fromSize(t,e){return new this({size:t,encoding:e})}static fromBuffer(t,e){return new this({buff:t,encoding:e})}static fromOptions(t){return new this(t)}static isSmartBufferOptions(t){const e=t;return e&&(void 0!==e.encoding||void 0!==e.size||void 0!==e.buff)}readInt8(t){return this._readNumberValue(Buffer.prototype.readInt8,1,t)}readInt16BE(t){return this._readNumberValue(Buffer.prototype.readInt16BE,2,t)}readInt16LE(t){return this._readNumberValue(Buffer.prototype.readInt16LE,2,t)}readInt32BE(t){return this._readNumberValue(Buffer.prototype.readInt32BE,4,t)}readInt32LE(t){return this._readNumberValue(Buffer.prototype.readInt32LE,4,t)}readBigInt64BE(t){return i.bigIntAndBufferInt64Check("readBigInt64BE"),this._readNumberValue(Buffer.prototype.readBigInt64BE,8,t)}readBigInt64LE(t){return i.bigIntAndBufferInt64Check("readBigInt64LE"),this._readNumberValue(Buffer.prototype.readBigInt64LE,8,t)}writeInt8(t,e){return this._writeNumberValue(Buffer.prototype.writeInt8,1,t,e),this}insertInt8(t,e){return this._insertNumberValue(Buffer.prototype.writeInt8,1,t,e)}writeInt16BE(t,e){return this._writeNumberValue(Buffer.prototype.writeInt16BE,2,t,e)}insertInt16BE(t,e){return this._insertNumberValue(Buffer.prototype.writeInt16BE,2,t,e)}writeInt16LE(t,e){return this._writeNumberValue(Buffer.prototype.writeInt16LE,2,t,e)}insertInt16LE(t,e){return this._insertNumberValue(Buffer.prototype.writeInt16LE,2,t,e)}writeInt32BE(t,e){return this._writeNumberValue(Buffer.prototype.writeInt32BE,4,t,e)}insertInt32BE(t,e){return this._insertNumberValue(Buffer.prototype.writeInt32BE,4,t,e)}writeInt32LE(t,e){return this._writeNumberValue(Buffer.prototype.writeInt32LE,4,t,e)}insertInt32LE(t,e){return this._insertNumberValue(Buffer.prototype.writeInt32LE,4,t,e)}writeBigInt64BE(t,e){return i.bigIntAndBufferInt64Check("writeBigInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigInt64BE,8,t,e)}insertBigInt64BE(t,e){return i.bigIntAndBufferInt64Check("writeBigInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigInt64BE,8,t,e)}writeBigInt64LE(t,e){return i.bigIntAndBufferInt64Check("writeBigInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigInt64LE,8,t,e)}insertBigInt64LE(t,e){return i.bigIntAndBufferInt64Check("writeBigInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigInt64LE,8,t,e)}readUInt8(t){return this._readNumberValue(Buffer.prototype.readUInt8,1,t)}readUInt16BE(t){return this._readNumberValue(Buffer.prototype.readUInt16BE,2,t)}readUInt16LE(t){return this._readNumberValue(Buffer.prototype.readUInt16LE,2,t)}readUInt32BE(t){return this._readNumberValue(Buffer.prototype.readUInt32BE,4,t)}readUInt32LE(t){return this._readNumberValue(Buffer.prototype.readUInt32LE,4,t)}readBigUInt64BE(t){return i.bigIntAndBufferInt64Check("readBigUInt64BE"),this._readNumberValue(Buffer.prototype.readBigUInt64BE,8,t)}readBigUInt64LE(t){return i.bigIntAndBufferInt64Check("readBigUInt64LE"),this._readNumberValue(Buffer.prototype.readBigUInt64LE,8,t)}writeUInt8(t,e){return this._writeNumberValue(Buffer.prototype.writeUInt8,1,t,e)}insertUInt8(t,e){return this._insertNumberValue(Buffer.prototype.writeUInt8,1,t,e)}writeUInt16BE(t,e){return this._writeNumberValue(Buffer.prototype.writeUInt16BE,2,t,e)}insertUInt16BE(t,e){return this._insertNumberValue(Buffer.prototype.writeUInt16BE,2,t,e)}writeUInt16LE(t,e){return this._writeNumberValue(Buffer.prototype.writeUInt16LE,2,t,e)}insertUInt16LE(t,e){return this._insertNumberValue(Buffer.prototype.writeUInt16LE,2,t,e)}writeUInt32BE(t,e){return this._writeNumberValue(Buffer.prototype.writeUInt32BE,4,t,e)}insertUInt32BE(t,e){return this._insertNumberValue(Buffer.prototype.writeUInt32BE,4,t,e)}writeUInt32LE(t,e){return this._writeNumberValue(Buffer.prototype.writeUInt32LE,4,t,e)}insertUInt32LE(t,e){return this._insertNumberValue(Buffer.prototype.writeUInt32LE,4,t,e)}writeBigUInt64BE(t,e){return i.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64BE,8,t,e)}insertBigUInt64BE(t,e){return i.bigIntAndBufferInt64Check("writeBigUInt64BE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64BE,8,t,e)}writeBigUInt64LE(t,e){return i.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._writeNumberValue(Buffer.prototype.writeBigUInt64LE,8,t,e)}insertBigUInt64LE(t,e){return i.bigIntAndBufferInt64Check("writeBigUInt64LE"),this._insertNumberValue(Buffer.prototype.writeBigUInt64LE,8,t,e)}readFloatBE(t){return this._readNumberValue(Buffer.prototype.readFloatBE,4,t)}readFloatLE(t){return this._readNumberValue(Buffer.prototype.readFloatLE,4,t)}writeFloatBE(t,e){return this._writeNumberValue(Buffer.prototype.writeFloatBE,4,t,e)}insertFloatBE(t,e){return this._insertNumberValue(Buffer.prototype.writeFloatBE,4,t,e)}writeFloatLE(t,e){return this._writeNumberValue(Buffer.prototype.writeFloatLE,4,t,e)}insertFloatLE(t,e){return this._insertNumberValue(Buffer.prototype.writeFloatLE,4,t,e)}readDoubleBE(t){return this._readNumberValue(Buffer.prototype.readDoubleBE,8,t)}readDoubleLE(t){return this._readNumberValue(Buffer.prototype.readDoubleLE,8,t)}writeDoubleBE(t,e){return this._writeNumberValue(Buffer.prototype.writeDoubleBE,8,t,e)}insertDoubleBE(t,e){return this._insertNumberValue(Buffer.prototype.writeDoubleBE,8,t,e)}writeDoubleLE(t,e){return this._writeNumberValue(Buffer.prototype.writeDoubleLE,8,t,e)}insertDoubleLE(t,e){return this._insertNumberValue(Buffer.prototype.writeDoubleLE,8,t,e)}readString(t,e){let s;"number"==typeof t?(i.checkLengthValue(t),s=Math.min(t,this.length-this._readOffset)):(e=t,s=this.length-this._readOffset),void 0!==e&&i.checkEncoding(e);const r=this._buff.slice(this._readOffset,this._readOffset+s).toString(e||this._encoding);return this._readOffset+=s,r}insertString(t,e,s){return i.checkOffsetValue(e),this._handleString(t,!0,e,s)}writeString(t,e,s){return this._handleString(t,!1,e,s)}readStringNT(t){void 0!==t&&i.checkEncoding(t);let e=this.length;for(let t=this._readOffset;t<this.length;t++)if(0===this._buff[t]){e=t;break}const s=this._buff.slice(this._readOffset,e);return this._readOffset=e+1,s.toString(t||this._encoding)}insertStringNT(t,e,s){return i.checkOffsetValue(e),this.insertString(t,e,s),this.insertUInt8(0,e+t.length),this}writeStringNT(t,e,s){return this.writeString(t,e,s),this.writeUInt8(0,"number"==typeof e?e+t.length:this.writeOffset),this}readBuffer(t){void 0!==t&&i.checkLengthValue(t);const e="number"==typeof t?t:this.length,s=Math.min(this.length,this._readOffset+e),r=this._buff.slice(this._readOffset,s);return this._readOffset=s,r}insertBuffer(t,e){return i.checkOffsetValue(e),this._handleBuffer(t,!0,e)}writeBuffer(t,e){return this._handleBuffer(t,!1,e)}readBufferNT(){let t=this.length;for(let e=this._readOffset;e<this.length;e++)if(0===this._buff[e]){t=e;break}const e=this._buff.slice(this._readOffset,t);return this._readOffset=t+1,e}insertBufferNT(t,e){return i.checkOffsetValue(e),this.insertBuffer(t,e),this.insertUInt8(0,e+t.length),this}writeBufferNT(t,e){return void 0!==e&&i.checkOffsetValue(e),this.writeBuffer(t,e),this.writeUInt8(0,"number"==typeof e?e+t.length:this._writeOffset),this}clear(){return this._writeOffset=0,this._readOffset=0,this.length=0,this}remaining(){return this.length-this._readOffset}get readOffset(){return this._readOffset}set readOffset(t){i.checkOffsetValue(t),i.checkTargetOffset(t,this),this._readOffset=t}get writeOffset(){return this._writeOffset}set writeOffset(t){i.checkOffsetValue(t),i.checkTargetOffset(t,this),this._writeOffset=t}get encoding(){return this._encoding}set encoding(t){i.checkEncoding(t),this._encoding=t}get internalBuffer(){return this._buff}toBuffer(){return this._buff.slice(0,this.length)}toString(t){const e="string"==typeof t?t:this._encoding;return i.checkEncoding(e),this._buff.toString(e,0,this.length)}destroy(){return this.clear(),this}_handleString(t,e,s,r){let n=this._writeOffset,o=this._encoding;"number"==typeof s?n=s:"string"==typeof s&&(i.checkEncoding(s),o=s),"string"==typeof r&&(i.checkEncoding(r),o=r);const a=Buffer.byteLength(t,o);return e?this.ensureInsertable(a,n):this._ensureWriteable(a,n),this._buff.write(t,n,a,o),e?this._writeOffset+=a:"number"==typeof s?this._writeOffset=Math.max(this._writeOffset,n+a):this._writeOffset+=a,this}_handleBuffer(t,e,s){const i="number"==typeof s?s:this._writeOffset;return e?this.ensureInsertable(t.length,i):this._ensureWriteable(t.length,i),t.copy(this._buff,i),e?this._writeOffset+=t.length:"number"==typeof s?this._writeOffset=Math.max(this._writeOffset,i+t.length):this._writeOffset+=t.length,this}ensureReadable(t,e){let s=this._readOffset;if(void 0!==e&&(i.checkOffsetValue(e),s=e),s<0||s+t>this.length)throw new Error(i.ERRORS.INVALID_READ_BEYOND_BOUNDS)}ensureInsertable(t,e){i.checkOffsetValue(e),this._ensureCapacity(this.length+t),e<this.length&&this._buff.copy(this._buff,e+t,e,this._buff.length),e+t>this.length?this.length=e+t:this.length+=t}_ensureWriteable(t,e){const s="number"==typeof e?e:this._writeOffset;this._ensureCapacity(s+t),s+t>this.length&&(this.length=s+t)}_ensureCapacity(t){const e=this._buff.length;if(t>e){let s=this._buff,i=3*e/2+1;i<t&&(i=t),this._buff=Buffer.allocUnsafe(i),s.copy(this._buff,0,0,e)}}_readNumberValue(t,e,s){this.ensureReadable(e,s);const i=t.call(this._buff,"number"==typeof s?s:this._readOffset);return void 0===s&&(this._readOffset+=e),i}_insertNumberValue(t,e,s,r){return i.checkOffsetValue(r),this.ensureInsertable(e,r),t.call(this._buff,s,r),this._writeOffset+=e,this}_writeNumberValue(t,e,s,r){if("number"==typeof r){if(r<0)throw new Error(i.ERRORS.INVALID_WRITE_BEYOND_BOUNDS);i.checkOffsetValue(r)}const n="number"==typeof r?r:this._writeOffset;return this._ensureWriteable(e,n),t.call(this._buff,s,n),"number"==typeof r?this._writeOffset=Math.max(this._writeOffset,n+e):this._writeOffset+=e,this}}e.SmartBuffer=r},6742:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});const i=s(181),r={INVALID_ENCODING:"Invalid encoding provided. Please specify a valid encoding the internal Node.js Buffer supports.",INVALID_SMARTBUFFER_SIZE:"Invalid size provided. Size must be a valid integer greater than zero.",INVALID_SMARTBUFFER_BUFFER:"Invalid Buffer provided in SmartBufferOptions.",INVALID_SMARTBUFFER_OBJECT:"Invalid SmartBufferOptions object supplied to SmartBuffer constructor or factory methods.",INVALID_OFFSET:"An invalid offset value was provided.",INVALID_OFFSET_NON_NUMBER:"An invalid offset value was provided. A numeric value is required.",INVALID_LENGTH:"An invalid length value was provided.",INVALID_LENGTH_NON_NUMBER:"An invalid length value was provived. A numeric value is required.",INVALID_TARGET_OFFSET:"Target offset is beyond the bounds of the internal SmartBuffer data.",INVALID_TARGET_LENGTH:"Specified length value moves cursor beyong the bounds of the internal SmartBuffer data.",INVALID_READ_BEYOND_BOUNDS:"Attempted to read beyond the bounds of the managed data.",INVALID_WRITE_BEYOND_BOUNDS:"Attempted to write beyond the bounds of the managed data."};function n(t){return"number"==typeof t&&isFinite(t)&&function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t}(t)}function o(t,e){if("number"!=typeof t)throw new Error(e?r.INVALID_OFFSET_NON_NUMBER:r.INVALID_LENGTH_NON_NUMBER);if(!n(t)||t<0)throw new Error(e?r.INVALID_OFFSET:r.INVALID_LENGTH)}e.ERRORS=r,e.checkEncoding=function(t){if(!i.Buffer.isEncoding(t))throw new Error(r.INVALID_ENCODING)},e.isFiniteInteger=n,e.checkLengthValue=function(t){o(t,!1)},e.checkOffsetValue=function(t){o(t,!0)},e.checkTargetOffset=function(t,e){if(t<0||t>e.length)throw new Error(r.INVALID_TARGET_OFFSET)},e.bigIntAndBufferInt64Check=function(t){if("undefined"==typeof BigInt)throw new Error("Platform does not support JS BigInt type.");if(void 0===i.Buffer.prototype[t])throw new Error(`Platform does not support Buffer.prototype.${t}.`)}},8148:function(t,e,s){"use strict";var i=this&&this.__awaiter||function(t,e,s,i){return new(s||(s=Promise))((function(r,n){function o(t){try{h(i.next(t))}catch(t){n(t)}}function a(t){try{h(i.throw(t))}catch(t){n(t)}}function h(t){var e;t.done?r(t.value):(e=t.value,e instanceof s?e:new s((function(t){t(e)}))).then(o,a)}h((i=i.apply(t,e||[])).next())}))};Object.defineProperty(e,"__esModule",{value:!0}),e.SocksClientError=e.SocksClient=void 0;const r=s(4434),n=s(9278),o=s(5092),a=s(1737),h=s(1761),c=s(6871),l=s(7606);Object.defineProperty(e,"SocksClientError",{enumerable:!0,get:function(){return l.SocksClientError}});const u=s(1039);class d extends r.EventEmitter{constructor(t){super(),this.options=Object.assign({},t),(0,h.validateSocksClientOptions)(t),this.setState(a.SocksClientState.Created)}static createConnection(t,e){return new Promise(((s,i)=>{try{(0,h.validateSocksClientOptions)(t,["connect"])}catch(t){return"function"==typeof e?(e(t),s(t)):i(t)}const r=new d(t);r.connect(t.existing_socket),r.once("established",(t=>{r.removeAllListeners(),"function"==typeof e?(e(null,t),s(t)):s(t)})),r.once("error",(t=>{r.removeAllListeners(),"function"==typeof e?(e(t),s(t)):i(t)}))}))}static createConnectionChain(t,e){return new Promise(((s,r)=>i(this,void 0,void 0,(function*(){try{(0,h.validateSocksClientChainOptions)(t)}catch(t){return"function"==typeof e?(e(t),s(t)):r(t)}t.randomizeChain&&(0,l.shuffleArray)(t.proxies);try{let i;for(let e=0;e<t.proxies.length;e++){const s=t.proxies[e],r=e===t.proxies.length-1?t.destination:{host:t.proxies[e+1].host||t.proxies[e+1].ipaddress,port:t.proxies[e+1].port},n=yield d.createConnection({command:"connect",proxy:s,destination:r,existing_socket:i});i=i||n.socket}"function"==typeof e?(e(null,{socket:i}),s({socket:i})):s({socket:i})}catch(t){"function"==typeof e?(e(t),s(t)):r(t)}}))))}static createUDPFrame(t){const e=new o.SmartBuffer;return e.writeUInt16BE(0),e.writeUInt8(t.frameNumber||0),n.isIPv4(t.remoteHost.host)?(e.writeUInt8(a.Socks5HostType.IPv4),e.writeUInt32BE((0,h.ipv4ToInt32)(t.remoteHost.host))):n.isIPv6(t.remoteHost.host)?(e.writeUInt8(a.Socks5HostType.IPv6),e.writeBuffer((0,h.ipToBuffer)(t.remoteHost.host))):(e.writeUInt8(a.Socks5HostType.Hostname),e.writeUInt8(Buffer.byteLength(t.remoteHost.host)),e.writeString(t.remoteHost.host)),e.writeUInt16BE(t.remoteHost.port),e.writeBuffer(t.data),e.toBuffer()}static parseUDPFrame(t){const e=o.SmartBuffer.fromBuffer(t);e.readOffset=2;const s=e.readUInt8(),i=e.readUInt8();let r;return r=i===a.Socks5HostType.IPv4?(0,h.int32ToIpv4)(e.readUInt32BE()):i===a.Socks5HostType.IPv6?u.Address6.fromByteArray(Array.from(e.readBuffer(16))).canonicalForm():e.readString(e.readUInt8()),{frameNumber:s,remoteHost:{host:r,port:e.readUInt16BE()},data:e.readBuffer()}}setState(t){this.state!==a.SocksClientState.Error&&(this.state=t)}connect(t){this.onDataReceived=t=>this.onDataReceivedHandler(t),this.onClose=()=>this.onCloseHandler(),this.onError=t=>this.onErrorHandler(t),this.onConnect=()=>this.onConnectHandler();const e=setTimeout((()=>this.onEstablishedTimeout()),this.options.timeout||a.DEFAULT_TIMEOUT);e.unref&&"function"==typeof e.unref&&e.unref(),this.socket=t||new n.Socket,this.socket.once("close",this.onClose),this.socket.once("error",this.onError),this.socket.once("connect",this.onConnect),this.socket.on("data",this.onDataReceived),this.setState(a.SocksClientState.Connecting),this.receiveBuffer=new c.ReceiveBuffer,t?this.socket.emit("connect"):(this.socket.connect(this.getSocketOptions()),void 0!==this.options.set_tcp_nodelay&&null!==this.options.set_tcp_nodelay&&this.socket.setNoDelay(!!this.options.set_tcp_nodelay)),this.prependOnceListener("established",(t=>{setImmediate((()=>{if(this.receiveBuffer.length>0){const e=this.receiveBuffer.get(this.receiveBuffer.length);t.socket.emit("data",e)}t.socket.resume()}))}))}getSocketOptions(){return Object.assign(Object.assign({},this.options.socket_options),{host:this.options.proxy.host||this.options.proxy.ipaddress,port:this.options.proxy.port})}onEstablishedTimeout(){this.state!==a.SocksClientState.Established&&this.state!==a.SocksClientState.BoundWaitingForConnection&&this.closeSocket(a.ERRORS.ProxyConnectionTimedOut)}onConnectHandler(){this.setState(a.SocksClientState.Connected),4===this.options.proxy.type?this.sendSocks4InitialHandshake():this.sendSocks5InitialHandshake(),this.setState(a.SocksClientState.SentInitialHandshake)}onDataReceivedHandler(t){this.receiveBuffer.append(t),this.processData()}processData(){for(;this.state!==a.SocksClientState.Established&&this.state!==a.SocksClientState.Error&&this.receiveBuffer.length>=this.nextRequiredPacketBufferSize;)if(this.state===a.SocksClientState.SentInitialHandshake)4===this.options.proxy.type?this.handleSocks4FinalHandshakeResponse():this.handleInitialSocks5HandshakeResponse();else if(this.state===a.SocksClientState.SentAuthentication)this.handleInitialSocks5AuthenticationHandshakeResponse();else if(this.state===a.SocksClientState.SentFinalHandshake)this.handleSocks5FinalHandshakeResponse();else{if(this.state!==a.SocksClientState.BoundWaitingForConnection){this.closeSocket(a.ERRORS.InternalError);break}4===this.options.proxy.type?this.handleSocks4IncomingConnectionResponse():this.handleSocks5IncomingConnectionResponse()}}onCloseHandler(){this.closeSocket(a.ERRORS.SocketClosed)}onErrorHandler(t){this.closeSocket(t.message)}removeInternalSocketHandlers(){this.socket.pause(),this.socket.removeListener("data",this.onDataReceived),this.socket.removeListener("close",this.onClose),this.socket.removeListener("error",this.onError),this.socket.removeListener("connect",this.onConnect)}closeSocket(t){this.state!==a.SocksClientState.Error&&(this.setState(a.SocksClientState.Error),this.socket.destroy(),this.removeInternalSocketHandlers(),this.emit("error",new l.SocksClientError(t,this.options)))}sendSocks4InitialHandshake(){const t=this.options.proxy.userId||"",e=new o.SmartBuffer;e.writeUInt8(4),e.writeUInt8(a.SocksCommand[this.options.command]),e.writeUInt16BE(this.options.destination.port),n.isIPv4(this.options.destination.host)?(e.writeBuffer((0,h.ipToBuffer)(this.options.destination.host)),e.writeStringNT(t)):(e.writeUInt8(0),e.writeUInt8(0),e.writeUInt8(0),e.writeUInt8(1),e.writeStringNT(t),e.writeStringNT(this.options.destination.host)),this.nextRequiredPacketBufferSize=a.SOCKS_INCOMING_PACKET_SIZES.Socks4Response,this.socket.write(e.toBuffer())}handleSocks4FinalHandshakeResponse(){const t=this.receiveBuffer.get(8);if(t[1]!==a.Socks4Response.Granted)this.closeSocket(`${a.ERRORS.Socks4ProxyRejectedConnection} - (${a.Socks4Response[t[1]]})`);else if(a.SocksCommand[this.options.command]===a.SocksCommand.bind){const e=o.SmartBuffer.fromBuffer(t);e.readOffset=2;const s={port:e.readUInt16BE(),host:(0,h.int32ToIpv4)(e.readUInt32BE())};"0.0.0.0"===s.host&&(s.host=this.options.proxy.ipaddress),this.setState(a.SocksClientState.BoundWaitingForConnection),this.emit("bound",{remoteHost:s,socket:this.socket})}else this.setState(a.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{socket:this.socket})}handleSocks4IncomingConnectionResponse(){const t=this.receiveBuffer.get(8);if(t[1]!==a.Socks4Response.Granted)this.closeSocket(`${a.ERRORS.Socks4ProxyRejectedIncomingBoundConnection} - (${a.Socks4Response[t[1]]})`);else{const e=o.SmartBuffer.fromBuffer(t);e.readOffset=2;const s={port:e.readUInt16BE(),host:(0,h.int32ToIpv4)(e.readUInt32BE())};this.setState(a.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:s,socket:this.socket})}}sendSocks5InitialHandshake(){const t=new o.SmartBuffer,e=[a.Socks5Auth.NoAuth];(this.options.proxy.userId||this.options.proxy.password)&&e.push(a.Socks5Auth.UserPass),void 0!==this.options.proxy.custom_auth_method&&e.push(this.options.proxy.custom_auth_method),t.writeUInt8(5),t.writeUInt8(e.length);for(const s of e)t.writeUInt8(s);this.nextRequiredPacketBufferSize=a.SOCKS_INCOMING_PACKET_SIZES.Socks5InitialHandshakeResponse,this.socket.write(t.toBuffer()),this.setState(a.SocksClientState.SentInitialHandshake)}handleInitialSocks5HandshakeResponse(){const t=this.receiveBuffer.get(2);5!==t[0]?this.closeSocket(a.ERRORS.InvalidSocks5IntiailHandshakeSocksVersion):t[1]===a.SOCKS5_NO_ACCEPTABLE_AUTH?this.closeSocket(a.ERRORS.InvalidSocks5InitialHandshakeNoAcceptedAuthType):t[1]===a.Socks5Auth.NoAuth?(this.socks5ChosenAuthType=a.Socks5Auth.NoAuth,this.sendSocks5CommandRequest()):t[1]===a.Socks5Auth.UserPass?(this.socks5ChosenAuthType=a.Socks5Auth.UserPass,this.sendSocks5UserPassAuthentication()):t[1]===this.options.proxy.custom_auth_method?(this.socks5ChosenAuthType=this.options.proxy.custom_auth_method,this.sendSocks5CustomAuthentication()):this.closeSocket(a.ERRORS.InvalidSocks5InitialHandshakeUnknownAuthType)}sendSocks5UserPassAuthentication(){const t=this.options.proxy.userId||"",e=this.options.proxy.password||"",s=new o.SmartBuffer;s.writeUInt8(1),s.writeUInt8(Buffer.byteLength(t)),s.writeString(t),s.writeUInt8(Buffer.byteLength(e)),s.writeString(e),this.nextRequiredPacketBufferSize=a.SOCKS_INCOMING_PACKET_SIZES.Socks5UserPassAuthenticationResponse,this.socket.write(s.toBuffer()),this.setState(a.SocksClientState.SentAuthentication)}sendSocks5CustomAuthentication(){return i(this,void 0,void 0,(function*(){this.nextRequiredPacketBufferSize=this.options.proxy.custom_auth_response_size,this.socket.write(yield this.options.proxy.custom_auth_request_handler()),this.setState(a.SocksClientState.SentAuthentication)}))}handleSocks5CustomAuthHandshakeResponse(t){return i(this,void 0,void 0,(function*(){return yield this.options.proxy.custom_auth_response_handler(t)}))}handleSocks5AuthenticationNoAuthHandshakeResponse(t){return i(this,void 0,void 0,(function*(){return 0===t[1]}))}handleSocks5AuthenticationUserPassHandshakeResponse(t){return i(this,void 0,void 0,(function*(){return 0===t[1]}))}handleInitialSocks5AuthenticationHandshakeResponse(){return i(this,void 0,void 0,(function*(){this.setState(a.SocksClientState.ReceivedAuthenticationResponse);let t=!1;this.socks5ChosenAuthType===a.Socks5Auth.NoAuth?t=yield this.handleSocks5AuthenticationNoAuthHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===a.Socks5Auth.UserPass?t=yield this.handleSocks5AuthenticationUserPassHandshakeResponse(this.receiveBuffer.get(2)):this.socks5ChosenAuthType===this.options.proxy.custom_auth_method&&(t=yield this.handleSocks5CustomAuthHandshakeResponse(this.receiveBuffer.get(this.options.proxy.custom_auth_response_size))),t?this.sendSocks5CommandRequest():this.closeSocket(a.ERRORS.Socks5AuthenticationFailed)}))}sendSocks5CommandRequest(){const t=new o.SmartBuffer;t.writeUInt8(5),t.writeUInt8(a.SocksCommand[this.options.command]),t.writeUInt8(0),n.isIPv4(this.options.destination.host)?(t.writeUInt8(a.Socks5HostType.IPv4),t.writeBuffer((0,h.ipToBuffer)(this.options.destination.host))):n.isIPv6(this.options.destination.host)?(t.writeUInt8(a.Socks5HostType.IPv6),t.writeBuffer((0,h.ipToBuffer)(this.options.destination.host))):(t.writeUInt8(a.Socks5HostType.Hostname),t.writeUInt8(this.options.destination.host.length),t.writeString(this.options.destination.host)),t.writeUInt16BE(this.options.destination.port),this.nextRequiredPacketBufferSize=a.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.socket.write(t.toBuffer()),this.setState(a.SocksClientState.SentFinalHandshake)}handleSocks5FinalHandshakeResponse(){const t=this.receiveBuffer.peek(5);if(5!==t[0]||t[1]!==a.Socks5Response.Granted)this.closeSocket(`${a.ERRORS.InvalidSocks5FinalHandshakeRejected} - ${a.Socks5Response[t[1]]}`);else{const e=t[3];let s,i;if(e===a.Socks5HostType.IPv4){const t=a.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<t)return void(this.nextRequiredPacketBufferSize=t);i=o.SmartBuffer.fromBuffer(this.receiveBuffer.get(t).slice(4)),s={host:(0,h.int32ToIpv4)(i.readUInt32BE()),port:i.readUInt16BE()},"0.0.0.0"===s.host&&(s.host=this.options.proxy.ipaddress)}else if(e===a.Socks5HostType.Hostname){const e=t[4],r=a.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(e);if(this.receiveBuffer.length<r)return void(this.nextRequiredPacketBufferSize=r);i=o.SmartBuffer.fromBuffer(this.receiveBuffer.get(r).slice(5)),s={host:i.readString(e),port:i.readUInt16BE()}}else if(e===a.Socks5HostType.IPv6){const t=a.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<t)return void(this.nextRequiredPacketBufferSize=t);i=o.SmartBuffer.fromBuffer(this.receiveBuffer.get(t).slice(4)),s={host:u.Address6.fromByteArray(Array.from(i.readBuffer(16))).canonicalForm(),port:i.readUInt16BE()}}this.setState(a.SocksClientState.ReceivedFinalResponse),a.SocksCommand[this.options.command]===a.SocksCommand.connect?(this.setState(a.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:s,socket:this.socket})):a.SocksCommand[this.options.command]===a.SocksCommand.bind?(this.setState(a.SocksClientState.BoundWaitingForConnection),this.nextRequiredPacketBufferSize=a.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHeader,this.emit("bound",{remoteHost:s,socket:this.socket})):a.SocksCommand[this.options.command]===a.SocksCommand.associate&&(this.setState(a.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:s,socket:this.socket}))}}handleSocks5IncomingConnectionResponse(){const t=this.receiveBuffer.peek(5);if(5!==t[0]||t[1]!==a.Socks5Response.Granted)this.closeSocket(`${a.ERRORS.Socks5ProxyRejectedIncomingBoundConnection} - ${a.Socks5Response[t[1]]}`);else{const e=t[3];let s,i;if(e===a.Socks5HostType.IPv4){const t=a.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv4;if(this.receiveBuffer.length<t)return void(this.nextRequiredPacketBufferSize=t);i=o.SmartBuffer.fromBuffer(this.receiveBuffer.get(t).slice(4)),s={host:(0,h.int32ToIpv4)(i.readUInt32BE()),port:i.readUInt16BE()},"0.0.0.0"===s.host&&(s.host=this.options.proxy.ipaddress)}else if(e===a.Socks5HostType.Hostname){const e=t[4],r=a.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseHostname(e);if(this.receiveBuffer.length<r)return void(this.nextRequiredPacketBufferSize=r);i=o.SmartBuffer.fromBuffer(this.receiveBuffer.get(r).slice(5)),s={host:i.readString(e),port:i.readUInt16BE()}}else if(e===a.Socks5HostType.IPv6){const t=a.SOCKS_INCOMING_PACKET_SIZES.Socks5ResponseIPv6;if(this.receiveBuffer.length<t)return void(this.nextRequiredPacketBufferSize=t);i=o.SmartBuffer.fromBuffer(this.receiveBuffer.get(t).slice(4)),s={host:u.Address6.fromByteArray(Array.from(i.readBuffer(16))).canonicalForm(),port:i.readUInt16BE()}}this.setState(a.SocksClientState.Established),this.removeInternalSocketHandlers(),this.emit("established",{remoteHost:s,socket:this.socket})}}get socksClientOptions(){return Object.assign({},this.options)}}e.SocksClient=d},1737:(t,e)=>{"use strict";var s,i,r,n,o,a;Object.defineProperty(e,"__esModule",{value:!0}),e.SOCKS5_NO_ACCEPTABLE_AUTH=e.SOCKS5_CUSTOM_AUTH_END=e.SOCKS5_CUSTOM_AUTH_START=e.SOCKS_INCOMING_PACKET_SIZES=e.SocksClientState=e.Socks5Response=e.Socks5HostType=e.Socks5Auth=e.Socks4Response=e.SocksCommand=e.ERRORS=e.DEFAULT_TIMEOUT=void 0,e.DEFAULT_TIMEOUT=3e4,e.ERRORS={InvalidSocksCommand:"An invalid SOCKS command was provided. Valid options are connect, bind, and associate.",InvalidSocksCommandForOperation:"An invalid SOCKS command was provided. Only a subset of commands are supported for this operation.",InvalidSocksCommandChain:"An invalid SOCKS command was provided. Chaining currently only supports the connect command.",InvalidSocksClientOptionsDestination:"An invalid destination host was provided.",InvalidSocksClientOptionsExistingSocket:"An invalid existing socket was provided. This should be an instance of stream.Duplex.",InvalidSocksClientOptionsProxy:"Invalid SOCKS proxy details were provided.",InvalidSocksClientOptionsTimeout:"An invalid timeout value was provided. Please enter a value above 0 (in ms).",InvalidSocksClientOptionsProxiesLength:"At least two socks proxies must be provided for chaining.",InvalidSocksClientOptionsCustomAuthRange:"Custom auth must be a value between 0x80 and 0xFE.",InvalidSocksClientOptionsCustomAuthOptions:"When a custom_auth_method is provided, custom_auth_request_handler, custom_auth_response_size, and custom_auth_response_handler must also be provided and valid.",NegotiationError:"Negotiation error",SocketClosed:"Socket closed",ProxyConnectionTimedOut:"Proxy connection timed out",InternalError:"SocksClient internal error (this should not happen)",InvalidSocks4HandshakeResponse:"Received invalid Socks4 handshake response",Socks4ProxyRejectedConnection:"Socks4 Proxy rejected connection",InvalidSocks4IncomingConnectionResponse:"Socks4 invalid incoming connection response",Socks4ProxyRejectedIncomingBoundConnection:"Socks4 Proxy rejected incoming bound connection",InvalidSocks5InitialHandshakeResponse:"Received invalid Socks5 initial handshake response",InvalidSocks5IntiailHandshakeSocksVersion:"Received invalid Socks5 initial handshake (invalid socks version)",InvalidSocks5InitialHandshakeNoAcceptedAuthType:"Received invalid Socks5 initial handshake (no accepted authentication type)",InvalidSocks5InitialHandshakeUnknownAuthType:"Received invalid Socks5 initial handshake (unknown authentication type)",Socks5AuthenticationFailed:"Socks5 Authentication failed",InvalidSocks5FinalHandshake:"Received invalid Socks5 final handshake response",InvalidSocks5FinalHandshakeRejected:"Socks5 proxy rejected connection",InvalidSocks5IncomingConnectionResponse:"Received invalid Socks5 incoming connection response",Socks5ProxyRejectedIncomingBoundConnection:"Socks5 Proxy rejected incoming bound connection"},e.SOCKS_INCOMING_PACKET_SIZES={Socks5InitialHandshakeResponse:2,Socks5UserPassAuthenticationResponse:2,Socks5ResponseHeader:5,Socks5ResponseIPv4:10,Socks5ResponseIPv6:22,Socks5ResponseHostname:t=>t+7,Socks4Response:8},function(t){t[t.connect=1]="connect",t[t.bind=2]="bind",t[t.associate=3]="associate"}(s||(e.SocksCommand=s={})),function(t){t[t.Granted=90]="Granted",t[t.Failed=91]="Failed",t[t.Rejected=92]="Rejected",t[t.RejectedIdent=93]="RejectedIdent"}(i||(e.Socks4Response=i={})),function(t){t[t.NoAuth=0]="NoAuth",t[t.GSSApi=1]="GSSApi",t[t.UserPass=2]="UserPass"}(r||(e.Socks5Auth=r={})),e.SOCKS5_CUSTOM_AUTH_START=128,e.SOCKS5_CUSTOM_AUTH_END=254,e.SOCKS5_NO_ACCEPTABLE_AUTH=255,function(t){t[t.Granted=0]="Granted",t[t.Failure=1]="Failure",t[t.NotAllowed=2]="NotAllowed",t[t.NetworkUnreachable=3]="NetworkUnreachable",t[t.HostUnreachable=4]="HostUnreachable",t[t.ConnectionRefused=5]="ConnectionRefused",t[t.TTLExpired=6]="TTLExpired",t[t.CommandNotSupported=7]="CommandNotSupported",t[t.AddressNotSupported=8]="AddressNotSupported"}(n||(e.Socks5Response=n={})),function(t){t[t.IPv4=1]="IPv4",t[t.Hostname=3]="Hostname",t[t.IPv6=4]="IPv6"}(o||(e.Socks5HostType=o={})),function(t){t[t.Created=0]="Created",t[t.Connecting=1]="Connecting",t[t.Connected=2]="Connected",t[t.SentInitialHandshake=3]="SentInitialHandshake",t[t.ReceivedInitialHandshakeResponse=4]="ReceivedInitialHandshakeResponse",t[t.SentAuthentication=5]="SentAuthentication",t[t.ReceivedAuthenticationResponse=6]="ReceivedAuthenticationResponse",t[t.SentFinalHandshake=7]="SentFinalHandshake",t[t.ReceivedFinalResponse=8]="ReceivedFinalResponse",t[t.BoundWaitingForConnection=9]="BoundWaitingForConnection",t[t.Established=10]="Established",t[t.Disconnected=11]="Disconnected",t[t.Error=99]="Error"}(a||(e.SocksClientState=a={}))},1761:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ipToBuffer=e.int32ToIpv4=e.ipv4ToInt32=e.validateSocksClientChainOptions=e.validateSocksClientOptions=void 0;const i=s(7606),r=s(1737),n=s(2203),o=s(1039),a=s(9278);function h(t,e){if(void 0!==t.custom_auth_method){if(t.custom_auth_method<r.SOCKS5_CUSTOM_AUTH_START||t.custom_auth_method>r.SOCKS5_CUSTOM_AUTH_END)throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsCustomAuthRange,e);if(void 0===t.custom_auth_request_handler||"function"!=typeof t.custom_auth_request_handler)throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(void 0===t.custom_auth_response_size)throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e);if(void 0===t.custom_auth_response_handler||"function"!=typeof t.custom_auth_response_handler)throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsCustomAuthOptions,e)}}function c(t){return t&&"string"==typeof t.host&&"number"==typeof t.port&&t.port>=0&&t.port<=65535}function l(t){return t&&("string"==typeof t.host||"string"==typeof t.ipaddress)&&"number"==typeof t.port&&t.port>=0&&t.port<=65535&&(4===t.type||5===t.type)}function u(t){return"number"==typeof t&&t>0}e.validateSocksClientOptions=function(t,e=["connect","bind","associate"]){if(!r.SocksCommand[t.command])throw new i.SocksClientError(r.ERRORS.InvalidSocksCommand,t);if(-1===e.indexOf(t.command))throw new i.SocksClientError(r.ERRORS.InvalidSocksCommandForOperation,t);if(!c(t.destination))throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsDestination,t);if(!l(t.proxy))throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsProxy,t);if(h(t.proxy,t),t.timeout&&!u(t.timeout))throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsTimeout,t);if(t.existing_socket&&!(t.existing_socket instanceof n.Duplex))throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsExistingSocket,t)},e.validateSocksClientChainOptions=function(t){if("connect"!==t.command)throw new i.SocksClientError(r.ERRORS.InvalidSocksCommandChain,t);if(!c(t.destination))throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsDestination,t);if(!(t.proxies&&Array.isArray(t.proxies)&&t.proxies.length>=2))throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsProxiesLength,t);if(t.proxies.forEach((e=>{if(!l(e))throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsProxy,t);h(e,t)})),t.timeout&&!u(t.timeout))throw new i.SocksClientError(r.ERRORS.InvalidSocksClientOptionsTimeout,t)},e.ipv4ToInt32=function(t){return new o.Address4(t).toArray().reduce(((t,e)=>(t<<8)+e),0)},e.int32ToIpv4=function(t){return[t>>>24&255,t>>>16&255,t>>>8&255,255&t].join(".")},e.ipToBuffer=function(t){if(a.isIPv4(t)){const e=new o.Address4(t);return Buffer.from(e.toArray())}if(a.isIPv6(t)){const e=new o.Address6(t);return Buffer.from(e.canonicalForm().split(":").map((t=>t.padStart(4,"0"))).join(""),"hex")}throw new Error("Invalid IP address format")}},6871:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.ReceiveBuffer=void 0,e.ReceiveBuffer=class{constructor(t=4096){this.buffer=Buffer.allocUnsafe(t),this.offset=0,this.originalSize=t}get length(){return this.offset}append(t){if(!Buffer.isBuffer(t))throw new Error("Attempted to append a non-buffer instance to ReceiveBuffer.");if(this.offset+t.length>=this.buffer.length){const e=this.buffer;this.buffer=Buffer.allocUnsafe(Math.max(this.buffer.length+this.originalSize,this.buffer.length+t.length)),e.copy(this.buffer)}return t.copy(this.buffer,this.offset),this.offset+=t.length}peek(t){if(t>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");return this.buffer.slice(0,t)}get(t){if(t>this.offset)throw new Error("Attempted to read beyond the bounds of the managed internal data.");const e=Buffer.allocUnsafe(t);return this.buffer.slice(0,t).copy(e),this.buffer.copyWithin(0,t,t+this.offset-t),this.offset-=t,e}}},7606:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.shuffleArray=e.SocksClientError=void 0;class s extends Error{constructor(t,e){super(t),this.options=e}}e.SocksClientError=s,e.shuffleArray=function(t){for(let e=t.length-1;e>0;e--){const s=Math.floor(Math.random()*(e+1));[t[e],t[s]]=[t[s],t[e]]}}},296:function(t,e,s){"use strict";var i=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),r=this&&this.__exportStar||function(t,e){for(var s in t)"default"===s||Object.prototype.hasOwnProperty.call(e,s)||i(e,t,s)};Object.defineProperty(e,"__esModule",{value:!0}),r(s(8148),e)},9471:(t,e,s)=>{var i;!function(){"use strict";var r={not_string:/[^s]/,not_bool:/[^t]/,not_type:/[^T]/,not_primitive:/[^v]/,number:/[diefg]/,numeric_arg:/[bcdiefguxX]/,json:/[j]/,not_json:/[^j]/,text:/^[^\x25]+/,modulo:/^\x25{2}/,placeholder:/^\x25(?:([1-9]\d*)\$|\(([^)]+)\))?(\+)?(0|'[^$])?(-)?(\d+)?(?:\.(\d+))?([b-gijostTuvxX])/,key:/^([a-z_][a-z_\d]*)/i,key_access:/^\.([a-z_][a-z_\d]*)/i,index_access:/^\[(\d+)\]/,sign:/^[+-]/};function n(t){return function(t,e){var s,i,o,a,h,c,l,u,d,f=1,p=t.length,m="";for(i=0;i<p;i++)if("string"==typeof t[i])m+=t[i];else if("object"==typeof t[i]){if((a=t[i]).keys)for(s=e[f],o=0;o<a.keys.length;o++){if(null==s)throw new Error(n('[sprintf] Cannot access property "%s" of undefined value "%s"',a.keys[o],a.keys[o-1]));s=s[a.keys[o]]}else s=a.param_no?e[a.param_no]:e[f++];if(r.not_type.test(a.type)&&r.not_primitive.test(a.type)&&s instanceof Function&&(s=s()),r.numeric_arg.test(a.type)&&"number"!=typeof s&&isNaN(s))throw new TypeError(n("[sprintf] expecting number but found %T",s));switch(r.number.test(a.type)&&(u=s>=0),a.type){case"b":s=parseInt(s,10).toString(2);break;case"c":s=String.fromCharCode(parseInt(s,10));break;case"d":case"i":s=parseInt(s,10);break;case"j":s=JSON.stringify(s,null,a.width?parseInt(a.width):0);break;case"e":s=a.precision?parseFloat(s).toExponential(a.precision):parseFloat(s).toExponential();break;case"f":s=a.precision?parseFloat(s).toFixed(a.precision):parseFloat(s);break;case"g":s=a.precision?String(Number(s.toPrecision(a.precision))):parseFloat(s);break;case"o":s=(parseInt(s,10)>>>0).toString(8);break;case"s":s=String(s),s=a.precision?s.substring(0,a.precision):s;break;case"t":s=String(!!s),s=a.precision?s.substring(0,a.precision):s;break;case"T":s=Object.prototype.toString.call(s).slice(8,-1).toLowerCase(),s=a.precision?s.substring(0,a.precision):s;break;case"u":s=parseInt(s,10)>>>0;break;case"v":s=s.valueOf(),s=a.precision?s.substring(0,a.precision):s;break;case"x":s=(parseInt(s,10)>>>0).toString(16);break;case"X":s=(parseInt(s,10)>>>0).toString(16).toUpperCase()}r.json.test(a.type)?m+=s:(!r.number.test(a.type)||u&&!a.sign?d="":(d=u?"+":"-",s=s.toString().replace(r.sign,"")),c=a.pad_char?"0"===a.pad_char?"0":a.pad_char.charAt(1):" ",l=a.width-(d+s).length,h=a.width&&l>0?c.repeat(l):"",m+=a.align?d+s+h:"0"===c?d+h+s:h+d+s)}return m}(function(t){if(a[t])return a[t];for(var e,s=t,i=[],n=0;s;){if(null!==(e=r.text.exec(s)))i.push(e[0]);else if(null!==(e=r.modulo.exec(s)))i.push("%");else{if(null===(e=r.placeholder.exec(s)))throw new SyntaxError("[sprintf] unexpected placeholder");if(e[2]){n|=1;var o=[],h=e[2],c=[];if(null===(c=r.key.exec(h)))throw new SyntaxError("[sprintf] failed to parse named argument key");for(o.push(c[1]);""!==(h=h.substring(c[0].length));)if(null!==(c=r.key_access.exec(h)))o.push(c[1]);else{if(null===(c=r.index_access.exec(h)))throw new SyntaxError("[sprintf] failed to parse named argument key");o.push(c[1])}e[2]=o}else n|=2;if(3===n)throw new Error("[sprintf] mixing positional and named placeholders is not (yet) supported");i.push({placeholder:e[0],param_no:e[1],keys:e[2],sign:e[3],pad_char:e[4],align:e[5],width:e[6],precision:e[7],type:e[8]})}s=s.substring(e[0].length)}return a[t]=i}(t),arguments)}function o(t,e){return n.apply(null,[t].concat(e||[]))}var a=Object.create(null);e.sprintf=n,e.vsprintf=o,"undefined"!=typeof window&&(window.sprintf=n,window.vsprintf=o,void 0===(i=function(){return{sprintf:n,vsprintf:o}}.call(e,s,e,t))||(t.exports=i))}()},4459:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.promptOpenRemoteSSHWindow=async function(t){const e=await h.window.createQuickPick();e.title="Select SSH Host",e.placeholder="Enter [user@]hostname[:port] or select configured host.";const s=(await u.default.loadFromFS()).getAllConfiguredHosts().map((t=>({label:t}))),i={label:p,iconPath:new h.ThemeIcon("plus"),alwaysShow:!0};e.items=[...s,i],e.onDidChangeValue((t=>{e.items=t?[...s,{label:t,iconPath:new h.ThemeIcon("triangle-right")},i]:[...s,i]})),e.onDidAccept((()=>{const s=e.selectedItems[0].label.trim();s===p?g():m(new f.default(s).toString(),t),e.dispose()})),e.show()},e.openRemoteSSHWindow=m,e.openRemoteSSHLocationWindow=function(t,e,s){h.commands.executeCommand("vscode.openFolder",h.Uri.from({scheme:"vscode-remote",authority:(0,l.encodeRemoteAuthority)(t),path:e}),{forceNewWindow:!s})},e.addNewHost=g,e.openSSHConfigFile=async function(){const t=(0,u.getSSHConfigPath)();await(0,d.exists)(t)||await c.promises.appendFile(t,""),h.commands.executeCommand("vscode.open",h.Uri.file(t))};const h=o(s(1398)),c=o(s(9896)),l=s(5527),u=o(s(1968)),d=s(5456),f=a(s(5624)),p="Add new host...";function m(t,e){h.commands.executeCommand("vscode.newWindow",{remoteAuthority:(0,l.encodeRemoteAuthority)(t),reuseWindow:e})}async function g(){const t=(0,u.getSSHConfigPath)();await(0,d.exists)(t)||await c.promises.appendFile(t,""),await h.commands.executeCommand("vscode.open",h.Uri.file(t),{preview:!1});const e=h.window.activeTextEditor;if(!e||e.document.uri.fsPath!==t)return;const s=e.document,i=s.lineAt(s.lineCount-1);i.isEmptyOrWhitespace||await e.edit((t=>{t.insert(i.range.end,"\n")})),await e.insertSnippet(new h.SnippetString("# # Example Host (uncomment to use)\n# Host ${1:dev}\n# \tHostName ${2:dev.example.com}\n# \tUser ${3:john}\n$ \tPort ${4:22}"),new h.Position(s.lineCount,0))}},1619:(t,e,s)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.IterableMessageDone=e.IterableMessageWithNext=e.DeferredPromise=void 0;const i=s(1398);e.DeferredPromise=class{get isRejected(){return 1===this.outcome?.outcome}get isResolved(){return 0===this.outcome?.outcome}get isSettled(){return!!this.outcome}get value(){return 0===this.outcome?.outcome?this.outcome?.value:void 0}constructor(){this.p=new Promise(((t,e)=>{this.completeCallback=t,this.errorCallback=e}))}complete(t){return new Promise((e=>{this.completeCallback(t),this.outcome={outcome:0,value:t},e()}))}error(t){return new Promise((e=>{this.errorCallback(t),this.outcome={outcome:1,value:t},e()}))}cancel(){return this.error(new i.CancellationError)}},e.IterableMessageWithNext=class{constructor(t,e){this.done=!1,this.message=t,this.next=e}},e.IterableMessageDone=class{constructor(){this.message=null,this.next=null,this.done=!0}}},7721:(t,e)=>{"use strict";function s(t){for(;t.length;){const e=t.pop();e&&e.dispose()}}Object.defineProperty(e,"__esModule",{value:!0}),e.Disposable=void 0,e.disposeAll=s,e.Disposable=class{constructor(){this._isDisposed=!1,this._disposables=[]}dispose(){this._isDisposed||(this._isDisposed=!0,s(this._disposables))}_register(t){return this._isDisposed?t.dispose():this._disposables.push(t),t}get isDisposed(){return this._isDisposed}}},5456:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.exists=async function(t){try{return await a.promises.access(t),!0}catch{return!1}},e.untildify=function(t){return t.replace(/^~(?=$|\/|\\)/,h)},e.normalizeToSlash=function(t){return t.replace(/\\/g,"/")};const a=o(s(9896)),h=o(s(857)).homedir()},4947:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e});Object.defineProperty(e,"__esModule",{value:!0});const a=o(s(1398));function h(t,e,s=" "){return s.repeat(Math.max(0,e-t.length))+t}e.default=class{constructor(t){this.output=a.window.createOutputChannel(t)}data2String(t){return t instanceof Error?t.stack||t.message:!1===t.success&&t.message?t.message:t.toString()}trace(t,e){this.logLevel("Trace",t,e)}info(t,e){this.logLevel("Info",t,e)}error(t,e){this.logLevel("Error",t,e)}logLevel(t,e,s){this.output.appendLine(`[${t}\t- ${this.now()}] ${e}`),s&&this.output.appendLine(this.data2String(s))}now(){const t=new Date;return h(t.getUTCHours()+"",2,"0")+":"+h(t.getMinutes()+"",2,"0")+":"+h(t.getUTCSeconds()+"",2,"0")+"."+t.getMilliseconds()}show(){this.output.show()}dispose(){this.output.dispose()}}},706:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.terminateProcess=function(t,e){if("win32"===process.platform)try{const e={stdio:["pipe","pipe","ignore"]};a.execFileSync("taskkill",["/T","/F","/PID",t.pid.toString()],e)}catch(t){return{success:!1,error:t}}else if("darwin"===process.platform||"linux"===process.platform)try{const s=h.join(e,"scripts","terminateProcess.sh"),i=a.spawnSync(s,[t.pid.toString()]);if(i.error)return{success:!1,error:i.error}}catch(t){return{success:!1,error:t}}else t.kill("SIGKILL");return{success:!0}};const a=o(s(5317)),h=o(s(6928))},5076:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.MsgPackManagedMessagePassing=e.MsgPackRemoteFileSystem=e.MsgpackExecServer=void 0;const a=o(s(1398)),h=o(s(304)),c=o(s(9278)),l=s(1619);class u{constructor(t,e,s,i){this.attempt=0,this.client=t,this.fs=new d(t),this.logger=e,this.token=s,this.remoteAuthority=i}static fromLocalPort(t,e,s,i,r=5e3){const n=new c.Socket;n.setTimeout(r),n.connect(t,"localhost",(()=>{s.trace(`Connected to localhost:${t}`)}));const o=h.MsgPackRpcClient.fromSocket(n,s,e);return new u(o,s,e,i)}static fromSpawnedCLI(t,e,s){const i=h.MsgPackRpcClient.fromSpawnedCLI(t,e);return new u(i,e,void 0,s)}spawn(t,e,s){return this.client.onAuth.then((()=>this.client.spawn(t,e,s)))}spawnRemoteServerConnector(t,e,s){throw new Error("Not implemented")}downloadCliExecutable(t,e,s,i){throw new Error("Not implemented")}env(){return this.client.onAuth.then((()=>this.client.getEnv()))}kill(t){return this.client.onAuth.then((()=>this.client.sysKill(t)))}tcpConnect(t,e){return this.client.onAuth.then((()=>this.client.netConnect(t,e)))}dispose(){this.client.dispose()}}e.MsgpackExecServer=u;class d{constructor(t){this.client=t}stat(t){return this.client.onAuth.then((()=>this.client.fsStat(t)))}mkdirp(t){return this.client.onAuth.then((()=>this.client.fsMkDirp(t)))}rm(t){return this.client.onAuth.then((()=>this.client.fsRm(t)))}read(t){return this.client.onAuth.then((()=>this.client.fsRead(t)))}write(t){return this.client.onAuth.then((()=>this.client.fsWrite(t)))}connect(t){return this.client.onAuth.then((()=>this.client.fsConnect(t)))}rename(t,e){return this.client.onAuth.then((()=>this.client.fsRename(t,e)))}readdir(t){return this.client.onAuth.then((()=>this.client.fsReadDir(t)))}}e.MsgPackRemoteFileSystem=d,e.MsgPackManagedMessagePassing=class{constructor(t,e,s){this.socketId=t,this.client=e,this.logger=s,this.onEndDeferred=new l.DeferredPromise,this.onCloseDeferred=new l.DeferredPromise,this.onEnd=this.onEndDeferred.p,this.onClose=this.onCloseDeferred.p,this.onDidReceiveMessageEmitter=new a.EventEmitter,this.onDidReceiveMessage=this.onDidReceiveMessageEmitter.event,this.client.onServerMsg((t=>{t.i===this.socketId&&this.onDidReceiveMessageEmitter.fire(t.body)}),this),this.logger.info("Server message event listener created."),this.client.onServerClose((()=>{this.onCloseDeferred.complete()}),this)}send(t){let e;this.logger.info(`Sending message to socket ${this.socketId}`),e=t instanceof ArrayBuffer?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):new Uint8Array(t),this.client.writeServerMsg(this.socketId,e)}end(){this.logger.info(`Closing socket ${this.socketId}`),this.onEndDeferred.complete(),this.client.close()}}},8922:(t,e)=>{"use strict";var s;Object.defineProperty(e,"__esModule",{value:!0}),e.RpcGetEnvResponse=e.RpcSysKillResponse=e.RpcFsReadDirResponse=e.RpcFsStatResponse=e.RpcFsFileKind=e.RpcServerMessageNotification=e.RpcStreamDataNotification=e.RpcStreamEndedNotification=e.RpcServerLogNotification=e.RpcServerClosedNotification=e.RpcStreamsStartedNotification=e.RpcErrorResponse=e.RpcSuccessResponse=e.RpcChallengeIssueResponse=e.RpcSpawnResult=void 0,e.RpcSpawnResult=class{constructor(t,e){this.message=t,this.exit_code=e}static isValid(t){return null!==t&&"object"==typeof t&&"message"in t&&null!==t.message&&"string"==typeof t.message&&"exit_code"in t&&null!==t.exit_code&&"number"==typeof t.exit_code}},e.RpcChallengeIssueResponse=class{constructor(t){this.challenge=t}static isValid(t){return null!==t&&"object"==typeof t&&"challenge"in t&&null!==t.challenge&&"string"==typeof t.challenge}},e.RpcSuccessResponse=class{constructor(t,e){this.id=t,this.result=e}static isValid(t){return null!==t&&"object"==typeof t&&"id"in t&&null!==t.id&&"number"==typeof t.id&&"result"in t&&null!==t.result&&"object"==typeof t.result}},e.RpcErrorResponse=class{constructor(t,e){this.id=t,this.error=e}static isValid(t){return null!==t&&"object"==typeof t&&"id"in t&&null!==t.id&&"number"==typeof t.id&&"error"in t&&null!==t.error&&"object"==typeof t.error&&"code"in t.error&&null!==t.error.code&&"number"==typeof t.error.code&&"message"in t.error&&null!==t.error.message&&"string"==typeof t.error.message}},e.RpcStreamsStartedNotification=class{constructor(t,e){this.id=null,this.method="streams_started",this.params={for_request_id:t,stream_ids:e}}static isValid(t){return null!==t&&"object"==typeof t&&"method"in t&&"streams_started"===t.method&&"params"in t&&null!==t.params&&"object"==typeof t.params&&"stream_ids"in t.params&&Array.isArray(t.params.stream_ids)&&"for_request_id"in t.params&&"number"==typeof t.params.for_request_id}},e.RpcServerClosedNotification=class{constructor(t){this.id=null,this.method="serverclose",this.params={i:t}}static isValid(t){return null!==t&&"object"==typeof t&&"method"in t&&"server_close"===t.method&&"params"in t&&null!==t.params&&"object"==typeof t.params&&"i"in t.params&&"number"==typeof t.params.i}},e.RpcServerLogNotification=class{constructor(t,e){this.id=null,this.method="serverlog",this.params={line:t,level:e}}static isValid(t){return null!==t&&"object"==typeof t&&"method"in t&&"serverlog"===t.method&&"params"in t&&null!==t.params&&"object"==typeof t.params&&"line"in t.params&&"string"==typeof t.params.line&&"level"in t.params&&"number"==typeof t.params.level}},e.RpcStreamEndedNotification=class{constructor(t){this.id=null,this.method="stream_ended",this.params={stream:t}}static isValid(t){return null!==t&&"object"==typeof t&&"method"in t&&"stream_ended"===t.method&&"params"in t&&null!==t.params&&"object"==typeof t.params&&"stream"in t.params&&"number"==typeof t.params.stream}},e.RpcStreamDataNotification=class{constructor(t,e){this.id=null,this.method="stream_data",this.params={stream:t,segment:e}}static isValid(t){return null!==t&&"object"==typeof t&&"method"in t&&"stream_data"===t.method&&"params"in t&&null!==t.params&&"object"==typeof t.params&&"stream"in t.params&&"number"==typeof t.params.stream&&"segment"in t.params}},e.RpcServerMessageNotification=class{constructor(t,e){this.id=null,this.method="servermsg",this.params={i:t,body:e}}static isValid(t){return null!==t&&"object"==typeof t&&"method"in t&&"servermsg"===t.method&&"params"in t&&null!==t.params&&"object"==typeof t.params&&"i"in t.params&&"number"==typeof t.params.i&&"body"in t.params&&"object"==typeof t.params.body}},function(t){t.Directory="dir",t.File="file",t.Link="link"}(s||(e.RpcFsFileKind=s={})),e.RpcFsStatResponse=class{constructor(t,e,s){this.exists=t,this.size=e,this.type=s}static isValid(t){return null!==t&&"object"==typeof t&&"exists"in t&&"boolean"==typeof t.exists&&"size"in t&&(null===t.size||"number"==typeof t.size)&&"type"in t&&(null===t.type||"string"==typeof t.type)}},e.RpcFsReadDirResponse=class{constructor(t){this.contents=t}static isValid(t){return null!==t&&"object"==typeof t&&"contents"in t&&null!==t.contents&&Array.isArray(t.contents)&&t.contents.every((t=>null!==t&&"object"==typeof t&&"name"in t&&"string"==typeof t.name&&"type"in t&&"string"==typeof t.type))}},e.RpcSysKillResponse=class{constructor(t){this.success=t}static isValid(t){return null!==t&&"object"==typeof t&&"success"in t&&"boolean"==typeof t.success}},e.RpcGetEnvResponse=class{constructor(t,e,s){this.env=t,this.os_platform=e,this.os_release=s}static isValid(t){return null!==t&&"object"==typeof t&&"env"in t&&"object"==typeof t.env&&"os_platform"in t&&"string"==typeof t.os_platform&&"os_release"in t&&"string"==typeof t.os_release}}},304:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.MsgPackRpcClient=void 0;const a=s(5530),h=o(s(6982)),c=s(1398),l=o(s(8922)),u=s(1619);class d{constructor(t,e){this.client=t,this.streamId=e}write(t){this.client.streamWrite(t,this.streamId)}end(){this.client.streamEnd(this.streamId)}}function f(t){switch(t){case l.RpcFsFileKind.Directory:return c.FileType.Directory;case l.RpcFsFileKind.File:return c.FileType.File;case l.RpcFsFileKind.Link:return c.FileType.SymbolicLink;default:return c.FileType.Unknown}}class p{constructor(t,e,s){this.requestId=0,this.streamsStarted=new Map,this.streamsEnded=new Map,this.streamsData=new Map,this.responses=new Map,this.connection=t,this.logger=e,this.token=s,this.connection.on("error",(t=>{this.logger.error(`Socket error: ${t.message}`)})),this.connection.on("close",(()=>{this.logger.trace("Connection closed"),this.close()})),this.processMessages(),this.authPromise=this.doAuth(s),this.onServerMsgEmitter=new c.EventEmitter,this.onServerCloseEmitter=new c.EventEmitter,this.onServerMsg=this.onServerMsgEmitter.event,this.logger.info("Server message event created."),this.onServerClose=this.onServerCloseEmitter.event}static fromSocket(t,e,s){return new p({[Symbol.asyncIterator]:async function*(){let e=new u.DeferredPromise,s=e;for(t.on("data",(t=>{const e=new u.DeferredPromise;s.complete(new u.IterableMessageWithNext(t,e)),s=e})),t.on("end",(()=>{s.complete(new u.IterableMessageDone)}));;){const t=await e.p;if(t.done)return;const s=t;yield s.message,e=s.next}},write:e=>{t.write(e)},end:()=>{t.end()},on:(e,s)=>{t.on(e,s)}},e,s)}static fromSpawnedCLI(t,e,s){return new p({[Symbol.asyncIterator]:async function*(){let e=new u.DeferredPromise,s=e;for(t.ProtocolOut.onDidReceiveMessage((t=>{const e=new u.DeferredPromise;s.complete(new u.IterableMessageWithNext(t,e)),s=e})),t.ProtocolOut.onEnd.then((()=>{s.complete(new u.IterableMessageDone)}));;){const t=await e.p;if(t.done)return;const s=t;yield s.message,e=s.next}},write:e=>{t.ProtocolIn.write(e)},end:()=>{t.ProtocolIn.end()},on:(e,s)=>{"close"===e&&t.ProtocolOut.onEnd.then((()=>{s()}))}},e,s)}static fromStreams(t,e,s){return new p({[Symbol.asyncIterator]:async function*(){let e=new u.DeferredPromise,s=e;for(t.onDidReceiveMessage((t=>{const e=new u.DeferredPromise;s.complete(new u.IterableMessageWithNext(t,e)),s=e})),t.onEnd.then((()=>{s.complete(new u.IterableMessageDone)}));;){const t=await e.p;if(t.done)return;const s=t;yield s.message,e=s.next}},write:e=>{t.write(e)},end:()=>{t.end()},on:(e,s)=>{"close"===e&&t.onEnd.then((()=>{s()}))}},e,s)}get onAuth(){return this.authPromise}doAuth(t){if(!t)return this.logger.trace("No token provided, skipping authentication"),Promise.resolve();this.logger.trace("Requesting authentication challenge...");const e={token:t},s=this.sendRequest("challenge_issue",e);return this.onResponse(s).then((t=>{if(l.RpcSuccessResponse.isValid(t)&&l.RpcChallengeIssueResponse.isValid(t.result)){const e=t.result.challenge;this.logger.trace(`Received challenge: ${e}`);const s=h.createHash("sha256").update(e).digest().toString("base64").replace(/\+/g,"-").replace(/\//g,"_").replace(/=/g,"");this.logger.trace(`Computed challenge answer: ${s}`);const i={response:s},r=this.sendRequest("challenge_verify",i);return this.onResponse(r).then((t=>l.RpcSuccessResponse.isValid(t)?void this.logger.trace("Authentication successful"):(this.logger.error(`Authentication failed: ${JSON.stringify(t.error)}`),this.close(),Promise.reject())))}return l.RpcErrorResponse.isValid(t)?(this.logger.error(`Authentication failed: ${JSON.stringify(t.error)}`),this.close(),Promise.reject()):(this.logger.error(`Authentication failed: ${JSON.stringify(t)}`),this.close(),Promise.reject())}))}onSyncRequestResponse(t,e){return this.onResponse(t).then((t=>{if(l.RpcSuccessResponse.isValid(t)&&(e?.isValid(t.result)??1))return t.result;throw l.RpcErrorResponse.isValid(t)?new Error("Error response: "+JSON.stringify(t.error)):new Error("Invalid response: "+JSON.stringify(t))}))}serve(t){const e={socket_id:t.socketId,commit_id:t.commit,quality:t.quality,extensions:t.extensions,connection_token:t.connectionToken,use_local_download:!1,compress:t.compress??!1},s=this.sendRequest("serve",e);return this.onSyncRequestResponse(s)}spawn(t,e,s){const i={command:t,args:e,env:s?.env??{},cwd:s?.cwd},r=this.sendRequest("spawn",i);return this.onStreamsStarted(r,3).then((([t,e,s])=>({stdin:new d(this,t),stdout:{onDidReceiveMessage:this.onStreamData(e),onEnd:this.onStreamEnded(e)},stderr:{onDidReceiveMessage:this.onStreamData(s),onEnd:this.onStreamEnded(s)},onExit:this.onSpawnExit(r)})))}spawnCli(t,e,s){const i={command:t,args:e,env:s?.env??{},cwd:s?.cwd},r=this.sendRequest("spawn_cli",i);return this.onStreamsStarted(r,3).then((([t,e,s])=>({ProtocolIn:new d(this,t),ProtocolOut:{onDidReceiveMessage:this.onStreamData(e),onEnd:this.onStreamEnded(e)},LogOut:{onDidReceiveMessage:this.onStreamData(s),onEnd:this.onStreamEnded(s)},onExit:this.onSpawnExit(r)})))}async getShellEnv(){if(this.cachedShellEnv)return this.cachedShellEnv;const t=/^===ENV=(.*)===$/,e=await this.spawn("bash",["-ilc",'env | while read line; do echo "===ENV=$line==="; done']),s={};let i="";return e.stdout.onDidReceiveMessage((e=>{i+=e.toString();const r=i.split(/\r?\n/);i=r.pop()??"";for(const e of r){const i=t.exec(e.trim());if(i){const t=i[1],e=t.indexOf("=");if(-1!==e){const i=t.substring(0,e),r=t.substring(e+1);s[i]=r}}}})),await Promise.race([e.onExit,new Promise((t=>setTimeout(t,1e3)))]),this.cachedShellEnv=s,s}getEnv(){const t=this.sendRequest("get_env",{});return this.onSyncRequestResponse(t,l.RpcGetEnvResponse).then((async t=>{const e=await this.getShellEnv();for(const[s,i]of Object.entries(e))s in t.env||(t.env[s]=i);return{env:t.env,osPlatform:t.os_platform,osRelease:t.os_release}}))}sysKill(t){const e={pid:t},s=this.sendRequest("sys_kill",e);return this.onSyncRequestResponse(s,l.RpcSysKillResponse).then((t=>{}))}fsStat(t){const e={path:t},s=this.sendRequest("fs_stat",e);return this.onSyncRequestResponse(s,l.RpcFsStatResponse).then((t=>{return{type:f((e=t).type),size:e.size??0,ctime:0,mtime:0};var e}))}fsMkDirp(t){const e={path:t},s=this.sendRequest("fs_mkdirp",e);return this.onSyncRequestResponse(s)}fsRm(t){const e={path:t},s=this.sendRequest("fs_rm",e);return this.onSyncRequestResponse(s)}fsRead(t){const e={path:t},s=this.sendRequest("fs_read",e);return this.onStreamsStarted(s,1).then((([t])=>({onDidReceiveMessage:this.onStreamData(t),onEnd:this.onStreamEndedOrRequestFailed(t,s)})))}fsWrite(t){const e=this.sendRequest("fs_write",{path:t});return this.onStreamsStarted(e,1).then((([t])=>({stream:new d(this,t),done:this.onStreamEndedOrRequestFailed(t,e)})))}fsConnect(t){const e=this.sendRequest("fs_connect",{path:t});return this.onStreamsStarted(e,2).then((([t,s])=>{const i=new d(this,t),r={onDidReceiveMessage:this.onStreamData(s),onEnd:this.onStreamEndedOrRequestFailed(s,e)},n=this.onSyncRequestResponse(e);return{stream:{write:t=>i.write(t),end:()=>i.end(),onDidReceiveMessage:r.onDidReceiveMessage,onEnd:r.onEnd},done:n}}))}netConnect(t,e){const s=this.sendRequest("net_connect",{host:t,port:e});return this.onStreamsStarted(s,1).then((([t])=>{const e=new d(this,t),i={onDidReceiveMessage:this.onStreamData(t),onEnd:this.onStreamEndedOrRequestFailed(t,s)},r=this.onSyncRequestResponse(s);return{stream:{write:t=>e.write(t),end:()=>e.end(),onDidReceiveMessage:i.onDidReceiveMessage,onEnd:i.onEnd},done:r}}))}fsRename(t,e){const s={from_path:t,to_path:e},i=this.sendRequest("fs_rename",s);return this.onSyncRequestResponse(i)}fsReadDir(t){const e={path:t},s=this.sendRequest("fs_readdir",e);return this.onSyncRequestResponse(s,l.RpcFsReadDirResponse).then((t=>t.contents.map((t=>({name:t.name,type:f(t.type)})))))}sendRequest(t,e,s){const i=this.requestId++,r={method:t,params:e,id:s?null:i},n=(0,a.encode)(r);return this.connection.write(n),i}streamWrite(t,e){const s={stream:e,segment:t};this.sendRequest("stream_data",s,!0)}streamEnd(t){const e={stream:t};this.sendRequest("stream_ended",e,!0)}async processMessages(){try{(async()=>{try{for await(const t of(0,a.decodeMultiStream)(this.connection))if(l.RpcSuccessResponse.isValid(t)||l.RpcErrorResponse.isValid(t)){let e=this.responses.get(t.id);e||(e=new u.DeferredPromise,this.responses.set(t.id,e)),e.complete(t)}else if(l.RpcStreamsStartedNotification.isValid(t)){const e=t.params.for_request_id,s=t.params.stream_ids;let i=this.streamsStarted.get(e);i||(i=new u.DeferredPromise,this.streamsStarted.set(e,i)),i.complete(s),this.logger.info("Streams started for request "+e+": "+s),await new Promise((t=>setTimeout(t,0)))}else if(l.RpcStreamEndedNotification.isValid(t)){const e=t.params.stream;let s=this.streamsEnded.get(e);s||(s=new u.DeferredPromise,this.streamsEnded.set(e,s)),this.logger.trace("Stream "+e+" ended."),s.complete()}else if(l.RpcStreamDataNotification.isValid(t)){const e=t.params.stream,s=t.params.segment;let i=this.streamsData.get(e);i||(i=new c.EventEmitter,this.streamsData.set(e,i)),i.fire(s)}else if(l.RpcServerMessageNotification.isValid(t)){const e=t.params.i,s=t.params.body;this.onServerMsgEmitter.fire({i:e,body:s})}else l.RpcServerClosedNotification.isValid(t)?this.onServerCloseEmitter.fire():l.RpcServerLogNotification.isValid(t)?this.logger.trace(t.params.line):this.logger.error("Received unknown message:",t)}catch(t){this.logger.error("Error in message processing loop:",t)}})()}catch(t){this.logger.error("Error starting MessagePack data processing:",t)}}onResponse(t){let e=this.responses.get(t);return e||(e=new u.DeferredPromise,this.responses.set(t,e)),e.p}onSpawnExit(t){return this.onSyncRequestResponse(t,l.RpcSpawnResult).then((t=>({status:t.exit_code,message:t.message})))}onStreamsStarted(t,e){let s=this.streamsStarted.get(t);return s||(s=new u.DeferredPromise,this.streamsStarted.set(t,s)),this.onResponse(t).then((t=>{l.RpcSuccessResponse.isValid(t)||(l.RpcErrorResponse.isValid(t)?s.error(t.error):s.error(new Error("Invalid response: "+JSON.stringify(t))))})),s.p.then((t=>{if(t.length!==e)throw new Error(`Expected ${e} stream IDs, but got ${t.length}`);return t}))}onStreamEnded(t){let e=this.streamsEnded.get(t);return e||(e=new u.DeferredPromise,this.streamsEnded.set(t,e)),e.p}onStreamEndedOrRequestFailed(t,e){return this.onSyncRequestResponse(e).then((()=>this.onStreamEnded(t)))}onStreamData(t){let e=this.streamsData.get(t);return e||(e=new c.EventEmitter,this.streamsData.set(t,e)),e.event}writeServerMsg(t,e){const s={i:t,body:e};this.sendRequest("servermsg",s)}close(){this.connection.end(),this.logger.trace("Client connection closed")}dispose(){this.close()}}e.MsgPackRpcClient=p},5256:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.activate=async function(t){const e=new c.default("Remote - SSH (Windsurf)");t.subscriptions.push(e);const s=new l.RemoteSSHResolver(t,e);t.subscriptions.push(h.workspace.registerRemoteAuthorityResolver(l.REMOTE_SSH_AUTHORITY,s)),t.subscriptions.push(s);const i=new f.RemoteLocationHistory(t),r=(0,f.getRemoteWorkspaceLocationData)();r&&await i.addLocation(r[0],r[1]);const n=new d.HostTreeDataProvider(i);t.subscriptions.push(h.window.createTreeView("windsurfSSHHosts",{treeDataProvider:n})),t.subscriptions.push(n),t.subscriptions.push(h.commands.registerCommand("windsurf-remote-openssh.closeSSHProcess",(()=>{const e=s.getOpensshProcesses();if(e)for(const s of e)(0,p.terminateProcess)(s,t.extensionPath)}))),t.subscriptions.push(h.commands.registerCommand("windsurf-remote-openssh.newWindow",(()=>{(0,u.promptOpenRemoteSSHWindow)(!1)}))),t.subscriptions.push(h.commands.registerCommand("windsurf-remote-openssh.currentWindow",(()=>{(0,u.promptOpenRemoteSSHWindow)(!0)}))),t.subscriptions.push(h.commands.registerCommand("windsurf-remote-openssh.showLog",(()=>{e.show()}))),h.commands.executeCommand("setContext","forwardedPortsViewEnabled",!0)},e.deactivate=function(){};const h=o(s(1398)),c=a(s(4947)),l=s(5527),u=s(4459),d=s(1074),f=s(4786),p=s(706)},1074:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.HostTreeDataProvider=void 0;const h=o(s(1398)),c=o(s(6928)),l=o(s(1968)),u=s(4786),d=s(7721),f=s(4459),p=a(s(5624)),m=s(1398);class g{constructor(t,e){this.hostname=t,this.locations=e}}class S{constructor(t,e){this.path=t,this.hostname=e}}class y extends d.Disposable{constructor(t){super(),this.locationHistory=t,this._onDidChangeTreeData=this._register(new h.EventEmitter),this.onDidChangeTreeData=this._onDidChangeTreeData.event,this._register(h.commands.registerCommand("windsurf-remote-openssh.explorer.add",(()=>(0,f.addNewHost)()))),this._register(h.commands.registerCommand("windsurf-remote-openssh.explorer.configure",(()=>(0,f.openSSHConfigFile)()))),this._register(h.commands.registerCommand("windsurf-remote-openssh.explorer.refresh",(()=>this.refresh()))),this._register(h.commands.registerCommand("windsurf-remote-openssh.explorer.emptyWindowInNewWindow",(t=>this.openRemoteSSHWindow(t,!1)))),this._register(h.commands.registerCommand("windsurf-remote-openssh.explorer.emptyWindowInCurrentWindow",(t=>this.openRemoteSSHWindow(t,!0)))),this._register(h.commands.registerCommand("windsurf-remote-openssh.explorer.reopenFolderInNewWindow",(t=>this.openRemoteSSHLocationWindow(t,!1)))),this._register(h.commands.registerCommand("windsurf-remote-openssh.explorer.reopenFolderInCurrentWindow",(t=>this.openRemoteSSHLocationWindow(t,!0)))),this._register(h.commands.registerCommand("windsurf-remote-openssh.explorer.deleteFolderHistoryItem",(t=>this.deleteHostLocation(t)))),this._register(h.workspace.onDidChangeConfiguration((t=>{t.affectsConfiguration("remote.windsurfSSH.configFile")&&this.refresh()}))),this._register(h.workspace.onDidSaveTextDocument((t=>{t.uri.fsPath===(0,l.getSSHConfigPath)()&&this.refresh()})))}getTreeItem(t){const e=(0,u.getRemoteWorkspaceLocationData)();if(t instanceof S){const s=c.posix.basename(t.path).replace(/\.code-workspace$/," (Workspace)"),i=new h.TreeItem(s);i.description=c.posix.dirname(t.path);const r=e?e[1]:"";return i.iconPath=r===t.path?new h.ThemeIcon("folder-active",new h.ThemeColor("charts.green")):new h.ThemeIcon("folder"),i.contextValue="windsurf-remote-openssh.explorer.folder",i}const s=new h.TreeItem(t.hostname);s.collapsibleState=t.locations.length?h.TreeItemCollapsibleState.Collapsed:h.TreeItemCollapsibleState.None;let i=e?e[0]:"";if(""===i){let t=m.env.remoteAuthority;t?.includes("@")&&(t=t.split("@")[1]),t?.startsWith("ssh-remote")&&(t=t.split("+")[1],i=t?p.default.parseEncoded(t).hostname:"")}return s.iconPath=i===t.hostname?new h.ThemeIcon("vm-active",new h.ThemeColor("charts.green")):new h.ThemeIcon("vm"),s.contextValue="windsurf-remote-openssh.explorer.host",s}async getChildren(t){return t?t instanceof g?t.locations.map((e=>new S(e,t.hostname))):[]:(await l.default.loadFromFS()).getAllConfiguredHosts().map((t=>new g(t,this.locationHistory.getHistory(t))))}refresh(){this._onDidChangeTreeData.fire()}async deleteHostLocation(t){await this.locationHistory.removeLocation(t.hostname,t.path),this.refresh()}async openRemoteSSHWindow(t,e){const s=new p.default(t.hostname);(0,f.openRemoteSSHWindow)(s.toString(),e)}async openRemoteSSHLocationWindow(t,e){const s=new p.default(t.hostname);(0,f.openRemoteSSHLocationWindow)(s.toString(),t.path,e)}}e.HostTreeDataProvider=y},4786:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.RemoteLocationHistory=void 0,e.getRemoteWorkspaceLocationData=function(){let t=h.workspace.workspaceFile;if(t&&"vscode-remote"===t.scheme&&t.authority.startsWith(c.REMOTE_SSH_AUTHORITY)&&t.path.endsWith(".code-workspace")){const[,e]=t.authority.split("+");return[l.default.parseEncoded(e).hostname,t.path]}if(t=h.workspace.workspaceFolders?.[0].uri,t&&"vscode-remote"===t.scheme&&t.authority.startsWith(c.REMOTE_SSH_AUTHORITY)){const[,e]=t.authority.split("+");return[l.default.parseEncoded(e).hostname,t.path]}};const h=o(s(1398)),c=s(5527),l=a(s(5624));class u{constructor(t){this.context=t,this.remoteLocationHistory={},this.remoteLocationHistory=t.globalState.get(u.STORAGE_KEY)||{}}getHistory(t){return this.remoteLocationHistory[t]||[]}async addLocation(t,e){const s=this.remoteLocationHistory[t]||[];s.includes(e)||(s.unshift(e),this.remoteLocationHistory[t]=s,await this.context.globalState.update(u.STORAGE_KEY,this.remoteLocationHistory))}async removeLocation(t,e){let s=this.remoteLocationHistory[t]||[];s=s.filter((t=>t!==e)),this.remoteLocationHistory[t]=s,await this.context.globalState.update(u.STORAGE_KEY,this.remoteLocationHistory)}}e.RemoteLocationHistory=u,u.STORAGE_KEY="remoteLocationHistory_v0"},5527:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.RemoteSSHResolver=e.REMOTE_SSH_AUTHORITY=void 0,e.encodeRemoteAuthority=function(t){const s=_.default.parse(t),i={hostName:s.hostname,user:s.user,port:s.port},r=Buffer.from(JSON.stringify(i)).toString("hex");return`${e.REMOTE_SSH_AUTHORITY}+${r}`},e.decodeRemoteAuthority=b;const h=o(s(1398)),c=o(s(5317)),l=o(s(6928)),u=o(s(857)),d=o(s(9278)),f=s(296),p=o(s(8611)),m=o(s(6982)),g=s(706),S=s(2596),y=s(5376),w=o(s(5873)),_=a(s(5624)),v=a(s(1968)),E=o(s(5076));function b(t){const e=t.split("+");return{remoteAuthority:e[0],sshDest:_.default.parseEncoded(e[1])}}e.REMOTE_SSH_AUTHORITY="ssh-remote",e.RemoteSSHResolver=class{constructor(t,e){this.context=t,this.logger=e,this.forwardingProcesses=[],this.forwardingServers=[]}resolve(t,e){return this.resolveHelper(t,e,!1).then((({localPort:t,connectionToken:e})=>new h.ResolvedAuthority("127.0.0.1",t,e)),(t=>{throw this.logger.error(t),new Error("Failed to resolve remote authority: "+t)}))}resolveExecServer(t,e){return this.resolveHelper(t,e,!0).then((({localPort:e,connectionToken:s})=>E.MsgpackExecServer.fromLocalPort(e,s,this.logger,t)),(t=>{throw this.logger.error(t),new Error("Failed to resolve exec server: "+t)}))}resolveHelper(t,e,s){this.logger.info(`Resolving ssh remote authority '${t}' (attempt #${e.resolveAttempt})`);const i=h.workspace.getConfiguration("remote.windsurfSSH").get("path")||"ssh",r=h.workspace.getConfiguration("remote.windsurfSSH.experimental").get("serverDownloadUrlTemplate");return h.window.withProgress({location:h.ProgressLocation.Notification,title:"Setting up SSH Host  ([details](command:windsurf-remote-openssh.showLog))",cancellable:!1},(async e=>{let n=!1;const o=m.randomBytes(12).toString("hex"),a=new Promise((async(a,g)=>{e.report({message:"Starting Windsurf SSH..."});const _=t=>{this.logger.error(t),n||(n=!0,this.logger.show(),h.window.showErrorMessage(t,{modal:!0},...this.getActions()).then((t=>{if(t)return t.execute()})).then((()=>{g(t)})))},{remoteAuthority:E,sshDest:I}=b(t),R=I.user?`${I.user}@${I.hostname}`:I.hostname,O=I.port,T=(await v.default.loadFromFS()).getHostConfiguration(I.hostname),k=void 0!==T.RemoteCommand,D="yes"===T.ForwardAgent;if(this.logger.info(`Connecting to ${R}...`),void 0!==O&&isNaN(O))return void _(`Invalid port: ${O}`);const A=String(m.randomInt(0xffffffffff));try{c.execSync(`${i} -V`,{timeout:5e3})}catch(t){return void _(`${i} -V failed - do you have OpenSSH installed?`)}const C=await(0,y.findRandomPort)(),L=await(0,y.findRandomPort)(),x=(0,y.findAskpassScriptPath)(),B=(0,y.findAskpassJSPath)();let P=!1;const $=p.createServer((async(t,e)=>{if("POST"===t.method){let s="";for await(const e of t)s+=e.toString();try{const t=JSON.parse(s);this.logger.info(`Received SSH askpass request: ${t.request}`);const i=P?void 0:await h.window.showInputBox({title:t.request,password:!0,ignoreFocusOut:!0});if(void 0===i)return P=!0,void _("Password authentication cancelled");e.writeHead(200,{"Content-Type":"text/plain"}),e.end(i||"")}catch(t){this.logger.error(`Error parsing request: ${t}`),e.writeHead(400,{"Content-Type":"text/plain"}),e.end("Invalid request format")}}else e.writeHead(405,{"Content-Type":"text/plain"}),e.end("Method Not Allowed")}));let N;if("win32"===process.platform){const t=await(0,y.findRandomPort)();N=`${t}`,$.listen(t,"127.0.0.1",(()=>{this.logger.info(`SSH askpass server listening on local port: ${N}`)}))}else{const t=m.randomBytes(16).toString("hex");N=l.join(u.tmpdir(),`socket_${t}.sock`),$.listen(N,(()=>{this.logger.info(`SSH askpass server listening on ${N}`)}))}const M=async t=>{for(const e of t.split("\n"))e&&this.logger.trace(`[stderr] ${e}`)},U=(t,s)=>{if(n)return;e.report({message:"Creating local forwarding server..."}),n=!0,this.logger.info(`Creating forwarding server ${L}(local) => ${C}(socks) => ${t}(remote)`);const i={proxy:{host:"127.0.0.1",port:C,type:5},command:"connect",destination:{host:"127.0.0.1",port:t}},r=d.createServer().on("error",g).on("connection",(async t=>{try{const e=await f.SocksClient.createConnection(i);t.pipe(e.socket),e.socket.pipe(t)}catch(t){this.logger.error(`Error while creating SOCKS connection ${t}`)}})).on("listening",(()=>{this.logger.info("Forwarding server listening on port "+L),this.labelFormatterDisposable=h.workspace.registerResourceLabelFormatter({scheme:"vscode-remote",authority:`${E}+*`,formatting:{label:"${path}",separator:"/",tildify:!0,workspaceSuffix:`SSH: ${R}${O?":"+O:""}`}}),a({localPort:L,connectionToken:s})})).listen(L);this.forwardingServers.push(r)};let F,j,H="";const V=async t=>{for(let e=0;e<t.length;e++){const s=t.charCodeAt(e);if(10===s){H&&this.logger.trace(H);const t=H.match(/listeningOn==(\d+)==/),e=H.match(/connectionToken==(.+)==/),s=H.match(/exitCode==(.+)==/),i=H.match(new RegExp(`${o}: end`));if(t&&(this.logger.info(`Remote port: ${t[1]}`),F=parseInt(t[1],10)),e&&(this.logger.info(`Connection token: ${e[1]}`),j=e[1]),s&&"1"===s[1]){const t=`An error occurred while starting the server, with exit code: ${s[1]}\nMore info can be found in the Output tab.`;return void _(t)}i&&(j&&F?U(F,j):_(`Failed to resolve remote port (${F}) or connection token (${j})`)),H=""}else 8===s?H=H.substr(0,H.length-1):H+=t.charAt(e)}},z=await(0,S.getVSCodeServerConfig)();let W=r??z.serverDownloadUrlTemplate??w.getDefaultDownloadUrlTemplate(z.quality);W===w.getDefaultDownloadUrlTemplate("stable")&&"insider"===z.quality&&(W=w.getDefaultDownloadUrlTemplate("insider"));const G=w.getDefaultCliDownloadUrlTemplate(z.quality),q={id:o,vscodeVersion:z.vscodeVersion,windsurfVersion:z.windsurfVersion,commit:z.commit,quality:z.quality,release:z.release,extensionIds:[],envVariables:[],useSocketPath:!1,serverApplicationName:z.serverApplicationName,serverDataFolderName:z.serverDataFolderName,serverDownloadUrlTemplate:W,distroId:z.commit+(z.isDevelopment?"-dev":""),agentForwarding:D,disableServerChecksum:h.workspace.getConfiguration("remote.windsurfSSH.experimental").get("disableServerChecksum",!1),isExecServer:s,cliDownloadUrlTemplate:G,httpProxy:h.workspace.getConfiguration("remote.windsurfSSH").get("httpProxy",""),httpsProxy:h.workspace.getConfiguration("remote.windsurfSSH").get("httpsProxy","")},K=w.generateBashInstallScript(q,A);this.logger.info(`Bash script:\n${K}`);const Y=["-v","-T"];k&&Y.push("-o","RemoteCommand=none"),Y.push("-D",`${C}`),Y.push(R),O&&Y.push("-p",`${O}`),Y.push("bash -s"),e.report({message:"Launching SSH server..."}),this.logger.info(`Launching SSH server with command: ${i} ${Y.join(" ")}`);const Z=c.spawn(i,Y,{env:{...process.env,SSH_ASKPASS:x,WINDSURF_SSH_ASKPASS_JS:B,WINDSURF_SSH_ASKPASS_HANDLE:N,WINDSURF_SSH_ELECTRON_PATH:process.execPath,DISPLAY:"1"},detached:!0,windowsHide:!0});this.forwardingProcesses.push(Z),Z.stdin.write(K),Z.stdin.end(),Z.stdout.on("data",(t=>V(t.toString()))),Z.stderr.on("data",(t=>M(t.toString()))),Z.on("error",(t=>{_(`SSH server failed with error:\n${t.message}`)})),Z.on("close",(t=>{let e="SSH server closed unexpectedly.";0!==t&&(e+=`\nError code: ${t}`),_(e)}))}));return a}))}dispose(){this.labelFormatterDisposable&&this.labelFormatterDisposable.dispose();for(const t of this.forwardingProcesses)(0,g.terminateProcess)(t,this.context.extensionPath);for(const t of this.forwardingServers)t.close((()=>{this.logger.info("Forwarding server closed")}))}getOpensshProcesses(){return this.forwardingProcesses}async getCanonicalURI(t){return h.Uri.file(t.path)}getActions(){const t=[],e=h.workspace.textDocuments.some((t=>t.isDirty))||h.workspace.workspaceFile&&"untitled"===h.workspace.workspaceFile.scheme;return t.push({title:"Retry",execute:async()=>{await h.commands.executeCommand("workbench.action.reloadWindow")}}),e||t.push({title:"Close Remote",execute:async()=>{await h.commands.executeCommand("vscode.newWindow",{reuseWindow:!0,remoteAuthority:null})}}),t.push({title:"Show Log",isCloseAffordance:!0,execute:async()=>{h.commands.executeCommand("windsurf-remote-openssh.showLog")}}),t}}},2596:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.getVSCodeServerConfig=async function(){const t=await async function(){if(!l){const t=await h.promises.readFile(c.join(a.env.appRoot,"product.json"),"utf8");l=JSON.parse(t)}return l}(),e=a.workspace.getConfiguration("remote.windsurfSSH.experimental").get("serverBinaryName",""),s=a.version.replace("-insider","");let i=t.windsurfVersion,r=t.commit,n=t.quality,o=e||t.serverApplicationName,d=t.serverDataFolderName,f=!1;if(void 0===n){const t=await fetch(u),e=await t.json();i=e.windsurfVersion,r=e.version,n="insider",o="windsurf-server-insiders",d=".windsurf-server-insiders",f=!0}return{vscodeVersion:s,windsurfVersion:i,commit:r,quality:n,release:t.release,serverApplicationName:o,serverDataFolderName:d,serverDownloadUrlTemplate:t.serverDownloadUrlTemplate,isDevelopment:f}};const a=o(s(1398)),h=o(s(9896)),c=o(s(6928));let l;const u="https://windsurf-nightly.corp.exafunction.com/api/update/linux-reh-x64/insider/latest"},5873:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.ServerInstallError=void 0,e.getDefaultDownloadUrlTemplate=function(t){return"insider"===t||void 0===t?"https://windsurf-nightly.codeiumdata.com/${os}-reh-${arch}/${quality}/${commit}/windsurf-reh-${os}-${arch}-${windsurfVersion}.tar.gz":"https://windsurf-stable.codeiumdata.com/${os}-reh-${arch}/${quality}/${commit}/windsurf-reh-${os}-${arch}-${windsurfVersion}.tar.gz"},e.getDefaultCliDownloadUrlTemplate=function(t){return"insider"===t||void 0===t?"https://windsurf-nightly.codeiumdata.com/${os}-cli-${arch}/${quality}/${commit}/Windsurf-cli-${os}-${arch}-${windsurfVersion}.tar.gz":"https://windsurf-stable.codeiumdata.com/${os}-cli-${arch}/${quality}/${commit}/Windsurf-cli-${os}-${arch}-${windsurfVersion}.tar.gz"},e.parseServerInstallOutput=function(t,e){const s=`${e}: start`,i=`${e}: end`,r=t.indexOf(s);if(r<0)return;const n=t.indexOf(i,r+s.length);if(n<0)return;const o={},a=t.substring(r+s.length,n).split(/\r?\n/);for(const t of a){const[e,s]=t.split("==");o[e]=s}return o},e.generateBashInstallScript=function({id:t,quality:e,vscodeVersion:s,windsurfVersion:i,commit:r,release:n,extensionIds:o,envVariables:h,useSocketPath:c,serverApplicationName:l,serverDataFolderName:u,serverDownloadUrlTemplate:d,distroId:f,agentForwarding:p,disableServerChecksum:m,cliDownloadUrlTemplate:g,isExecServer:S,httpProxy:y,httpsProxy:w},_){const v=o.map((t=>"--install-extension "+t)).join(" ");return`\n# Server installation script\nDISABLE_SERVER_CHECKSUM="${m}"\nTMP_DIR="\${XDG_RUNTIME_DIR:-"/tmp"}"\n\nDISTRO_VSCODE_VERSION="${s}"\nDISTRO_WINDSURF_VERSION="${i}"\nDISTRO_COMMIT="${r}"\nDISTRO_ID="${f}"\nDISTRO_QUALITY="${e}"\nDISTRO_VSCODIUM_RELEASE="${n??""}"\n\nSERVER_APP_NAME="${l}"\nSERVER_INITIAL_EXTENSIONS="${v}"\nSERVER_LISTEN_FLAG="${c?`--socket-path="$TMP_DIR/vscode-server-sock-${a.randomUUID()}"`:"--port=0"}"\nSERVER_DATA_DIR="$HOME/${u}"\nSERVER_DIR="$SERVER_DATA_DIR/bin/$DISTRO_ID"\nSERVER_SCRIPT="$SERVER_DIR/bin/$SERVER_APP_NAME"\nSERVER_LOGFILE="$SERVER_DATA_DIR/.$DISTRO_ID.log"\nSERVER_PIDFILE="$SERVER_DATA_DIR/.$DISTRO_ID.pid"\nSERVER_TOKENFILE="$SERVER_DATA_DIR/.$DISTRO_ID.token"\nSERVER_SSH_AGENT_SOCKET="$SERVER_DATA_DIR/.$DISTRO_ID-ssh-auth.sock"\nAGENT_FORWARDING="${p}"\nIS_EXEC_SERVER="${S}"\n\nCLI_PATH="$SERVER_DATA_DIR/windsurf-cli-$DISTRO_ID"\nCLI_LOGFILE="$SERVER_DATA_DIR/.$DISTRO_ID-cli.log"\nCLI_PIDFILE="$SERVER_DATA_DIR/.$DISTRO_ID-cli.pid"\nCLI_TOKENFILE="$SERVER_DATA_DIR/.$DISTRO_ID-cli.token"\nCLI_DATA_DIR="$SERVER_DATA_DIR/cli"\n\nDOWNLOAD_ARCH=\n\nLISTENING_ON=\nCONNECTION_TOKEN=\nOUTPUT_LOGFILE=\nOUTPUT_PIDFILE=\nOUTPUT_TOKENFILE=\nOS_RELEASE_ID=\nARCH=\nPLATFORM=\n\nGLIBC_VERSION_GOOD=\n\nHTTP_PROXY="${y}"\nHTTPS_PROXY="${w}"\n\nif [ -n "$HTTP_PROXY" ]; then\n\techo "Exporting HTTP_PROXY=$HTTP_PROXY"\n\texport HTTP_PROXY\nfi\nif [ -n "$HTTPS_PROXY" ]; then\n\techo "Exporting HTTPS_PROXY=$HTTPS_PROXY"\n\texport HTTPS_PROXY\nfi\n\n# Add lock mechanism\nLOCK_FILE="$SERVER_DATA_DIR/.installation_lock"\n\n# Function to acquire lock\nacquire_lock() {\n\texec 200>$LOCK_FILE\n\techo "Waiting for lock..."\n\tflock 200\n\techo "Lock acquired, proceeding with installation."\n}\n\n# Function to release lock\nrelease_lock() {\n\tflock -u 200\n\texec 200>&-\n}\n\n# Mimic output from logs of remote-ssh extension\nprint_install_results_and_exit() {\n\tif [[ $1 -eq 1 ]]; then\n\t\techo ""\n\t\techo "Error: installation failed."\n\t\tif [[ -f "$OUTPUT_LOGFILE" ]]; then\n\t\t\techo "Server log:\n $(cat "$OUTPUT_LOGFILE")\n"\n\t\tfi\n\tfi\n\tif [[ "$GLIBC_VERSION_GOOD" = "false" ]]; then\n\t\techo "Warning: valid glibc version not found. Windsurf only supports remote connections with glibc >= 2.28, such as Ubuntu 20.04, Debian 10, or CentOS 8."\n\t\techo ""\n\tfi\n\techo "${t}: start"\n\techo "exitCode==$1=="\n\techo "listeningOn==$LISTENING_ON=="\n\techo "connectionToken==$CONNECTION_TOKEN=="\n\techo "logFile==$OUTPUT_LOGFILE=="\n\techo "osReleaseId==$OS_RELEASE_ID=="\n\techo "arch==$ARCH=="\n\techo "platform==$PLATFORM=="\n\techo "tmpDir==$TMP_DIR=="\n\t${h.map((t=>`echo "${t}==$${t}=="`)).join("\n")}\n\techo "${t}: end"\n\n\texit 0\n}\n\nprint_install_results_and_wait() {\n\t# Check server is indeed running\n\tif [[ ! -f $OUTPUT_PIDFILE ]]; then\n\t\tOUTPUT_PID=$(pgrep -f "$OUTPUT_SCRIPT")\n\t\tif [[ -n $OUTPUT_PID ]]; then\n\t\t\ttouch $OUTPUT_PIDFILE\n\t\t\techo $OUTPUT_PID > $OUTPUT_PIDFILE\n\t\tfi\n\tfi\n\tif [[ -f $OUTPUT_PIDFILE ]]; then\n\t\tOUTPUT_PID="$(cat $OUTPUT_PIDFILE)"\n\t\tif [[ -z $OUTPUT_PID ]]; then\n\t\t\tOUTPUT_PID=$(pgrep -f "$OUTPUT_SCRIPT")\n\n\t\t\tif [[ -n $OUTPUT_PID ]]; then\n\t\t\t\techo $OUTPUT_PID > $OUTPUT_PIDFILE\n\t\t\telse\n\t\t\t\tprint_install_results_and_exit 1\n\t\t\tfi\n\t\tfi\n\telse\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\techo "${t}: start"\n\t# pretend exit code is 0\n\techo "exitCode==0=="\n\techo "listeningOn==$LISTENING_ON=="\n\techo "connectionToken==$CONNECTION_TOKEN=="\n\techo "logFile==$OUTPUT_LOGFILE=="\n\techo "osReleaseId==$OS_RELEASE_ID=="\n\techo "arch==$ARCH=="\n\techo "platform==$PLATFORM=="\n\techo "tmpDir==$TMP_DIR=="\n\t${h.map((t=>`echo "${t}==$${t}=="`)).join("\n")}\n\techo "${t}: end"\n\n\trelease_lock\n\n\t# Wait for server to exit\n\twhile ps -p $OUTPUT_PID >/dev/null 2>&1\n\tdo\n\t\tsleep 10\n\tdone\n}\n\ndelete_server() {\n\tif [[ -n "$OLD_DISTRO_ID" ]]; then\n\t\techo "Cleaning up files for $OLD_DISTRO_ID..."\n\t\trm -rf "$SERVER_DATA_DIR/bin/$OLD_DISTRO_ID"\n\t\trm -f "$SERVER_DATA_DIR/.$OLD_DISTRO_ID.pid"\n\t\trm -f "$SERVER_DATA_DIR/.$OLD_DISTRO_ID.log"\n\t\trm -f "$SERVER_DATA_DIR/.$OLD_DISTRO_ID.token"\n\t\trm -f "$SERVER_DATA_DIR/.$OLD_DISTRO_ID-ssh-auth.sock"\n\t\techo "$OLD_DISTRO_ID cleaned up."\n\tfi\n}\n\nclean_up_old_servers() {\n\tif [[ "$IS_EXEC_SERVER" != "true" ]]; then\n\t\techo "Cleaning up old server installations..."\n\t\tif [[ -d "$SERVER_DATA_DIR/bin" ]]; then\n\t\t\tfor OLD_DISTRO_DIR_FULL_PATH in "$SERVER_DATA_DIR"/bin/*; do\n\t\t\t\tif [[ -d "$OLD_DISTRO_DIR_FULL_PATH" ]]; then\n\t\t\t\t\tOLD_DISTRO_ID=$(basename "$OLD_DISTRO_DIR_FULL_PATH")\n\n\t\t\t\t\tif [[ "$OLD_DISTRO_ID" = "$DISTRO_ID" ]]; then\n\t\t\t\t\t\tcontinue\n\t\t\t\t\tfi\n\n\t\t\t\t\techo "Checking old server: $OLD_DISTRO_ID"\n\t\t\t\t\tOLD_SERVER_PIDFILE="$SERVER_DATA_DIR/.$OLD_DISTRO_ID.pid"\n\n\t\t\t\t\techo "Old server PID file: $OLD_SERVER_PIDFILE"\n\n\t\t\t\t\tNUMBER_OF_EXECUTABLES=$(find "$OLD_DISTRO_DIR_FULL_PATH/bin/" -maxdepth 1 -type f -executable | wc -l)\n\t\t\t\t\tif (( $NUMBER_OF_EXECUTABLES == 0 )); then\n\t\t\t\t\t\techo "No executables found in $OLD_DISTRO_DIR_FULL_PATH/bin/"\n\t\t\t\t\t\tdelete_server\n\t\t\t\t\t\tcontinue\n\t\t\t\t\telif (( $NUMBER_OF_EXECUTABLES > 1 )); then\n\t\t\t\t\t\techo "Multiple executables found in $OLD_DISTRO_DIR_FULL_PATH/bin/, leaving it alone."\n\t\t\t\t\t\tcontinue\n\t\t\t\t\telse\n\t\t\t\t\t\tOLD_SERVER_SCRIPT=$(find "$OLD_DISTRO_DIR_FULL_PATH/bin/" -maxdepth 1 -type f -executable)\n\t\t\t\t\t\techo "Old server script: $OLD_SERVER_SCRIPT"\n\t\t\t\t\tfi\n\n\t\t\t\t\tOLD_SERVER_SCRIPT_PATH=$(readlink -f "$OLD_SERVER_SCRIPT")\n\t\t\t\t\techo "Old server script path: $OLD_SERVER_SCRIPT_PATH"\n\n\t\t\t\t\tif [[ -z "$OLD_SERVER_SCRIPT_PATH" ]]; then\n\t\t\t\t\t\techo "Something went wrong, old server script path is empty. Skipping old server $OLD_DISTRO_ID."\n\t\t\t\t\t\tcontinue\n\t\t\t\t\tfi\n\n\t\t\t\t\tif [[ ! -f "$OLD_SERVER_PIDFILE" ]]; then\n\t\t\t\t\t\tOLD_PID=$(pgrep -f "$OLD_SERVER_SCRIPT_PATH")\n\t\t\t\t\t\tif [[ -n "$OLD_PID" ]]; then\n\t\t\t\t\t\t\ttouch "$OLD_SERVER_PIDFILE"\n\t\t\t\t\t\t\techo "$OLD_PID" > "$OLD_SERVER_PIDFILE"\n\t\t\t\t\t\t\techo "Wrote to PID file for $OLD_DISTRO_ID: $OLD_SERVER_PIDFILE"\n\t\t\t\t\t\tfi\n\t\t\t\t\tfi\n\n\t\t\t\t\tif [[ -f "$OLD_SERVER_PIDFILE" ]]; then\n\t\t\t\t\t\tOLD_PID=$(cat "$OLD_SERVER_PIDFILE")\n\t\t\t\t\t\techo "Old server PID: $OLD_PID"\n\t\t\t\t\t\tif [[ -z "$OLD_PID" ]]; then\n\t\t\t\t\t\t\tOLD_PID=$(pgrep -f "$OLD_SERVER_SCRIPT_PATH")\n\t\t\t\t\t\t\tif [[ -n "$OLD_PID" ]]; then\n\t\t\t\t\t\t\t\techo "$OLD_PID" > "$OLD_SERVER_PIDFILE"\n\t\t\t\t\t\t\t\techo "Restored PID file for $OLD_DISTRO_ID: $OLD_SERVER_PIDFILE."\n\t\t\t\t\t\t\tfi\n\t\t\t\t\t\tfi\n\t\t\t\t\t\t# Check if PID is non-empty and if process with OLD_PID is running\n\t\t\t\t\t\tif [[ -n "$OLD_PID" ]] && ps -p "$OLD_PID" > /dev/null 2>&1; then\n\t\t\t\t\t\t\tOLD_RUNNING_PROCESS="$(ps -o pid,args -p $OLD_PID | grep -F $OLD_SERVER_SCRIPT_PATH)"\n\t\t\t\t\t\t\tif [[ -n "$OLD_RUNNING_PROCESS" ]]; then\n\t\t\t\t\t\t\t\techo "Old server $OLD_DISTRO_ID (PID $OLD_PID) is running."\n\t\t\t\t\t\t\tfi\n\t\t\t\t\t\telse\n\t\t\t\t\t\t\techo "Old server $OLD_DISTRO_ID (PID $OLD_PID) is not running."\n\t\t\t\t\t\t\tdelete_server\n\t\t\t\t\t\t\tcontinue\n\t\t\t\t\t\tfi\n\t\t\t\t\telse\n\t\t\t\t\t\techo "No PID file found for old server $OLD_DISTRO_ID ($OLD_SERVER_PIDFILE) and no processes matching the script path."\n\t\t\t\t\t\tdelete_server\n\t\t\t\t\t\tcontinue\n\t\t\t\t\tfi\n\t\t\t\tfi\n\t\t\tdone\n\t\t\techo "Finished cleaning up old server installations."\n\t\telse\n\t\t\techo "No server bin directory found at $SERVER_DATA_DIR/bin. Skipping cleanup of old servers."\n\t\tfi\n\telse\n\t\techo "Cleaning up old CLI binaries..."\n\t\tCURRENT_CLI_BINARY="$CLI_PATH"\n\t\tfor CLI_BIN in "$SERVER_DATA_DIR"/windsurf-cli-*; do\n\t\t\t# Skip if glob didn"t match anything\n\t\t\t[ -e "$CLI_BIN" ] || continue\n\t\t\t# Skip the current CLI binary\n\t\t\tif [[ "$CLI_BIN" = "$CURRENT_CLI_BINARY" ]]; then\n\t\t\t\techo "Skipping current CLI binary $CLI_BIN"\n\t\t\t\tcontinue\n\t\t\tfi\n\n\t\t\tRUNNING_CLI_PIDS=$(pgrep -f "$CLI_BIN" || true)\n\t\t\tif [[ -n "$RUNNING_CLI_PIDS" ]]; then\n\t\t\t\techo "Killing running CLI processes for outdated binary $CLI_BIN: $RUNNING_CLI_PIDS"\n\t\t\t\tfor CPID in $RUNNING_CLI_PIDS; do\n\t\t\t\t\tterminateTree $CPID\n\t\t\t\tdone\n\t\t\tfi\n\n\t\t\techo "Removing outdated CLI binary $CLI_BIN"\n\t\t\trm -f "$CLI_BIN"\n\n\t\t\t# Extract the distro/commit hash from the filename (windsurf-cli-<id>)\n\t\t\tCLI_DISTRO_ID=$(basename "$CLI_BIN" | sed 's/^windsurf-cli-//')\n\n\t\t\trm -f "$SERVER_DATA_DIR/.$CLI_DISTRO_ID-cli.pid" || true\n\t\t\trm -f "$SERVER_DATA_DIR/.$CLI_DISTRO_ID-cli.log" || true\n\t\t\trm -f "$SERVER_DATA_DIR/.$CLI_DISTRO_ID-cli.token" || true\n\t\tdone\n\tfi\n}\n\nterminateTree() {\n\tfor cpid in $(/usr/bin/pgrep -P $1); do\n\t\tterminateTree $cpid\n\tdone\n\tkill -9 $1 > /dev/null 2>&1\n}\n\nkill_running_server() {\n\tif [[ -n "$1" ]]; then\n\t\techo "Killing server process with PID $1 (and all its children)"\n\t\tterminateTree $1\n\tfi\n}\n\n# Check if platform is supported\nKERNEL="$(uname -s)"\ncase $KERNEL in\n\tDarwin)\n\t\tPLATFORM="darwin"\n\t\t;;\n\tLinux)\n\t\tPLATFORM="linux"\n\t\t;;\n\tFreeBSD)\n\t\tPLATFORM="freebsd"\n\t\t;;\n\tDragonFly)\n\t\tPLATFORM="dragonfly"\n\t\t;;\n\t*)\n\t\techo "Error platform not supported: $KERNEL"\n\t\tprint_install_results_and_exit 1\n\t\t;;\nesac\n\n# Check machine architecture\nARCH="$(uname -m)"\ncase $ARCH in\n\tx86_64 | amd64)\n\t\tSERVER_ARCH="x64"\n\t\t;;\n\tarmv7l | armv8l)\n\t\tSERVER_ARCH="armhf"\n\t\t;;\n\tarm64 | aarch64)\n\t\tSERVER_ARCH="arm64"\n\t\t;;\n\tppc64le)\n\t\tSERVER_ARCH="ppc64le"\n\t\t;;\n\triscv64)\n\t\tSERVER_ARCH="riscv64"\n\t\t;;\n\t*)\n\t\techo "Error architecture not supported: $ARCH"\n\t\tprint_install_results_and_exit 1\n\t\t;;\nesac\n\n# Attempt to get checksum from manifest\n\nif [[ "$DISTRO_QUALITY" = "insider" ]]; then\n\tMANIFEST_URL="https://windsurf-nightly.codeiumdata.com/\${PLATFORM}-reh-\${SERVER_ARCH}/\${DISTRO_QUALITY}/manifest-\${DISTRO_COMMIT}.json"\nelse\n\tMANIFEST_URL="https://windsurf-stable.codeiumdata.com/\${PLATFORM}-reh-\${SERVER_ARCH}/\${DISTRO_QUALITY}/manifest-\${DISTRO_COMMIT}.json"\nfi\n\nif [[ $DISABLE_SERVER_CHECKSUM = "true" ]]; then\n\tSHA256_HASH=""\nelse\n\t# Curl the manifest URL, and get the sha256 hash\n\tSHA256_HASH="$(curl -s "$MANIFEST_URL" --max-time 5 | grep -o '"sha256hash": "[^"]*"' | sed 's/"sha256hash": "\\(.*\\)"/\\1/')"\n\n\tif [[ $? -ne 0 || -z $SHA256_HASH ]]; then\n\t\techo "Warning: could not get sha256 hash from manifest: will not verify checksum."\n\t\tSHA256_HASH=""\n\tfi\n\n\tif ! sha256sum --version > /dev/null 2>&1; then\n\t\techo "Warning: sha256sum could not be found: will not verify checksum."\n\t\tSHA256_HASH=""\n\tfi\nfi\n\n# https://www.freedesktop.org/software/systemd/man/os-release.html\nOS_RELEASE_ID="$(grep -i '^ID=' /etc/os-release 2>/dev/null | sed 's/^ID=//gi' | sed 's/"//g')"\nif [[ -z $OS_RELEASE_ID ]]; then\n\tOS_RELEASE_ID="$(grep -i '^ID=' /usr/lib/os-release 2>/dev/null | sed 's/^ID=//gi' | sed 's/"//g')"\n\tif [[ -z $OS_RELEASE_ID ]]; then\n\t\tOS_RELEASE_ID="unknown"\n\tfi\nfi\n\n# Create installation folder\nif [[ ! -d $SERVER_DIR ]]; then\n\tmkdir -p $SERVER_DIR\n\tif (( $? > 0 )); then\n\t\techo "Error creating server install directory"\n\t\tprint_install_results_and_exit 1\n\tfi\nfi\n\n# Acquire lock at the beginning of the script\nacquire_lock\n\n# Add trap to release lock on exit\ntrap release_lock EXIT INT TERM\n\nclean_up_old_servers\n\nif [[ "$AGENT_FORWARDING" = "true" ]]; then\n\tln -s -f "$SSH_AUTH_SOCK" "$SERVER_SSH_AGENT_SOCKET"\n\texport SSH_AUTH_SOCK="$SERVER_SSH_AGENT_SOCKET"\nfi\n\nif [[ "$PLATFORM" == "linux" ]]; then\n\t# Check ldd version based on format "ldd (.*) 2.28"\n\tversion=$(ldd --version | head -n 1 | grep -oE '[0-9]+.[0-9]+$')\n\tif (( $? > 0 )); then\n\t\techo "Warning: ldd not found"\n\t\tGLIBC_VERSION_GOOD="false"\n\telse\n\t\tmajor=$(echo "$version" | cut -d '.' -f 1)\n\t\tminor=$(echo "$version" | cut -d '.' -f 2)\n\n\t\tif [[ "$major" -eq 2 && "$minor" -ge 28 ]]; then\n\t\t\tGLIBC_VERSION_GOOD="true"\n\t\telse\n\t\t\tGLIBC_VERSION_GOOD="false"\n\t\tfi\n\tfi\n\n\tif [[ "$GLIBC_VERSION_GOOD" = "false" ]]; then\n\t\techo "Warning: valid glibc version not found. Windsurf only supports remote connections with glibc >= 2.28, such as Ubuntu 20.04, Debian 10, or CentOS 8."\n\tfi\nfi\n\nSERVER_DOWNLOAD_URL="$(echo "${d.replace(/\$\{/g,"\\${")}" | sed "s/\\\${quality}/$DISTRO_QUALITY/g" | sed "s/\\\${vscodeVersion}/$DISTRO_VSCODE_VERSION/g" | sed "s/\\\${commit}/$DISTRO_COMMIT/g" | sed "s/\\\${os}/$PLATFORM/g" | sed "s/\\\${arch}/$SERVER_ARCH/g" | sed "s/\\\${release}/$DISTRO_VSCODIUM_RELEASE/g" | sed "s/\\\${windsurfVersion}/$DISTRO_WINDSURF_VERSION/g")"\nOUTPUT_LOGFILE=$SERVER_LOGFILE\nOUTPUT_TOKENFILE=$SERVER_TOKENFILE\nOUTPUT_PIDFILE=$SERVER_PIDFILE\nOUTPUT_SCRIPT=$SERVER_SCRIPT\n\nif [[ "$DISABLE_SERVER_CHECKSUM" == "true" ]]; then\n\tSHA256_HASH=""\nelse\n\t# Curl the manifest URL, and get the sha256 hash\n\tSHA256_HASH="$(curl -s "$MANIFEST_URL" --max-time 5 | grep -o '"sha256hash": "[^"]*"' | sed 's/"sha256hash": "\\(.*\\)"/\\1/')"\n\n\tif [[ $? -ne 0 || -z $SHA256_HASH ]]; then\n\t\techo "Warning: could not get sha256 hash from manifest: will not verify checksum."\n\t\tSHA256_HASH=""\n\tfi\n\n\tif ! sha256sum --version > /dev/null 2>&1; then\n\t\techo "Warning: sha256sum could not be found: will not verify checksum."\n\t\tSHA256_HASH=""\n\tfi\nfi\n\n# Check if server script is already installed\nif [[ ! -f $SERVER_SCRIPT ]]; then\n\tif [[ "$PLATFORM" != "darwin" ]] && [[ "$PLATFORM" != "linux" ]]; then\n\t\techo "Error "$PLATFORM" needs manual installation of remote extension host"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tpushd $SERVER_DIR > /dev/null\n\techo "Downloading server from $SERVER_DOWNLOAD_URL"\n\n\ttemp_file=$(mktemp)\n\tif [[ ! -z $(which curl) ]]; then\n\t\tcurl --retry 3 --connect-timeout 10 --location --show-error --silent --output $temp_file $SERVER_DOWNLOAD_URL\n\telif [ ! -z $(which wget) ]; then\n\t\twget --tries=3 --timeout=10 --continue --quiet -O $temp_file $SERVER_DOWNLOAD_URL\n\telse\n\t\techo "Error no tool to download server binary"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tif (( $? > 0 )); then\n\t\techo "Error downloading server from $SERVER_DOWNLOAD_URL"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tmv $temp_file vscode-server.tar.gz\n\n\t# Before extracting it, check the checksum\n\tif [[ ! -z $SHA256_HASH ]]; then\n\t\t# Calculate the checksum of the downloaded file\n\t\tCALCULATED_HASH="$(sha256sum vscode-server.tar.gz | cut -d ' ' -f 1)"\n\n\t\t# Compare with the expected hash\n\t\tif [[ "$CALCULATED_HASH" != "$SHA256_HASH" ]]; then\n\t\t\techo "Error: Checksum verification failed: this usually means the server failed to download correctly."\n\t\t\techo "Expected: $SHA256_HASH"\n\t\t\techo "Got: $CALCULATED_HASH"\n\n\t\t\trm -f vscode-server.tar.gz\n\n\t\t\tprint_install_results_and_exit 1\n\t\telse\n\t\t\techo "Checksum verification passed: $CALCULATED_HASH"\n\t\tfi\n\telse\n\t\techo "Skipping checksum verification"\n\tfi\n\n\ttar -xf vscode-server.tar.gz --strip-components 1\n\tif (( $? > 0 )); then\n\t\techo "Error while extracting server contents"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tif [[ ! -f $SERVER_SCRIPT ]]; then\n\t\techo "Error server contents are corrupted"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\trm -f vscode-server.tar.gz\n\n\tpopd > /dev/null\nelse\n\techo "Server script already installed in $SERVER_SCRIPT"\nfi\n\nif [[ "$IS_EXEC_SERVER" != "true" ]]; then\n\t# Try to find if server is already running\n\tif [[ -f $SERVER_PIDFILE ]]; then\n\t\tSERVER_PID="$(cat $SERVER_PIDFILE)"\n\t\tif [[ -z $SERVER_PID ]]; then\n\t\t\tSERVER_PID=$(pgrep -f "$SERVER_SCRIPT")\n\t\t\tif [[ -n $SERVER_PID ]]; then\n\t\t\t\ttouch $SERVER_PIDFILE\n\t\t\t\techo $SERVER_PID > $SERVER_PIDFILE\n\t\t\tfi\n\t\tfi\n\t\tif [[ -n $SERVER_PID ]]; then\n\t\t\tif [[ -n $(ps -o pid,args -p $SERVER_PID | grep $SERVER_SCRIPT) ]]; then\n\t\t\t\tSERVER_RUNNING_PROCESS="$SERVER_PID"\n\t\t\t\techo "Found running server process: $SERVER_RUNNING_PROCESS"\n\t\t\tfi\n\t\tfi\n\telse\n\t\tSERVER_RUNNING_PROCESS="$(pgrep -f "$SERVER_SCRIPT")"\n\t\tif [[ -z $SERVER_RUNNING_PROCESS ]]; then\n\t\t\tSERVER_PID=\n\t\telse\n\t\t\tSERVER_PID=$SERVER_RUNNING_PROCESS\n\t\t\ttouch $SERVER_PIDFILE\n\t\t\techo $SERVER_PID > $SERVER_PIDFILE\n\t\tfi\n\tfi\n\n\tif [[ -z $SERVER_RUNNING_PROCESS ]]; then\n\t\tif [[ -f $SERVER_LOGFILE ]]; then\n\t\t\trm $SERVER_LOGFILE\n\t\tfi\n\t\tif [[ -f $SERVER_TOKENFILE ]]; then\n\t\t\trm $SERVER_TOKENFILE\n\t\tfi\n\n\t\ttouch $SERVER_TOKENFILE\n\t\tchmod 600 $SERVER_TOKENFILE\n\t\tCONNECTION_TOKEN="${_}"\n\t\techo $CONNECTION_TOKEN > $SERVER_TOKENFILE\n\n\t\t$SERVER_SCRIPT --start-server --host=127.0.0.1 $SERVER_LISTEN_FLAG $SERVER_INITIAL_EXTENSIONS --connection-token-file $SERVER_TOKENFILE --telemetry-level off --enable-remote-auto-shutdown --accept-server-license-terms &> $SERVER_LOGFILE &\n\t\techo $! > $SERVER_PIDFILE\n\t\tSERVER_PID=$(cat $SERVER_PIDFILE)\n\telse\n\t\techo "Server script is already running $SERVER_SCRIPT"\n\tfi\n\n\tif [[ -f $SERVER_TOKENFILE && -n $(cat $SERVER_TOKENFILE) ]]; then\n\t\tCONNECTION_TOKEN="$(cat $SERVER_TOKENFILE)"\n\telse\n\t\techo "Error server token file not found $SERVER_TOKENFILE"\n\t\tif [[ -n $SERVER_PID ]]; then\n\t\t\tkill_running_server $SERVER_PID\n\t\tfi\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tif [[ -f $SERVER_LOGFILE ]]; then\n\t\tfor i in {1..5}; do\n\t\t\tLISTENING_ON="$(cat $SERVER_LOGFILE | grep -E 'Extension host agent listening on .+' | sed 's/Extension host agent listening on //')"\n\t\t\tif [[ -n $LISTENING_ON ]]; then\n\t\t\t\tbreak\n\t\t\tfi\n\t\t\tsleep 0.5\n\t\tdone\n\n\tif [[ -z $LISTENING_ON ]]; then\n\t\t\techo "Error server did not start sucessfully"\n\t\t\tif [[ -n $SERVER_PID ]]; then\n\t\t\t\tkill_running_server $SERVER_PID\n\t\t\tfi\n\t\t\tprint_install_results_and_exit 1\n\t\tfi\n\telse\n\t\techo "Error server log file not found $SERVER_LOGFILE"\n\t\tif [[ -n $SERVER_PID ]]; then\n\t\t\tkill_running_server $SERVER_PID\n\t\tfi\n\t\tprint_install_results_and_exit 1\n\tfi\nelse\n\tCLI_DOWNLOAD_URL="$(echo "${g.replace(/\$\{/g,"\\${")}" | sed "s/\\\${quality}/$DISTRO_QUALITY/g" | sed "s/\\\${vscodeVersion}/$DISTRO_VSCODE_VERSION/g" | sed "s/\\\${commit}/$DISTRO_COMMIT/g" | sed "s/\\\${os}/$PLATFORM/g" | sed "s/\\\${arch}/$SERVER_ARCH/g" | sed "s/\\\${release}/$DISTRO_VSCODIUM_RELEASE/g" | sed "s/\\\${windsurfVersion}/$DISTRO_WINDSURF_VERSION/g")"\n\tOUTPUT_LOGFILE=$CLI_LOGFILE\n\tOUTPUT_TOKENFILE=$CLI_TOKENFILE\n\tOUTPUT_PIDFILE=$CLI_PIDFILE\n\tOUTPUT_SCRIPT=$CLI_PATH\n\techo "Installing CLI Exec Server..."\n\n\tpushd $SERVER_DATA_DIR\n\tif [[ -f $CLI_PATH ]]; then\n\t\techo "CLI already installed in $CLI_PATH."\n\telse\n\t\techo "CLI not found in $CLI_PATH, downloading."\n\n\t\trm -f $CLI_PATH\n\t\trm -f $CLI_PIDFILE\n\t\trm -f $CLI_LOGFILE\n\t\trm -f $CLI_TOKENFILE\n\n\t\ttemp_file=$(mktemp)\n\t\tif [[ ! -z $(which curl) ]]; then\n\t\t\tcurl --retry 3 --connect-timeout 10 --location --show-error --silent --output $temp_file $CLI_DOWNLOAD_URL\n\t\telif [ ! -z $(which wget) ]; then\n\t\t\twget --tries=3 --timeout=10 --continue --quiet -O $temp_file $CLI_DOWNLOAD_URL\n\t\telse\n\t\t\techo "Error no tool to download CLI binary"\n\t\t\tprint_install_results_and_exit 1\n\t\tfi\n\n\t\tif (( $? > 0 )); then\n\t\t\techo "Error downloading CLI from $CLI_DOWNLOAD_URL"\n\t\t\tprint_install_results_and_exit 1\n\t\tfi\n\n\t\ttar -xf $temp_file -O > $CLI_PATH\n\t\tif (( $? > 0 )); then\n\t\t\techo "Error while extracting CLI contents"\n\t\t\tprint_install_results_and_exit 1\n\t\tfi\n\n\t\tif [[ ! -f $CLI_PATH ]]; then\n\t\t\techo "Error CLI contents are corrupted"\n\t\t\tprint_install_results_and_exit 1\n\t\tfi\n\n\t\tchmod +x $CLI_PATH\n\n\t\techo "Downloaded CLI to $CLI_PATH."\n\tfi\n\n\trm -f $CLI_PIDFILE\n\trm -f $CLI_LOGFILE\n\trm -f $CLI_TOKENFILE\n\n\ttouch $CLI_LOGFILE\n\tchmod 600 $CLI_LOGFILE\n\ttouch $CLI_TOKENFILE\n\tchmod 600 $CLI_TOKENFILE\n\n\tCONNECTION_TOKEN="${_}"\n\techo $CONNECTION_TOKEN > $CLI_TOKENFILE\n\n\t"$CLI_PATH" command-shell --on-host 127.0.0.1 --cli-data-dir "$CLI_DATA_DIR" --parent-process-id "$PPID" --require-token "${_}" > "$CLI_LOGFILE" 2>&1 < /dev/null &\n\n\techo $! > $CLI_PIDFILE\n\n\tif [[ -f $CLI_TOKENFILE ]]; then\n\t\tCONNECTION_TOKEN="$(cat $CLI_TOKENFILE)"\n\telse\n\t\techo "Error CLI token file not found $CLI_TOKENFILE"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tif [[ -f $CLI_LOGFILE ]]; then\n\t\tfor i in {1..5}; do\n\t\t\tLISTENING_ON="$(cat $CLI_LOGFILE | grep -E 'Listening on 127.0.0.1:.+' | sed 's/Listening on 127.0.0.1://')"\n\t\t\tif [[ -n $LISTENING_ON ]]; then\n\t\t\t\tbreak\n\t\t\tfi\n\t\t\tsleep 0.5\n\t\tdone\n\n\t\tif [[ -z $LISTENING_ON ]]; then\n\t\t\techo "Error CLI did not start sucessfully"\n\t\t\tprint_install_results_and_exit 1\n\t\tfi\n\telse\n\t\techo "Error CLI log file not found: $CLI_LOGFILE"\n\t\tprint_install_results_and_exit 1\n\tfi\n\tpopd\nfi\n\n# Finish server setup\nprint_install_results_and_wait\n`};const a=o(s(6982));class h extends Error{constructor(t){super(t)}}e.ServerInstallError=h},1968:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e}),a=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(e,"__esModule",{value:!0}),e.getSSHConfigPath=S;const h=o(s(857)),c=o(s(9896)),l=o(s(6928)),u=a(s(1182)),d=o(s(1398)),f=s(5456),p=s(592),m="win32"===process.platform?l.resolve(process.env.ALLUSERSPROFILE||"C:\\ProgramData","ssh\\ssh_config"):"/etc/ssh/ssh_config",g=l.resolve(h.homedir(),".ssh/config");function S(){const t=d.workspace.getConfiguration("remote.windsurfSSH").get("configFile");return t?(0,f.untildify)(t):g}function y(t){return t.type===u.default.DIRECTIVE}function w(t){return y(t)&&"Host"===t.param&&!!t.value&&!!t.config}function _(t){return y(t)&&"Include"===t.param&&!!t.value}const v={host:"Host",hostname:"HostName",user:"User",port:"Port",identityagent:"IdentityAgent",identitiesonly:"IdentitiesOnly",identityfile:"IdentityFile",forwardagent:"ForwardAgent",preferredauthentications:"PreferredAuthentications",proxyjump:"ProxyJump",proxycommand:"ProxyCommand",include:"Include"};function E(t){for(const s of t)y(s)&&((e=s).param=v[e.param.toLowerCase()]||e.param),w(s)&&E(s.config);var e;return t}async function b(t,e){let s="";await(0,f.exists)(t)&&(s=(await c.promises.readFile(t,"utf8")).trim());const i=E(u.default.parse(s)),r=[];for(let t=0;t<i.length;t++){const s=i[t];if(_(s)){const i=s.value.split(",").map((t=>t.trim())),n=[];for(const t of i){const s=await(0,p.glob)((0,f.normalizeToSlash)((0,f.untildify)(t)),{absolute:!0,cwd:(0,f.normalizeToSlash)(l.dirname(e?g:m))});for(const t of s)n.push(await b(t,e))}r.push([t,n])}}for(const[t,e]of r.reverse())i.splice(t,1,...e.flat());return i}class I{static async loadFromFS(){const t=await b(S(),!0);return t.push(...await b(m,!1)),new I(t)}constructor(t){this.sshConfig=t}getAllConfiguredHosts(){const t=new Set;for(const e of this.sshConfig)if(w(e)){const s=Array.isArray(e.value)?e.value:[{val:e.value,separator:" "}];for(const e of s)/^!/.test(e.val)||/[?*]/.test(e.val)||t.add(e.val)}return[...t.keys()]}getHostConfiguration(t){return this.sshConfig.compute(t)}}e.default=I},5624:(t,e)=>{"use strict";Object.defineProperty(e,"__esModule",{value:!0});class s{constructor(t,e,s){this.hostname=t,this.user=e,this.port=s}static parse(t){let e;const i=t.lastIndexOf("@");let r;-1!==i&&(e=t.substring(0,i));const n=t.lastIndexOf(":");-1!==n&&(r=parseInt(t.substring(n+1),10));const o=-1!==i?i+1:0,a=-1!==n?n:t.length,h=t.substring(o,a);return new s(h,e,r)}toString(){let t=this.hostname;return this.user&&(t=`${this.user}@`+t),this.port&&(t+=`:${this.port}`),t}static parseEncoded(t){try{const e=JSON.parse(Buffer.from(t,"hex").toString());return new s(e.hostName,e.user,e.port)}catch{}return s.parse(t.replace(/\\x([0-9a-f]{2})/g,((t,e)=>String.fromCharCode(parseInt(e,16)))))}toEncodedString(){return this.toString().replace(/[A-Z]/g,(t=>`\\x${t.charCodeAt(0).toString(16).toLowerCase()}`))}}e.default=s},5376:function(t,e,s){"use strict";var i,r=this&&this.__createBinding||(Object.create?function(t,e,s,i){void 0===i&&(i=s);var r=Object.getOwnPropertyDescriptor(e,s);r&&!("get"in r?!e.__esModule:r.writable||r.configurable)||(r={enumerable:!0,get:function(){return e[s]}}),Object.defineProperty(t,i,r)}:function(t,e,s,i){void 0===i&&(i=s),t[i]=e[s]}),n=this&&this.__setModuleDefault||(Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e}),o=this&&this.__importStar||(i=function(t){return i=Object.getOwnPropertyNames||function(t){var e=[];for(var s in t)Object.prototype.hasOwnProperty.call(t,s)&&(e[e.length]=s);return e},i(t)},function(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var s=i(t),o=0;o<s.length;o++)"default"!==s[o]&&r(e,t,s[o]);return n(e,t),e});Object.defineProperty(e,"__esModule",{value:!0}),e.findRandomPort=function(){return new Promise(((t,e)=>{const s=h.createServer({pauseOnConnect:!0});s.on("error",e),s.on("listening",(()=>{const e=s.address().port;s.close((()=>t(e)))})),s.listen(0,"127.0.0.1")}))},e.findAskpassScriptPath=function(){return"win32"===process.platform?f(u):f(l)},e.findAskpassJSPath=function(){return f(d)};const a=o(s(6928)),h=o(s(9278)),c="windsurf-remote-openssh",l="launchSSHAskpass.sh",u="launchSSHAskpass.bat",d="sshAskClient.js";function f(t){const e=function(t,e){const s=t.split(a.sep).reverse();for(const[t,i]of s.entries())if(i===e)return s.slice(t).reverse().join(a.sep)}(__dirname,c);if(void 0!==e)return a.join(e,"scripts",t)}},1398:t=>{"use strict";t.exports=require("vscode")},181:t=>{"use strict";t.exports=require("buffer")},5317:t=>{"use strict";t.exports=require("child_process")},6982:t=>{"use strict";t.exports=require("crypto")},4434:t=>{"use strict";t.exports=require("events")},9896:t=>{"use strict";t.exports=require("fs")},8611:t=>{"use strict";t.exports=require("http")},9278:t=>{"use strict";t.exports=require("net")},857:t=>{"use strict";t.exports=require("os")},6928:t=>{"use strict";t.exports=require("path")},2203:t=>{"use strict";t.exports=require("stream")},5530:(t,e,s)=>{"use strict";s.r(e),s.d(e,{DecodeError:()=>h,Decoder:()=>P,EXT_TIMESTAMP:()=>d,Encoder:()=>b,ExtData:()=>a,ExtensionCodec:()=>v,decode:()=>$,decodeArrayStream:()=>F,decodeAsync:()=>U,decodeMulti:()=>N,decodeMultiStream:()=>j,decodeTimestampExtension:()=>w,decodeTimestampToTimeSpec:()=>y,encode:()=>I,encodeDateToTimeSpec:()=>g,encodeTimeSpecToTimestamp:()=>m,encodeTimestampExtension:()=>S});const i=new TextEncoder;const r=4096;function n(t,e,s){let i=e;const n=i+s,o=[];let a="";for(;i<n;){const e=t[i++];if(128&e)if(192==(224&e)){const s=63&t[i++];o.push((31&e)<<6|s)}else if(224==(240&e)){const s=63&t[i++],r=63&t[i++];o.push((31&e)<<12|s<<6|r)}else if(240==(248&e)){let s=(7&e)<<18|(63&t[i++])<<12|(63&t[i++])<<6|63&t[i++];s>65535&&(s-=65536,o.push(s>>>10&1023|55296),s=56320|1023&s),o.push(s)}else o.push(e);else o.push(e);o.length>=r&&(a+=String.fromCharCode(...o),o.length=0)}return o.length>0&&(a+=String.fromCharCode(...o)),a}const o=new TextDecoder;class a{constructor(t,e){this.type=t,this.data=e}}class h extends Error{constructor(t){super(t);const e=Object.create(h.prototype);Object.setPrototypeOf(this,e),Object.defineProperty(this,"name",{configurable:!0,enumerable:!1,value:h.name})}}const c=4294967295;function l(t,e,s){const i=Math.floor(s/4294967296),r=s;t.setUint32(e,i),t.setUint32(e+4,r)}function u(t,e){return 4294967296*t.getInt32(e)+t.getUint32(e+4)}const d=-1,f=4294967295,p=17179869183;function m({sec:t,nsec:e}){if(t>=0&&e>=0&&t<=p){if(0===e&&t<=f){const e=new Uint8Array(4);return new DataView(e.buffer).setUint32(0,t),e}{const s=t/4294967296,i=4294967295&t,r=new Uint8Array(8),n=new DataView(r.buffer);return n.setUint32(0,e<<2|3&s),n.setUint32(4,i),r}}{const s=new Uint8Array(12),i=new DataView(s.buffer);return i.setUint32(0,e),l(i,4,t),s}}function g(t){const e=t.getTime(),s=Math.floor(e/1e3),i=1e6*(e-1e3*s),r=Math.floor(i/1e9);return{sec:s+r,nsec:i-1e9*r}}function S(t){return t instanceof Date?m(g(t)):null}function y(t){const e=new DataView(t.buffer,t.byteOffset,t.byteLength);switch(t.byteLength){case 4:return{sec:e.getUint32(0),nsec:0};case 8:{const t=e.getUint32(0);return{sec:4294967296*(3&t)+e.getUint32(4),nsec:t>>>2}}case 12:return{sec:u(e,4),nsec:e.getUint32(0)};default:throw new h(`Unrecognized data size for timestamp (expected 4, 8, or 12): ${t.length}`)}}function w(t){const e=y(t);return new Date(1e3*e.sec+e.nsec/1e6)}const _={type:d,encode:S,decode:w};class v{constructor(){this.builtInEncoders=[],this.builtInDecoders=[],this.encoders=[],this.decoders=[],this.register(_)}register({type:t,encode:e,decode:s}){if(t>=0)this.encoders[t]=e,this.decoders[t]=s;else{const i=-1-t;this.builtInEncoders[i]=e,this.builtInDecoders[i]=s}}tryToEncode(t,e){for(let s=0;s<this.builtInEncoders.length;s++){const i=this.builtInEncoders[s];if(null!=i){const r=i(t,e);if(null!=r)return new a(-1-s,r)}}for(let s=0;s<this.encoders.length;s++){const i=this.encoders[s];if(null!=i){const r=i(t,e);if(null!=r)return new a(s,r)}}return t instanceof a?t:null}decode(t,e,s){const i=e<0?this.builtInDecoders[-1-e]:this.decoders[e];return i?i(t,e,s):new a(e,t)}}function E(t){return t instanceof Uint8Array?t:ArrayBuffer.isView(t)?new Uint8Array(t.buffer,t.byteOffset,t.byteLength):function(t){return t instanceof ArrayBuffer||"undefined"!=typeof SharedArrayBuffer&&t instanceof SharedArrayBuffer}(t)?new Uint8Array(t):Uint8Array.from(t)}v.defaultCodec=new v;class b{constructor(t){this.entered=!1,this.extensionCodec=t?.extensionCodec??v.defaultCodec,this.context=t?.context,this.useBigInt64=t?.useBigInt64??!1,this.maxDepth=t?.maxDepth??100,this.initialBufferSize=t?.initialBufferSize??2048,this.sortKeys=t?.sortKeys??!1,this.forceFloat32=t?.forceFloat32??!1,this.ignoreUndefined=t?.ignoreUndefined??!1,this.forceIntegerToFloat=t?.forceIntegerToFloat??!1,this.pos=0,this.view=new DataView(new ArrayBuffer(this.initialBufferSize)),this.bytes=new Uint8Array(this.view.buffer)}clone(){return new b({extensionCodec:this.extensionCodec,context:this.context,useBigInt64:this.useBigInt64,maxDepth:this.maxDepth,initialBufferSize:this.initialBufferSize,sortKeys:this.sortKeys,forceFloat32:this.forceFloat32,ignoreUndefined:this.ignoreUndefined,forceIntegerToFloat:this.forceIntegerToFloat})}reinitializeState(){this.pos=0}encodeSharedRef(t){if(this.entered)return this.clone().encodeSharedRef(t);try{return this.entered=!0,this.reinitializeState(),this.doEncode(t,1),this.bytes.subarray(0,this.pos)}finally{this.entered=!1}}encode(t){if(this.entered)return this.clone().encode(t);try{return this.entered=!0,this.reinitializeState(),this.doEncode(t,1),this.bytes.slice(0,this.pos)}finally{this.entered=!1}}doEncode(t,e){if(e>this.maxDepth)throw new Error(`Too deep objects in depth ${e}`);null==t?this.encodeNil():"boolean"==typeof t?this.encodeBoolean(t):"number"==typeof t?this.forceIntegerToFloat?this.encodeNumberAsFloat(t):this.encodeNumber(t):"string"==typeof t?this.encodeString(t):this.useBigInt64&&"bigint"==typeof t?this.encodeBigInt64(t):this.encodeObject(t,e)}ensureBufferSizeToWrite(t){const e=this.pos+t;this.view.byteLength<e&&this.resizeBuffer(2*e)}resizeBuffer(t){const e=new ArrayBuffer(t),s=new Uint8Array(e),i=new DataView(e);s.set(this.bytes),this.view=i,this.bytes=s}encodeNil(){this.writeU8(192)}encodeBoolean(t){!1===t?this.writeU8(194):this.writeU8(195)}encodeNumber(t){!this.forceIntegerToFloat&&Number.isSafeInteger(t)?t>=0?t<128?this.writeU8(t):t<256?(this.writeU8(204),this.writeU8(t)):t<65536?(this.writeU8(205),this.writeU16(t)):t<4294967296?(this.writeU8(206),this.writeU32(t)):this.useBigInt64?this.encodeNumberAsFloat(t):(this.writeU8(207),this.writeU64(t)):t>=-32?this.writeU8(224|t+32):t>=-128?(this.writeU8(208),this.writeI8(t)):t>=-32768?(this.writeU8(209),this.writeI16(t)):t>=-2147483648?(this.writeU8(210),this.writeI32(t)):this.useBigInt64?this.encodeNumberAsFloat(t):(this.writeU8(211),this.writeI64(t)):this.encodeNumberAsFloat(t)}encodeNumberAsFloat(t){this.forceFloat32?(this.writeU8(202),this.writeF32(t)):(this.writeU8(203),this.writeF64(t))}encodeBigInt64(t){t>=BigInt(0)?(this.writeU8(207),this.writeBigUint64(t)):(this.writeU8(211),this.writeBigInt64(t))}writeStringHeader(t){if(t<32)this.writeU8(160+t);else if(t<256)this.writeU8(217),this.writeU8(t);else if(t<65536)this.writeU8(218),this.writeU16(t);else{if(!(t<4294967296))throw new Error(`Too long string: ${t} bytes in UTF-8`);this.writeU8(219),this.writeU32(t)}}encodeString(t){const e=function(t){const e=t.length;let s=0,i=0;for(;i<e;){let r=t.charCodeAt(i++);if(4294967168&r)if(4294965248&r){if(r>=55296&&r<=56319&&i<e){const e=t.charCodeAt(i);56320==(64512&e)&&(++i,r=((1023&r)<<10)+(1023&e)+65536)}s+=4294901760&r?4:3}else s+=2;else s++}return s}(t);var s,r,n;this.ensureBufferSizeToWrite(5+e),this.writeStringHeader(e),s=t,r=this.bytes,n=this.pos,s.length>50?function(t,e,s){i.encodeInto(t,e.subarray(s))}(s,r,n):function(t,e,s){const i=t.length;let r=s,n=0;for(;n<i;){let s=t.charCodeAt(n++);if(4294967168&s){if(4294965248&s){if(s>=55296&&s<=56319&&n<i){const e=t.charCodeAt(n);56320==(64512&e)&&(++n,s=((1023&s)<<10)+(1023&e)+65536)}4294901760&s?(e[r++]=s>>18&7|240,e[r++]=s>>12&63|128,e[r++]=s>>6&63|128):(e[r++]=s>>12&15|224,e[r++]=s>>6&63|128)}else e[r++]=s>>6&31|192;e[r++]=63&s|128}else e[r++]=s}}(s,r,n),this.pos+=e}encodeObject(t,e){const s=this.extensionCodec.tryToEncode(t,this.context);if(null!=s)this.encodeExtension(s);else if(Array.isArray(t))this.encodeArray(t,e);else if(ArrayBuffer.isView(t))this.encodeBinary(t);else{if("object"!=typeof t)throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(t)}`);this.encodeMap(t,e)}}encodeBinary(t){const e=t.byteLength;if(e<256)this.writeU8(196),this.writeU8(e);else if(e<65536)this.writeU8(197),this.writeU16(e);else{if(!(e<4294967296))throw new Error(`Too large binary: ${e}`);this.writeU8(198),this.writeU32(e)}const s=E(t);this.writeU8a(s)}encodeArray(t,e){const s=t.length;if(s<16)this.writeU8(144+s);else if(s<65536)this.writeU8(220),this.writeU16(s);else{if(!(s<4294967296))throw new Error(`Too large array: ${s}`);this.writeU8(221),this.writeU32(s)}for(const s of t)this.doEncode(s,e+1)}countWithoutUndefined(t,e){let s=0;for(const i of e)void 0!==t[i]&&s++;return s}encodeMap(t,e){const s=Object.keys(t);this.sortKeys&&s.sort();const i=this.ignoreUndefined?this.countWithoutUndefined(t,s):s.length;if(i<16)this.writeU8(128+i);else if(i<65536)this.writeU8(222),this.writeU16(i);else{if(!(i<4294967296))throw new Error(`Too large map object: ${i}`);this.writeU8(223),this.writeU32(i)}for(const i of s){const s=t[i];this.ignoreUndefined&&void 0===s||(this.encodeString(i),this.doEncode(s,e+1))}}encodeExtension(t){if("function"==typeof t.data){const e=t.data(this.pos+6),s=e.length;if(s>=4294967296)throw new Error(`Too large extension object: ${s}`);return this.writeU8(201),this.writeU32(s),this.writeI8(t.type),void this.writeU8a(e)}const e=t.data.length;if(1===e)this.writeU8(212);else if(2===e)this.writeU8(213);else if(4===e)this.writeU8(214);else if(8===e)this.writeU8(215);else if(16===e)this.writeU8(216);else if(e<256)this.writeU8(199),this.writeU8(e);else if(e<65536)this.writeU8(200),this.writeU16(e);else{if(!(e<4294967296))throw new Error(`Too large extension object: ${e}`);this.writeU8(201),this.writeU32(e)}this.writeI8(t.type),this.writeU8a(t.data)}writeU8(t){this.ensureBufferSizeToWrite(1),this.view.setUint8(this.pos,t),this.pos++}writeU8a(t){const e=t.length;this.ensureBufferSizeToWrite(e),this.bytes.set(t,this.pos),this.pos+=e}writeI8(t){this.ensureBufferSizeToWrite(1),this.view.setInt8(this.pos,t),this.pos++}writeU16(t){this.ensureBufferSizeToWrite(2),this.view.setUint16(this.pos,t),this.pos+=2}writeI16(t){this.ensureBufferSizeToWrite(2),this.view.setInt16(this.pos,t),this.pos+=2}writeU32(t){this.ensureBufferSizeToWrite(4),this.view.setUint32(this.pos,t),this.pos+=4}writeI32(t){this.ensureBufferSizeToWrite(4),this.view.setInt32(this.pos,t),this.pos+=4}writeF32(t){this.ensureBufferSizeToWrite(4),this.view.setFloat32(this.pos,t),this.pos+=4}writeF64(t){this.ensureBufferSizeToWrite(8),this.view.setFloat64(this.pos,t),this.pos+=8}writeU64(t){this.ensureBufferSizeToWrite(8),function(t,e,s){const i=s/4294967296,r=s;t.setUint32(e,i),t.setUint32(e+4,r)}(this.view,this.pos,t),this.pos+=8}writeI64(t){this.ensureBufferSizeToWrite(8),l(this.view,this.pos,t),this.pos+=8}writeBigUint64(t){this.ensureBufferSizeToWrite(8),this.view.setBigUint64(this.pos,t),this.pos+=8}writeBigInt64(t){this.ensureBufferSizeToWrite(8),this.view.setBigInt64(this.pos,t),this.pos+=8}}function I(t,e){return new b(e).encodeSharedRef(t)}function R(t){return`${t<0?"-":""}0x${Math.abs(t).toString(16).padStart(2,"0")}`}const O="array",T="map_key",k="map_value",D=t=>{if("string"==typeof t||"number"==typeof t)return t;throw new h("The type of key must be string or number but "+typeof t)};class A{constructor(){this.stack=[],this.stackHeadPosition=-1}get length(){return this.stackHeadPosition+1}top(){return this.stack[this.stackHeadPosition]}pushArrayState(t){const e=this.getUninitializedStateFromPool();e.type=O,e.position=0,e.size=t,e.array=new Array(t)}pushMapState(t){const e=this.getUninitializedStateFromPool();e.type=T,e.readCount=0,e.size=t,e.map={}}getUninitializedStateFromPool(){if(this.stackHeadPosition++,this.stackHeadPosition===this.stack.length){const t={type:void 0,size:0,array:void 0,position:0,readCount:0,map:void 0,key:null};this.stack.push(t)}return this.stack[this.stackHeadPosition]}release(t){if(this.stack[this.stackHeadPosition]!==t)throw new Error("Invalid stack state. Released state is not on top of the stack.");if(t.type===O){const e=t;e.size=0,e.array=void 0,e.position=0,e.type=void 0}if(t.type===T||t.type===k){const e=t;e.size=0,e.map=void 0,e.readCount=0,e.type=void 0}this.stackHeadPosition--}reset(){this.stack.length=0,this.stackHeadPosition=-1}}const C=new DataView(new ArrayBuffer(0)),L=new Uint8Array(C.buffer);try{C.getInt8(0)}catch(t){if(!(t instanceof RangeError))throw new Error("This module is not supported in the current JavaScript engine because DataView does not throw RangeError on out-of-bounds access")}const x=new RangeError("Insufficient data"),B=new class{constructor(t=16,e=16){this.hit=0,this.miss=0,this.maxKeyLength=t,this.maxLengthPerKey=e,this.caches=[];for(let t=0;t<this.maxKeyLength;t++)this.caches.push([])}canBeCached(t){return t>0&&t<=this.maxKeyLength}find(t,e,s){const i=this.caches[s-1];t:for(const r of i){const i=r.bytes;for(let r=0;r<s;r++)if(i[r]!==t[e+r])continue t;return r.str}return null}store(t,e){const s=this.caches[t.length-1],i={bytes:t,str:e};s.length>=this.maxLengthPerKey?s[Math.random()*s.length|0]=i:s.push(i)}decode(t,e,s){const i=this.find(t,e,s);if(null!=i)return this.hit++,i;this.miss++;const r=n(t,e,s),o=Uint8Array.prototype.slice.call(t,e,e+s);return this.store(o,r),r}};class P{constructor(t){this.totalPos=0,this.pos=0,this.view=C,this.bytes=L,this.headByte=-1,this.stack=new A,this.entered=!1,this.extensionCodec=t?.extensionCodec??v.defaultCodec,this.context=t?.context,this.useBigInt64=t?.useBigInt64??!1,this.rawStrings=t?.rawStrings??!1,this.maxStrLength=t?.maxStrLength??c,this.maxBinLength=t?.maxBinLength??c,this.maxArrayLength=t?.maxArrayLength??c,this.maxMapLength=t?.maxMapLength??c,this.maxExtLength=t?.maxExtLength??c,this.keyDecoder=void 0!==t?.keyDecoder?t.keyDecoder:B,this.mapKeyConverter=t?.mapKeyConverter??D}clone(){return new P({extensionCodec:this.extensionCodec,context:this.context,useBigInt64:this.useBigInt64,rawStrings:this.rawStrings,maxStrLength:this.maxStrLength,maxBinLength:this.maxBinLength,maxArrayLength:this.maxArrayLength,maxMapLength:this.maxMapLength,maxExtLength:this.maxExtLength,keyDecoder:this.keyDecoder})}reinitializeState(){this.totalPos=0,this.headByte=-1,this.stack.reset()}setBuffer(t){const e=E(t);this.bytes=e,this.view=new DataView(e.buffer,e.byteOffset,e.byteLength),this.pos=0}appendBuffer(t){if(-1!==this.headByte||this.hasRemaining(1)){const e=this.bytes.subarray(this.pos),s=E(t),i=new Uint8Array(e.length+s.length);i.set(e),i.set(s,e.length),this.setBuffer(i)}else this.setBuffer(t)}hasRemaining(t){return this.view.byteLength-this.pos>=t}createExtraByteError(t){const{view:e,pos:s}=this;return new RangeError(`Extra ${e.byteLength-s} of ${e.byteLength} byte(s) found at buffer[${t}]`)}decode(t){if(this.entered)return this.clone().decode(t);try{this.entered=!0,this.reinitializeState(),this.setBuffer(t);const e=this.doDecodeSync();if(this.hasRemaining(1))throw this.createExtraByteError(this.pos);return e}finally{this.entered=!1}}*decodeMulti(t){if(this.entered){const e=this.clone();yield*e.decodeMulti(t)}else try{for(this.entered=!0,this.reinitializeState(),this.setBuffer(t);this.hasRemaining(1);)yield this.doDecodeSync()}finally{this.entered=!1}}async decodeAsync(t){if(this.entered)return this.clone().decodeAsync(t);try{this.entered=!0;let e,s=!1;for await(const i of t){if(s)throw this.entered=!1,this.createExtraByteError(this.totalPos);this.appendBuffer(i);try{e=this.doDecodeSync(),s=!0}catch(t){if(!(t instanceof RangeError))throw t}this.totalPos+=this.pos}if(s){if(this.hasRemaining(1))throw this.createExtraByteError(this.totalPos);return e}const{headByte:i,pos:r,totalPos:n}=this;throw new RangeError(`Insufficient data in parsing ${R(i)} at ${n} (${r} in the current buffer)`)}finally{this.entered=!1}}decodeArrayStream(t){return this.decodeMultiAsync(t,!0)}decodeStream(t){return this.decodeMultiAsync(t,!1)}async*decodeMultiAsync(t,e){if(this.entered){const s=this.clone();yield*s.decodeMultiAsync(t,e)}else try{this.entered=!0;let s=e,i=-1;for await(const r of t){if(e&&0===i)throw this.createExtraByteError(this.totalPos);this.appendBuffer(r),s&&(i=this.readArraySize(),s=!1,this.complete());try{for(;yield this.doDecodeSync(),0!=--i;);}catch(t){if(!(t instanceof RangeError))throw t}this.totalPos+=this.pos}}finally{this.entered=!1}}doDecodeSync(){t:for(;;){const t=this.readHeadByte();let e;if(t>=224)e=t-256;else if(t<192)if(t<128)e=t;else if(t<144){const s=t-128;if(0!==s){this.pushMapState(s),this.complete();continue t}e={}}else if(t<160){const s=t-144;if(0!==s){this.pushArrayState(s),this.complete();continue t}e=[]}else{const s=t-160;e=this.decodeString(s,0)}else if(192===t)e=null;else if(194===t)e=!1;else if(195===t)e=!0;else if(202===t)e=this.readF32();else if(203===t)e=this.readF64();else if(204===t)e=this.readU8();else if(205===t)e=this.readU16();else if(206===t)e=this.readU32();else if(207===t)e=this.useBigInt64?this.readU64AsBigInt():this.readU64();else if(208===t)e=this.readI8();else if(209===t)e=this.readI16();else if(210===t)e=this.readI32();else if(211===t)e=this.useBigInt64?this.readI64AsBigInt():this.readI64();else if(217===t){const t=this.lookU8();e=this.decodeString(t,1)}else if(218===t){const t=this.lookU16();e=this.decodeString(t,2)}else if(219===t){const t=this.lookU32();e=this.decodeString(t,4)}else if(220===t){const t=this.readU16();if(0!==t){this.pushArrayState(t),this.complete();continue t}e=[]}else if(221===t){const t=this.readU32();if(0!==t){this.pushArrayState(t),this.complete();continue t}e=[]}else if(222===t){const t=this.readU16();if(0!==t){this.pushMapState(t),this.complete();continue t}e={}}else if(223===t){const t=this.readU32();if(0!==t){this.pushMapState(t),this.complete();continue t}e={}}else if(196===t){const t=this.lookU8();e=this.decodeBinary(t,1)}else if(197===t){const t=this.lookU16();e=this.decodeBinary(t,2)}else if(198===t){const t=this.lookU32();e=this.decodeBinary(t,4)}else if(212===t)e=this.decodeExtension(1,0);else if(213===t)e=this.decodeExtension(2,0);else if(214===t)e=this.decodeExtension(4,0);else if(215===t)e=this.decodeExtension(8,0);else if(216===t)e=this.decodeExtension(16,0);else if(199===t){const t=this.lookU8();e=this.decodeExtension(t,1)}else if(200===t){const t=this.lookU16();e=this.decodeExtension(t,2)}else{if(201!==t)throw new h(`Unrecognized type byte: ${R(t)}`);{const t=this.lookU32();e=this.decodeExtension(t,4)}}this.complete();const s=this.stack;for(;s.length>0;){const t=s.top();if(t.type===O){if(t.array[t.position]=e,t.position++,t.position!==t.size)continue t;e=t.array,s.release(t)}else{if(t.type===T){if("__proto__"===e)throw new h("The key __proto__ is not allowed");t.key=this.mapKeyConverter(e),t.type=k;continue t}if(t.map[t.key]=e,t.readCount++,t.readCount!==t.size){t.key=null,t.type=T;continue t}e=t.map,s.release(t)}}return e}}readHeadByte(){return-1===this.headByte&&(this.headByte=this.readU8()),this.headByte}complete(){this.headByte=-1}readArraySize(){const t=this.readHeadByte();switch(t){case 220:return this.readU16();case 221:return this.readU32();default:if(t<160)return t-144;throw new h(`Unrecognized array type byte: ${R(t)}`)}}pushMapState(t){if(t>this.maxMapLength)throw new h(`Max length exceeded: map length (${t}) > maxMapLengthLength (${this.maxMapLength})`);this.stack.pushMapState(t)}pushArrayState(t){if(t>this.maxArrayLength)throw new h(`Max length exceeded: array length (${t}) > maxArrayLength (${this.maxArrayLength})`);this.stack.pushArrayState(t)}decodeString(t,e){return!this.rawStrings||this.stateIsMapKey()?this.decodeUtf8String(t,e):this.decodeBinary(t,e)}decodeUtf8String(t,e){if(t>this.maxStrLength)throw new h(`Max length exceeded: UTF-8 byte length (${t}) > maxStrLength (${this.maxStrLength})`);if(this.bytes.byteLength<this.pos+e+t)throw x;const s=this.pos+e;let i;return i=this.stateIsMapKey()&&this.keyDecoder?.canBeCached(t)?this.keyDecoder.decode(this.bytes,s,t):function(t,e,s){return s>200?function(t,e,s){const i=t.subarray(e,e+s);return o.decode(i)}(t,e,s):n(t,e,s)}(this.bytes,s,t),this.pos+=e+t,i}stateIsMapKey(){return this.stack.length>0&&this.stack.top().type===T}decodeBinary(t,e){if(t>this.maxBinLength)throw new h(`Max length exceeded: bin length (${t}) > maxBinLength (${this.maxBinLength})`);if(!this.hasRemaining(t+e))throw x;const s=this.pos+e,i=this.bytes.subarray(s,s+t);return this.pos+=e+t,i}decodeExtension(t,e){if(t>this.maxExtLength)throw new h(`Max length exceeded: ext length (${t}) > maxExtLength (${this.maxExtLength})`);const s=this.view.getInt8(this.pos+e),i=this.decodeBinary(t,e+1);return this.extensionCodec.decode(i,s,this.context)}lookU8(){return this.view.getUint8(this.pos)}lookU16(){return this.view.getUint16(this.pos)}lookU32(){return this.view.getUint32(this.pos)}readU8(){const t=this.view.getUint8(this.pos);return this.pos++,t}readI8(){const t=this.view.getInt8(this.pos);return this.pos++,t}readU16(){const t=this.view.getUint16(this.pos);return this.pos+=2,t}readI16(){const t=this.view.getInt16(this.pos);return this.pos+=2,t}readU32(){const t=this.view.getUint32(this.pos);return this.pos+=4,t}readI32(){const t=this.view.getInt32(this.pos);return this.pos+=4,t}readU64(){const t=(e=this.view,s=this.pos,4294967296*e.getUint32(s)+e.getUint32(s+4));var e,s;return this.pos+=8,t}readI64(){const t=u(this.view,this.pos);return this.pos+=8,t}readU64AsBigInt(){const t=this.view.getBigUint64(this.pos);return this.pos+=8,t}readI64AsBigInt(){const t=this.view.getBigInt64(this.pos);return this.pos+=8,t}readF32(){const t=this.view.getFloat32(this.pos);return this.pos+=4,t}readF64(){const t=this.view.getFloat64(this.pos);return this.pos+=8,t}}function $(t,e){return new P(e).decode(t)}function N(t,e){return new P(e).decodeMulti(t)}function M(t){return null!=t[Symbol.asyncIterator]?t:async function*(t){const e=t.getReader();try{for(;;){const{done:t,value:s}=await e.read();if(t)return;yield s}}finally{e.releaseLock()}}(t)}async function U(t,e){const s=M(t);return new P(e).decodeAsync(s)}function F(t,e){const s=M(t);return new P(e).decodeArrayStream(s)}function j(t,e){const s=M(t);return new P(e).decodeStream(s)}},592:(t,e,s)=>{"use strict";s.r(e),s.d(e,{Glob:()=>Rs,default:()=>Ms,escape:()=>_,glob:()=>As,globIterate:()=>Ls,globIterateSync:()=>Cs,globStream:()=>ks,globStreamSync:()=>Ts,globSync:()=>Ds,hasMagic:()=>Os,iterate:()=>$s,iterateSync:()=>Ps,stream:()=>Bs,streamSync:()=>xs,sync:()=>Ns,unescape:()=>c});var i=s(8928);const r=t=>{if("string"!=typeof t)throw new TypeError("invalid pattern");if(t.length>65536)throw new TypeError("pattern is too long")},n={"[:alnum:]":["\\p{L}\\p{Nl}\\p{Nd}",!0],"[:alpha:]":["\\p{L}\\p{Nl}",!0],"[:ascii:]":["\\x00-\\x7f",!1],"[:blank:]":["\\p{Zs}\\t",!0],"[:cntrl:]":["\\p{Cc}",!0],"[:digit:]":["\\p{Nd}",!0],"[:graph:]":["\\p{Z}\\p{C}",!0,!0],"[:lower:]":["\\p{Ll}",!0],"[:print:]":["\\p{C}",!0],"[:punct:]":["\\p{P}",!0],"[:space:]":["\\p{Z}\\t\\r\\n\\v\\f",!0],"[:upper:]":["\\p{Lu}",!0],"[:word:]":["\\p{L}\\p{Nl}\\p{Nd}\\p{Pc}",!0],"[:xdigit:]":["A-Fa-f0-9",!1]},o=t=>t.replace(/[[\]\\-]/g,"\\$&"),a=t=>t.join(""),h=(t,e)=>{const s=e;if("["!==t.charAt(s))throw new Error("not in a brace expression");const i=[],r=[];let h=s+1,c=!1,l=!1,u=!1,d=!1,f=s,p="";t:for(;h<t.length;){const e=t.charAt(h);if("!"!==e&&"^"!==e||h!==s+1){if("]"===e&&c&&!u){f=h+1;break}if(c=!0,"\\"!==e||u){if("["===e&&!u)for(const[e,[o,a,c]]of Object.entries(n))if(t.startsWith(e,h)){if(p)return["$.",!1,t.length-s,!0];h+=e.length,c?r.push(o):i.push(o),l=l||a;continue t}u=!1,p?(e>p?i.push(o(p)+"-"+o(e)):e===p&&i.push(o(e)),p="",h++):t.startsWith("-]",h+1)?(i.push(o(e+"-")),h+=2):t.startsWith("-",h+1)?(p=e,h+=2):(i.push(o(e)),h++)}else u=!0,h++}else d=!0,h++}if(f<h)return["",!1,0,!1];if(!i.length&&!r.length)return["$.",!1,t.length-s,!0];if(0===r.length&&1===i.length&&/^\\?.$/.test(i[0])&&!d){return[(m=2===i[0].length?i[0].slice(-1):i[0],m.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&")),!1,f-s,!1]}var m;const g="["+(d?"^":"")+a(i)+"]",S="["+(d?"":"^")+a(r)+"]";return[i.length&&r.length?"("+g+"|"+S+")":i.length?g:S,l,f-s,!0]},c=(t,{windowsPathsNoEscape:e=!1}={})=>e?t.replace(/\[([^\/\\])\]/g,"$1"):t.replace(/((?!\\).|^)\[([^\/\\])\]/g,"$1$2").replace(/\\([^\/])/g,"$1"),l=new Set(["!","?","+","*","@"]),u=t=>l.has(t),d="(?!\\.)",f=new Set(["[","."]),p=new Set(["..","."]),m=new Set("().*{}+?[]^$\\!"),g="[^/]",S=g+"*?",y=g+"+?";class w{type;#t;#e;#s=!1;#i=[];#r;#n;#o;#a=!1;#h;#c;#l=!1;constructor(t,e,s={}){this.type=t,t&&(this.#e=!0),this.#r=e,this.#t=this.#r?this.#r.#t:this,this.#h=this.#t===this?s:this.#t.#h,this.#o=this.#t===this?[]:this.#t.#o,"!"!==t||this.#t.#a||this.#o.push(this),this.#n=this.#r?this.#r.#i.length:0}get hasMagic(){if(void 0!==this.#e)return this.#e;for(const t of this.#i)if("string"!=typeof t&&(t.type||t.hasMagic))return this.#e=!0;return this.#e}toString(){return void 0!==this.#c?this.#c:this.type?this.#c=this.type+"("+this.#i.map((t=>String(t))).join("|")+")":this.#c=this.#i.map((t=>String(t))).join("")}#u(){if(this!==this.#t)throw new Error("should only call on root");if(this.#a)return this;let t;for(this.toString(),this.#a=!0;t=this.#o.pop();){if("!"!==t.type)continue;let e=t,s=e.#r;for(;s;){for(let i=e.#n+1;!s.type&&i<s.#i.length;i++)for(const e of t.#i){if("string"==typeof e)throw new Error("string part in extglob AST??");e.copyIn(s.#i[i])}e=s,s=e.#r}}return this}push(...t){for(const e of t)if(""!==e){if("string"!=typeof e&&!(e instanceof w&&e.#r===this))throw new Error("invalid part: "+e);this.#i.push(e)}}toJSON(){const t=null===this.type?this.#i.slice().map((t=>"string"==typeof t?t:t.toJSON())):[this.type,...this.#i.map((t=>t.toJSON()))];return this.isStart()&&!this.type&&t.unshift([]),this.isEnd()&&(this===this.#t||this.#t.#a&&"!"===this.#r?.type)&&t.push({}),t}isStart(){if(this.#t===this)return!0;if(!this.#r?.isStart())return!1;if(0===this.#n)return!0;const t=this.#r;for(let e=0;e<this.#n;e++){const s=t.#i[e];if(!(s instanceof w&&"!"===s.type))return!1}return!0}isEnd(){if(this.#t===this)return!0;if("!"===this.#r?.type)return!0;if(!this.#r?.isEnd())return!1;if(!this.type)return this.#r?.isEnd();const t=this.#r?this.#r.#i.length:0;return this.#n===t-1}copyIn(t){"string"==typeof t?this.push(t):this.push(t.clone(this))}clone(t){const e=new w(this.type,t);for(const t of this.#i)e.copyIn(t);return e}static#d(t,e,s,i){let r=!1,n=!1,o=-1,a=!1;if(null===e.type){let h=s,c="";for(;h<t.length;){const s=t.charAt(h++);if(r||"\\"===s)r=!r,c+=s;else if(n)h===o+1?"^"!==s&&"!"!==s||(a=!0):"]"!==s||h===o+2&&a||(n=!1),c+=s;else if("["!==s)if(i.noext||!u(s)||"("!==t.charAt(h))c+=s;else{e.push(c),c="";const r=new w(s,e);h=w.#d(t,r,h,i),e.push(r)}else n=!0,o=h,a=!1,c+=s}return e.push(c),h}let h=s+1,c=new w(null,e);const l=[];let d="";for(;h<t.length;){const s=t.charAt(h++);if(r||"\\"===s)r=!r,d+=s;else if(n)h===o+1?"^"!==s&&"!"!==s||(a=!0):"]"!==s||h===o+2&&a||(n=!1),d+=s;else if("["!==s)if(u(s)&&"("===t.charAt(h)){c.push(d),d="";const e=new w(s,c);c.push(e),h=w.#d(t,e,h,i)}else if("|"!==s){if(")"===s)return""===d&&0===e.#i.length&&(e.#l=!0),c.push(d),d="",e.push(...l,c),h;d+=s}else c.push(d),d="",l.push(c),c=new w(null,e);else n=!0,o=h,a=!1,d+=s}return e.type=null,e.#e=void 0,e.#i=[t.substring(s-1)],h}static fromGlob(t,e={}){const s=new w(null,void 0,e);return w.#d(t,s,0,e),s}toMMPattern(){if(this!==this.#t)return this.#t.toMMPattern();const t=this.toString(),[e,s,i,r]=this.toRegExpSource();if(!(i||this.#e||this.#h.nocase&&!this.#h.nocaseMagicOnly&&t.toUpperCase()!==t.toLowerCase()))return s;const n=(this.#h.nocase?"i":"")+(r?"u":"");return Object.assign(new RegExp(`^${e}$`,n),{_src:e,_glob:t})}toRegExpSource(){if(this.#t===this&&this.#u(),!this.type){const t=this.isStart()&&this.isEnd(),e=this.#i.map((e=>{const[s,i,r,n]="string"==typeof e?w.#f(e,this.#e,t):e.toRegExpSource();return this.#e=this.#e||r,this.#s=this.#s||n,s})).join("");let s="";if(this.isStart()&&"string"==typeof this.#i[0]&&(1!==this.#i.length||!p.has(this.#i[0]))){const t=f,i=this.#h.dot&&t.has(e.charAt(0))||e.startsWith("\\.")&&t.has(e.charAt(2))||e.startsWith("\\.\\.")&&t.has(e.charAt(4)),r=!this.#h.dot&&t.has(e.charAt(0));s=i?"(?!\\.\\.?(?:$|/))":r?d:""}let i="";return this.isEnd()&&this.#t.#a&&"!"===this.#r?.type&&(i="(?:$|\\/)"),[s+e+i,c(e),this.#e=!!this.#e,this.#s]}const t="!"===this.type?"(?:(?!(?:":"(?:",e=this.#i.map((t=>{if("string"==typeof t)throw new Error("string type in extglob ast??");const[e,s,i,r]=t.toRegExpSource();return this.#s=this.#s||r,e})).filter((t=>!(this.isStart()&&this.isEnd()&&!t))).join("|");if(this.isStart()&&this.isEnd()&&!e&&"!"!==this.type){const t=this.toString();return this.#i=[t],this.type=null,this.#e=void 0,[t,c(this.toString()),!1,!1]}let s="";return s="!"===this.type&&this.#l?(this.isStart()&&!this.#h.dot?d:"")+y:t+e+("!"===this.type?"))"+(this.isStart()&&!this.#h.dot?d:"")+S+")":"@"===this.type?")":`)${this.type}`),[s,c(e),this.#e=!!this.#e,this.#s]}static#f(t,e,s=!1){let i=!1,r="",n=!1;for(let o=0;o<t.length;o++){const a=t.charAt(o);if(i)i=!1,r+=(m.has(a)?"\\":"")+a;else if("\\"!==a){if("["===a){const[s,i,a,c]=h(t,o);if(a){r+=s,n=n||i,o+=a-1,e=e||c;continue}}"*"!==a?"?"!==a?r+=a.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"):(r+=g,e=!0):(r+=s&&"*"===t?y:S,e=!0)}else o===t.length-1?r+="\\\\":i=!0}return[r,c(t),!!e,n]}}const _=(t,{windowsPathsNoEscape:e=!1}={})=>e?t.replace(/[?*()[\]]/g,"[$&]"):t.replace(/[?*()[\]\\]/g,"\\$&"),v=(t,e,s={})=>(r(e),!(!s.nocomment&&"#"===e.charAt(0))&&new q(e,s).match(t)),E=/^\*+([^+@!?\*\[\(]*)$/,b=t=>e=>!e.startsWith(".")&&e.endsWith(t),I=t=>e=>e.endsWith(t),R=t=>(t=t.toLowerCase(),e=>!e.startsWith(".")&&e.toLowerCase().endsWith(t)),O=t=>(t=t.toLowerCase(),e=>e.toLowerCase().endsWith(t)),T=/^\*+\.\*+$/,k=t=>!t.startsWith(".")&&t.includes("."),D=t=>"."!==t&&".."!==t&&t.includes("."),A=/^\.\*+$/,C=t=>"."!==t&&".."!==t&&t.startsWith("."),L=/^\*+$/,x=t=>0!==t.length&&!t.startsWith("."),B=t=>0!==t.length&&"."!==t&&".."!==t,P=/^\?+([^+@!?\*\[\(]*)?$/,$=([t,e=""])=>{const s=F([t]);return e?(e=e.toLowerCase(),t=>s(t)&&t.toLowerCase().endsWith(e)):s},N=([t,e=""])=>{const s=j([t]);return e?(e=e.toLowerCase(),t=>s(t)&&t.toLowerCase().endsWith(e)):s},M=([t,e=""])=>{const s=j([t]);return e?t=>s(t)&&t.endsWith(e):s},U=([t,e=""])=>{const s=F([t]);return e?t=>s(t)&&t.endsWith(e):s},F=([t])=>{const e=t.length;return t=>t.length===e&&!t.startsWith(".")},j=([t])=>{const e=t.length;return t=>t.length===e&&"."!==t&&".."!==t},H="object"==typeof process&&process?"object"==typeof process.env&&process.env&&process.env.__MINIMATCH_TESTING_PLATFORM__||process.platform:"posix";v.sep="win32"===H?"\\":"/";const V=Symbol("globstar **");v.GLOBSTAR=V,v.filter=(t,e={})=>s=>v(s,t,e);const z=(t,e={})=>Object.assign({},t,e);v.defaults=t=>{if(!t||"object"!=typeof t||!Object.keys(t).length)return v;const e=v;return Object.assign(((s,i,r={})=>e(s,i,z(t,r))),{Minimatch:class extends e.Minimatch{constructor(e,s={}){super(e,z(t,s))}static defaults(s){return e.defaults(z(t,s)).Minimatch}},AST:class extends e.AST{constructor(e,s,i={}){super(e,s,z(t,i))}static fromGlob(s,i={}){return e.AST.fromGlob(s,z(t,i))}},unescape:(s,i={})=>e.unescape(s,z(t,i)),escape:(s,i={})=>e.escape(s,z(t,i)),filter:(s,i={})=>e.filter(s,z(t,i)),defaults:s=>e.defaults(z(t,s)),makeRe:(s,i={})=>e.makeRe(s,z(t,i)),braceExpand:(s,i={})=>e.braceExpand(s,z(t,i)),match:(s,i,r={})=>e.match(s,i,z(t,r)),sep:e.sep,GLOBSTAR:V})};const W=(t,e={})=>(r(t),e.nobrace||!/\{(?:(?!\{).)*\}/.test(t)?[t]:i(t));v.braceExpand=W,v.makeRe=(t,e={})=>new q(t,e).makeRe(),v.match=(t,e,s={})=>{const i=new q(e,s);return t=t.filter((t=>i.match(t))),i.options.nonull&&!t.length&&t.push(e),t};const G=/[?*]|[+@!]\(.*?\)|\[|\]/;class q{options;set;pattern;windowsPathsNoEscape;nonegate;negate;comment;empty;preserveMultipleSlashes;partial;globSet;globParts;nocase;isWindows;platform;windowsNoMagicRoot;regexp;constructor(t,e={}){r(t),e=e||{},this.options=e,this.pattern=t,this.platform=e.platform||H,this.isWindows="win32"===this.platform,this.windowsPathsNoEscape=!!e.windowsPathsNoEscape||!1===e.allowWindowsEscape,this.windowsPathsNoEscape&&(this.pattern=this.pattern.replace(/\\/g,"/")),this.preserveMultipleSlashes=!!e.preserveMultipleSlashes,this.regexp=null,this.negate=!1,this.nonegate=!!e.nonegate,this.comment=!1,this.empty=!1,this.partial=!!e.partial,this.nocase=!!this.options.nocase,this.windowsNoMagicRoot=void 0!==e.windowsNoMagicRoot?e.windowsNoMagicRoot:!(!this.isWindows||!this.nocase),this.globSet=[],this.globParts=[],this.set=[],this.make()}hasMagic(){if(this.options.magicalBraces&&this.set.length>1)return!0;for(const t of this.set)for(const e of t)if("string"!=typeof e)return!0;return!1}debug(...t){}make(){const t=this.pattern,e=this.options;if(!e.nocomment&&"#"===t.charAt(0))return void(this.comment=!0);if(!t)return void(this.empty=!0);this.parseNegate(),this.globSet=[...new Set(this.braceExpand())],e.debug&&(this.debug=(...t)=>console.error(...t)),this.debug(this.pattern,this.globSet);const s=this.globSet.map((t=>this.slashSplit(t)));this.globParts=this.preprocess(s),this.debug(this.pattern,this.globParts);let i=this.globParts.map(((t,e,s)=>{if(this.isWindows&&this.windowsNoMagicRoot){const e=!(""!==t[0]||""!==t[1]||"?"!==t[2]&&G.test(t[2])||G.test(t[3])),s=/^[a-z]:/i.test(t[0]);if(e)return[...t.slice(0,4),...t.slice(4).map((t=>this.parse(t)))];if(s)return[t[0],...t.slice(1).map((t=>this.parse(t)))]}return t.map((t=>this.parse(t)))}));if(this.debug(this.pattern,i),this.set=i.filter((t=>-1===t.indexOf(!1))),this.isWindows)for(let t=0;t<this.set.length;t++){const e=this.set[t];""===e[0]&&""===e[1]&&"?"===this.globParts[t][2]&&"string"==typeof e[3]&&/^[a-z]:$/i.test(e[3])&&(e[2]="?")}this.debug(this.pattern,this.set)}preprocess(t){if(this.options.noglobstar)for(let e=0;e<t.length;e++)for(let s=0;s<t[e].length;s++)"**"===t[e][s]&&(t[e][s]="*");const{optimizationLevel:e=1}=this.options;return e>=2?(t=this.firstPhasePreProcess(t),t=this.secondPhasePreProcess(t)):t=e>=1?this.levelOneOptimize(t):this.adjascentGlobstarOptimize(t),t}adjascentGlobstarOptimize(t){return t.map((t=>{let e=-1;for(;-1!==(e=t.indexOf("**",e+1));){let s=e;for(;"**"===t[s+1];)s++;s!==e&&t.splice(e,s-e)}return t}))}levelOneOptimize(t){return t.map((t=>0===(t=t.reduce(((t,e)=>{const s=t[t.length-1];return"**"===e&&"**"===s?t:".."===e&&s&&".."!==s&&"."!==s&&"**"!==s?(t.pop(),t):(t.push(e),t)}),[])).length?[""]:t))}levelTwoFileOptimize(t){Array.isArray(t)||(t=this.slashSplit(t));let e=!1;do{if(e=!1,!this.preserveMultipleSlashes){for(let s=1;s<t.length-1;s++){const i=t[s];1===s&&""===i&&""===t[0]||"."!==i&&""!==i||(e=!0,t.splice(s,1),s--)}"."!==t[0]||2!==t.length||"."!==t[1]&&""!==t[1]||(e=!0,t.pop())}let s=0;for(;-1!==(s=t.indexOf("..",s+1));){const i=t[s-1];i&&"."!==i&&".."!==i&&"**"!==i&&(e=!0,t.splice(s-1,2),s-=2)}}while(e);return 0===t.length?[""]:t}firstPhasePreProcess(t){let e=!1;do{e=!1;for(let s of t){let i=-1;for(;-1!==(i=s.indexOf("**",i+1));){let r=i;for(;"**"===s[r+1];)r++;r>i&&s.splice(i+1,r-i);let n=s[i+1];const o=s[i+2],a=s[i+3];if(".."!==n)continue;if(!o||"."===o||".."===o||!a||"."===a||".."===a)continue;e=!0,s.splice(i,1);const h=s.slice(0);h[i]="**",t.push(h),i--}if(!this.preserveMultipleSlashes){for(let t=1;t<s.length-1;t++){const i=s[t];1===t&&""===i&&""===s[0]||"."!==i&&""!==i||(e=!0,s.splice(t,1),t--)}"."!==s[0]||2!==s.length||"."!==s[1]&&""!==s[1]||(e=!0,s.pop())}let r=0;for(;-1!==(r=s.indexOf("..",r+1));){const t=s[r-1];if(t&&"."!==t&&".."!==t&&"**"!==t){e=!0;const t=1===r&&"**"===s[r+1]?["."]:[];s.splice(r-1,2,...t),0===s.length&&s.push(""),r-=2}}}}while(e);return t}secondPhasePreProcess(t){for(let e=0;e<t.length-1;e++)for(let s=e+1;s<t.length;s++){const i=this.partsMatch(t[e],t[s],!this.preserveMultipleSlashes);i&&(t[e]=i,t[s]=[])}return t.filter((t=>t.length))}partsMatch(t,e,s=!1){let i=0,r=0,n=[],o="";for(;i<t.length&&r<e.length;)if(t[i]===e[r])n.push("b"===o?e[r]:t[i]),i++,r++;else if(s&&"**"===t[i]&&e[r]===t[i+1])n.push(t[i]),i++;else if(s&&"**"===e[r]&&t[i]===e[r+1])n.push(e[r]),r++;else if("*"!==t[i]||!e[r]||!this.options.dot&&e[r].startsWith(".")||"**"===e[r]){if("*"!==e[r]||!t[i]||!this.options.dot&&t[i].startsWith(".")||"**"===t[i])return!1;if("a"===o)return!1;o="b",n.push(e[r]),i++,r++}else{if("b"===o)return!1;o="a",n.push(t[i]),i++,r++}return t.length===e.length&&n}parseNegate(){if(this.nonegate)return;const t=this.pattern;let e=!1,s=0;for(let i=0;i<t.length&&"!"===t.charAt(i);i++)e=!e,s++;s&&(this.pattern=t.slice(s)),this.negate=e}matchOne(t,e,s=!1){const i=this.options;if(this.isWindows){const s=""===t[0]&&""===t[1]&&"?"===t[2]&&"string"==typeof t[3]&&/^[a-z]:$/i.test(t[3]),i=""===e[0]&&""===e[1]&&"?"===e[2]&&"string"==typeof e[3]&&/^[a-z]:$/i.test(e[3]);if(s&&i){const s=t[3],i=e[3];s.toLowerCase()===i.toLowerCase()&&(t[3]=i)}else if(i&&"string"==typeof t[0]){const s=e[3],i=t[0];s.toLowerCase()===i.toLowerCase()&&(e[3]=i,e=e.slice(3))}else if(s&&"string"==typeof e[0]){const s=t[3];s.toLowerCase()===e[0].toLowerCase()&&(e[0]=s,t=t.slice(3))}}const{optimizationLevel:r=1}=this.options;r>=2&&(t=this.levelTwoFileOptimize(t)),this.debug("matchOne",this,{file:t,pattern:e}),this.debug("matchOne",t.length,e.length);for(var n=0,o=0,a=t.length,h=e.length;n<a&&o<h;n++,o++){this.debug("matchOne loop");var c=e[o],l=t[n];if(this.debug(e,c,l),!1===c)return!1;if(c===V){this.debug("GLOBSTAR",[e,c,l]);var u=n,d=o+1;if(d===h){for(this.debug("** at the end");n<a;n++)if("."===t[n]||".."===t[n]||!i.dot&&"."===t[n].charAt(0))return!1;return!0}for(;u<a;){var f=t[u];if(this.debug("\nglobstar while",t,u,e,d,f),this.matchOne(t.slice(u),e.slice(d),s))return this.debug("globstar found match!",u,a,f),!0;if("."===f||".."===f||!i.dot&&"."===f.charAt(0)){this.debug("dot detected!",t,u,e,d);break}this.debug("globstar swallow a segment, and continue"),u++}return!(!s||(this.debug("\n>>> no match, partial?",t,u,e,d),u!==a))}let r;if("string"==typeof c?(r=l===c,this.debug("string match",c,l,r)):(r=c.test(l),this.debug("pattern match",c,l,r)),!r)return!1}if(n===a&&o===h)return!0;if(n===a)return s;if(o===h)return n===a-1&&""===t[n];throw new Error("wtf?")}braceExpand(){return W(this.pattern,this.options)}parse(t){r(t);const e=this.options;if("**"===t)return V;if(""===t)return"";let s,i=null;(s=t.match(L))?i=e.dot?B:x:(s=t.match(E))?i=(e.nocase?e.dot?O:R:e.dot?I:b)(s[1]):(s=t.match(P))?i=(e.nocase?e.dot?N:$:e.dot?M:U)(s):(s=t.match(T))?i=e.dot?D:k:(s=t.match(A))&&(i=C);const n=w.fromGlob(t,this.options).toMMPattern();return i?Object.assign(n,{test:i}):n}makeRe(){if(this.regexp||!1===this.regexp)return this.regexp;const t=this.set;if(!t.length)return this.regexp=!1,this.regexp;const e=this.options,s=e.noglobstar?"[^/]*?":e.dot?"(?:(?!(?:\\/|^)(?:\\.{1,2})($|\\/)).)*?":"(?:(?!(?:\\/|^)\\.).)*?",i=new Set(e.nocase?["i"]:[]);let r=t.map((t=>{const e=t.map((t=>{if(t instanceof RegExp)for(const e of t.flags.split(""))i.add(e);return"string"==typeof t?t.replace(/[-[\]{}()*+?.,\\^$|#\s]/g,"\\$&"):t===V?V:t._src}));return e.forEach(((t,i)=>{const r=e[i+1],n=e[i-1];t===V&&n!==V&&(void 0===n?void 0!==r&&r!==V?e[i+1]="(?:\\/|"+s+"\\/)?"+r:e[i]=s:void 0===r?e[i-1]=n+"(?:\\/|"+s+")?":r!==V&&(e[i-1]=n+"(?:\\/|\\/"+s+"\\/)"+r,e[i+1]=V))})),e.filter((t=>t!==V)).join("/")})).join("|");const[n,o]=t.length>1?["(?:",")"]:["",""];r="^"+n+r+o+"$",this.negate&&(r="^(?!"+r+").+$");try{this.regexp=new RegExp(r,[...i].join(""))}catch(t){this.regexp=!1}return this.regexp}slashSplit(t){return this.preserveMultipleSlashes?t.split("/"):this.isWindows&&/^\/\/[^\/]+/.test(t)?["",...t.split(/\/+/)]:t.split(/\/+/)}match(t,e=this.partial){if(this.debug("match",t,this.pattern),this.comment)return!1;if(this.empty)return""===t;if("/"===t&&e)return!0;const s=this.options;this.isWindows&&(t=t.split("\\").join("/"));const i=this.slashSplit(t);this.debug(this.pattern,"split",i);const r=this.set;this.debug(this.pattern,"set",r);let n=i[i.length-1];if(!n)for(let t=i.length-2;!n&&t>=0;t--)n=i[t];for(let t=0;t<r.length;t++){const o=r[t];let a=i;if(s.matchBase&&1===o.length&&(a=[n]),this.matchOne(a,o,e))return!!s.flipNegate||!this.negate}return!s.flipNegate&&this.negate}static defaults(t){return v.defaults(t).Minimatch}}v.AST=w,v.Minimatch=q,v.escape=_,v.unescape=c;const K="object"==typeof performance&&performance&&"function"==typeof performance.now?performance:Date,Y=new Set,Z="object"==typeof process&&process?process:{},X=(t,e,s,i)=>{"function"==typeof Z.emitWarning?Z.emitWarning(t,e,s,i):console.error(`[${s}] ${e}: ${t}`)};let J=globalThis.AbortController,Q=globalThis.AbortSignal;if(void 0===J){Q=class{onabort;_onabort=[];reason;aborted=!1;addEventListener(t,e){this._onabort.push(e)}},J=class{constructor(){e()}signal=new Q;abort(t){if(!this.signal.aborted){this.signal.reason=t,this.signal.aborted=!0;for(const e of this.signal._onabort)e(t);this.signal.onabort?.(t)}}};let t="1"!==Z.env?.LRU_CACHE_IGNORE_AC_WARNING;const e=()=>{t&&(t=!1,X("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}Symbol("type");const tt=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),et=t=>tt(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?st:null:null;class st extends Array{constructor(t){super(t),this.fill(0)}}class it{heap;length;static#p=!1;static create(t){const e=et(t);if(!e)return[];it.#p=!0;const s=new it(t,e);return it.#p=!1,s}constructor(t,e){if(!it.#p)throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new e(t),this.length=0}push(t){this.heap[this.length++]=t}pop(){return this.heap[--this.length]}}class rt{#m;#g;#S;#y;#w;#_;ttl;ttlResolution;ttlAutopurge;updateAgeOnGet;updateAgeOnHas;allowStale;noDisposeOnSet;noUpdateTTL;maxEntrySize;sizeCalculation;noDeleteOnFetchRejection;noDeleteOnStaleGet;allowStaleOnFetchAbort;allowStaleOnFetchRejection;ignoreFetchAbort;#v;#E;#b;#I;#R;#O;#T;#k;#D;#A;#C;#L;#x;#B;#P;#$;#N;static unsafeExposeInternals(t){return{starts:t.#x,ttls:t.#B,sizes:t.#L,keyMap:t.#b,keyList:t.#I,valList:t.#R,next:t.#O,prev:t.#T,get head(){return t.#k},get tail(){return t.#D},free:t.#A,isBackgroundFetch:e=>t.#M(e),backgroundFetch:(e,s,i,r)=>t.#U(e,s,i,r),moveToTail:e=>t.#F(e),indexes:e=>t.#j(e),rindexes:e=>t.#H(e),isStale:e=>t.#V(e)}}get max(){return this.#m}get maxSize(){return this.#g}get calculatedSize(){return this.#E}get size(){return this.#v}get fetchMethod(){return this.#w}get memoMethod(){return this.#_}get dispose(){return this.#S}get disposeAfter(){return this.#y}constructor(t){const{max:e=0,ttl:s,ttlResolution:i=1,ttlAutopurge:r,updateAgeOnGet:n,updateAgeOnHas:o,allowStale:a,dispose:h,disposeAfter:c,noDisposeOnSet:l,noUpdateTTL:u,maxSize:d=0,maxEntrySize:f=0,sizeCalculation:p,fetchMethod:m,memoMethod:g,noDeleteOnFetchRejection:S,noDeleteOnStaleGet:y,allowStaleOnFetchRejection:w,allowStaleOnFetchAbort:_,ignoreFetchAbort:v}=t;if(0!==e&&!tt(e))throw new TypeError("max option must be a nonnegative integer");const E=e?et(e):Array;if(!E)throw new Error("invalid max value: "+e);if(this.#m=e,this.#g=d,this.maxEntrySize=f||this.#g,this.sizeCalculation=p,this.sizeCalculation){if(!this.#g&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if("function"!=typeof this.sizeCalculation)throw new TypeError("sizeCalculation set to non-function")}if(void 0!==g&&"function"!=typeof g)throw new TypeError("memoMethod must be a function if defined");if(this.#_=g,void 0!==m&&"function"!=typeof m)throw new TypeError("fetchMethod must be a function if specified");if(this.#w=m,this.#$=!!m,this.#b=new Map,this.#I=new Array(e).fill(void 0),this.#R=new Array(e).fill(void 0),this.#O=new E(e),this.#T=new E(e),this.#k=0,this.#D=0,this.#A=it.create(e),this.#v=0,this.#E=0,"function"==typeof h&&(this.#S=h),"function"==typeof c?(this.#y=c,this.#C=[]):(this.#y=void 0,this.#C=void 0),this.#P=!!this.#S,this.#N=!!this.#y,this.noDisposeOnSet=!!l,this.noUpdateTTL=!!u,this.noDeleteOnFetchRejection=!!S,this.allowStaleOnFetchRejection=!!w,this.allowStaleOnFetchAbort=!!_,this.ignoreFetchAbort=!!v,0!==this.maxEntrySize){if(0!==this.#g&&!tt(this.#g))throw new TypeError("maxSize must be a positive integer if specified");if(!tt(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");this.#z()}if(this.allowStale=!!a,this.noDeleteOnStaleGet=!!y,this.updateAgeOnGet=!!n,this.updateAgeOnHas=!!o,this.ttlResolution=tt(i)||0===i?i:1,this.ttlAutopurge=!!r,this.ttl=s||0,this.ttl){if(!tt(this.ttl))throw new TypeError("ttl must be a positive integer if specified");this.#W()}if(0===this.#m&&0===this.ttl&&0===this.#g)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!this.#m&&!this.#g){const t="LRU_CACHE_UNBOUNDED";(t=>!Y.has(t))(t)&&(Y.add(t),X("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",t,rt))}}getRemainingTTL(t){return this.#b.has(t)?1/0:0}#W(){const t=new st(this.#m),e=new st(this.#m);this.#B=t,this.#x=e,this.#G=(s,i,r=K.now())=>{if(e[s]=0!==i?r:0,t[s]=i,0!==i&&this.ttlAutopurge){const t=setTimeout((()=>{this.#V(s)&&this.#q(this.#I[s],"expire")}),i+1);t.unref&&t.unref()}},this.#K=s=>{e[s]=0!==t[s]?K.now():0},this.#Y=(r,n)=>{if(t[n]){const o=t[n],a=e[n];if(!o||!a)return;r.ttl=o,r.start=a,r.now=s||i();const h=r.now-a;r.remainingTTL=o-h}};let s=0;const i=()=>{const t=K.now();if(this.ttlResolution>0){s=t;const e=setTimeout((()=>s=0),this.ttlResolution);e.unref&&e.unref()}return t};this.getRemainingTTL=r=>{const n=this.#b.get(r);if(void 0===n)return 0;const o=t[n],a=e[n];return o&&a?o-((s||i())-a):1/0},this.#V=r=>{const n=e[r],o=t[r];return!!o&&!!n&&(s||i())-n>o}}#K=()=>{};#Y=()=>{};#G=()=>{};#V=()=>!1;#z(){const t=new st(this.#m);this.#E=0,this.#L=t,this.#Z=e=>{this.#E-=t[e],t[e]=0},this.#X=(t,e,s,i)=>{if(this.#M(e))return 0;if(!tt(s)){if(!i)throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");if("function"!=typeof i)throw new TypeError("sizeCalculation must be a function");if(s=i(e,t),!tt(s))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}return s},this.#J=(e,s,i)=>{if(t[e]=s,this.#g){const s=this.#g-t[e];for(;this.#E>s;)this.#Q(!0)}this.#E+=t[e],i&&(i.entrySize=s,i.totalCalculatedSize=this.#E)}}#Z=t=>{};#J=(t,e,s)=>{};#X=(t,e,s,i)=>{if(s||i)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0};*#j({allowStale:t=this.allowStale}={}){if(this.#v)for(let e=this.#D;this.#tt(e)&&(!t&&this.#V(e)||(yield e),e!==this.#k);)e=this.#T[e]}*#H({allowStale:t=this.allowStale}={}){if(this.#v)for(let e=this.#k;this.#tt(e)&&(!t&&this.#V(e)||(yield e),e!==this.#D);)e=this.#O[e]}#tt(t){return void 0!==t&&this.#b.get(this.#I[t])===t}*entries(){for(const t of this.#j())void 0===this.#R[t]||void 0===this.#I[t]||this.#M(this.#R[t])||(yield[this.#I[t],this.#R[t]])}*rentries(){for(const t of this.#H())void 0===this.#R[t]||void 0===this.#I[t]||this.#M(this.#R[t])||(yield[this.#I[t],this.#R[t]])}*keys(){for(const t of this.#j()){const e=this.#I[t];void 0===e||this.#M(this.#R[t])||(yield e)}}*rkeys(){for(const t of this.#H()){const e=this.#I[t];void 0===e||this.#M(this.#R[t])||(yield e)}}*values(){for(const t of this.#j())void 0===this.#R[t]||this.#M(this.#R[t])||(yield this.#R[t])}*rvalues(){for(const t of this.#H())void 0===this.#R[t]||this.#M(this.#R[t])||(yield this.#R[t])}[Symbol.iterator](){return this.entries()}[Symbol.toStringTag]="LRUCache";find(t,e={}){for(const s of this.#j()){const i=this.#R[s],r=this.#M(i)?i.__staleWhileFetching:i;if(void 0!==r&&t(r,this.#I[s],this))return this.get(this.#I[s],e)}}forEach(t,e=this){for(const s of this.#j()){const i=this.#R[s],r=this.#M(i)?i.__staleWhileFetching:i;void 0!==r&&t.call(e,r,this.#I[s],this)}}rforEach(t,e=this){for(const s of this.#H()){const i=this.#R[s],r=this.#M(i)?i.__staleWhileFetching:i;void 0!==r&&t.call(e,r,this.#I[s],this)}}purgeStale(){let t=!1;for(const e of this.#H({allowStale:!0}))this.#V(e)&&(this.#q(this.#I[e],"expire"),t=!0);return t}info(t){const e=this.#b.get(t);if(void 0===e)return;const s=this.#R[e],i=this.#M(s)?s.__staleWhileFetching:s;if(void 0===i)return;const r={value:i};if(this.#B&&this.#x){const t=this.#B[e],s=this.#x[e];if(t&&s){const e=t-(K.now()-s);r.ttl=e,r.start=Date.now()}}return this.#L&&(r.size=this.#L[e]),r}dump(){const t=[];for(const e of this.#j({allowStale:!0})){const s=this.#I[e],i=this.#R[e],r=this.#M(i)?i.__staleWhileFetching:i;if(void 0===r||void 0===s)continue;const n={value:r};if(this.#B&&this.#x){n.ttl=this.#B[e];const t=K.now()-this.#x[e];n.start=Math.floor(Date.now()-t)}this.#L&&(n.size=this.#L[e]),t.unshift([s,n])}return t}load(t){this.clear();for(const[e,s]of t){if(s.start){const t=Date.now()-s.start;s.start=K.now()-t}this.set(e,s.value,s)}}set(t,e,s={}){if(void 0===e)return this.delete(t),this;const{ttl:i=this.ttl,start:r,noDisposeOnSet:n=this.noDisposeOnSet,sizeCalculation:o=this.sizeCalculation,status:a}=s;let{noUpdateTTL:h=this.noUpdateTTL}=s;const c=this.#X(t,e,s.size||0,o);if(this.maxEntrySize&&c>this.maxEntrySize)return a&&(a.set="miss",a.maxEntrySizeExceeded=!0),this.#q(t,"set"),this;let l=0===this.#v?void 0:this.#b.get(t);if(void 0===l)l=0===this.#v?this.#D:0!==this.#A.length?this.#A.pop():this.#v===this.#m?this.#Q(!1):this.#v,this.#I[l]=t,this.#R[l]=e,this.#b.set(t,l),this.#O[this.#D]=l,this.#T[l]=this.#D,this.#D=l,this.#v++,this.#J(l,c,a),a&&(a.set="add"),h=!1;else{this.#F(l);const s=this.#R[l];if(e!==s){if(this.#$&&this.#M(s)){s.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:e}=s;void 0===e||n||(this.#P&&this.#S?.(e,t,"set"),this.#N&&this.#C?.push([e,t,"set"]))}else n||(this.#P&&this.#S?.(s,t,"set"),this.#N&&this.#C?.push([s,t,"set"]));if(this.#Z(l),this.#J(l,c,a),this.#R[l]=e,a){a.set="replace";const t=s&&this.#M(s)?s.__staleWhileFetching:s;void 0!==t&&(a.oldValue=t)}}else a&&(a.set="update")}if(0===i||this.#B||this.#W(),this.#B&&(h||this.#G(l,i,r),a&&this.#Y(a,l)),!n&&this.#N&&this.#C){const t=this.#C;let e;for(;e=t?.shift();)this.#y?.(...e)}return this}pop(){try{for(;this.#v;){const t=this.#R[this.#k];if(this.#Q(!0),this.#M(t)){if(t.__staleWhileFetching)return t.__staleWhileFetching}else if(void 0!==t)return t}}finally{if(this.#N&&this.#C){const t=this.#C;let e;for(;e=t?.shift();)this.#y?.(...e)}}}#Q(t){const e=this.#k,s=this.#I[e],i=this.#R[e];return this.#$&&this.#M(i)?i.__abortController.abort(new Error("evicted")):(this.#P||this.#N)&&(this.#P&&this.#S?.(i,s,"evict"),this.#N&&this.#C?.push([i,s,"evict"])),this.#Z(e),t&&(this.#I[e]=void 0,this.#R[e]=void 0,this.#A.push(e)),1===this.#v?(this.#k=this.#D=0,this.#A.length=0):this.#k=this.#O[e],this.#b.delete(s),this.#v--,e}has(t,e={}){const{updateAgeOnHas:s=this.updateAgeOnHas,status:i}=e,r=this.#b.get(t);if(void 0!==r){const t=this.#R[r];if(this.#M(t)&&void 0===t.__staleWhileFetching)return!1;if(!this.#V(r))return s&&this.#K(r),i&&(i.has="hit",this.#Y(i,r)),!0;i&&(i.has="stale",this.#Y(i,r))}else i&&(i.has="miss");return!1}peek(t,e={}){const{allowStale:s=this.allowStale}=e,i=this.#b.get(t);if(void 0===i||!s&&this.#V(i))return;const r=this.#R[i];return this.#M(r)?r.__staleWhileFetching:r}#U(t,e,s,i){const r=void 0===e?void 0:this.#R[e];if(this.#M(r))return r;const n=new J,{signal:o}=s;o?.addEventListener("abort",(()=>n.abort(o.reason)),{signal:n.signal});const a={signal:n.signal,options:s,context:i},h=(i,r=!1)=>{const{aborted:o}=n.signal,h=s.ignoreFetchAbort&&void 0!==i;if(s.status&&(o&&!r?(s.status.fetchAborted=!0,s.status.fetchError=n.signal.reason,h&&(s.status.fetchAbortIgnored=!0)):s.status.fetchResolved=!0),o&&!h&&!r)return c(n.signal.reason);const u=l;return this.#R[e]===l&&(void 0===i?u.__staleWhileFetching?this.#R[e]=u.__staleWhileFetching:this.#q(t,"fetch"):(s.status&&(s.status.fetchUpdated=!0),this.set(t,i,a.options))),i},c=i=>{const{aborted:r}=n.signal,o=r&&s.allowStaleOnFetchAbort,a=o||s.allowStaleOnFetchRejection,h=a||s.noDeleteOnFetchRejection,c=l;if(this.#R[e]===l&&(h&&void 0!==c.__staleWhileFetching?o||(this.#R[e]=c.__staleWhileFetching):this.#q(t,"fetch")),a)return s.status&&void 0!==c.__staleWhileFetching&&(s.status.returnedStale=!0),c.__staleWhileFetching;if(c.__returned===c)throw i};s.status&&(s.status.fetchDispatched=!0);const l=new Promise(((e,i)=>{const o=this.#w?.(t,r,a);o&&o instanceof Promise&&o.then((t=>e(void 0===t?void 0:t)),i),n.signal.addEventListener("abort",(()=>{s.ignoreFetchAbort&&!s.allowStaleOnFetchAbort||(e(void 0),s.allowStaleOnFetchAbort&&(e=t=>h(t,!0)))}))})).then(h,(t=>(s.status&&(s.status.fetchRejected=!0,s.status.fetchError=t),c(t)))),u=Object.assign(l,{__abortController:n,__staleWhileFetching:r,__returned:void 0});return void 0===e?(this.set(t,u,{...a.options,status:void 0}),e=this.#b.get(t)):this.#R[e]=u,u}#M(t){if(!this.#$)return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof J}async fetch(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,ttl:n=this.ttl,noDisposeOnSet:o=this.noDisposeOnSet,size:a=0,sizeCalculation:h=this.sizeCalculation,noUpdateTTL:c=this.noUpdateTTL,noDeleteOnFetchRejection:l=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:u=this.allowStaleOnFetchRejection,ignoreFetchAbort:d=this.ignoreFetchAbort,allowStaleOnFetchAbort:f=this.allowStaleOnFetchAbort,context:p,forceRefresh:m=!1,status:g,signal:S}=e;if(!this.#$)return g&&(g.fetch="get"),this.get(t,{allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,status:g});const y={allowStale:s,updateAgeOnGet:i,noDeleteOnStaleGet:r,ttl:n,noDisposeOnSet:o,size:a,sizeCalculation:h,noUpdateTTL:c,noDeleteOnFetchRejection:l,allowStaleOnFetchRejection:u,allowStaleOnFetchAbort:f,ignoreFetchAbort:d,status:g,signal:S};let w=this.#b.get(t);if(void 0===w){g&&(g.fetch="miss");const e=this.#U(t,w,y,p);return e.__returned=e}{const e=this.#R[w];if(this.#M(e)){const t=s&&void 0!==e.__staleWhileFetching;return g&&(g.fetch="inflight",t&&(g.returnedStale=!0)),t?e.__staleWhileFetching:e.__returned=e}const r=this.#V(w);if(!m&&!r)return g&&(g.fetch="hit"),this.#F(w),i&&this.#K(w),g&&this.#Y(g,w),e;const n=this.#U(t,w,y,p),o=void 0!==n.__staleWhileFetching&&s;return g&&(g.fetch=r?"stale":"refresh",o&&r&&(g.returnedStale=!0)),o?n.__staleWhileFetching:n.__returned=n}}async forceFetch(t,e={}){const s=await this.fetch(t,e);if(void 0===s)throw new Error("fetch() returned undefined");return s}memo(t,e={}){const s=this.#_;if(!s)throw new Error("no memoMethod provided to constructor");const{context:i,forceRefresh:r,...n}=e,o=this.get(t,n);if(!r&&void 0!==o)return o;const a=s(t,o,{options:n,context:i});return this.set(t,a,n),a}get(t,e={}){const{allowStale:s=this.allowStale,updateAgeOnGet:i=this.updateAgeOnGet,noDeleteOnStaleGet:r=this.noDeleteOnStaleGet,status:n}=e,o=this.#b.get(t);if(void 0!==o){const e=this.#R[o],a=this.#M(e);return n&&this.#Y(n,o),this.#V(o)?(n&&(n.get="stale"),a?(n&&s&&void 0!==e.__staleWhileFetching&&(n.returnedStale=!0),s?e.__staleWhileFetching:void 0):(r||this.#q(t,"expire"),n&&s&&(n.returnedStale=!0),s?e:void 0)):(n&&(n.get="hit"),a?e.__staleWhileFetching:(this.#F(o),i&&this.#K(o),e))}n&&(n.get="miss")}#et(t,e){this.#T[e]=t,this.#O[t]=e}#F(t){t!==this.#D&&(t===this.#k?this.#k=this.#O[t]:this.#et(this.#T[t],this.#O[t]),this.#et(this.#D,t),this.#D=t)}delete(t){return this.#q(t,"delete")}#q(t,e){let s=!1;if(0!==this.#v){const i=this.#b.get(t);if(void 0!==i)if(s=!0,1===this.#v)this.#st(e);else{this.#Z(i);const s=this.#R[i];if(this.#M(s)?s.__abortController.abort(new Error("deleted")):(this.#P||this.#N)&&(this.#P&&this.#S?.(s,t,e),this.#N&&this.#C?.push([s,t,e])),this.#b.delete(t),this.#I[i]=void 0,this.#R[i]=void 0,i===this.#D)this.#D=this.#T[i];else if(i===this.#k)this.#k=this.#O[i];else{const t=this.#T[i];this.#O[t]=this.#O[i];const e=this.#O[i];this.#T[e]=this.#T[i]}this.#v--,this.#A.push(i)}}if(this.#N&&this.#C?.length){const t=this.#C;let e;for(;e=t?.shift();)this.#y?.(...e)}return s}clear(){return this.#st("delete")}#st(t){for(const e of this.#H({allowStale:!0})){const s=this.#R[e];if(this.#M(s))s.__abortController.abort(new Error("deleted"));else{const i=this.#I[e];this.#P&&this.#S?.(s,i,t),this.#N&&this.#C?.push([s,i,t])}}if(this.#b.clear(),this.#R.fill(void 0),this.#I.fill(void 0),this.#B&&this.#x&&(this.#B.fill(0),this.#x.fill(0)),this.#L&&this.#L.fill(0),this.#k=0,this.#D=0,this.#A.length=0,this.#E=0,this.#v=0,this.#N&&this.#C){const t=this.#C;let e;for(;e=t?.shift();)this.#y?.(...e)}}}const nt=require("node:path"),ot=require("node:url");var at=s(9896);const ht=require("node:fs");var ct=s.t(ht,2);const lt=require("node:fs/promises"),ut=require("node:events"),dt=require("node:stream"),ft=require("node:string_decoder"),pt="object"==typeof process&&process?process:{stdout:null,stderr:null},mt=t=>!!t&&"object"==typeof t&&(t instanceof Qt||t instanceof dt||gt(t)||St(t)),gt=t=>!!t&&"object"==typeof t&&t instanceof ut.EventEmitter&&"function"==typeof t.pipe&&t.pipe!==dt.Writable.prototype.pipe,St=t=>!!t&&"object"==typeof t&&t instanceof ut.EventEmitter&&"function"==typeof t.write&&"function"==typeof t.end,yt=Symbol("EOF"),wt=Symbol("maybeEmitEnd"),_t=Symbol("emittedEnd"),vt=Symbol("emittingEnd"),Et=Symbol("emittedError"),bt=Symbol("closed"),It=Symbol("read"),Rt=Symbol("flush"),Ot=Symbol("flushChunk"),Tt=Symbol("encoding"),kt=Symbol("decoder"),Dt=Symbol("flowing"),At=Symbol("paused"),Ct=Symbol("resume"),Lt=Symbol("buffer"),xt=Symbol("pipes"),Bt=Symbol("bufferLength"),Pt=Symbol("bufferPush"),$t=Symbol("bufferShift"),Nt=Symbol("objectMode"),Mt=Symbol("destroyed"),Ut=Symbol("error"),Ft=Symbol("emitData"),jt=Symbol("emitEnd"),Ht=Symbol("emitEnd2"),Vt=Symbol("async"),zt=Symbol("abort"),Wt=Symbol("aborted"),Gt=Symbol("signal"),qt=Symbol("dataListeners"),Kt=Symbol("discarded"),Yt=t=>Promise.resolve().then(t),Zt=t=>t();class Xt{src;dest;opts;ondrain;constructor(t,e,s){this.src=t,this.dest=e,this.opts=s,this.ondrain=()=>t[Ct](),this.dest.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(t){}end(){this.unpipe(),this.opts.end&&this.dest.end()}}class Jt extends Xt{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(t,e,s){super(t,e,s),this.proxyErrors=t=>e.emit("error",t),t.on("error",this.proxyErrors)}}class Qt extends ut.EventEmitter{[Dt]=!1;[At]=!1;[xt]=[];[Lt]=[];[Nt];[Tt];[Vt];[kt];[yt]=!1;[_t]=!1;[vt]=!1;[bt]=!1;[Et]=null;[Bt]=0;[Mt]=!1;[Gt];[Wt]=!1;[qt]=0;[Kt]=!1;writable=!0;readable=!0;constructor(...t){const e=t[0]||{};if(super(),e.objectMode&&"string"==typeof e.encoding)throw new TypeError("Encoding and objectMode may not be used together");var s;e.objectMode?(this[Nt]=!0,this[Tt]=null):!(s=e).objectMode&&s.encoding&&"buffer"!==s.encoding?(this[Tt]=e.encoding,this[Nt]=!1):(this[Nt]=!1,this[Tt]=null),this[Vt]=!!e.async,this[kt]=this[Tt]?new ft.StringDecoder(this[Tt]):null,e&&!0===e.debugExposeBuffer&&Object.defineProperty(this,"buffer",{get:()=>this[Lt]}),e&&!0===e.debugExposePipes&&Object.defineProperty(this,"pipes",{get:()=>this[xt]});const{signal:i}=e;i&&(this[Gt]=i,i.aborted?this[zt]():i.addEventListener("abort",(()=>this[zt]())))}get bufferLength(){return this[Bt]}get encoding(){return this[Tt]}set encoding(t){throw new Error("Encoding must be set at instantiation time")}setEncoding(t){throw new Error("Encoding must be set at instantiation time")}get objectMode(){return this[Nt]}set objectMode(t){throw new Error("objectMode must be set at instantiation time")}get async(){return this[Vt]}set async(t){this[Vt]=this[Vt]||!!t}[zt](){this[Wt]=!0,this.emit("abort",this[Gt]?.reason),this.destroy(this[Gt]?.reason)}get aborted(){return this[Wt]}set aborted(t){}write(t,e,s){if(this[Wt])return!1;if(this[yt])throw new Error("write after end");if(this[Mt])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;"function"==typeof e&&(s=e,e="utf8"),e||(e="utf8");const i=this[Vt]?Yt:Zt;if(!this[Nt]&&!Buffer.isBuffer(t))if(r=t,!Buffer.isBuffer(r)&&ArrayBuffer.isView(r))t=Buffer.from(t.buffer,t.byteOffset,t.byteLength);else if((t=>t instanceof ArrayBuffer||!!t&&"object"==typeof t&&t.constructor&&"ArrayBuffer"===t.constructor.name&&t.byteLength>=0)(t))t=Buffer.from(t);else if("string"!=typeof t)throw new Error("Non-contiguous data written to non-objectMode stream");var r;return this[Nt]?(this[Dt]&&0!==this[Bt]&&this[Rt](!0),this[Dt]?this.emit("data",t):this[Pt](t),0!==this[Bt]&&this.emit("readable"),s&&i(s),this[Dt]):t.length?("string"!=typeof t||e===this[Tt]&&!this[kt]?.lastNeed||(t=Buffer.from(t,e)),Buffer.isBuffer(t)&&this[Tt]&&(t=this[kt].write(t)),this[Dt]&&0!==this[Bt]&&this[Rt](!0),this[Dt]?this.emit("data",t):this[Pt](t),0!==this[Bt]&&this.emit("readable"),s&&i(s),this[Dt]):(0!==this[Bt]&&this.emit("readable"),s&&i(s),this[Dt])}read(t){if(this[Mt])return null;if(this[Kt]=!1,0===this[Bt]||0===t||t&&t>this[Bt])return this[wt](),null;this[Nt]&&(t=null),this[Lt].length>1&&!this[Nt]&&(this[Lt]=[this[Tt]?this[Lt].join(""):Buffer.concat(this[Lt],this[Bt])]);const e=this[It](t||null,this[Lt][0]);return this[wt](),e}[It](t,e){if(this[Nt])this[$t]();else{const s=e;t===s.length||null===t?this[$t]():"string"==typeof s?(this[Lt][0]=s.slice(t),e=s.slice(0,t),this[Bt]-=t):(this[Lt][0]=s.subarray(t),e=s.subarray(0,t),this[Bt]-=t)}return this.emit("data",e),this[Lt].length||this[yt]||this.emit("drain"),e}end(t,e,s){return"function"==typeof t&&(s=t,t=void 0),"function"==typeof e&&(s=e,e="utf8"),void 0!==t&&this.write(t,e),s&&this.once("end",s),this[yt]=!0,this.writable=!1,!this[Dt]&&this[At]||this[wt](),this}[Ct](){this[Mt]||(this[qt]||this[xt].length||(this[Kt]=!0),this[At]=!1,this[Dt]=!0,this.emit("resume"),this[Lt].length?this[Rt]():this[yt]?this[wt]():this.emit("drain"))}resume(){return this[Ct]()}pause(){this[Dt]=!1,this[At]=!0,this[Kt]=!1}get destroyed(){return this[Mt]}get flowing(){return this[Dt]}get paused(){return this[At]}[Pt](t){this[Nt]?this[Bt]+=1:this[Bt]+=t.length,this[Lt].push(t)}[$t](){return this[Nt]?this[Bt]-=1:this[Bt]-=this[Lt][0].length,this[Lt].shift()}[Rt](t=!1){do{}while(this[Ot](this[$t]())&&this[Lt].length);t||this[Lt].length||this[yt]||this.emit("drain")}[Ot](t){return this.emit("data",t),this[Dt]}pipe(t,e){if(this[Mt])return t;this[Kt]=!1;const s=this[_t];return e=e||{},t===pt.stdout||t===pt.stderr?e.end=!1:e.end=!1!==e.end,e.proxyErrors=!!e.proxyErrors,s?e.end&&t.end():(this[xt].push(e.proxyErrors?new Jt(this,t,e):new Xt(this,t,e)),this[Vt]?Yt((()=>this[Ct]())):this[Ct]()),t}unpipe(t){const e=this[xt].find((e=>e.dest===t));e&&(1===this[xt].length?(this[Dt]&&0===this[qt]&&(this[Dt]=!1),this[xt]=[]):this[xt].splice(this[xt].indexOf(e),1),e.unpipe())}addListener(t,e){return this.on(t,e)}on(t,e){const s=super.on(t,e);if("data"===t)this[Kt]=!1,this[qt]++,this[xt].length||this[Dt]||this[Ct]();else if("readable"===t&&0!==this[Bt])super.emit("readable");else if((t=>"end"===t||"finish"===t||"prefinish"===t)(t)&&this[_t])super.emit(t),this.removeAllListeners(t);else if("error"===t&&this[Et]){const t=e;this[Vt]?Yt((()=>t.call(this,this[Et]))):t.call(this,this[Et])}return s}removeListener(t,e){return this.off(t,e)}off(t,e){const s=super.off(t,e);return"data"===t&&(this[qt]=this.listeners("data").length,0!==this[qt]||this[Kt]||this[xt].length||(this[Dt]=!1)),s}removeAllListeners(t){const e=super.removeAllListeners(t);return"data"!==t&&void 0!==t||(this[qt]=0,this[Kt]||this[xt].length||(this[Dt]=!1)),e}get emittedEnd(){return this[_t]}[wt](){this[vt]||this[_t]||this[Mt]||0!==this[Lt].length||!this[yt]||(this[vt]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[bt]&&this.emit("close"),this[vt]=!1)}emit(t,...e){const s=e[0];if("error"!==t&&"close"!==t&&t!==Mt&&this[Mt])return!1;if("data"===t)return!(!this[Nt]&&!s)&&(this[Vt]?(Yt((()=>this[Ft](s))),!0):this[Ft](s));if("end"===t)return this[jt]();if("close"===t){if(this[bt]=!0,!this[_t]&&!this[Mt])return!1;const t=super.emit("close");return this.removeAllListeners("close"),t}if("error"===t){this[Et]=s,super.emit(Ut,s);const t=!(this[Gt]&&!this.listeners("error").length)&&super.emit("error",s);return this[wt](),t}if("resume"===t){const t=super.emit("resume");return this[wt](),t}if("finish"===t||"prefinish"===t){const e=super.emit(t);return this.removeAllListeners(t),e}const i=super.emit(t,...e);return this[wt](),i}[Ft](t){for(const e of this[xt])!1===e.dest.write(t)&&this.pause();const e=!this[Kt]&&super.emit("data",t);return this[wt](),e}[jt](){return!this[_t]&&(this[_t]=!0,this.readable=!1,this[Vt]?(Yt((()=>this[Ht]())),!0):this[Ht]())}[Ht](){if(this[kt]){const t=this[kt].end();if(t){for(const e of this[xt])e.dest.write(t);this[Kt]||super.emit("data",t)}}for(const t of this[xt])t.end();const t=super.emit("end");return this.removeAllListeners("end"),t}async collect(){const t=Object.assign([],{dataLength:0});this[Nt]||(t.dataLength=0);const e=this.promise();return this.on("data",(e=>{t.push(e),this[Nt]||(t.dataLength+=e.length)})),await e,t}async concat(){if(this[Nt])throw new Error("cannot concat in objectMode");const t=await this.collect();return this[Tt]?t.join(""):Buffer.concat(t,t.dataLength)}async promise(){return new Promise(((t,e)=>{this.on(Mt,(()=>e(new Error("stream destroyed")))),this.on("error",(t=>e(t))),this.on("end",(()=>t()))}))}[Symbol.asyncIterator](){this[Kt]=!1;let t=!1;const e=async()=>(this.pause(),t=!0,{value:void 0,done:!0});return{next:()=>{if(t)return e();const s=this.read();if(null!==s)return Promise.resolve({done:!1,value:s});if(this[yt])return e();let i,r;const n=t=>{this.off("data",o),this.off("end",a),this.off(Mt,h),e(),r(t)},o=t=>{this.off("error",n),this.off("end",a),this.off(Mt,h),this.pause(),i({value:t,done:!!this[yt]})},a=()=>{this.off("error",n),this.off("data",o),this.off(Mt,h),e(),i({done:!0,value:void 0})},h=()=>n(new Error("stream destroyed"));return new Promise(((t,e)=>{r=e,i=t,this.once(Mt,h),this.once("error",n),this.once("end",a),this.once("data",o)}))},throw:e,return:e,[Symbol.asyncIterator](){return this}}}[Symbol.iterator](){this[Kt]=!1;let t=!1;const e=()=>(this.pause(),this.off(Ut,e),this.off(Mt,e),this.off("end",e),t=!0,{done:!0,value:void 0});return this.once("end",e),this.once(Ut,e),this.once(Mt,e),{next:()=>{if(t)return e();const s=this.read();return null===s?e():{done:!1,value:s}},throw:e,return:e,[Symbol.iterator](){return this}}}destroy(t){return this[Mt]?(t?this.emit("error",t):this.emit(Mt),this):(this[Mt]=!0,this[Kt]=!0,this[Lt].length=0,this[Bt]=0,"function"!=typeof this.close||this[bt]||this.close(),t?this.emit("error",t):this.emit(Mt),this)}static get isStream(){return mt}}const te=at.realpathSync.native,ee={lstatSync:at.lstatSync,readdir:at.readdir,readdirSync:at.readdirSync,readlinkSync:at.readlinkSync,realpathSync:te,promises:{lstat:lt.lstat,readdir:lt.readdir,readlink:lt.readlink,realpath:lt.realpath}},se=t=>t&&t!==ee&&t!==ct?{...ee,...t,promises:{...ee.promises,...t.promises||{}}}:ee,ie=/^\\\\\?\\([a-z]:)\\?$/i,re=/[\\\/]/,ne=10,oe=15,ae=-16,he=128,ce=t=>t.isFile()?8:t.isDirectory()?4:t.isSymbolicLink()?ne:t.isCharacterDevice()?2:t.isBlockDevice()?6:t.isSocket()?12:t.isFIFO()?1:0,le=new Map,ue=t=>{const e=le.get(t);if(e)return e;const s=t.normalize("NFKD");return le.set(t,s),s},de=new Map,fe=t=>{const e=de.get(t);if(e)return e;const s=ue(t.toLowerCase());return de.set(t,s),s};class pe extends rt{constructor(){super({max:256})}}class me extends rt{constructor(t=16384){super({maxSize:t,sizeCalculation:t=>t.length+1})}}const ge=Symbol("PathScurry setAsCwd");class Se{name;root;roots;parent;nocase;isCWD=!1;#it;#rt;get dev(){return this.#rt}#nt;get mode(){return this.#nt}#ot;get nlink(){return this.#ot}#at;get uid(){return this.#at}#ht;get gid(){return this.#ht}#ct;get rdev(){return this.#ct}#lt;get blksize(){return this.#lt}#ut;get ino(){return this.#ut}#v;get size(){return this.#v}#dt;get blocks(){return this.#dt}#ft;get atimeMs(){return this.#ft}#pt;get mtimeMs(){return this.#pt}#mt;get ctimeMs(){return this.#mt}#gt;get birthtimeMs(){return this.#gt}#St;get atime(){return this.#St}#yt;get mtime(){return this.#yt}#wt;get ctime(){return this.#wt}#_t;get birthtime(){return this.#_t}#vt;#Et;#bt;#It;#Rt;#Ot;#Tt;#kt;#Dt;#At;get parentPath(){return(this.parent||this).fullpath()}get path(){return this.parentPath}constructor(t,e=0,s,i,r,n,o){this.name=t,this.#vt=r?fe(t):ue(t),this.#Tt=1023&e,this.nocase=r,this.roots=i,this.root=s||this,this.#kt=n,this.#bt=o.fullpath,this.#Rt=o.relative,this.#Ot=o.relativePosix,this.parent=o.parent,this.parent?this.#it=this.parent.#it:this.#it=se(o.fs)}depth(){return void 0!==this.#Et?this.#Et:this.parent?this.#Et=this.parent.depth()+1:this.#Et=0}childrenCache(){return this.#kt}resolve(t){if(!t)return this;const e=this.getRootString(t),s=t.substring(e.length).split(this.splitSep);return e?this.getRoot(e).#Ct(s):this.#Ct(s)}#Ct(t){let e=this;for(const s of t)e=e.child(s);return e}children(){const t=this.#kt.get(this);if(t)return t;const e=Object.assign([],{provisional:0});return this.#kt.set(this,e),this.#Tt&=-17,e}child(t,e){if(""===t||"."===t)return this;if(".."===t)return this.parent||this;const s=this.children(),i=this.nocase?fe(t):ue(t);for(const t of s)if(t.#vt===i)return t;const r=this.parent?this.sep:"",n=this.#bt?this.#bt+r+t:void 0,o=this.newChild(t,0,{...e,parent:this,fullpath:n});return this.canReaddir()||(o.#Tt|=he),s.push(o),o}relative(){if(this.isCWD)return"";if(void 0!==this.#Rt)return this.#Rt;const t=this.name,e=this.parent;if(!e)return this.#Rt=this.name;const s=e.relative();return s+(s&&e.parent?this.sep:"")+t}relativePosix(){if("/"===this.sep)return this.relative();if(this.isCWD)return"";if(void 0!==this.#Ot)return this.#Ot;const t=this.name,e=this.parent;if(!e)return this.#Ot=this.fullpathPosix();const s=e.relativePosix();return s+(s&&e.parent?"/":"")+t}fullpath(){if(void 0!==this.#bt)return this.#bt;const t=this.name,e=this.parent;if(!e)return this.#bt=this.name;const s=e.fullpath()+(e.parent?this.sep:"")+t;return this.#bt=s}fullpathPosix(){if(void 0!==this.#It)return this.#It;if("/"===this.sep)return this.#It=this.fullpath();if(!this.parent){const t=this.fullpath().replace(/\\/g,"/");return/^[a-z]:\//i.test(t)?this.#It=`//?/${t}`:this.#It=t}const t=this.parent,e=t.fullpathPosix(),s=e+(e&&t.parent?"/":"")+this.name;return this.#It=s}isUnknown(){return!(this.#Tt&oe)}isType(t){return this[`is${t}`]()}getType(){return this.isUnknown()?"Unknown":this.isDirectory()?"Directory":this.isFile()?"File":this.isSymbolicLink()?"SymbolicLink":this.isFIFO()?"FIFO":this.isCharacterDevice()?"CharacterDevice":this.isBlockDevice()?"BlockDevice":this.isSocket()?"Socket":"Unknown"}isFile(){return 8==(this.#Tt&oe)}isDirectory(){return 4==(this.#Tt&oe)}isCharacterDevice(){return 2==(this.#Tt&oe)}isBlockDevice(){return 6==(this.#Tt&oe)}isFIFO(){return 1==(this.#Tt&oe)}isSocket(){return 12==(this.#Tt&oe)}isSymbolicLink(){return(this.#Tt&ne)===ne}lstatCached(){return 32&this.#Tt?this:void 0}readlinkCached(){return this.#Dt}realpathCached(){return this.#At}readdirCached(){const t=this.children();return t.slice(0,t.provisional)}canReadlink(){if(this.#Dt)return!0;if(!this.parent)return!1;const t=this.#Tt&oe;return!(0!==t&&t!==ne||256&this.#Tt||this.#Tt&he)}calledReaddir(){return!!(16&this.#Tt)}isENOENT(){return!!(this.#Tt&he)}isNamed(t){return this.nocase?this.#vt===fe(t):this.#vt===ue(t)}async readlink(){const t=this.#Dt;if(t)return t;if(this.canReadlink()&&this.parent)try{const t=await this.#it.promises.readlink(this.fullpath()),e=(await this.parent.realpath())?.resolve(t);if(e)return this.#Dt=e}catch(t){return void this.#Lt(t.code)}}readlinkSync(){const t=this.#Dt;if(t)return t;if(this.canReadlink()&&this.parent)try{const t=this.#it.readlinkSync(this.fullpath()),e=this.parent.realpathSync()?.resolve(t);if(e)return this.#Dt=e}catch(t){return void this.#Lt(t.code)}}#xt(t){this.#Tt|=16;for(let e=t.provisional;e<t.length;e++){const s=t[e];s&&s.#Bt()}}#Bt(){this.#Tt&he||(this.#Tt=(this.#Tt|he)&ae,this.#Pt())}#Pt(){const t=this.children();t.provisional=0;for(const e of t)e.#Bt()}#$t(){this.#Tt|=512,this.#Nt()}#Nt(){if(64&this.#Tt)return;let t=this.#Tt;4==(t&oe)&&(t&=ae),this.#Tt=64|t,this.#Pt()}#Mt(t=""){"ENOTDIR"===t||"EPERM"===t?this.#Nt():"ENOENT"===t?this.#Bt():this.children().provisional=0}#Ut(t=""){"ENOTDIR"===t?this.parent.#Nt():"ENOENT"===t&&this.#Bt()}#Lt(t=""){let e=this.#Tt;e|=256,"ENOENT"===t&&(e|=he),"EINVAL"!==t&&"UNKNOWN"!==t||(e&=ae),this.#Tt=e,"ENOTDIR"===t&&this.parent&&this.parent.#Nt()}#Ft(t,e){return this.#jt(t,e)||this.#Ht(t,e)}#Ht(t,e){const s=ce(t),i=this.newChild(t.name,s,{parent:this}),r=i.#Tt&oe;return 4!==r&&r!==ne&&0!==r&&(i.#Tt|=64),e.unshift(i),e.provisional++,i}#jt(t,e){for(let s=e.provisional;s<e.length;s++){const i=e[s];if((this.nocase?fe(t.name):ue(t.name))===i.#vt)return this.#Vt(t,i,s,e)}}#Vt(t,e,s,i){const r=e.name;return e.#Tt=e.#Tt&ae|ce(t),r!==t.name&&(e.name=t.name),s!==i.provisional&&(s===i.length-1?i.pop():i.splice(s,1),i.unshift(e)),i.provisional++,e}async lstat(){if(!(this.#Tt&he))try{return this.#zt(await this.#it.promises.lstat(this.fullpath())),this}catch(t){this.#Ut(t.code)}}lstatSync(){if(!(this.#Tt&he))try{return this.#zt(this.#it.lstatSync(this.fullpath())),this}catch(t){this.#Ut(t.code)}}#zt(t){const{atime:e,atimeMs:s,birthtime:i,birthtimeMs:r,blksize:n,blocks:o,ctime:a,ctimeMs:h,dev:c,gid:l,ino:u,mode:d,mtime:f,mtimeMs:p,nlink:m,rdev:g,size:S,uid:y}=t;this.#St=e,this.#ft=s,this.#_t=i,this.#gt=r,this.#lt=n,this.#dt=o,this.#wt=a,this.#mt=h,this.#rt=c,this.#ht=l,this.#ut=u,this.#nt=d,this.#yt=f,this.#pt=p,this.#ot=m,this.#ct=g,this.#v=S,this.#at=y;const w=ce(t);this.#Tt=this.#Tt&ae|w|32,0!==w&&4!==w&&w!==ne&&(this.#Tt|=64)}#Wt=[];#Gt=!1;#qt(t){this.#Gt=!1;const e=this.#Wt.slice();this.#Wt.length=0,e.forEach((e=>e(null,t)))}readdirCB(t,e=!1){if(!this.canReaddir())return void(e?t(null,[]):queueMicrotask((()=>t(null,[]))));const s=this.children();if(this.calledReaddir()){const i=s.slice(0,s.provisional);return void(e?t(null,i):queueMicrotask((()=>t(null,i))))}if(this.#Wt.push(t),this.#Gt)return;this.#Gt=!0;const i=this.fullpath();this.#it.readdir(i,{withFileTypes:!0},((t,e)=>{if(t)this.#Mt(t.code),s.provisional=0;else{for(const t of e)this.#Ft(t,s);this.#xt(s)}this.#qt(s.slice(0,s.provisional))}))}#Kt;async readdir(){if(!this.canReaddir())return[];const t=this.children();if(this.calledReaddir())return t.slice(0,t.provisional);const e=this.fullpath();if(this.#Kt)await this.#Kt;else{let s=()=>{};this.#Kt=new Promise((t=>s=t));try{for(const s of await this.#it.promises.readdir(e,{withFileTypes:!0}))this.#Ft(s,t);this.#xt(t)}catch(e){this.#Mt(e.code),t.provisional=0}this.#Kt=void 0,s()}return t.slice(0,t.provisional)}readdirSync(){if(!this.canReaddir())return[];const t=this.children();if(this.calledReaddir())return t.slice(0,t.provisional);const e=this.fullpath();try{for(const s of this.#it.readdirSync(e,{withFileTypes:!0}))this.#Ft(s,t);this.#xt(t)}catch(e){this.#Mt(e.code),t.provisional=0}return t.slice(0,t.provisional)}canReaddir(){if(704&this.#Tt)return!1;const t=oe&this.#Tt;return 0===t||4===t||t===ne}shouldWalk(t,e){return!(4&~this.#Tt)&&!(704&this.#Tt)&&!t.has(this)&&(!e||e(this))}async realpath(){if(this.#At)return this.#At;if(!(896&this.#Tt))try{const t=await this.#it.promises.realpath(this.fullpath());return this.#At=this.resolve(t)}catch(t){this.#$t()}}realpathSync(){if(this.#At)return this.#At;if(!(896&this.#Tt))try{const t=this.#it.realpathSync(this.fullpath());return this.#At=this.resolve(t)}catch(t){this.#$t()}}[ge](t){if(t===this)return;t.isCWD=!1,this.isCWD=!0;const e=new Set([]);let s=[],i=this;for(;i&&i.parent;)e.add(i),i.#Rt=s.join(this.sep),i.#Ot=s.join("/"),i=i.parent,s.push("..");for(i=t;i&&i.parent&&!e.has(i);)i.#Rt=void 0,i.#Ot=void 0,i=i.parent}}class ye extends Se{sep="\\";splitSep=re;constructor(t,e=0,s,i,r,n,o){super(t,e,s,i,r,n,o)}newChild(t,e=0,s={}){return new ye(t,e,this.root,this.roots,this.nocase,this.childrenCache(),s)}getRootString(t){return nt.win32.parse(t).root}getRoot(t){if((t=(t=>t.replace(/\//g,"\\").replace(ie,"$1\\"))(t.toUpperCase()))===this.root.name)return this.root;for(const[e,s]of Object.entries(this.roots))if(this.sameRoot(t,e))return this.roots[t]=s;return this.roots[t]=new ve(t,this).root}sameRoot(t,e=this.root.name){return(t=t.toUpperCase().replace(/\//g,"\\").replace(ie,"$1\\"))===e}}class we extends Se{splitSep="/";sep="/";constructor(t,e=0,s,i,r,n,o){super(t,e,s,i,r,n,o)}getRootString(t){return t.startsWith("/")?"/":""}getRoot(t){return this.root}newChild(t,e=0,s={}){return new we(t,e,this.root,this.roots,this.nocase,this.childrenCache(),s)}}class _e{root;rootPath;roots;cwd;#Yt;#Zt;#kt;nocase;#it;constructor(t=process.cwd(),e,s,{nocase:i,childrenCacheSize:r=16384,fs:n=ee}={}){this.#it=se(n),(t instanceof URL||t.startsWith("file://"))&&(t=(0,ot.fileURLToPath)(t));const o=e.resolve(t);this.roots=Object.create(null),this.rootPath=this.parseRootPath(o),this.#Yt=new pe,this.#Zt=new pe,this.#kt=new me(r);const a=o.substring(this.rootPath.length).split(s);if(1!==a.length||a[0]||a.pop(),void 0===i)throw new TypeError("must provide nocase setting to PathScurryBase ctor");this.nocase=i,this.root=this.newRoot(this.#it),this.roots[this.rootPath]=this.root;let h=this.root,c=a.length-1;const l=e.sep;let u=this.rootPath,d=!1;for(const t of a){const e=c--;h=h.child(t,{relative:new Array(e).fill("..").join(l),relativePosix:new Array(e).fill("..").join("/"),fullpath:u+=(d?"":l)+t}),d=!0}this.cwd=h}depth(t=this.cwd){return"string"==typeof t&&(t=this.cwd.resolve(t)),t.depth()}childrenCache(){return this.#kt}resolve(...t){let e="";for(let s=t.length-1;s>=0;s--){const i=t[s];if(i&&"."!==i&&(e=e?`${i}/${e}`:i,this.isAbsolute(i)))break}const s=this.#Yt.get(e);if(void 0!==s)return s;const i=this.cwd.resolve(e).fullpath();return this.#Yt.set(e,i),i}resolvePosix(...t){let e="";for(let s=t.length-1;s>=0;s--){const i=t[s];if(i&&"."!==i&&(e=e?`${i}/${e}`:i,this.isAbsolute(i)))break}const s=this.#Zt.get(e);if(void 0!==s)return s;const i=this.cwd.resolve(e).fullpathPosix();return this.#Zt.set(e,i),i}relative(t=this.cwd){return"string"==typeof t&&(t=this.cwd.resolve(t)),t.relative()}relativePosix(t=this.cwd){return"string"==typeof t&&(t=this.cwd.resolve(t)),t.relativePosix()}basename(t=this.cwd){return"string"==typeof t&&(t=this.cwd.resolve(t)),t.name}dirname(t=this.cwd){return"string"==typeof t&&(t=this.cwd.resolve(t)),(t.parent||t).fullpath()}async readdir(t=this.cwd,e={withFileTypes:!0}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t,t=this.cwd);const{withFileTypes:s}=e;if(t.canReaddir()){const e=await t.readdir();return s?e:e.map((t=>t.name))}return[]}readdirSync(t=this.cwd,e={withFileTypes:!0}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t,t=this.cwd);const{withFileTypes:s=!0}=e;return t.canReaddir()?s?t.readdirSync():t.readdirSync().map((t=>t.name)):[]}async lstat(t=this.cwd){return"string"==typeof t&&(t=this.cwd.resolve(t)),t.lstat()}lstatSync(t=this.cwd){return"string"==typeof t&&(t=this.cwd.resolve(t)),t.lstatSync()}async readlink(t=this.cwd,{withFileTypes:e}={withFileTypes:!1}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t.withFileTypes,t=this.cwd);const s=await t.readlink();return e?s:s?.fullpath()}readlinkSync(t=this.cwd,{withFileTypes:e}={withFileTypes:!1}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t.withFileTypes,t=this.cwd);const s=t.readlinkSync();return e?s:s?.fullpath()}async realpath(t=this.cwd,{withFileTypes:e}={withFileTypes:!1}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t.withFileTypes,t=this.cwd);const s=await t.realpath();return e?s:s?.fullpath()}realpathSync(t=this.cwd,{withFileTypes:e}={withFileTypes:!1}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t.withFileTypes,t=this.cwd);const s=t.realpathSync();return e?s:s?.fullpath()}async walk(t=this.cwd,e={}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t,t=this.cwd);const{withFileTypes:s=!0,follow:i=!1,filter:r,walkFilter:n}=e,o=[];r&&!r(t)||o.push(s?t:t.fullpath());const a=new Set,h=(t,e)=>{a.add(t),t.readdirCB(((t,c)=>{if(t)return e(t);let l=c.length;if(!l)return e();const u=()=>{0==--l&&e()};for(const t of c)r&&!r(t)||o.push(s?t:t.fullpath()),i&&t.isSymbolicLink()?t.realpath().then((t=>t?.isUnknown()?t.lstat():t)).then((t=>t?.shouldWalk(a,n)?h(t,u):u())):t.shouldWalk(a,n)?h(t,u):u()}),!0)},c=t;return new Promise(((t,e)=>{h(c,(s=>{if(s)return e(s);t(o)}))}))}walkSync(t=this.cwd,e={}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t,t=this.cwd);const{withFileTypes:s=!0,follow:i=!1,filter:r,walkFilter:n}=e,o=[];r&&!r(t)||o.push(s?t:t.fullpath());const a=new Set([t]);for(const t of a){const e=t.readdirSync();for(const t of e){r&&!r(t)||o.push(s?t:t.fullpath());let e=t;if(t.isSymbolicLink()){if(!i||!(e=t.realpathSync()))continue;e.isUnknown()&&e.lstatSync()}e.shouldWalk(a,n)&&a.add(e)}}return o}[Symbol.asyncIterator](){return this.iterate()}iterate(t=this.cwd,e={}){return"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t,t=this.cwd),this.stream(t,e)[Symbol.asyncIterator]()}[Symbol.iterator](){return this.iterateSync()}*iterateSync(t=this.cwd,e={}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t,t=this.cwd);const{withFileTypes:s=!0,follow:i=!1,filter:r,walkFilter:n}=e;r&&!r(t)||(yield s?t:t.fullpath());const o=new Set([t]);for(const t of o){const e=t.readdirSync();for(const t of e){r&&!r(t)||(yield s?t:t.fullpath());let e=t;if(t.isSymbolicLink()){if(!i||!(e=t.realpathSync()))continue;e.isUnknown()&&e.lstatSync()}e.shouldWalk(o,n)&&o.add(e)}}}stream(t=this.cwd,e={}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t,t=this.cwd);const{withFileTypes:s=!0,follow:i=!1,filter:r,walkFilter:n}=e,o=new Qt({objectMode:!0});r&&!r(t)||o.write(s?t:t.fullpath());const a=new Set,h=[t];let c=0;const l=()=>{let t=!1;for(;!t;){const e=h.shift();if(!e)return void(0===c&&o.end());c++,a.add(e);const u=(e,f,p=!1)=>{if(e)return o.emit("error",e);if(i&&!p){const t=[];for(const e of f)e.isSymbolicLink()&&t.push(e.realpath().then((t=>t?.isUnknown()?t.lstat():t)));if(t.length)return void Promise.all(t).then((()=>u(null,f,!0)))}for(const e of f)!e||r&&!r(e)||o.write(s?e:e.fullpath())||(t=!0);c--;for(const t of f){const e=t.realpathCached()||t;e.shouldWalk(a,n)&&h.push(e)}t&&!o.flowing?o.once("drain",l):d||l()};let d=!0;e.readdirCB(u,!0),d=!1}};return l(),o}streamSync(t=this.cwd,e={}){"string"==typeof t?t=this.cwd.resolve(t):t instanceof Se||(e=t,t=this.cwd);const{withFileTypes:s=!0,follow:i=!1,filter:r,walkFilter:n}=e,o=new Qt({objectMode:!0}),a=new Set;r&&!r(t)||o.write(s?t:t.fullpath());const h=[t];let c=0;const l=()=>{let t=!1;for(;!t;){const e=h.shift();if(!e)return void(0===c&&o.end());c++,a.add(e);const l=e.readdirSync();for(const e of l)r&&!r(e)||o.write(s?e:e.fullpath())||(t=!0);c--;for(const t of l){let e=t;if(t.isSymbolicLink()){if(!i||!(e=t.realpathSync()))continue;e.isUnknown()&&e.lstatSync()}e.shouldWalk(a,n)&&h.push(e)}}t&&!o.flowing&&o.once("drain",l)};return l(),o}chdir(t=this.cwd){const e=this.cwd;this.cwd="string"==typeof t?this.cwd.resolve(t):t,this.cwd[ge](e)}}class ve extends _e{sep="\\";constructor(t=process.cwd(),e={}){const{nocase:s=!0}=e;super(t,nt.win32,"\\",{...e,nocase:s}),this.nocase=s;for(let t=this.cwd;t;t=t.parent)t.nocase=this.nocase}parseRootPath(t){return nt.win32.parse(t).root.toUpperCase()}newRoot(t){return new ye(this.rootPath,4,void 0,this.roots,this.nocase,this.childrenCache(),{fs:t})}isAbsolute(t){return t.startsWith("/")||t.startsWith("\\")||/^[a-z]:(\/|\\)/i.test(t)}}class Ee extends _e{sep="/";constructor(t=process.cwd(),e={}){const{nocase:s=!1}=e;super(t,nt.posix,"/",{...e,nocase:s}),this.nocase=s}parseRootPath(t){return"/"}newRoot(t){return new we(this.rootPath,4,void 0,this.roots,this.nocase,this.childrenCache(),{fs:t})}isAbsolute(t){return t.startsWith("/")}}class be extends Ee{constructor(t=process.cwd(),e={}){const{nocase:s=!0}=e;super(t,{...e,nocase:s})}}process.platform;const Ie="win32"===process.platform?ve:"darwin"===process.platform?be:Ee,Re=require("url");class Oe{#Xt;#Jt;#Qt;length;#te;#ee;#se;#ie;#re;#ne;#oe=!0;constructor(t,e,s,i){if(!(t.length>=1))throw new TypeError("empty pattern list");if(!(e.length>=1))throw new TypeError("empty glob list");if(e.length!==t.length)throw new TypeError("mismatched pattern list and glob list lengths");if(this.length=t.length,s<0||s>=this.length)throw new TypeError("index out of range");if(this.#Xt=t,this.#Jt=e,this.#Qt=s,this.#te=i,0===this.#Qt)if(this.isUNC()){const[t,e,s,i,...r]=this.#Xt,[n,o,a,h,...c]=this.#Jt;""===r[0]&&(r.shift(),c.shift());const l=[t,e,s,i,""].join("/"),u=[n,o,a,h,""].join("/");this.#Xt=[l,...r],this.#Jt=[u,...c],this.length=this.#Xt.length}else if(this.isDrive()||this.isAbsolute()){const[t,...e]=this.#Xt,[s,...i]=this.#Jt;""===e[0]&&(e.shift(),i.shift());const r=t+"/",n=s+"/";this.#Xt=[r,...e],this.#Jt=[n,...i],this.length=this.#Xt.length}}pattern(){return this.#Xt[this.#Qt]}isString(){return"string"==typeof this.#Xt[this.#Qt]}isGlobstar(){return this.#Xt[this.#Qt]===V}isRegExp(){return this.#Xt[this.#Qt]instanceof RegExp}globString(){return this.#se=this.#se||(0===this.#Qt?this.isAbsolute()?this.#Jt[0]+this.#Jt.slice(1).join("/"):this.#Jt.join("/"):this.#Jt.slice(this.#Qt).join("/"))}hasMore(){return this.length>this.#Qt+1}rest(){return void 0!==this.#ee?this.#ee:this.hasMore()?(this.#ee=new Oe(this.#Xt,this.#Jt,this.#Qt+1,this.#te),this.#ee.#ne=this.#ne,this.#ee.#re=this.#re,this.#ee.#ie=this.#ie,this.#ee):this.#ee=null}isUNC(){const t=this.#Xt;return void 0!==this.#re?this.#re:this.#re="win32"===this.#te&&0===this.#Qt&&""===t[0]&&""===t[1]&&"string"==typeof t[2]&&!!t[2]&&"string"==typeof t[3]&&!!t[3]}isDrive(){const t=this.#Xt;return void 0!==this.#ie?this.#ie:this.#ie="win32"===this.#te&&0===this.#Qt&&this.length>1&&"string"==typeof t[0]&&/^[a-z]:$/i.test(t[0])}isAbsolute(){const t=this.#Xt;return void 0!==this.#ne?this.#ne:this.#ne=""===t[0]&&t.length>1||this.isDrive()||this.isUNC()}root(){const t=this.#Xt[0];return"string"==typeof t&&this.isAbsolute()&&0===this.#Qt?t:""}checkFollowGlobstar(){return!(0===this.#Qt||!this.isGlobstar()||!this.#oe)}markFollowGlobstar(){return!(0===this.#Qt||!this.isGlobstar()||!this.#oe||(this.#oe=!1,0))}}var Te=s(4434),ke=s(2203);const De=require("string_decoder"),Ae="object"==typeof process&&process?process:{stdout:null,stderr:null},Ce=De.StringDecoder,Le=Symbol("EOF"),xe=Symbol("maybeEmitEnd"),Be=Symbol("emittedEnd"),Pe=Symbol("emittingEnd"),$e=Symbol("emittedError"),Ne=Symbol("closed"),Me=Symbol("read"),Ue=Symbol("flush"),Fe=Symbol("flushChunk"),je=Symbol("encoding"),He=Symbol("decoder"),Ve=Symbol("flowing"),ze=Symbol("paused"),We=Symbol("resume"),Ge=Symbol("buffer"),qe=Symbol("pipes"),Ke=Symbol("bufferLength"),Ye=Symbol("bufferPush"),Ze=Symbol("bufferShift"),Xe=Symbol("objectMode"),Je=Symbol("destroyed"),Qe=Symbol("error"),ts=Symbol("emitData"),es=Symbol("emitEnd"),ss=Symbol("emitEnd2"),is=Symbol("async"),rs=Symbol("abort"),ns=Symbol("aborted"),os=Symbol("signal"),as=t=>Promise.resolve().then(t),hs="1"!==global._MP_NO_ITERATOR_SYMBOLS_,cs=hs&&Symbol.asyncIterator||Symbol("asyncIterator not implemented"),ls=hs&&Symbol.iterator||Symbol("iterator not implemented");class us{constructor(t,e,s){this.src=t,this.dest=e,this.opts=s,this.ondrain=()=>t[We](),e.on("drain",this.ondrain)}unpipe(){this.dest.removeListener("drain",this.ondrain)}proxyErrors(){}end(){this.unpipe(),this.opts.end&&this.dest.end()}}class ds extends us{unpipe(){this.src.removeListener("error",this.proxyErrors),super.unpipe()}constructor(t,e,s){super(t,e,s),this.proxyErrors=t=>e.emit("error",t),t.on("error",this.proxyErrors)}}class fs extends ke{constructor(t){super(),this[Ve]=!1,this[ze]=!1,this[qe]=[],this[Ge]=[],this[Xe]=t&&t.objectMode||!1,this[Xe]?this[je]=null:this[je]=t&&t.encoding||null,"buffer"===this[je]&&(this[je]=null),this[is]=t&&!!t.async||!1,this[He]=this[je]?new Ce(this[je]):null,this[Le]=!1,this[Be]=!1,this[Pe]=!1,this[Ne]=!1,this[$e]=null,this.writable=!0,this.readable=!0,this[Ke]=0,this[Je]=!1,t&&!0===t.debugExposeBuffer&&Object.defineProperty(this,"buffer",{get:()=>this[Ge]}),t&&!0===t.debugExposePipes&&Object.defineProperty(this,"pipes",{get:()=>this[qe]}),this[os]=t&&t.signal,this[ns]=!1,this[os]&&(this[os].addEventListener("abort",(()=>this[rs]())),this[os].aborted&&this[rs]())}get bufferLength(){return this[Ke]}get encoding(){return this[je]}set encoding(t){if(this[Xe])throw new Error("cannot set encoding in objectMode");if(this[je]&&t!==this[je]&&(this[He]&&this[He].lastNeed||this[Ke]))throw new Error("cannot change encoding");this[je]!==t&&(this[He]=t?new Ce(t):null,this[Ge].length&&(this[Ge]=this[Ge].map((t=>this[He].write(t))))),this[je]=t}setEncoding(t){this.encoding=t}get objectMode(){return this[Xe]}set objectMode(t){this[Xe]=this[Xe]||!!t}get async(){return this[is]}set async(t){this[is]=this[is]||!!t}[rs](){this[ns]=!0,this.emit("abort",this[os].reason),this.destroy(this[os].reason)}get aborted(){return this[ns]}set aborted(t){}write(t,e,s){if(this[ns])return!1;if(this[Le])throw new Error("write after end");if(this[Je])return this.emit("error",Object.assign(new Error("Cannot call write after a stream was destroyed"),{code:"ERR_STREAM_DESTROYED"})),!0;"function"==typeof e&&(s=e,e="utf8"),e||(e="utf8");const i=this[is]?as:t=>t();var r;return this[Xe]||Buffer.isBuffer(t)||(r=t,!Buffer.isBuffer(r)&&ArrayBuffer.isView(r)?t=Buffer.from(t.buffer,t.byteOffset,t.byteLength):(t=>t instanceof ArrayBuffer||"object"==typeof t&&t.constructor&&"ArrayBuffer"===t.constructor.name&&t.byteLength>=0)(t)?t=Buffer.from(t):"string"!=typeof t&&(this.objectMode=!0)),this[Xe]?(this.flowing&&0!==this[Ke]&&this[Ue](!0),this.flowing?this.emit("data",t):this[Ye](t),0!==this[Ke]&&this.emit("readable"),s&&i(s),this.flowing):t.length?("string"!=typeof t||e===this[je]&&!this[He].lastNeed||(t=Buffer.from(t,e)),Buffer.isBuffer(t)&&this[je]&&(t=this[He].write(t)),this.flowing&&0!==this[Ke]&&this[Ue](!0),this.flowing?this.emit("data",t):this[Ye](t),0!==this[Ke]&&this.emit("readable"),s&&i(s),this.flowing):(0!==this[Ke]&&this.emit("readable"),s&&i(s),this.flowing)}read(t){if(this[Je])return null;if(0===this[Ke]||0===t||t>this[Ke])return this[xe](),null;this[Xe]&&(t=null),this[Ge].length>1&&!this[Xe]&&(this.encoding?this[Ge]=[this[Ge].join("")]:this[Ge]=[Buffer.concat(this[Ge],this[Ke])]);const e=this[Me](t||null,this[Ge][0]);return this[xe](),e}[Me](t,e){return t===e.length||null===t?this[Ze]():(this[Ge][0]=e.slice(t),e=e.slice(0,t),this[Ke]-=t),this.emit("data",e),this[Ge].length||this[Le]||this.emit("drain"),e}end(t,e,s){return"function"==typeof t&&(s=t,t=null),"function"==typeof e&&(s=e,e="utf8"),t&&this.write(t,e),s&&this.once("end",s),this[Le]=!0,this.writable=!1,!this.flowing&&this[ze]||this[xe](),this}[We](){this[Je]||(this[ze]=!1,this[Ve]=!0,this.emit("resume"),this[Ge].length?this[Ue]():this[Le]?this[xe]():this.emit("drain"))}resume(){return this[We]()}pause(){this[Ve]=!1,this[ze]=!0}get destroyed(){return this[Je]}get flowing(){return this[Ve]}get paused(){return this[ze]}[Ye](t){this[Xe]?this[Ke]+=1:this[Ke]+=t.length,this[Ge].push(t)}[Ze](){return this[Xe]?this[Ke]-=1:this[Ke]-=this[Ge][0].length,this[Ge].shift()}[Ue](t){do{}while(this[Fe](this[Ze]())&&this[Ge].length);t||this[Ge].length||this[Le]||this.emit("drain")}[Fe](t){return this.emit("data",t),this.flowing}pipe(t,e){if(this[Je])return;const s=this[Be];return e=e||{},t===Ae.stdout||t===Ae.stderr?e.end=!1:e.end=!1!==e.end,e.proxyErrors=!!e.proxyErrors,s?e.end&&t.end():(this[qe].push(e.proxyErrors?new ds(this,t,e):new us(this,t,e)),this[is]?as((()=>this[We]())):this[We]()),t}unpipe(t){const e=this[qe].find((e=>e.dest===t));e&&(this[qe].splice(this[qe].indexOf(e),1),e.unpipe())}addListener(t,e){return this.on(t,e)}on(t,e){const s=super.on(t,e);return"data"!==t||this[qe].length||this.flowing?"readable"===t&&0!==this[Ke]?super.emit("readable"):(t=>"end"===t||"finish"===t||"prefinish"===t)(t)&&this[Be]?(super.emit(t),this.removeAllListeners(t)):"error"===t&&this[$e]&&(this[is]?as((()=>e.call(this,this[$e]))):e.call(this,this[$e])):this[We](),s}get emittedEnd(){return this[Be]}[xe](){this[Pe]||this[Be]||this[Je]||0!==this[Ge].length||!this[Le]||(this[Pe]=!0,this.emit("end"),this.emit("prefinish"),this.emit("finish"),this[Ne]&&this.emit("close"),this[Pe]=!1)}emit(t,e,...s){if("error"!==t&&"close"!==t&&t!==Je&&this[Je])return;if("data"===t)return!(!this[Xe]&&!e)&&(this[is]?as((()=>this[ts](e))):this[ts](e));if("end"===t)return this[es]();if("close"===t){if(this[Ne]=!0,!this[Be]&&!this[Je])return;const t=super.emit("close");return this.removeAllListeners("close"),t}if("error"===t){this[$e]=e,super.emit(Qe,e);const t=!(this[os]&&!this.listeners("error").length)&&super.emit("error",e);return this[xe](),t}if("resume"===t){const t=super.emit("resume");return this[xe](),t}if("finish"===t||"prefinish"===t){const e=super.emit(t);return this.removeAllListeners(t),e}const i=super.emit(t,e,...s);return this[xe](),i}[ts](t){for(const e of this[qe])!1===e.dest.write(t)&&this.pause();const e=super.emit("data",t);return this[xe](),e}[es](){this[Be]||(this[Be]=!0,this.readable=!1,this[is]?as((()=>this[ss]())):this[ss]())}[ss](){if(this[He]){const t=this[He].end();if(t){for(const e of this[qe])e.dest.write(t);super.emit("data",t)}}for(const t of this[qe])t.end();const t=super.emit("end");return this.removeAllListeners("end"),t}collect(){const t=[];this[Xe]||(t.dataLength=0);const e=this.promise();return this.on("data",(e=>{t.push(e),this[Xe]||(t.dataLength+=e.length)})),e.then((()=>t))}concat(){return this[Xe]?Promise.reject(new Error("cannot concat in objectMode")):this.collect().then((t=>this[Xe]?Promise.reject(new Error("cannot concat in objectMode")):this[je]?t.join(""):Buffer.concat(t,t.dataLength)))}promise(){return new Promise(((t,e)=>{this.on(Je,(()=>e(new Error("stream destroyed")))),this.on("error",(t=>e(t))),this.on("end",(()=>t()))}))}[cs](){let t=!1;const e=()=>(this.pause(),t=!0,Promise.resolve({done:!0}));return{next:()=>{if(t)return e();const s=this.read();if(null!==s)return Promise.resolve({done:!1,value:s});if(this[Le])return e();let i=null,r=null;const n=t=>{this.removeListener("data",o),this.removeListener("end",a),this.removeListener(Je,h),e(),r(t)},o=t=>{this.removeListener("error",n),this.removeListener("end",a),this.removeListener(Je,h),this.pause(),i({value:t,done:!!this[Le]})},a=()=>{this.removeListener("error",n),this.removeListener("data",o),this.removeListener(Je,h),e(),i({done:!0})},h=()=>n(new Error("stream destroyed"));return new Promise(((t,e)=>{r=e,i=t,this.once(Je,h),this.once("error",n),this.once("end",a),this.once("data",o)}))},throw:e,return:e,[cs](){return this}}}[ls](){let t=!1;const e=()=>(this.pause(),this.removeListener(Qe,e),this.removeListener(Je,e),this.removeListener("end",e),t=!0,{done:!0});return this.once("end",e),this.once(Qe,e),this.once(Je,e),{next:()=>{if(t)return e();const s=this.read();return null===s?e():{value:s}},throw:e,return:e,[ls](){return this}}}destroy(t){return this[Je]?(t?this.emit("error",t):this.emit(Je),this):(this[Je]=!0,this[Ge].length=0,this[Ke]=0,"function"!=typeof this.close||this[Ne]||this.close(),t?this.emit("error",t):this.emit(Je),this)}static isStream(t){return!!t&&(t instanceof fs||t instanceof ke||t instanceof Te&&("function"==typeof t.pipe||"function"==typeof t.write&&"function"==typeof t.end))}}const ps=fs,ms="object"==typeof process&&process&&"string"==typeof process.platform?process.platform:"linux";class gs{relative;relativeChildren;absolute;absoluteChildren;constructor(t,{nobrace:e,nocase:s,noext:i,noglobstar:r,platform:n=ms}){this.relative=[],this.absolute=[],this.relativeChildren=[],this.absoluteChildren=[];const o={dot:!0,nobrace:e,nocase:s,noext:i,noglobstar:r,optimizationLevel:2,platform:n,nocomment:!0,nonegate:!0};for(const e of t){const t=new q(e,o);for(let e=0;e<t.set.length;e++){const s=t.set[e],i=t.globParts[e],r=new Oe(s,i,0,n),a=new q(r.globString(),o),h="**"===i[i.length-1],c=r.isAbsolute();c?this.absolute.push(a):this.relative.push(a),h&&(c?this.absoluteChildren.push(a):this.relativeChildren.push(a))}}}ignored(t){const e=t.fullpath(),s=`${e}/`,i=t.relative()||".",r=`${i}/`;for(const t of this.relative)if(t.match(i)||t.match(r))return!0;for(const t of this.absolute)if(t.match(e)||t.match(s))return!0;return!1}childrenIgnored(t){const e=t.fullpath()+"/",s=(t.relative()||".")+"/";for(const t of this.relativeChildren)if(t.match(s))return!0;for(const t of this.absoluteChildren)t.match(e);return!1}}class Ss{store;constructor(t=new Map){this.store=t}copy(){return new Ss(new Map(this.store))}hasWalked(t,e){return this.store.get(t.fullpath())?.has(e.globString())}storeWalked(t,e){const s=t.fullpath(),i=this.store.get(s);i?i.add(e.globString()):this.store.set(s,new Set([e.globString()]))}}class ys{store=new Map;add(t,e,s){const i=(e?2:0)|(s?1:0),r=this.store.get(t);this.store.set(t,void 0===r?i:i&r)}entries(){return[...this.store.entries()].map((([t,e])=>[t,!!(2&e),!!(1&e)]))}}class ws{store=new Map;add(t,e){if(!t.canReaddir())return;const s=this.store.get(t);s?s.find((t=>t.globString()===e.globString()))||s.push(e):this.store.set(t,[e])}get(t){const e=this.store.get(t);if(!e)throw new Error("attempting to walk unknown path");return e}entries(){return this.keys().map((t=>[t,this.store.get(t)]))}keys(){return[...this.store.keys()].filter((t=>t.canReaddir()))}}class _s{hasWalkedCache;matches=new ys;subwalks=new ws;patterns;follow;dot;opts;constructor(t,e){this.opts=t,this.follow=!!t.follow,this.dot=!!t.dot,this.hasWalkedCache=e?e.copy():new Ss}processPatterns(t,e){this.patterns=e;const s=e.map((e=>[t,e]));for(let[t,e]of s){this.hasWalkedCache.storeWalked(t,e);const s=e.root(),i=e.isAbsolute()&&!1!==this.opts.absolute;if(s){t=t.resolve("/"===s&&void 0!==this.opts.root?this.opts.root:s);const i=e.rest();if(!i){this.matches.add(t,!0,!1);continue}e=i}if(t.isENOENT())continue;let r,n,o=!1;for(;"string"==typeof(r=e.pattern())&&(n=e.rest());){const s=t.resolve(r);if(s.isUnknown()&&".."!==r)break;t=s,e=n,o=!0}if(r=e.pattern(),n=e.rest(),o){if(this.hasWalkedCache.hasWalked(t,e))continue;this.hasWalkedCache.storeWalked(t,e)}if("string"!=typeof r)if(r===V){(!t.isSymbolicLink()||this.follow||e.checkFollowGlobstar())&&this.subwalks.add(t,e);const s=n?.pattern(),r=n?.rest();if(n&&(""!==s&&"."!==s||r)){if(".."===s){const e=t.parent||t;r?this.hasWalkedCache.hasWalked(e,r)||this.subwalks.add(e,r):this.matches.add(e,i,!0)}}else this.matches.add(t,i,""===s||"."===s)}else r instanceof RegExp&&this.subwalks.add(t,e);else if(n)this.subwalks.add(t,e);else{const e=".."===r||""===r||"."===r;this.matches.add(t.resolve(r),i,e)}}return this}subwalkTargets(){return this.subwalks.keys()}child(){return new _s(this.opts,this.hasWalkedCache)}filterEntries(t,e){const s=this.subwalks.get(t),i=this.child();for(const t of e)for(const e of s){const s=e.isAbsolute(),r=e.pattern(),n=e.rest();r===V?i.testGlobstar(t,e,n,s):r instanceof RegExp?i.testRegExp(t,r,n,s):i.testString(t,r,n,s)}return i}testGlobstar(t,e,s,i){if(!this.dot&&t.name.startsWith(".")||(e.hasMore()||this.matches.add(t,i,!1),t.canReaddir()&&(this.follow||!t.isSymbolicLink()?this.subwalks.add(t,e):t.isSymbolicLink()&&(s&&e.checkFollowGlobstar()?this.subwalks.add(t,s):e.markFollowGlobstar()&&this.subwalks.add(t,e)))),s){const e=s.pattern();if("string"==typeof e&&".."!==e&&""!==e&&"."!==e)this.testString(t,e,s.rest(),i);else if(".."===e){const e=t.parent||t;this.subwalks.add(e,s)}else e instanceof RegExp&&this.testRegExp(t,e,s.rest(),i)}}testRegExp(t,e,s,i){e.test(t.name)&&(s?this.subwalks.add(t,s):this.matches.add(t,i,!1))}testString(t,e,s,i){t.isNamed(e)&&(s?this.subwalks.add(t,s):this.matches.add(t,i,!1))}}class vs{path;patterns;opts;seen=new Set;paused=!1;aborted=!1;#ae=[];#he;#ce;signal;maxDepth;constructor(t,e,s){this.patterns=t,this.path=e,this.opts=s,this.#ce="win32"===s.platform?"\\":"/",s.ignore&&(this.#he=((t,e)=>"string"==typeof t?new gs([t],e):Array.isArray(t)?new gs(t,e):t)(s.ignore,s)),this.maxDepth=s.maxDepth||1/0,s.signal&&(this.signal=s.signal,this.signal.addEventListener("abort",(()=>{this.#ae.length=0})))}#le(t){return this.seen.has(t)||!!this.#he?.ignored?.(t)}#ue(t){return!!this.#he?.childrenIgnored?.(t)}pause(){this.paused=!0}resume(){if(this.signal?.aborted)return;let t;for(this.paused=!1;!this.paused&&(t=this.#ae.shift());)t()}onResume(t){this.signal?.aborted||(this.paused?this.#ae.push(t):t())}async matchCheck(t,e){if(e&&this.opts.nodir)return;let s;if(this.opts.realpath){if(s=t.realpathCached()||await t.realpath(),!s)return;t=s}const i=t.isUnknown()||this.opts.stat;return this.matchCheckTest(i?await t.lstat():t,e)}matchCheckTest(t,e){return!t||!(this.maxDepth===1/0||t.depth()<=this.maxDepth)||e&&!t.canReaddir()||this.opts.nodir&&t.isDirectory()||this.#le(t)?void 0:t}matchCheckSync(t,e){if(e&&this.opts.nodir)return;let s;if(this.opts.realpath){if(s=t.realpathCached()||t.realpathSync(),!s)return;t=s}const i=t.isUnknown()||this.opts.stat;return this.matchCheckTest(i?t.lstatSync():t,e)}matchFinish(t,e){if(this.#le(t))return;const s=void 0===this.opts.absolute?e:this.opts.absolute;this.seen.add(t);const i=this.opts.mark&&t.isDirectory()?this.#ce:"";if(this.opts.withFileTypes)this.matchEmit(t);else if(s)this.matchEmit(t.fullpath()+i);else{const e=t.relative(),s=this.opts.dotRelative&&!e.startsWith(".."+this.#ce)?"."+this.#ce:"";this.matchEmit(!e&&i?"."+i:s+e+i)}}async match(t,e,s){const i=await this.matchCheck(t,s);i&&this.matchFinish(i,e)}matchSync(t,e,s){const i=this.matchCheckSync(t,s);i&&this.matchFinish(i,e)}walkCB(t,e,s){this.signal?.aborted&&s(),this.walkCB2(t,e,new _s(this.opts),s)}walkCB2(t,e,s,i){if(this.#ue(t))return i();if(this.signal?.aborted&&i(),this.paused)return void this.onResume((()=>this.walkCB2(t,e,s,i)));s.processPatterns(t,e);let r=1;const n=()=>{0==--r&&i()};for(const[t,e,i]of s.matches.entries())this.#le(t)||(r++,this.match(t,e,i).then((()=>n())));for(const t of s.subwalkTargets()){if(this.maxDepth!==1/0&&t.depth()>=this.maxDepth)continue;r++;const e=t.readdirCached();t.calledReaddir()?this.walkCB3(t,e,s,n):t.readdirCB(((e,i)=>this.walkCB3(t,i,s,n)),!0)}n()}walkCB3(t,e,s,i){s=s.filterEntries(t,e);let r=1;const n=()=>{0==--r&&i()};for(const[t,e,i]of s.matches.entries())this.#le(t)||(r++,this.match(t,e,i).then((()=>n())));for(const[t,e]of s.subwalks.entries())r++,this.walkCB2(t,e,s.child(),n);n()}walkCBSync(t,e,s){this.signal?.aborted&&s(),this.walkCB2Sync(t,e,new _s(this.opts),s)}walkCB2Sync(t,e,s,i){if(this.#ue(t))return i();if(this.signal?.aborted&&i(),this.paused)return void this.onResume((()=>this.walkCB2Sync(t,e,s,i)));s.processPatterns(t,e);let r=1;const n=()=>{0==--r&&i()};for(const[t,e,i]of s.matches.entries())this.#le(t)||this.matchSync(t,e,i);for(const t of s.subwalkTargets()){if(this.maxDepth!==1/0&&t.depth()>=this.maxDepth)continue;r++;const e=t.readdirSync();this.walkCB3Sync(t,e,s,n)}n()}walkCB3Sync(t,e,s,i){s=s.filterEntries(t,e);let r=1;const n=()=>{0==--r&&i()};for(const[t,e,i]of s.matches.entries())this.#le(t)||this.matchSync(t,e,i);for(const[t,e]of s.subwalks.entries())r++,this.walkCB2Sync(t,e,s.child(),n);n()}}class Es extends vs{matches;constructor(t,e,s){super(t,e,s),this.matches=new Set}matchEmit(t){this.matches.add(t)}async walk(){if(this.signal?.aborted)throw this.signal.reason;return this.path.isUnknown()&&await this.path.lstat(),await new Promise(((t,e)=>{this.walkCB(this.path,this.patterns,(()=>{this.signal?.aborted?e(this.signal.reason):t(this.matches)}))})),this.matches}walkSync(){if(this.signal?.aborted)throw this.signal.reason;return this.path.isUnknown()&&this.path.lstatSync(),this.walkCBSync(this.path,this.patterns,(()=>{if(this.signal?.aborted)throw this.signal.reason})),this.matches}}class bs extends vs{results;constructor(t,e,s){super(t,e,s),this.results=new ps({signal:this.signal,objectMode:!0}),this.results.on("drain",(()=>this.resume())),this.results.on("resume",(()=>this.resume()))}matchEmit(t){this.results.write(t),this.results.flowing||this.pause()}stream(){const t=this.path;return t.isUnknown()?t.lstat().then((()=>{this.walkCB(t,this.patterns,(()=>this.results.end()))})):this.walkCB(t,this.patterns,(()=>this.results.end())),this.results}streamSync(){return this.path.isUnknown()&&this.path.lstatSync(),this.walkCBSync(this.path,this.patterns,(()=>this.results.end())),this.results}}const Is="object"==typeof process&&process&&"string"==typeof process.platform?process.platform:"linux";class Rs{absolute;cwd;root;dot;dotRelative;follow;ignore;magicalBraces;mark;matchBase;maxDepth;nobrace;nocase;nodir;noext;noglobstar;pattern;platform;realpath;scurry;stat;signal;windowsPathsNoEscape;withFileTypes;opts;patterns;constructor(t,e){if(this.withFileTypes=!!e.withFileTypes,this.signal=e.signal,this.follow=!!e.follow,this.dot=!!e.dot,this.dotRelative=!!e.dotRelative,this.nodir=!!e.nodir,this.mark=!!e.mark,e.cwd?(e.cwd instanceof URL||e.cwd.startsWith("file://"))&&(e.cwd=(0,Re.fileURLToPath)(e.cwd)):this.cwd="",this.cwd=e.cwd||"",this.root=e.root,this.magicalBraces=!!e.magicalBraces,this.nobrace=!!e.nobrace,this.noext=!!e.noext,this.realpath=!!e.realpath,this.absolute=e.absolute,this.noglobstar=!!e.noglobstar,this.matchBase=!!e.matchBase,this.maxDepth="number"==typeof e.maxDepth?e.maxDepth:1/0,this.stat=!!e.stat,this.ignore=e.ignore,this.withFileTypes&&void 0!==this.absolute)throw new Error("cannot set absolute and withFileTypes:true");if("string"==typeof t&&(t=[t]),this.windowsPathsNoEscape=!!e.windowsPathsNoEscape||!1===e.allowWindowsEscape,this.windowsPathsNoEscape&&(t=t.map((t=>t.replace(/\\/g,"/")))),this.matchBase){if(e.noglobstar)throw new TypeError("base matching requires globstar");t=t.map((t=>t.includes("/")?t:`./**/${t}`))}if(this.pattern=t,this.platform=e.platform||Is,this.opts={...e,platform:this.platform},e.scurry){if(this.scurry=e.scurry,void 0!==e.nocase&&e.nocase!==e.scurry.nocase)throw new Error("nocase option contradicts provided scurry option")}else{const t="win32"===e.platform?ve:"darwin"===e.platform?be:e.platform?Ee:Ie;this.scurry=new t(this.cwd,{nocase:e.nocase,fs:e.fs})}this.nocase=this.scurry.nocase;const s={...e,dot:this.dot,matchBase:this.matchBase,nobrace:this.nobrace,nocase:this.nocase,nocaseMagicOnly:!0,nocomment:!0,noext:this.noext,nonegate:!0,optimizationLevel:2,platform:this.platform,windowsPathsNoEscape:this.windowsPathsNoEscape,debug:!!this.opts.debug},i=this.pattern.map((t=>new q(t,s))),[r,n]=i.reduce(((t,e)=>(t[0].push(...e.set),t[1].push(...e.globParts),t)),[[],[]]);this.patterns=r.map(((t,e)=>new Oe(t,n[e],0,this.platform)))}async walk(){return[...await new Es(this.patterns,this.scurry.cwd,{...this.opts,maxDepth:this.maxDepth!==1/0?this.maxDepth+this.scurry.cwd.depth():1/0,platform:this.platform,nocase:this.nocase}).walk()]}walkSync(){return[...new Es(this.patterns,this.scurry.cwd,{...this.opts,maxDepth:this.maxDepth!==1/0?this.maxDepth+this.scurry.cwd.depth():1/0,platform:this.platform,nocase:this.nocase}).walkSync()]}stream(){return new bs(this.patterns,this.scurry.cwd,{...this.opts,maxDepth:this.maxDepth!==1/0?this.maxDepth+this.scurry.cwd.depth():1/0,platform:this.platform,nocase:this.nocase}).stream()}streamSync(){return new bs(this.patterns,this.scurry.cwd,{...this.opts,maxDepth:this.maxDepth!==1/0?this.maxDepth+this.scurry.cwd.depth():1/0,platform:this.platform,nocase:this.nocase}).streamSync()}iterateSync(){return this.streamSync()[Symbol.iterator]()}[Symbol.iterator](){return this.iterateSync()}iterate(){return this.stream()[Symbol.asyncIterator]()}[Symbol.asyncIterator](){return this.iterate()}}const Os=(t,e={})=>{Array.isArray(t)||(t=[t]);for(const s of t)if(new q(s,e).hasMagic())return!0;return!1};function Ts(t,e={}){return new Rs(t,e).streamSync()}function ks(t,e={}){return new Rs(t,e).stream()}function Ds(t,e={}){return new Rs(t,e).walkSync()}async function As(t,e={}){return new Rs(t,e).walk()}function Cs(t,e={}){return new Rs(t,e).iterateSync()}function Ls(t,e={}){return new Rs(t,e).iterate()}const xs=Ts,Bs=Object.assign(ks,{sync:Ts}),Ps=Cs,$s=Object.assign(Ls,{sync:Cs}),Ns=Object.assign(Ds,{stream:Ts,iterate:Cs}),Ms=Object.assign(As,{glob:As,globSync:Ds,sync:Ns,globStream:ks,stream:Bs,globStreamSync:Ts,streamSync:xs,globIterate:Ls,iterate:$s,globIterateSync:Cs,iterateSync:Ps,Glob:Rs,hasMagic:Os,escape:_,unescape:c})}},i={};function r(t){var e=i[t];if(void 0!==e)return e.exports;var n=i[t]={exports:{}};return s[t].call(n.exports,n,n.exports,r),n.exports}e=Object.getPrototypeOf?t=>Object.getPrototypeOf(t):t=>t.__proto__,r.t=function(s,i){if(1&i&&(s=this(s)),8&i)return s;if("object"==typeof s&&s){if(4&i&&s.__esModule)return s;if(16&i&&"function"==typeof s.then)return s}var n=Object.create(null);r.r(n);var o={};t=t||[null,e({}),e([]),e(e)];for(var a=2&i&&s;"object"==typeof a&&!~t.indexOf(a);a=e(a))Object.getOwnPropertyNames(a).forEach((t=>o[t]=()=>s[t]));return o.default=()=>s,r.d(n,o),n},r.d=(t,e)=>{for(var s in e)r.o(e,s)&&!r.o(t,s)&&Object.defineProperty(t,s,{enumerable:!0,get:e[s]})},r.o=(t,e)=>Object.prototype.hasOwnProperty.call(t,e),r.r=t=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})};var n=r(5256),o=exports;for(var a in n)o[a]=n[a];n.__esModule&&Object.defineProperty(o,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/b8f002c02d165600299a109bf21d02d139c52644/extensions/windsurf-remote-openssh/dist/extension.js.map
/**
 * This file will be compiled into the extension under media/chat.js.
 */
// This must be imported
import React from 'react';
import { createRoot } from 'react-dom/client';

import { WindsurfPanelManager } from '@exa/chat-client/dist/app/windsurf/WindsurfPanelManager';

import './style.css';

const domNode = document.getElementById('react-app');
if (!domNode) {
  console.error('Failed to find the root element');
  console.log(React);
} else {
  const root = createRoot(domNode);
  root.render(<WindsurfPanelManager />);
}

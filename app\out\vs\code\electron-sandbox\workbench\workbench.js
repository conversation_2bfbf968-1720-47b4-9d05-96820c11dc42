/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/(function(){const y=window.vscode,h=y.process;async function f(r,e){const s=await b();e?.beforeImport?.(s);const{enableDeveloperKeybindings:d,removeDeveloperKeybindingsAfterLoad:t,developerDeveloperKeybindingsDisposable:o,forceDisableShowDevtoolsOnError:c}=g(s,e);n(s);const l=new URL(`${u(s.appRoot,{isWindows:h.platform==="win32",scheme:"vscode-file",fallbackAuthority:"vscode-app"})}/out/`);globalThis._VSCODE_FILE_ROOT=l.toString(),m(s,l);try{const a=await import(new URL(`${r}.js`,l).href);return o&&t&&o(),{result:a,configuration:s}}catch(a){throw p(a,d&&!c),a}}async function b(){const r=setTimeout(()=>{console.error("[resolve window config] Could not resolve window configuration within 10 seconds, but will continue to wait...")},1e4);performance.mark("code/willWaitForWindowConfig");const e=await y.context.resolveConfiguration();return performance.mark("code/didWaitForWindowConfig"),clearTimeout(r),e}function g(r,e){const{forceEnableDeveloperKeybindings:s,disallowReloadKeybinding:d,removeDeveloperKeybindingsAfterLoad:t,forceDisableShowDevtoolsOnError:o}=typeof e?.configureDeveloperSettings=="function"?e.configureDeveloperSettings(r):{forceEnableDeveloperKeybindings:!1,disallowReloadKeybinding:!1,removeDeveloperKeybindingsAfterLoad:!1,forceDisableShowDevtoolsOnError:!1},l=!!(!!h.env.VSCODE_DEV||s);let a;return l&&(a=i(d)),{enableDeveloperKeybindings:l,removeDeveloperKeybindingsAfterLoad:t,developerDeveloperKeybindingsDisposable:a,forceDisableShowDevtoolsOnError:o}}function i(r){const e=y.ipcRenderer,s=function(l){return[l.ctrlKey?"ctrl-":"",l.metaKey?"meta-":"",l.altKey?"alt-":"",l.shiftKey?"shift-":"",l.keyCode].join("")},d=h.platform==="darwin"?"meta-alt-73":"ctrl-shift-73",t="123",o=h.platform==="darwin"?"meta-82":"ctrl-82";let c=function(l){const a=s(l);a===d||a===t?e.send("vscode:toggleDevTools"):a===o&&!r&&e.send("vscode:reloadWindow")};return window.addEventListener("keydown",c),function(){c&&(window.removeEventListener("keydown",c),c=void 0)}}function n(r){globalThis._VSCODE_NLS_MESSAGES=r.nls.messages,globalThis._VSCODE_NLS_LANGUAGE=r.nls.language;let e=r.nls.language||"en";e==="zh-tw"?e="zh-Hant":e==="zh-cn"&&(e="zh-Hans"),window.document.documentElement.setAttribute("lang",e)}function p(r,e){e&&y.ipcRenderer.send("vscode:openDevTools"),console.error(`[uncaught exception]: ${r}`),r&&typeof r!="string"&&r.stack&&console.error(r.stack)}function u(r,e){let s=r.replace(/\\/g,"/");s.length>0&&s.charAt(0)!=="/"&&(s=`/${s}`);let d;return e.isWindows&&s.startsWith("//")?d=encodeURI(`${e.scheme||"file"}:${s}`):d=encodeURI(`${e.scheme||"file"}://${e.fallbackAuthority||""}${s}`),d.replace(/#/g,"%23")}function m(r,e){if(Array.isArray(r.cssModules)&&r.cssModules.length>0){performance.mark("code/willAddCssLoader");const s=document.createElement("style");s.type="text/css",s.media="screen",s.id="vscode-css-loading",document.head.appendChild(s),globalThis._VSCODE_CSS_LOAD=function(l){s.textContent+=`@import url(${l});
`};const d={imports:{react:"../../../../../node_modules/preact/compat/dist/compat.mjs",preact:"../../../../../node_modules/preact/dist/preact.mjs","preact/jsx-runtime":"../../../../../node_modules/preact/jsx-runtime/dist/jsxRuntime.mjs","preact/hooks":"../../../../../node_modules/preact/hooks/dist/hooks.mjs","react-dom/client":"../../../../../node_modules/preact/compat/client.mjs","react/jsx-runtime":"../../../../../node_modules/preact/jsx-runtime/dist/jsxRuntime.mjs","@bufbuild/protobuf":"../../../../../node_modules/@bufbuild/protobuf/dist/esm/index.js","jsonc-parser":"../../../../../node_modules/jsonc-parser/lib/esm/main.js","@connectrpc/connect":"../../../../../node_modules/@connectrpc/connect/dist/esm/index.js","@connectrpc/connect/protocol":"../../../../../node_modules/@connectrpc/connect/dist/esm/protocol/index.js","@connectrpc/connect/protocol-connect":"../../../../../node_modules/@connectrpc/connect/dist/esm/protocol-connect/index.js","@connectrpc/connect/protocol-grpc-web":"../../../../../node_modules/@connectrpc/connect/dist/esm/protocol-grpc-web/index.js","@connectrpc/connect-web":"../../../../../node_modules/@connectrpc/connect-web/dist/esm/index.js","unleash-proxy-client":"../../../../../node_modules/unleash-proxy-client/build/main.esm.js","react-tooltip":"../../../../../node_modules/react-tooltip/dist/react-tooltip.mjs","@floating-ui/dom":"../../../../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs","@floating-ui/core":"../../../../../node_modules/@floating-ui/core/dist/floating-ui.core.mjs","@floating-ui/utils":"../../../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs","@floating-ui/utils/dom":"../../../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs",classnames:"../../../../../node_modules/classnames/index.js","react-redux":"../../../../../node_modules/react-redux/dist/react-redux.browser.mjs","@reduxjs/toolkit":"../../../../../node_modules/@reduxjs/toolkit/dist/redux-toolkit.browser.mjs",redux:"../../../../../node_modules/redux/dist/redux.browser.mjs",immer:"../../../../../node_modules/immer/dist/immer.production.mjs",reselect:"../../../../../node_modules/reselect/dist/reselect.mjs","redux-thunk":"../../../../../node_modules/redux-thunk/dist/redux-thunk.mjs","use-sync-external-store/with-selector.js":"../../../../../node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.production.js","lucide-react":"../../../../../node_modules/lucide-react/dist/esm/lucide-react.js"}};for(const l of r.cssModules){const a=new URL(l,e).href,w=`globalThis._VSCODE_CSS_LOAD('${a}');
`,v=new Blob([w],{type:"application/javascript"});d.imports[a]=URL.createObjectURL(v)}const t=window.trustedTypes?.createPolicy("vscode-bootstrapImportMap",{createScript(l){return l}}),o=JSON.stringify(d,void 0,2),c=document.createElement("script");c.type="importmap",c.setAttribute("nonce","0c6a828f1297"),c.textContent=t?.createScript(o)??o,document.head.appendChild(c),performance.mark("code/didAddCssLoader")}}globalThis.MonacoBootstrapWindow={load:f}})(),async function(){performance.mark("code/didStartRenderer");const y=window.MonacoBootstrapWindow,h=window.vscode;function f(i){performance.mark("code/willShowPartsSplash");let n=i.partsSplash;n&&(i.autoDetectHighContrast&&i.colorScheme.highContrast?(i.colorScheme.dark&&n.baseTheme!=="hc-black"||!i.colorScheme.dark&&n.baseTheme!=="hc-light")&&(n=void 0):i.autoDetectColorScheme&&(i.colorScheme.dark&&n.baseTheme!=="vs-dark"||!i.colorScheme.dark&&n.baseTheme!=="vs")&&(n=void 0)),n&&i.extensionDevelopmentPath&&(n.layoutInfo=void 0);let p,u,m;n?(p=n.baseTheme,u=n.colorInfo.editorBackground,m=n.colorInfo.foreground):i.autoDetectHighContrast&&i.colorScheme.highContrast?i.colorScheme.dark?(p="hc-black",u="#000000",m="#FFFFFF"):(p="hc-light",u="#FFFFFF",m="#000000"):i.autoDetectColorScheme&&(i.colorScheme.dark?(p="vs-dark",u="#1E1E1E",m="#CCCCCC"):(p="vs",u="#FFFFFF",m="#000000"));const r=document.createElement("style");if(r.className="initialShellColors",window.document.head.appendChild(r),r.textContent=`body {	background-color: ${u}; color: ${m}; margin: 0; padding: 0; }`,typeof n?.zoomLevel=="number"&&typeof h?.webFrame?.setZoomLevel=="function"&&h.webFrame.setZoomLevel(n.zoomLevel),n?.layoutInfo){const{layoutInfo:e,colorInfo:s}=n,d=document.createElement("div");if(d.id="monaco-parts-splash",d.className=p??"vs-dark",e.windowBorder&&s.windowBorder){const t=document.createElement("div");t.style.position="absolute",t.style.width="calc(100vw - 2px)",t.style.height="calc(100vh - 2px)",t.style.zIndex="1",t.style.border="1px solid var(--window-border-color)",t.style.setProperty("--window-border-color",s.windowBorder),e.windowBorderRadius&&(t.style.borderRadius=e.windowBorderRadius),d.appendChild(t)}if(e.auxiliarySideBarWidth=Math.min(e.auxiliarySideBarWidth,window.innerWidth-(e.activityBarWidth+e.editorPartMinWidth+e.sideBarWidth)),e.sideBarWidth=Math.min(e.sideBarWidth,window.innerWidth-(e.activityBarWidth+e.editorPartMinWidth+e.auxiliarySideBarWidth)),e.titleBarHeight>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width="100%",t.style.height=`${e.titleBarHeight}px`,t.style.left="0",t.style.top="0",t.style.backgroundColor=`${s.titleBarBackground}`,t.style["-webkit-app-region"]="drag",d.appendChild(t),s.titleBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="100%",o.style.height="1px",o.style.left="0",o.style.bottom="0",o.style.borderBottom=`1px solid ${s.titleBarBorder}`,t.appendChild(o)}}if(e.activityBarWidth>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width=`${e.activityBarWidth}px`,t.style.height=`calc(100% - ${e.titleBarHeight+e.statusBarHeight}px)`,t.style.top=`${e.titleBarHeight}px`,e.sideBarSide==="left"?t.style.left="0":t.style.right="0",t.style.backgroundColor=`${s.activityBarBackground}`,d.appendChild(t),s.activityBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="1px",o.style.height="100%",o.style.top="0",e.sideBarSide==="left"?(o.style.right="0",o.style.borderRight=`1px solid ${s.activityBarBorder}`):(o.style.left="0",o.style.borderLeft=`1px solid ${s.activityBarBorder}`),t.appendChild(o)}}if(i.workspace&&e.sideBarWidth>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width=`${e.sideBarWidth}px`,t.style.height=`calc(100% - ${e.titleBarHeight+e.statusBarHeight}px)`,t.style.top=`${e.titleBarHeight}px`,e.sideBarSide==="left"?t.style.left=`${e.activityBarWidth}px`:t.style.right=`${e.activityBarWidth}px`,t.style.backgroundColor=`${s.sideBarBackground}`,d.appendChild(t),s.sideBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="1px",o.style.height="100%",o.style.top="0",o.style.right="0",e.sideBarSide==="left"?o.style.borderRight=`1px solid ${s.sideBarBorder}`:(o.style.left="0",o.style.borderLeft=`1px solid ${s.sideBarBorder}`),t.appendChild(o)}}if(e.auxiliarySideBarWidth>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width=`${e.auxiliarySideBarWidth}px`,t.style.height=`calc(100% - ${e.titleBarHeight+e.statusBarHeight}px)`,t.style.top=`${e.titleBarHeight}px`,e.sideBarSide==="left"?t.style.right="0":t.style.left="0",t.style.backgroundColor=`${s.sideBarBackground}`,d.appendChild(t),s.sideBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="1px",o.style.height="100%",o.style.top="0",e.sideBarSide==="left"?(o.style.left="0",o.style.borderLeft=`1px solid ${s.sideBarBorder}`):(o.style.right="0",o.style.borderRight=`1px solid ${s.sideBarBorder}`),t.appendChild(o)}}if(e.statusBarHeight>0){const t=document.createElement("div");if(t.style.position="absolute",t.style.width="100%",t.style.height=`${e.statusBarHeight}px`,t.style.bottom="0",t.style.left="0",i.workspace&&s.statusBarBackground?t.style.backgroundColor=s.statusBarBackground:!i.workspace&&s.statusBarNoFolderBackground&&(t.style.backgroundColor=s.statusBarNoFolderBackground),d.appendChild(t),s.statusBarBorder){const o=document.createElement("div");o.style.position="absolute",o.style.width="100%",o.style.height="1px",o.style.top="0",o.style.borderTop=`1px solid ${s.statusBarBorder}`,t.appendChild(o)}}window.document.body.appendChild(d)}performance.mark("code/didShowPartsSplash")}const{result:b,configuration:g}=await y.load("vs/workbench/workbench.desktop.main",{configureDeveloperSettings:function(i){return{forceDisableShowDevtoolsOnError:typeof i.extensionTestsPath=="string"||i["enable-smoke-test-driver"]===!0,forceEnableDeveloperKeybindings:Array.isArray(i.extensionDevelopmentPath)&&i.extensionDevelopmentPath.length>0,removeDeveloperKeybindingsAfterLoad:!0}},beforeImport:function(i){f(i),Object.defineProperty(window,"vscodeWindowId",{get:()=>i.windowId}),window.requestIdleCallback(()=>{const n=document.createElement("canvas");n.getContext("2d")?.clearRect(0,0,n.width,n.height),n.remove()},{timeout:50}),performance.mark("code/willLoadWorkbenchMain")}});performance.mark("code/didLoadWorkbenchMain"),b.main(g)}();

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/b8f002c02d165600299a109bf21d02d139c52644/core/vs/code/electron-sandbox/workbench/workbench.js.map

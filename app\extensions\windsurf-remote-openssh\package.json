{"name": "windsurf-remote-openssh", "displayName": "Windsurf Remote - SSH", "description": "Connect to remote machines over SSH using Windsurf", "version": "0.0.1", "publisher": "codeium", "license": "MIT", "enabledApiProposals": ["resolvers", "contribViewsRemote"], "private": true, "engines": {"vscode": "^1.25.0"}, "extensionKind": ["ui"], "activationEvents": ["onResolveRemoteAuthority:ssh-remote", "onCommand:windsurf-remote-openssh.newWindow", "onCommand:windsurf-remote-openssh.currentWindow", "onCommand:windsurf-remote-openssh.showLog", "onView:windsurfSSHHosts"], "main": "./dist/extension", "capabilities": {"untrustedWorkspaces": {"supported": true}, "virtualWorkspaces": true}, "contributes": {"configuration": {"title": "Windsurf Remote - SSH", "properties": {"remote.windsurfSSH.configFile": {"type": "string", "description": "The absolute file path to a custom SSH config file.", "default": "", "scope": "application"}, "remote.windsurfSSH.path": {"type": "string", "description": "The absolute file path to the SSH executable. If empty, will use the ssh on the PATH.", "default": "", "scope": "application"}, "remote.windsurfSSH.httpProxy": {"type": "string", "description": "The HTTP proxy to pass to the Windsurf server. Kill running server (or wait for it to exit) for this change to take effect.", "default": "", "scope": "application"}, "remote.windsurfSSH.httpsProxy": {"type": "string", "description": "The HTTPS proxy to pass to the Windsurf server. Kill running server (or wait for it to exit) for this change to take effect.", "default": "", "scope": "application"}, "remote.windsurfSSH.experimental.serverDownloadUrlTemplate": {"type": "string", "description": "Experimental: The URL from where the Windsurf server will be downloaded. The following variables can be substituted: ${os}, ${arch}, ${windsurfVersion}, ${vscodeVersion} ${commit}, ${quality}.", "scope": "application", "default": "https://windsurf-stable.codeiumdata.com/${os}-reh-${arch}/${quality}/${commit}/windsurf-reh-${os}-${arch}-${windsurfVersion}.tar.gz"}, "remote.windsurfSSH.experimental.serverBinaryName": {"type": "string", "description": "Experimental: The name of the server binary, use this **only if** you are using a client without a corresponding server release.", "scope": "application", "default": ""}, "remote.windsurfSSH.experimental.disableServerChecksum": {"type": "boolean", "description": "Experimental: Disable the server checksum verification. This is only recommended for development and testing.", "scope": "application", "default": false}}}, "resourceLabelFormatters": [{"scheme": "vscode-remote", "authority": "ssh-remote+*", "formatting": {"label": "${path}", "separator": "/", "tildify": true, "workspaceSuffix": "SSH", "workspaceTooltip": "SSH to a Remote Host"}}], "views": {"remote": [{"id": "windsurfSSHHosts", "name": "SSH (Windsurf)", "group": "targets@1", "remoteName": "ssh-remote"}]}, "commands": [{"title": "Close SSH Process", "category": "Remote-SSH", "command": "windsurf-remote-openssh.closeSSHProcess"}, {"title": "Connect to SSH Host...", "category": "Remote-SSH", "command": "windsurf-remote-openssh.newWindow"}, {"title": "Connect to SSH Host in Current Window...", "category": "Remote-SSH", "command": "windsurf-remote-openssh.currentWindow"}, {"title": "Show SSH Log...", "category": "Remote-SSH", "command": "windsurf-remote-openssh.showLog"}, {"command": "windsurf-remote-openssh.explorer.emptyWindowInNewWindow", "title": "Connect to SSH Host in New Window", "icon": "$(empty-window)"}, {"command": "windsurf-remote-openssh.explorer.emptyWindowInCurrentWindow", "title": "Connect to SSH Host in Current Window", "icon": "$(arrow-right)"}, {"command": "windsurf-remote-openssh.explorer.reopenFolderInCurrentWindow", "title": "Open on SSH Host in Current Window", "icon": "$(arrow-right)"}, {"command": "windsurf-remote-openssh.explorer.reopenFolderInNewWindow", "title": "Open on SSH Host in New Window", "icon": "$(folder-opened)"}, {"command": "windsurf-remote-openssh.explorer.deleteFolderHistoryItem", "title": "Remove from List", "icon": "$(x)"}, {"command": "windsurf-remote-openssh.explorer.refresh", "title": "Refresh", "icon": "$(refresh)"}, {"command": "windsurf-remote-openssh.explorer.configure", "title": "Configure", "icon": "$(gear)"}, {"command": "windsurf-remote-openssh.explorer.add", "title": "Add New", "icon": "$(plus)"}], "menus": {"statusBar/remoteIndicator": [{"command": "windsurf-remote-openssh.newWindow", "when": "remoteName =~ /^ssh-remote$/ && remoteConnectionState == connected", "group": "remote_10_openssh_1general@1"}, {"command": "windsurf-remote-openssh.currentWindow", "when": "remoteName =~ /^ssh-remote$/ && remoteConnectionState == connected", "group": "remote_10_openssh_1general@2"}, {"command": "windsurf-remote-openssh.showLog", "when": "remoteName =~ /^ssh-remote$/ && remoteConnectionState == connected", "group": "remote_10_openssh_1general@4"}, {"command": "windsurf-remote-openssh.newWindow", "when": "remoteConnectionState == disconnected", "group": "remote_10_openssh_3local@1"}, {"command": "windsurf-remote-openssh.currentWindow", "when": "remoteConnectionState == disconnected", "group": "remote_10_openssh_3local@2"}, {"command": "windsurf-remote-openssh.newWindow", "when": "!remoteName && !virtualWorkspace", "group": "remote_10_openssh_3local@5"}, {"command": "windsurf-remote-openssh.currentWindow", "when": "!remoteName && !virtualWorkspace", "group": "remote_10_openssh_3local@6"}], "commandPalette": [{"command": "windsurf-remote-openssh.explorer.refresh", "when": "false"}, {"command": "windsurf-remote-openssh.explorer.configure", "when": "false"}, {"command": "windsurf-remote-openssh.explorer.add", "when": "false"}, {"command": "windsurf-remote-openssh.explorer.emptyWindowInNewWindow", "when": "false"}, {"command": "windsurf-remote-openssh.explorer.emptyWindowInCurrentWindow", "when": "false"}, {"command": "windsurf-remote-openssh.explorer.reopenFolderInCurrentWindow", "when": "false"}, {"command": "windsurf-remote-openssh.explorer.reopenFolderInNewWindow", "when": "false"}, {"command": "windsurf-remote-openssh.explorer.deleteFolderHistoryItem", "when": "false"}], "view/title": [{"command": "windsurf-remote-openssh.explorer.add", "when": "view == windsurfSSHHosts", "group": "navigation"}, {"command": "windsurf-remote-openssh.explorer.configure", "when": "view == windsurfSSHHosts", "group": "navigation"}, {"command": "windsurf-remote-openssh.explorer.refresh", "when": "view == windsurfSSHHosts", "group": "navigation"}], "view/item/context": [{"command": "windsurf-remote-openssh.explorer.emptyWindowInCurrentWindow", "when": "viewItem =~ /^windsurf-remote-openssh.explorer.host$/", "group": "inline@1"}, {"command": "windsurf-remote-openssh.explorer.emptyWindowInNewWindow", "when": "viewItem =~ /^windsurf-remote-openssh.explorer.host$/", "group": "inline@2"}, {"command": "windsurf-remote-openssh.explorer.deleteFolderHistoryItem", "when": "viewItem =~ /^windsurf-remote-openssh.explorer.folder/", "group": "inline@2"}, {"command": "windsurf-remote-openssh.explorer.emptyWindowInCurrentWindow", "when": "viewItem =~ /^windsurf-remote-openssh.explorer.host$/", "group": "navigation@1"}, {"command": "windsurf-remote-openssh.explorer.emptyWindowInNewWindow", "when": "viewItem =~ /^windsurf-remote-openssh.explorer.host$/", "group": "navigation@2"}, {"command": "windsurf-remote-openssh.explorer.reopenFolderInCurrentWindow", "when": "viewItem == windsurf-remote-openssh.explorer.folder", "group": "inline@1"}, {"command": "windsurf-remote-openssh.explorer.reopenFolderInNewWindow", "when": "viewItem == windsurf-remote-openssh.explorer.folder", "group": "inline@2"}, {"command": "windsurf-remote-openssh.explorer.reopenFolderInCurrentWindow", "when": "viewItem == windsurf-remote-openssh.explorer.folder", "group": "navigation@1"}, {"command": "windsurf-remote-openssh.explorer.reopenFolderInNewWindow", "when": "viewItem == windsurf-remote-openssh.explorer.folder", "group": "navigation@2"}, {"command": "windsurf-remote-openssh.explorer.deleteFolderHistoryItem", "when": "viewItem =~ /^windsurf-remote-openssh.explorer.folder/", "group": "navigation@3"}]}}, "prettier": {"singleQuote": true, "useTabs": true, "tabWidth": 4}}
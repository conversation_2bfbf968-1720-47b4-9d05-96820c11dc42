import fetch from "node-fetch";

/**
 * 测试Windsurf API适配器
 */
async function testWindsurfAPI() {
  console.log("🧪 测试Windsurf API适配器...");

  const baseUrl = "http://localhost:3001";
  const apiKey = "123456";

  try {
    // 测试1: 检查模型列表
    console.log("\n📋 测试1: 获取模型列表");
    const modelsResponse = await fetch(`${baseUrl}/v1/models`, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${apiKey}`,
        "model-provider": "claude-windsurf-groq",
      },
    });

    console.log(`状态码: ${modelsResponse.status}`);
    if (modelsResponse.ok) {
      const models = await modelsResponse.json();
      console.log("✅ 模型列表获取成功");
      console.log(`支持的模型数量: ${models.data?.length || 0}`);
      if (models.data && models.data.length > 0) {
        console.log(`第一个模型: ${models.data[0].id}`);
      }
    } else {
      const error = await modelsResponse.text();
      console.log("❌ 模型列表获取失败:", error);
    }

    // 测试2: 发送聊天请求
    console.log("\n💬 测试2: 发送聊天请求");
    const chatResponse = await fetch(`${baseUrl}/v1/chat/completions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
        "model-provider": "claude-windsurf-groq",
      },
      body: JSON.stringify({
        model: "claude-4-sonnet",
        messages: [
          {
            role: "user",
            content: "Hello! Please respond with a simple greeting.",
          },
        ],
        max_tokens: 100,
      }),
    });

    console.log(`状态码: ${chatResponse.status}`);
    if (chatResponse.ok) {
      const chatResult = await chatResponse.json();
      console.log("✅ 聊天请求成功");
      console.log(
        `响应内容: ${
          chatResult.choices?.[0]?.message?.content ||
          chatResult.content?.[0]?.text ||
          "无内容"
        }`
      );
      console.log(`使用的模型: ${chatResult.model || "未知"}`);
    } else {
      const error = await chatResponse.text();
      console.log("❌ 聊天请求失败:", error);
    }

    // 测试3: 测试Claude原生格式
    console.log("\n🎭 测试3: 测试Claude原生格式");
    const claudeResponse = await fetch(`${baseUrl}/v1/messages`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${apiKey}`,
        "model-provider": "claude-windsurf-groq",
      },
      body: JSON.stringify({
        model: "claude-4-sonnet",
        messages: [
          {
            role: "user",
            content: "Hello! Please respond with a simple greeting.",
          },
        ],
        max_tokens: 100,
      }),
    });

    console.log(`状态码: ${claudeResponse.status}`);
    if (claudeResponse.ok) {
      const claudeResult = await claudeResponse.json();
      console.log("✅ Claude格式请求成功");
      console.log(`响应内容: ${claudeResult.content?.[0]?.text || "无内容"}`);
      console.log(`使用的模型: ${claudeResult.model || "未知"}`);
    } else {
      const error = await claudeResponse.text();
      console.log("❌ Claude格式请求失败:", error);
    }
  } catch (error) {
    console.error("🚨 测试过程中发生错误:", error.message);
  }
}

// 运行测试
testWindsurfAPI().catch(console.error);

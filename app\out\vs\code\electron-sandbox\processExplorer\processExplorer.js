/*!--------------------------------------------------------
 * Copyright (C) Microsoft Corporation. All rights reserved.
 *--------------------------------------------------------*/(function(){const l=window.vscode,a=l.process;async function u(t,e){const o=await p();e?.beforeImport?.(o);const{enableDeveloperKeybindings:r,removeDeveloperKeybindingsAfterLoad:d,developerDeveloperKeybindingsDisposable:c,forceDisableShowDevtoolsOnError:i}=m(o,e);g(o);const n=new URL(`${w(o.appRoot,{isWindows:a.platform==="win32",scheme:"vscode-file",fallbackAuthority:"vscode-app"})}/out/`);globalThis._VSCODE_FILE_ROOT=n.toString(),_(o,n);try{const s=await import(new URL(`${t}.js`,n).href);return c&&d&&c(),{result:s,configuration:o}}catch(s){throw b(s,r&&!i),s}}async function p(){const t=setTimeout(()=>{console.error("[resolve window config] Could not resolve window configuration within 10 seconds, but will continue to wait...")},1e4);performance.mark("code/willWaitForWindowConfig");const e=await l.context.resolveConfiguration();return performance.mark("code/didWaitForWindowConfig"),clearTimeout(t),e}function m(t,e){const{forceEnableDeveloperKeybindings:o,disallowReloadKeybinding:r,removeDeveloperKeybindingsAfterLoad:d,forceDisableShowDevtoolsOnError:c}=typeof e?.configureDeveloperSettings=="function"?e.configureDeveloperSettings(t):{forceEnableDeveloperKeybindings:!1,disallowReloadKeybinding:!1,removeDeveloperKeybindingsAfterLoad:!1,forceDisableShowDevtoolsOnError:!1},n=!!(!!a.env.VSCODE_DEV||o);let s;return n&&(s=f(r)),{enableDeveloperKeybindings:n,removeDeveloperKeybindingsAfterLoad:d,developerDeveloperKeybindingsDisposable:s,forceDisableShowDevtoolsOnError:c}}function f(t){const e=l.ipcRenderer,o=function(n){return[n.ctrlKey?"ctrl-":"",n.metaKey?"meta-":"",n.altKey?"alt-":"",n.shiftKey?"shift-":"",n.keyCode].join("")},r=a.platform==="darwin"?"meta-alt-73":"ctrl-shift-73",d="123",c=a.platform==="darwin"?"meta-82":"ctrl-82";let i=function(n){const s=o(n);s===r||s===d?e.send("vscode:toggleDevTools"):s===c&&!t&&e.send("vscode:reloadWindow")};return window.addEventListener("keydown",i),function(){i&&(window.removeEventListener("keydown",i),i=void 0)}}function g(t){globalThis._VSCODE_NLS_MESSAGES=t.nls.messages,globalThis._VSCODE_NLS_LANGUAGE=t.nls.language;let e=t.nls.language||"en";e==="zh-tw"?e="zh-Hant":e==="zh-cn"&&(e="zh-Hans"),window.document.documentElement.setAttribute("lang",e)}function b(t,e){e&&l.ipcRenderer.send("vscode:openDevTools"),console.error(`[uncaught exception]: ${t}`),t&&typeof t!="string"&&t.stack&&console.error(t.stack)}function w(t,e){let o=t.replace(/\\/g,"/");o.length>0&&o.charAt(0)!=="/"&&(o=`/${o}`);let r;return e.isWindows&&o.startsWith("//")?r=encodeURI(`${e.scheme||"file"}:${o}`):r=encodeURI(`${e.scheme||"file"}://${e.fallbackAuthority||""}${o}`),r.replace(/#/g,"%23")}function _(t,e){if(Array.isArray(t.cssModules)&&t.cssModules.length>0){performance.mark("code/willAddCssLoader");const o=document.createElement("style");o.type="text/css",o.media="screen",o.id="vscode-css-loading",document.head.appendChild(o),globalThis._VSCODE_CSS_LOAD=function(n){o.textContent+=`@import url(${n});
`};const r={imports:{react:"../../../../../node_modules/preact/compat/dist/compat.mjs",preact:"../../../../../node_modules/preact/dist/preact.mjs","preact/jsx-runtime":"../../../../../node_modules/preact/jsx-runtime/dist/jsxRuntime.mjs","preact/hooks":"../../../../../node_modules/preact/hooks/dist/hooks.mjs","react-dom/client":"../../../../../node_modules/preact/compat/client.mjs","react/jsx-runtime":"../../../../../node_modules/preact/jsx-runtime/dist/jsxRuntime.mjs","@bufbuild/protobuf":"../../../../../node_modules/@bufbuild/protobuf/dist/esm/index.js","jsonc-parser":"../../../../../node_modules/jsonc-parser/lib/esm/main.js","@connectrpc/connect":"../../../../../node_modules/@connectrpc/connect/dist/esm/index.js","@connectrpc/connect/protocol":"../../../../../node_modules/@connectrpc/connect/dist/esm/protocol/index.js","@connectrpc/connect/protocol-connect":"../../../../../node_modules/@connectrpc/connect/dist/esm/protocol-connect/index.js","@connectrpc/connect/protocol-grpc-web":"../../../../../node_modules/@connectrpc/connect/dist/esm/protocol-grpc-web/index.js","@connectrpc/connect-web":"../../../../../node_modules/@connectrpc/connect-web/dist/esm/index.js","unleash-proxy-client":"../../../../../node_modules/unleash-proxy-client/build/main.esm.js","react-tooltip":"../../../../../node_modules/react-tooltip/dist/react-tooltip.mjs","@floating-ui/dom":"../../../../../node_modules/@floating-ui/dom/dist/floating-ui.dom.mjs","@floating-ui/core":"../../../../../node_modules/@floating-ui/core/dist/floating-ui.core.mjs","@floating-ui/utils":"../../../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.mjs","@floating-ui/utils/dom":"../../../../../node_modules/@floating-ui/utils/dist/floating-ui.utils.dom.mjs",classnames:"../../../../../node_modules/classnames/index.js","react-redux":"../../../../../node_modules/react-redux/dist/react-redux.browser.mjs","@reduxjs/toolkit":"../../../../../node_modules/@reduxjs/toolkit/dist/redux-toolkit.browser.mjs",redux:"../../../../../node_modules/redux/dist/redux.browser.mjs",immer:"../../../../../node_modules/immer/dist/immer.production.mjs",reselect:"../../../../../node_modules/reselect/dist/reselect.mjs","redux-thunk":"../../../../../node_modules/redux-thunk/dist/redux-thunk.mjs","use-sync-external-store/with-selector.js":"../../../../../node_modules/use-sync-external-store/cjs/use-sync-external-store-with-selector.production.js","lucide-react":"../../../../../node_modules/lucide-react/dist/esm/lucide-react.js"}};for(const n of t.cssModules){const s=new URL(n,e).href,v=`globalThis._VSCODE_CSS_LOAD('${s}');
`,h=new Blob([v],{type:"application/javascript"});r.imports[s]=URL.createObjectURL(h)}const d=window.trustedTypes?.createPolicy("vscode-bootstrapImportMap",{createScript(n){return n}}),c=JSON.stringify(r,void 0,2),i=document.createElement("script");i.type="importmap",i.setAttribute("nonce","0c6a828f1297"),i.textContent=d?.createScript(c)??c,document.head.appendChild(i),performance.mark("code/didAddCssLoader")}}globalThis.MonacoBootstrapWindow={load:u}})(),async function(){const l=window.MonacoBootstrapWindow,{result:a,configuration:u}=await l.load("vs/code/electron-sandbox/processExplorer/processExplorerMain",{configureDeveloperSettings:function(){return{forceEnableDeveloperKeybindings:!0}}});a.startup(u)}();

//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/b8f002c02d165600299a109bf21d02d139c52644/core/vs/code/electron-sandbox/processExplorer/processExplorer.js.map

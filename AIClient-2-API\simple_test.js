import http from "http";

console.log("🔍 测试服务器连接...");

const options = {
  hostname: "localhost",
  port: 3002,
  path: "/health",
  method: "GET",
};

const req = http.request(options, (res) => {
  console.log(`状态码: ${res.statusCode}`);
  console.log(`响应头: ${JSON.stringify(res.headers)}`);

  res.setEncoding("utf8");
  res.on("data", (chunk) => {
    console.log(`响应体: ${chunk}`);
  });
  res.on("end", () => {
    console.log("✅ 健康检查完成");
  });
});

req.on("error", (e) => {
  console.error(`❌ 请求错误: ${e.message}`);
});

req.end();

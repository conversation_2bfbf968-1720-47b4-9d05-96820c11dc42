(()=>{"use strict";var e={852:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.RemoteWSLResolver=t.getRemoteAuthority=t.REMOTE_WSL_AUTHORITY=void 0;const s=i(n(398)),a=n(722);t.REMOTE_WSL_AUTHORITY="wsl",t.getRemoteAuthority=function(e){return`${t.REMOTE_WSL_AUTHORITY}+${e}`};class l{constructor(e,t){this.remoteAddress=e,this.localAddress=t,this._onDidDisposeEmitter=new s.EventEmitter,this.onDidDispose=this._onDidDisposeEmitter.event,"localhost"!==t.host&&"127.0.0.1"!==t.host&&(t.host="localhost")}dispose(){this._onDidDisposeEmitter.fire()}}t.RemoteWSLResolver=class{constructor(e,t){this.wslManager=e,this.logger=t}resolve(e,n){const[r,o]=e.split("+");if(r!==t.REMOTE_WSL_AUTHORITY)throw new Error(`Invalid authority type for WSL resolver: ${r}`);this.logger.info(`Resolving wsl remote authority '${e}' (attempt #${n.resolveAttempt})`);const i=s.workspace.getConfiguration("remote.WSL").get("serverDownloadUrlTemplate"),l=s.workspace.getConfiguration("remote.WSL").get("experimental.disableServerChecksum",!1);return s.window.withProgress({title:`Setting up WSL Distro: ${o}`,location:s.ProgressLocation.Notification,cancellable:!1},(async()=>{try{const e=await(0,a.installCodeServer)(this.wslManager,o,i,[],[],this.logger,l);return this.labelFormatterDisposable?.dispose(),this.labelFormatterDisposable=s.workspace.registerResourceLabelFormatter({scheme:"vscode-remote",authority:`${t.REMOTE_WSL_AUTHORITY}+*`,formatting:{label:"${path}",separator:"/",tildify:!0,workspaceSuffix:`WSL: ${o}`,workspaceTooltip:`Running in ${o}`}}),new s.ResolvedAuthority("127.0.0.1",e.listeningOn,e.connectionToken)}catch(e){if(this.logger.error("Error resolving authority",e),1===n.resolveAttempt){this.logger.show();const e="Close Remote",t="Retry",n=await s.window.showErrorMessage(`Could not establish connection to WSL distro "${o}"`,{modal:!0},e,t);n===e?await s.commands.executeCommand("workbench.action.remote.close"):n===t&&await s.commands.executeCommand("workbench.action.reloadWindow")}throw e instanceof a.ServerInstallError||!(e instanceof Error)?s.RemoteAuthorityResolverError.NotAvailable(e instanceof Error?e.message:String(e)):s.RemoteAuthorityResolverError.TemporarilyNotAvailable(e.message)}}))}async tunnelFactory(e){return new l(e.remoteAddress,{host:e.remoteAddress.host,port:e.localAddressPort??e.remoteAddress.port})}dispose(){this.labelFormatterDisposable?.dispose()}}},414:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.deleteWSLDistro=t.setDefaultWSLDistro=t.openRemoteWSLLocationWindow=t.openRemoteWSLWindow=t.promptInstallNewWSLDistro=t.promptOpenRemoteWSLWindow=void 0;const a=i(n(398)),l=n(852),c=s(n(309));function d(e,t){a.commands.executeCommand("vscode.newWindow",{remoteAuthority:(0,l.getRemoteAuthority)(e),reuseWindow:t})}t.promptOpenRemoteWSLWindow=async function(e,t,n){let r;if(t){const t=await e.listDistros();r=t.find((e=>e.isDefault))?.name}else r=(await async function(e,t){const n=e.listDistros().then((e=>e.map((e=>({...e,label:`${e.name}`,detail:e.isDefault?"default distro":void 0})))));return await a.window.showQuickPick(n,{canPickMany:!1,placeHolder:"Select WSL distro"})}(e))?.name;r&&d(r,n)},t.promptInstallNewWSLDistro=async function(e){const t=(await async function(e,t){const n=Promise.all([e.listOnlineDistros(),e.listDistros()]).then((([e,t])=>e.filter((e=>!t.some((t=>t.name===e.name)))).map((e=>({...e,label:`${e.friendlyName}`})))));return await a.window.showQuickPick(n,{canPickMany:!1,placeHolder:"Select the WSL distro to install"})}(e))?.name;t&&c.default.runCommand(`wsl.exe --install -d ${t}`)},t.openRemoteWSLWindow=d,t.openRemoteWSLLocationWindow=function(e,t,n){a.commands.executeCommand("vscode.openFolder",a.Uri.from({scheme:"vscode-remote",authority:(0,l.getRemoteAuthority)(e),path:t}),{forceNewWindow:!n})},t.setDefaultWSLDistro=async function(e,t){await e.setDefaultDistro(t)},t.deleteWSLDistro=async function(e,t){const n="Delete";return await a.window.showInformationMessage(`Are you sure you want to permanently delete the distro "${t}" including all its data?`,{modal:!0},n)===n&&(await e.deleteDistro(t),!0)}},130:(e,t)=>{function n(e){for(;e.length;){const t=e.pop();t&&t.dispose()}}Object.defineProperty(t,"__esModule",{value:!0}),t.Disposable=t.disposeAll=void 0,t.disposeAll=n,t.Disposable=class{constructor(){this._isDisposed=!1,this._disposables=[]}dispose(){this._isDisposed||(this._isDisposed=!0,n(this._disposables))}_register(e){return this._isDisposed?e.dispose():this._disposables.push(e),e}get isDisposed(){return this._isDisposed}}},166:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.EventEmitter=t.once=t.toPromise=void 0,t.toPromise=function(e,n){return n?n.aborted?Promise.resolve(void 0):new Promise((r=>{const o=(0,t.once)(e,(e=>{n.removeEventListener("abort",i),r(e)})),i=()=>{o.dispose(),n.removeEventListener("abort",i),r(void 0)};n.addEventListener("abort",i)})):new Promise((n=>(0,t.once)(e,n)))},t.once=(e,t)=>{const n=e((e=>{t(e),n.dispose()}));return n},t.EventEmitter=class{constructor(){this.event=(e,t,n)=>{const r=this.add(t?e.bind(t):e);return n?.push(r),r}}get size(){return this.listeners?"function"==typeof this.listeners?1:this.listeners.length:0}fire(e){if(this.listeners)if("function"==typeof this.listeners)this.listeners(e);else for(const t of this.listeners)t(e)}dispose(){this.listeners=void 0}add(e){return this.listeners?"function"==typeof this.listeners?this.listeners=[this.listeners,e]:this.listeners.push(e):this.listeners=e,{dispose:()=>this.rm(e)}}rm(e){if(!this.listeners)return;if("function"==typeof this.listeners)return void(this.listeners===e&&(this.listeners=void 0));const t=this.listeners.indexOf(e);-1!==t&&(2===this.listeners.length?this.listeners=0===t?this.listeners[1]:this.listeners[0]:this.listeners=this.listeners.slice(0,t).concat(this.listeners.slice(t+1)))}}},620:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=i(n(398));function a(e,t,n=" "){return n.repeat(Math.max(0,t-e.length))+e}t.default=class{constructor(e){this.output=s.window.createOutputChannel(e)}data2String(e){return e instanceof Error?e.stack||e.message:!1===e.success&&e.message?e.message:e.toString()}trace(e,t){this.logLevel("Trace",e,t)}info(e,t){this.logLevel("Info",e,t)}error(e,t){this.logLevel("Error",e,t)}logLevel(e,t,n){this.output.appendLine(`[${e}  - ${this.now()}] ${t}`),n&&this.output.appendLine(this.data2String(n))}now(){const e=new Date;return a(e.getUTCHours()+"",2,"0")+":"+a(e.getMinutes()+"",2,"0")+":"+a(e.getUTCSeconds()+"",2,"0")+"."+e.getMilliseconds()}show(){this.output.show()}dispose(){this.output.dispose()}}},611:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.isLinux=t.isMacintosh=t.isWindows=void 0,t.isWindows="win32"===process.platform,t.isMacintosh="darwin"===process.platform,t.isLinux="linux"===process.platform},146:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.DistroTreeDataProvider=void 0;const s=i(n(398)),a=i(n(928)),l=n(130),c=n(414);class d{constructor(e,t,n){this.name=e,this.isDefault=t,this.locations=n}}class u{constructor(e,t){this.path=e,this.name=t}}class f extends l.Disposable{constructor(e,t){super(),this.locationHistory=e,this.wslManager=t,this._onDidChangeTreeData=this._register(new s.EventEmitter),this.onDidChangeTreeData=this._onDidChangeTreeData.event,this._register(s.commands.registerCommand("windsurfremotewsl.explorer.addDistro",(()=>(0,c.promptInstallNewWSLDistro)(t)))),this._register(s.commands.registerCommand("windsurfremotewsl.explorer.refresh",(()=>this.refresh()))),this._register(s.commands.registerCommand("windsurfremotewsl.explorer.emptyWindowInNewWindow",(e=>this.openRemoteWSLWindow(e,!1)))),this._register(s.commands.registerCommand("windsurfremotewsl.explorer.emptyWindowInCurrentWindow",(e=>this.openRemoteWSLWindow(e,!0)))),this._register(s.commands.registerCommand("windsurfremotewsl.explorer.reopenFolderInNewWindow",(e=>this.openRemoteWSLocationWindow(e,!1)))),this._register(s.commands.registerCommand("windsurfremotewsl.explorer.reopenFolderInCurrentWindow",(e=>this.openRemoteWSLocationWindow(e,!0)))),this._register(s.commands.registerCommand("windsurfremotewsl.explorer.deleteFolderHistoryItem",(e=>this.deleteDistroLocation(e)))),this._register(s.commands.registerCommand("windsurfremotewsl.explorer.setDefaultDistro",(e=>this.setDefaultDistro(e)))),this._register(s.commands.registerCommand("windsurfremotewsl.explorer.deleteDistro",(e=>this.deleteDistro(e))))}getTreeItem(e){if(e instanceof u){const t=a.posix.basename(e.path).replace(/\.code-workspace$/," (Workspace)"),n=new s.TreeItem(t);return n.description=a.posix.dirname(e.path),n.iconPath=new s.ThemeIcon("folder"),n.contextValue="windsurfremotewsl.explorer.folder",n}const t=new s.TreeItem(e.name);return t.description=e.isDefault?"default distro":void 0,t.collapsibleState=e.locations.length?s.TreeItemCollapsibleState.Collapsed:s.TreeItemCollapsibleState.None,t.iconPath=new s.ThemeIcon("vm"),t.contextValue="windsurfremotewsl.explorer.distro",t}async getChildren(e){return e?e instanceof d?e.locations.map((t=>new u(t,e.name))):[]:(await this.wslManager.listDistros()).map((e=>new d(e.name,e.isDefault,this.locationHistory.getHistory(e.name))))}refresh(){this._onDidChangeTreeData.fire()}async deleteDistroLocation(e){await this.locationHistory.removeLocation(e.name,e.path),this.refresh()}async openRemoteWSLWindow(e,t){(0,c.openRemoteWSLWindow)(e.name,t)}async openRemoteWSLocationWindow(e,t){(0,c.openRemoteWSLLocationWindow)(e.name,e.path,t)}async setDefaultDistro(e){await(0,c.setDefaultWSLDistro)(this.wslManager,e.name),this.refresh()}async deleteDistro(e){await(0,c.deleteWSLDistro)(this.wslManager,e.name),this.refresh()}}t.DistroTreeDataProvider=f},927:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t},s=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.deactivate=t.activate=void 0;const a=i(n(398)),l=s(n(620)),c=n(852),d=n(414),u=n(146),f=n(193),h=n(506),m=n(611);t.activate=async function(e){if(!m.isWindows)return;const t=new l.default("Remote - WSL");e.subscriptions.push(t);const n=new h.WSLManager(t),r=new c.RemoteWSLResolver(n,t);e.subscriptions.push(a.workspace.registerRemoteAuthorityResolver(c.REMOTE_WSL_AUTHORITY,r)),e.subscriptions.push(r);const o=new f.RemoteLocationHistory(e),i=(0,f.getRemoteWorkspaceLocationData)();i&&await o.addLocation(i[0],i[1]);const s=new u.DistroTreeDataProvider(o,n);e.subscriptions.push(s),e.subscriptions.push(a.window.createTreeView("windsurfWslTargets",{treeDataProvider:s})),e.subscriptions.push(a.commands.registerCommand("windsurfremotewsl.connect",(()=>(0,d.promptOpenRemoteWSLWindow)(n,!0,!0)))),e.subscriptions.push(a.commands.registerCommand("windsurfremotewsl.connectInNewWindow",(()=>(0,d.promptOpenRemoteWSLWindow)(n,!0,!1)))),e.subscriptions.push(a.commands.registerCommand("windsurfremotewsl.connectUsingDistro",(()=>(0,d.promptOpenRemoteWSLWindow)(n,!1,!0)))),e.subscriptions.push(a.commands.registerCommand("windsurfremotewsl.connectUsingDistroInNewWindow",(()=>(0,d.promptOpenRemoteWSLWindow)(n,!1,!1)))),e.subscriptions.push(a.commands.registerCommand("windsurfremotewsl.showLog",(()=>t.show())))},t.deactivate=function(){}},193:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.getRemoteWorkspaceLocationData=t.RemoteLocationHistory=void 0;const s=i(n(398)),a=n(852);class l{constructor(e){this.context=e,this.remoteLocationHistory={},this.remoteLocationHistory=e.globalState.get(l.STORAGE_KEY)||{}}getHistory(e){return this.remoteLocationHistory[e]||[]}async addLocation(e,t){const n=this.remoteLocationHistory[e]||[];n.includes(t)||(n.unshift(t),this.remoteLocationHistory[e]=n,await this.context.globalState.update(l.STORAGE_KEY,this.remoteLocationHistory))}async removeLocation(e,t){let n=this.remoteLocationHistory[e]||[];n=n.filter((e=>e!==t)),this.remoteLocationHistory[e]=n,await this.context.globalState.update(l.STORAGE_KEY,this.remoteLocationHistory)}}t.RemoteLocationHistory=l,l.STORAGE_KEY="remoteLocationHistory_v0",t.getRemoteWorkspaceLocationData=function(){let e=s.workspace.workspaceFile;if(e&&"vscode-remote"===e.scheme&&e.authority.startsWith(a.REMOTE_WSL_AUTHORITY)&&e.path.endsWith(".code-workspace")){const[,t]=e.authority.split("+");return[t,e.path]}if(e=s.workspace.workspaceFolders?.[0].uri,e&&"vscode-remote"===e.scheme&&e.authority.startsWith(a.REMOTE_WSL_AUTHORITY)){const[,t]=e.authority.split("+");return[t,e.path]}}},553:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.getVSCodeServerConfig=void 0;const s=i(n(398)),a=i(n(896)),l=i(n(928));let c;t.getVSCodeServerConfig=async function(){const e=await async function(){if(!c){const e=await a.promises.readFile(l.join(s.env.appRoot,"product.json"),"utf8");c=JSON.parse(e)}return c}(),t=s.version.replace("-insider","");let n=e.windsurfVersion,r=e.commit,o=e.quality,i=e.serverApplicationName,d=e.serverDataFolderName;if(void 0===o){const e=await fetch("https://windsurf-nightly.corp.exafunction.com/api/update/linux-reh-x64/insider/latest"),t=await e.json();n=t.windsurfVersion,r=t.version,o="insider",i="windsurf-server-insiders",d=".windsurf-server-insiders"}return{vscodeVersion:t,windsurfVersion:n,commit:r,quality:o,release:e.release,serverApplicationName:i,serverDataFolderName:d,serverDownloadUrlTemplate:e.serverDownloadUrlTemplate}}},722:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.generateBashInstallScript=t.installCodeServer=t.getDefaultDownloadUrlTemplate=t.ServerInstallError=void 0;const s=i(n(982)),a=n(553);class l extends Error{constructor(e){super(e)}}function c(e){return"insider"===e||void 0===e?"https://windsurf-nightly.codeiumdata.com/${os}-reh-${arch}/${quality}/${commit}/windsurf-reh-${os}-${arch}-${windsurfVersion}.tar.gz":"https://windsurf-stable.codeiumdata.com/${os}-reh-${arch}/${quality}/${commit}/windsurf-reh-${os}-${arch}-${windsurfVersion}.tar.gz"}function d({id:e,quality:t,vscodeVersion:n,windsurfVersion:r,commit:o,release:i,extensionIds:s,envVariables:a,serverApplicationName:l,serverDataFolderName:c,serverDownloadUrlTemplate:d,disableServerChecksum:u},f){const h=s.map((e=>"--install-extension "+e)).join(" ");return`\n# Server installation script\n\nTMP_DIR="\${XDG_RUNTIME_DIR:-"/tmp"}"\n\nDISTRO_VSCODE_VERSION="${n}"\nDISTRO_WINDSURF_VERSION="${r}"\nDISTRO_COMMIT="${o}"\nDISTRO_QUALITY="${t}"\nDISTRO_VSCODIUM_RELEASE="${i??""}"\n\nSERVER_APP_NAME="${l}"\nSERVER_INITIAL_EXTENSIONS="${h}"\nSERVER_LISTEN_FLAG="--port=0"\nSERVER_DATA_DIR="$HOME/${c}"\nSERVER_DIR="$SERVER_DATA_DIR/bin/$DISTRO_COMMIT"\nSERVER_SCRIPT="$SERVER_DIR/bin/$SERVER_APP_NAME"\nSERVER_LOGFILE="$SERVER_DATA_DIR/.$DISTRO_COMMIT.log"\nSERVER_PIDFILE="$SERVER_DATA_DIR/.$DISTRO_COMMIT.pid"\nSERVER_TOKENFILE="$SERVER_DATA_DIR/.$DISTRO_COMMIT.token"\nSERVER_ARCH=\nSERVER_CONNECTION_TOKEN=\nSERVER_DOWNLOAD_URL=\n\nLISTENING_ON=\nOS_RELEASE_ID=\nARCH=\nPLATFORM=\nSERVER_PID=\n\nGLIBC_VERSION_GOOD=\n\n# Add lock mechanism\nLOCK_FILE="$SERVER_DATA_DIR/.installation_lock"\n\n# Function to acquire lock\nacquire_lock() {\n\texec 200>$LOCK_FILE\n\techo "Waiting for lock..."\n\tflock 200\n\techo "Lock acquired, proceeding with installation."\n}\n\n# Function to release lock\nrelease_lock() {\n\tflock -u 200\n\texec 200>&-\n}\n\n# Mimic output from logs of remote-ssh extension\nprint_install_results_and_exit() {\n\tif [[ $1 -eq 1 ]]; then\n\t\techo ""\n\t\techo "Error: installation failed."\n\t\tif [[ -f "$SERVER_LOGFILE" ]]; then\n\t\t\techo "Server log:\n $(cat "$SERVER_LOGFILE")\n"\n\t\tfi\n\tfi\n\tif [[ "$GLIBC_VERSION_GOOD" = "false" ]]; then\n\t\techo "Warning: valid glibc version not found. Windsurf only supports remote connections with glibc >= 2.28, such as Ubuntu 20.04, Debian 10, or CentOS 8."\n\t\techo ""\n\tfi\n\techo "${e}: start"\n\techo "exitCode==$1=="\n\techo "listeningOn==$LISTENING_ON=="\n\techo "connectionToken==$SERVER_CONNECTION_TOKEN=="\n\techo "logFile==$SERVER_LOGFILE=="\n\techo "osReleaseId==$OS_RELEASE_ID=="\n\techo "arch==$ARCH=="\n\techo "platform==$PLATFORM=="\n\techo "tmpDir==$TMP_DIR=="\n\t${a.map((e=>`echo "${e}==$${e}=="`)).join("\n")}\n\techo "${e}: end"\n\t\n\texit 0\n}\n\n# Check if platform is supported\nKERNEL="$(uname -s)"\ncase $KERNEL in\n\tLinux)\n\t\tPLATFORM="linux"\n\t\t;;\n\t*)\n\t\techo "Error platform not supported: $KERNEL"\n\t\tprint_install_results_and_exit 1\n\t\t;;\nesac\n\n# Check machine architecture\nARCH="$(uname -m)"\ncase $ARCH in\n\tx86_64 | amd64)\n\t\tSERVER_ARCH="x64"\n\t\t;;\n\tarm64 | aarch64)\n\t\tSERVER_ARCH="arm64"\n\t\t;;\n\t*)\n\t\techo "Error architecture not supported: $ARCH"\n\t\tprint_install_results_and_exit 1\n\t\t;;\nesac\n\n# Attempt to get checksum from manifest\n\nif [[ "$DISTRO_QUALITY" = "insider" ]]; then\n\tMANIFEST_URL="https://windsurf-nightly.codeiumdata.com/\${PLATFORM}-reh-\${SERVER_ARCH}/\${DISTRO_QUALITY}/manifest-\${DISTRO_COMMIT}.json"\nelse\n\tMANIFEST_URL="https://windsurf-stable.codeiumdata.com/\${PLATFORM}-reh-\${SERVER_ARCH}/\${DISTRO_QUALITY}/manifest-\${DISTRO_COMMIT}.json"\nfi\n\nif [[ "${u}" = "true" ]]; then\n\tSHA256_HASH=""\nelse\n\t# Curl the manifest URL, and get the sha256 hash\n\tSHA256_HASH="$(curl -s "$MANIFEST_URL" --max-time 5 | grep -o '"sha256hash": "[^"]*"' | sed 's/"sha256hash": "\\(.*\\)"/\\1/')"\n\n\tif [[ $? -ne 0 || -z $SHA256_HASH ]]; then\n\t\techo "Warning: could not get sha256 hash from manifest: will not verify checksum."\n\t\tSHA256_HASH=""\n\tfi\n\n\tif ! sha256sum --version > /dev/null 2>&1; then\n\t\techo "Warning: sha256sum could not be found: will not verify checksum."\n\t\tSHA256_HASH=""\n\tfi\nfi\n\n# https://www.freedesktop.org/software/systemd/man/os-release.html\nOS_RELEASE_ID="$(grep -i '^ID=' /etc/os-release 2>/dev/null | sed 's/^ID=//gi' | sed 's/"//g')"\nif [[ -z $OS_RELEASE_ID ]]; then\n\tOS_RELEASE_ID="$(grep -i '^ID=' /usr/lib/os-release 2>/dev/null | sed 's/^ID=//gi' | sed 's/"//g')"\n\tif [[ -z $OS_RELEASE_ID ]]; then\n\t\tOS_RELEASE_ID="unknown"\n\tfi\nfi\n\n# Create installation folder\nif [[ ! -d $SERVER_DIR ]]; then\n\tmkdir -p $SERVER_DIR\n\tif (( $? > 0 )); then\n\t\techo "Error creating server install directory"\n\t\tprint_install_results_and_exit 1\n\tfi\nfi\n\n# Acquire lock at the beginning of the script\nacquire_lock\n\n# Add trap to release lock on exit\ntrap release_lock EXIT INT TERM\n\nSERVER_DOWNLOAD_URL="$(echo "${d.replace(/\$\{/g,"\\${")}" | sed "s/\\\${quality}/$DISTRO_QUALITY/g" | sed "s/\\\${vscodeVersion}/$DISTRO_VSCODE_VERSION/g" | sed "s/\\\${commit}/$DISTRO_COMMIT/g" | sed "s/\\\${os}/$PLATFORM/g" | sed "s/\\\${arch}/$SERVER_ARCH/g" | sed "s/\\\${release}/$DISTRO_VSCODIUM_RELEASE/g" | sed "s/\\\${windsurfVersion}/$DISTRO_WINDSURF_VERSION/g")"\n\nif [[ "$PLATFORM" == "linux" ]]; then\n\t# Check ldd version based on format "ldd (.*) 2.28"\n\tversion=$(ldd --version | head -n 1 | grep -oE '[0-9]+.[0-9]+$')\n\tif (( $? > 0 )); then\n\t\techo "Warning: ldd not found"\n\t\tGLIBC_VERSION_GOOD="false"\n\telse\n\t\tmajor=$(echo "$version" | cut -d '.' -f 1)\n\t\tminor=$(echo "$version" | cut -d '.' -f 2)\n\n\t\tif [[ "$major" -eq 2 && "$minor" -ge 28 ]]; then\n\t\t\tGLIBC_VERSION_GOOD="true"\n\t\telse\n\t\t\tGLIBC_VERSION_GOOD="false"\n\t\tfi\n\tfi\n\n\tif [[ "$GLIBC_VERSION_GOOD" = "false" ]]; then\n\t\techo "Warning: valid glibc version not found. Windsurf only supports remote connections with glibc >= 2.28, such as Ubuntu 20.04, Debian 10, or CentOS 8."\n\tfi\nfi\n\n# Check if server script is already installed\nif [[ ! -f $SERVER_SCRIPT ]]; then\n\tpushd $SERVER_DIR > /dev/null\n\n\ttemp_file=$(mktemp)\n\tif [[ ! -z $(which wget) ]]; then\n\t\twget --tries=3 --timeout=10 --continue --quiet -O $temp_file $SERVER_DOWNLOAD_URL\n\telif [[ ! -z $(which curl) ]]; then\n\t\tcurl --retry 3 --connect-timeout 10 --location --show-error --silent --output $temp_file $SERVER_DOWNLOAD_URL\n\telse\n\t\techo "Error no tool to download server binary"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tif (( $? > 0 )); then\n\t\techo "Error downloading server from $SERVER_DOWNLOAD_URL"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tmv $temp_file vscode-server.tar.gz\n\n\t# Before extracting it, check the checksum\n\tif [[ ! -z $SHA256_HASH ]]; then\n\t\t# Calculate the checksum of the downloaded file\n\t\tCALCULATED_HASH="$(sha256sum vscode-server.tar.gz | cut -d ' ' -f 1)"\n\t\t\n\t\t# Compare with the expected hash\n\t\tif [[ "$CALCULATED_HASH" != "$SHA256_HASH" ]]; then\n\t\t\techo "Error: Checksum verification failed: this usually means the server failed to download correctly."\n\t\t\techo "Expected: $SHA256_HASH"\n\t\t\techo "Got: $CALCULATED_HASH"\n\n\t\t\trm -f vscode-server.tar.gz\n\n\t\t\tprint_install_results_and_exit 1\n\t\telse\n\t\t\techo "Checksum verification passed: $CALCULATED_HASH"\n\t\tfi\n\telse \n\t\techo "Skipping checksum verification"\n\tfi\n\n\ttar -xf vscode-server.tar.gz --strip-components 1\n\tif (( $? > 0 )); then\n\t\techo "Error while extracting server contents"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\tif [[ ! -f $SERVER_SCRIPT ]]; then\n\t\techo "Error server contents are corrupted"\n\t\tprint_install_results_and_exit 1\n\tfi\n\n\trm -f vscode-server.tar.gz\n\n\tpopd > /dev/null\nelse\n\techo "Server script already installed in $SERVER_SCRIPT"\nfi\n\n# Try to find if server is already running\nif [[ -f $SERVER_PIDFILE ]]; then\n\tSERVER_PID="$(cat $SERVER_PIDFILE)"\n\tSERVER_RUNNING_PROCESS="$(ps -o pid,args -p $SERVER_PID | grep $SERVER_SCRIPT)"\nelse\n\tSERVER_RUNNING_PROCESS="$(ps -o pid,args -A | grep $SERVER_SCRIPT | grep -v grep)"\n\tif [[ -z $SERVER_RUNNING_PROCESS ]]; then\n\t\tSERVER_PID=\n\telse\n\t\tSERVER_PID="$(echo $SERVER_RUNNING_PROCESS | cut -d ' ' -f 1 | head -n 1)"\n\tfi\nfi\n\nif [[ -z $SERVER_RUNNING_PROCESS ]]; then\n\tif [[ -f $SERVER_LOGFILE ]]; then\n\t\trm $SERVER_LOGFILE\n\tfi\n\tif [[ -f $SERVER_TOKENFILE ]]; then\n\t\trm $SERVER_TOKENFILE\n\tfi\n\n\ttouch $SERVER_TOKENFILE\n\tchmod 600 $SERVER_TOKENFILE\n\tSERVER_CONNECTION_TOKEN="${f}"\n\techo $SERVER_CONNECTION_TOKEN > $SERVER_TOKENFILE\n\n\t$SERVER_SCRIPT --start-server --host=127.0.0.1 $SERVER_LISTEN_FLAG $SERVER_INITIAL_EXTENSIONS --connection-token-file $SERVER_TOKENFILE --telemetry-level off --use-host-proxy --disable-websocket-compression --without-browser-env-var --enable-remote-auto-shutdown --accept-server-license-terms &> $SERVER_LOGFILE &\n\techo $! > $SERVER_PIDFILE\nelse\n\techo "Server script is already running $SERVER_SCRIPT"\nfi\n\nif [[ -f $SERVER_TOKENFILE ]]; then\n\tSERVER_CONNECTION_TOKEN="$(cat $SERVER_TOKENFILE)"\nelse\n\techo "Error server token file not found $SERVER_TOKENFILE"\n\tprint_install_results_and_exit 1\nfi\n\nif [[ -f $SERVER_LOGFILE ]]; then\n\tfor i in {1..5}; do\n\t\tLISTENING_ON="$(cat $SERVER_LOGFILE | grep -E 'Extension host agent listening on .+' | sed 's/Extension host agent listening on //')"\n\t\tif [[ -n $LISTENING_ON ]]; then\n\t\t\tbreak\n\tfi\n\t\tsleep 0.5\n\tdone\n\n\tif [[ -z $LISTENING_ON ]]; then\n\t\techo "Error server did not start sucessfully"\n\t\tprint_install_results_and_exit 1\n\tfi\nelse\n\techo "Error server log file not found $SERVER_LOGFILE"\n\tprint_install_results_and_exit 1\nfi\n\n# Finish server setup and keep script running\nif [[ -z $SERVER_RUNNING_PROCESS ]]; then\n\techo "${e}: start"\n\techo "exitCode==0=="\n\techo "listeningOn==$LISTENING_ON=="\n\techo "connectionToken==$SERVER_CONNECTION_TOKEN=="\n\techo "logFile==$SERVER_LOGFILE=="\n\techo "osReleaseId==$OS_RELEASE_ID=="\n\techo "arch==$ARCH=="\n\techo "platform==$PLATFORM=="\n\techo "tmpDir==$TMP_DIR=="\n\t${a.map((e=>`echo "${e}==$${e}=="`)).join("\n")}\n\techo "${e}: end"\n\n\techo "${e}: Server installation script done"\n\n\tSERVER_PID="$(cat $SERVER_PIDFILE)"\n\tSERVER_RUNNING_PROCESS="$(ps -o pid,args -p $SERVER_PID | grep $SERVER_SCRIPT)"\n\n\trelease_lock\n\n\twhile [[ -n $SERVER_RUNNING_PROCESS ]]; do\n\t\tsleep 300;\n\t\tSERVER_RUNNING_PROCESS="$(ps -o pid,args -p $SERVER_PID | grep $SERVER_SCRIPT)"\n\tdone\nelse\n\tprint_install_results_and_exit 0\nfi\n`}t.ServerInstallError=l,t.getDefaultDownloadUrlTemplate=c,t.installCodeServer=async function(e,t,n,r,o,i,u){const f=s.randomBytes(12).toString("hex"),h=await(0,a.getVSCodeServerConfig)(),m=String(s.randomInt(4294967295));let p=n??h.serverDownloadUrlTemplate??c(h.quality);p===c("stable")&&"insider"===h.quality&&(p=c("insider"));const _=d({id:f,vscodeVersion:h.vscodeVersion,windsurfVersion:h.windsurfVersion,commit:h.commit,quality:h.quality,release:h.release,extensionIds:r,envVariables:o,serverApplicationName:h.serverApplicationName,serverDataFolderName:h.serverDataFolderName,serverDownloadUrlTemplate:p,disableServerChecksum:u},m);i.trace("Running server install script");const E=await e.exec("bash",["-c",`'${_.replace(/'/g,"'\\''")}'`],t);i.trace("Passed server install script");const R=new RegExp(`${f}: Server installation script done`,"m"),S=await Promise.race([E.exitPromise.then((e=>({stdout:E.stdout,stderr:E.stderr,exitCode:e.exitCode}))),new Promise((e=>{E.onStdoutData((t=>{R.test(t.toString("utf8"))&&e({stdout:E.stdout,stderr:E.stderr,exitCode:0})}))}))]);S.exitCode&&i.trace("Server install command stderr:",S.stderr),i.trace("Server install command stdout:",S.stdout);const w=function(e,t){const n=`${t}: start`,r=`${t}: end`,o=e.indexOf(n);if(o<0)return;const i=e.indexOf(r,o+n.length);if(i<0)return;const s={},a=e.substring(o+n.length,i).split(/\r?\n/);for(const e of a){const[t,n]=e.split("==");s[t]=n}return s}(S.stdout,f);if(!w)throw new l("Failed parsing install script output");const g=parseInt(w.exitCode,10);if(0!==g)throw new l("Couldn't install vscode server on remote server, install script returned non-zero exit status");const O=parseInt(w.listeningOn,10);i.trace(`Listening on port ${O}`);const v=Object.fromEntries(Object.entries(w).filter((([e])=>o.includes(e))));return{exitCode:g,listeningOn:O,connectionToken:w.connectionToken,logFile:w.logFile,osReleaseId:w.osReleaseId,arch:w.arch,platform:w.platform,tmpDir:w.tmpDir,...v}},t.generateBashInstallScript=d},506:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0}),t.WSLManager=void 0;const s=i(n(317)),a=n(166),l="wsl.exe";t.WSLManager=class{constructor(e){this.logger=e}async listDistros(){const e=this._runWSLCommand(["--list","--verbose"],"utf16le"),{exitCode:t}=await e.exitPromise,{stdout:n,stderr:r}=e;if(t)throw this.logger.trace(`Command wsl listDistros exited with code ${t}`,n+"\n\n"+r),new Error(`Command wsl listDistros exited with code ${t}`);const o=/(?<default>\*|\s)\s+(?<name>[\w\.-]+)\s+(?<state>[\w]+)\s+(?<version>\d)/,i=[];for(const e of n.split(/\r?\n/)){const t=e.match(o);t&&t.groups&&i.push({isDefault:"*"===t.groups.default,name:t.groups.name,state:t.groups.state,version:t.groups.version})}return i}async listOnlineDistros(){const e=this._runWSLCommand(["--list","--online"],"utf16le"),{exitCode:t}=await e.exitPromise,{stdout:n,stderr:r}=e;if(t)throw this.logger.trace(`Command wsl listOnlineDistros exited with code ${t}`,n+"\n\n"+r),new Error(`Command wsl listOnlineDistros exited with code ${t}`);let o=n.split(/\r?\n/);const i=o.findIndex((e=>/\s*NAME\s+FRIENDLY NAME\s*/.test(e)));o=o.slice(i+1);const s=/(?<name>[\w\.-]+)\s+(?<friendlyName>\w.+\w)/,a=[];for(const e of o){const t=e.match(s);t&&t.groups&&a.push({name:t.groups.name,friendlyName:t.groups.friendlyName})}return a}async setDefaultDistro(e){const t=this._runWSLCommand(["--set-default",e],"utf16le"),{exitCode:n}=await t.exitPromise,{stdout:r,stderr:o}=t;if(n)throw this.logger.trace(`Command wsl setDefaultDistro exited with code ${n}`,r+"\n\n"+o),new Error(`Command wsl setDefaultDistro exited with code ${n}`)}async deleteDistro(e){const t=this._runWSLCommand(["--unregister",e],"utf16le"),{exitCode:n}=await t.exitPromise,{stdout:r,stderr:o}=t;if(n)throw this.logger.trace(`Command wsl deleteDistro exited with code ${n}`,r+"\n\n"+o),new Error(`Command wsl deleteDistro exited with code ${n}`)}async exec(e,t,n){return this._runWSLCommand(["--distribution",n,"--",e,...t],"utf8")}_runWSLCommand(e,t){this.logger.trace(`Running WSL command: ${l} ${e.join(" ")}`);const n=s.spawn(l,e,{windowsHide:!0,windowsVerbatimArguments:!0}),r=new a.EventEmitter,o=[],i=new a.EventEmitter,c=[];return n.stdout.on("data",(e=>{o.push(e),r.fire(e)})),n.stderr.on("data",(e=>{c.push(e),i.fire(e)})),{get stdout(){return Buffer.concat(o).toString(t)},get stderr(){return Buffer.concat(c).toString(t)},get onStdoutData(){return r.event},get onStderrData(){return i.event},exitPromise:new Promise(((t,r)=>{n.on("error",(t=>{this.logger.error(`Error running WSL command: ${l} ${e.join(" ")}`,t),r(t)})),n.on("exit",((e,n)=>{t({exitCode:e??0})}))}))}}}},309:function(e,t,n){var r=this&&this.__createBinding||(Object.create?function(e,t,n,r){void 0===r&&(r=n);var o=Object.getOwnPropertyDescriptor(t,n);o&&!("get"in o?!t.__esModule:o.writable||o.configurable)||(o={enumerable:!0,get:function(){return t[n]}}),Object.defineProperty(e,r,o)}:function(e,t,n,r){void 0===r&&(r=n),e[r]=t[n]}),o=this&&this.__setModuleDefault||(Object.create?function(e,t){Object.defineProperty(e,"default",{enumerable:!0,value:t})}:function(e,t){e.default=t}),i=this&&this.__importStar||function(e){if(e&&e.__esModule)return e;var t={};if(null!=e)for(var n in e)"default"!==n&&Object.prototype.hasOwnProperty.call(e,n)&&r(t,e,n);return o(t,e),t};Object.defineProperty(t,"__esModule",{value:!0});const s=i(n(398));class a{getTerminal(){return s.window.terminals.find((e=>e.name===a.NAME))||s.window.createTerminal(a.NAME)}runCommand(e){const t=this.getTerminal();t.show(!1),t.sendText(e,!0)}}a.NAME="WSL",t.default=new a},398:e=>{e.exports=require("vscode")},317:e=>{e.exports=require("child_process")},982:e=>{e.exports=require("crypto")},896:e=>{e.exports=require("fs")},928:e=>{e.exports=require("path")}},t={},n=function n(r){var o=t[r];if(void 0!==o)return o.exports;var i=t[r]={exports:{}};return e[r].call(i.exports,i,i.exports,n),i.exports}(927),r=exports;for(var o in n)r[o]=n[o];n.__esModule&&Object.defineProperty(r,"__esModule",{value:!0})})();
//# sourceMappingURL=https://main.vscode-cdn.net/sourcemaps/b8f002c02d165600299a109bf21d02d139c52644/extensions/windsurf-remote-wsl/dist/extension.js.map